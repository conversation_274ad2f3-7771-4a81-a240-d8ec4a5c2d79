// models/Payment.ts
import mongoose, { Schema, Document, Model } from 'mongoose';
import { PaymentStatus, PaymentMethodType, PaymentProvider } from '@/types/payment';

// Interface for the Payment document
export interface IPayment extends Document {
  orderId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethodType;
  provider: PaymentProvider;
  transactionId?: string;
  providerPaymentId?: string;
  processingFee?: number;
  netAmount?: number;
  description?: string;
  metadata?: Record<string, any>;
  failureReason?: string;
  refundAmount?: number;
  refundReason?: string;
  processedAt?: Date;
  settledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Interface for model methods and statics
interface IPaymentModel extends Model<IPayment> {
  findByOrderId(orderId: string): Promise<IPayment[]>;
  findByUserId(userId: string): Promise<IPayment[]>;
  findByStatus(status: PaymentStatus): Promise<IPayment[]>;
  findByTransactionId(transactionId: string): Promise<IPayment | null>;
  getTotalAmountByUser(userId: string): Promise<number>;
  getPaymentAnalytics(startDate?: Date, endDate?: Date): Promise<any>;
}

// Payment status enum values
const PaymentStatusValues = [
  'pending',
  'processing', 
  'completed',
  'failed',
  'cancelled',
  'refunded',
  'partially_refunded'
] as const;

// Payment method enum values
const PaymentMethodValues = [
  'credit_card',
  'bank_transfer',
  'eft',
  'paypal',
  'apple_pay',
  'google_pay'
] as const;

// Payment provider enum values
const PaymentProviderValues = [
  'stripe',
  'paypal',
  'payfast',
  'ozow',
  'yoco',
  'peach_payments'
] as const;

// Payment schema definition
const PaymentSchema: Schema<IPayment, IPaymentModel> = new Schema(
  {
    orderId: {
      type: Schema.Types.ObjectId,
      ref: 'GroupOrder',
      required: true,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
      validate: {
        validator: function(value: number) {
          return value > 0;
        },
        message: 'Payment amount must be greater than 0'
      }
    },
    currency: {
      type: String,
      required: true,
      default: 'ZAR',
      uppercase: true,
      validate: {
        validator: function(value: string) {
          return ['ZAR', 'USD', 'EUR', 'GBP'].includes(value);
        },
        message: 'Unsupported currency'
      }
    },
    status: {
      type: String,
      enum: PaymentStatusValues,
      default: 'pending',
      required: true,
      index: true
    },
    paymentMethod: {
      type: String,
      enum: PaymentMethodValues,
      required: true,
      index: true
    },
    provider: {
      type: String,
      enum: PaymentProviderValues,
      required: true
    },
    transactionId: {
      type: String,
      unique: true,
      sparse: true,
      index: true
    },
    providerPaymentId: {
      type: String,
      index: true
    },
    processingFee: {
      type: Number,
      min: 0,
      default: 0
    },
    netAmount: {
      type: Number,
      min: 0
    },
    description: {
      type: String,
      maxlength: 500
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {}
    },
    failureReason: {
      type: String,
      maxlength: 1000
    },
    refundAmount: {
      type: Number,
      min: 0,
      default: 0
    },
    refundReason: {
      type: String,
      maxlength: 500
    },
    processedAt: {
      type: Date,
      index: true
    },
    settledAt: {
      type: Date,
      index: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual for calculating net amount if not set
PaymentSchema.virtual('calculatedNetAmount').get(function() {
  return this.netAmount || (this.amount - (this.processingFee || 0));
});

// Virtual for checking if payment is successful
PaymentSchema.virtual('isSuccessful').get(function() {
  return this.status === 'completed';
});

// Virtual for checking if payment is pending
PaymentSchema.virtual('isPending').get(function() {
  return ['pending', 'processing'].includes(this.status);
});

// Virtual for checking if payment failed
PaymentSchema.virtual('isFailed').get(function() {
  return ['failed', 'cancelled'].includes(this.status);
});

// Pre-save middleware to calculate net amount
PaymentSchema.pre('save', function(next) {
  if (!this.netAmount) {
    this.netAmount = this.amount - (this.processingFee || 0);
  }
  next();
});

// Pre-save middleware to set processedAt when status changes to completed
PaymentSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'completed' && !this.processedAt) {
    this.processedAt = new Date();
  }
  next();
});

// Static methods
PaymentSchema.statics.findByOrderId = async function(orderId: string) {
  return this.find({ orderId: new mongoose.Types.ObjectId(orderId) })
    .populate('userId', 'name email')
    .sort({ createdAt: -1 });
};

PaymentSchema.statics.findByUserId = async function(userId: string) {
  return this.find({ userId: new mongoose.Types.ObjectId(userId) })
    .populate('orderId')
    .sort({ createdAt: -1 });
};

PaymentSchema.statics.findByStatus = async function(status: PaymentStatus) {
  return this.find({ status })
    .populate('userId', 'name email')
    .populate('orderId')
    .sort({ createdAt: -1 });
};

PaymentSchema.statics.findByTransactionId = async function(transactionId: string) {
  return this.findOne({ transactionId });
};

PaymentSchema.statics.getTotalAmountByUser = async function(userId: string) {
  const result = await this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId), status: 'completed' } },
    { $group: { _id: null, total: { $sum: '$amount' } } }
  ]);
  return result.length > 0 ? result[0].total : 0;
};

PaymentSchema.statics.getPaymentAnalytics = async function(startDate?: Date, endDate?: Date) {
  const matchStage: any = {};
  if (startDate && endDate) {
    matchStage.createdAt = { $gte: startDate, $lte: endDate };
  }

  const analytics = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        averageAmount: { $avg: '$amount' },
        successfulPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        failedPayments: {
          $sum: { $cond: [{ $in: ['$status', ['failed', 'cancelled']] }, 1, 0] }
        }
      }
    }
  ]);

  const result = analytics[0] || {
    totalPayments: 0,
    totalAmount: 0,
    averageAmount: 0,
    successfulPayments: 0,
    failedPayments: 0
  };

  result.successRate = result.totalPayments > 0 
    ? (result.successfulPayments / result.totalPayments) * 100 
    : 0;
  result.failureRate = result.totalPayments > 0 
    ? (result.failedPayments / result.totalPayments) * 100 
    : 0;

  return result;
};

// Indexes for performance
PaymentSchema.index({ orderId: 1, status: 1 });
PaymentSchema.index({ userId: 1, status: 1 });
PaymentSchema.index({ createdAt: -1 });
PaymentSchema.index({ status: 1, createdAt: -1 });
PaymentSchema.index({ paymentMethod: 1, status: 1 });

// Prevent recompiling the model
let PaymentModel: IPaymentModel;

try {
  PaymentModel = mongoose.model<IPayment, IPaymentModel>('Payment');
} catch {
  PaymentModel = mongoose.model<IPayment, IPaymentModel>('Payment', PaymentSchema);
}

export const Payment = PaymentModel;
