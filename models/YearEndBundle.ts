// import mongoose, { Schema, type Document, model } from "mongoose"

// export interface IYearEndBundle extends Document {
//   userId: mongoose.Types.ObjectId
//   name: string
//   description: string
//   price: number
//   status: "active" | "inactive" | "pending"
//   startDate: Date
//   endDate: Date
//   bundleSize: "basic" | "standard" | "premium"
//   itemList: string[]
//   deliveryMonth: number // Month of the year when the bundle is delivered
//   createdAt: Date
//   updatedAt: Date
// }

// const YearEndBundleSchema: Schema<IYearEndBundle> = new Schema(
//   {
//     userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
//     name: { type: String, required: true },
//     description: { type: String, required: true },
//     price: { type: Number, required: true },
//     status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
//     startDate: { type: Date },
//     endDate: { type: Date },
//     bundleSize: { type: String, enum: ["basic", "standard", "premium"], required: true },
//     itemList: [{ type: String }],
//     deliveryMonth: { type: Number, min: 1, max: 12, required: true },
//   },
//   { timestamps: true },
// )

// // Indexes for efficient queries
// YearEndBundleSchema.index({ userId: 1 })
// YearEndBundleSchema.index({ status: 1 })

// export const YearEndBundle =
//   mongoose.models.YearEndBundle || model<IYearEndBundle>("YearEndBundle", YearEndBundleSchema)



import mongoose, { Schema, type Document, model } from "mongoose"

export interface IYearEndBundle extends Document {
  userId: mongoose.Types.ObjectId
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: "active" | "inactive" | "pending"
  startDate: Date
  endDate: Date
  bundleSize: "basic" | "standard" | "premium"
  itemList: string[]
  deliveryMonth: number // Month of the year when the bundle is delivered
  totalSavingsGoal: number
  createdAt: Date
  updatedAt: Date
}

const YearEndBundleSchema: Schema<IYearEndBundle> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    price: { type: Number, required: true },
    monthlyContribution: { type: Number, required: true },
    status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
    startDate: { type: Date },
    endDate: { type: Date },
    bundleSize: { type: String, enum: ["basic", "standard", "premium"], required: true },
    itemList: [{ type: String }],
    deliveryMonth: { type: Number, min: 1, max: 12, required: true },
    totalSavingsGoal: { type: Number, required: true },
  },
  { timestamps: true },
)

YearEndBundleSchema.index({ userId: 1 })
YearEndBundleSchema.index({ status: 1 })

export const YearEndBundle =
  mongoose.models.YearEndBundle || model<IYearEndBundle>("YearEndBundle", YearEndBundleSchema)
