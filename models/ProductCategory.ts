import mongoose, { Schema, Document, model } from 'mongoose';

export interface IProductCategory extends Document {
  name: string;
  description: string;
  product_count: number;
  is_active: boolean;
  parent_category?: mongoose.Types.ObjectId;
  archivedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ProductCategorySchema: Schema<IProductCategory> = new Schema(
  {
    name: { 
      type: String, 
      required: true, 
      unique: true,
      index: true  
    },
    description: { type: String, required: true },
    product_count: { type: Number, default: 0 },
    is_active: { type: Boolean, default: true },
    parent_category: { 
      type: mongoose.Types.ObjectId, 
      ref: 'ProductCategory',
      index: true  
    },
    archivedAt: { 
      type: Date, 
      default: null,
      index: true  
    }
  },
  { timestamps: true }
);

export const ProductCategory = mongoose.models.ProductCategory || model<IProductCategory>('ProductCategory', ProductCategorySchema);
