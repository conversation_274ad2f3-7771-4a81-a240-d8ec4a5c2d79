// models/Location.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface ILocation extends Document {
  name: string;
  townshipId: mongoose.Types.ObjectId;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const LocationSchema: Schema<ILocation> = new Schema(
  {
    name: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    townshipId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Township', 
      required: true 
    },
    description: { 
      type: String,
      trim: true,
      maxlength: 500
    },
    isActive: { 
      type: Boolean, 
      default: true 
    }
  },
  { timestamps: true }
);

// Compound unique index to prevent duplicate location names within the same township
LocationSchema.index({ name: 1, townshipId: 1 }, { unique: true });

// Additional indexes for efficient queries
// Note: name index is covered by compound unique index above
LocationSchema.index({ townshipId: 1 });
LocationSchema.index({ isActive: 1 });
LocationSchema.index({ townshipId: 1, isActive: 1 });

// Virtual for populated township
LocationSchema.virtual('township', {
  ref: 'Township',
  localField: 'townshipId',
  foreignField: '_id',
  justOne: true
});

// Ensure virtual fields are serialized
LocationSchema.set('toJSON', { virtuals: true });
LocationSchema.set('toObject', { virtuals: true });

// Export the model
export const Location = mongoose.models.Location || model<ILocation>('Location', LocationSchema);
