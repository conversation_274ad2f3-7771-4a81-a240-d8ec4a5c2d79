// import mongoose, { Schema, type Document, model } from "mongoose"

// export interface IFuneralBenefits extends Document {
//   userId: mongoose.Types.ObjectId
//   name: string
//   description: string
//   price: number
//   status: "active" | "inactive" | "pending"
//   startDate: Date
//   endDate: Date
//   coverageAmount: number
//   beneficiaries: {
//     name: string
//     relationship: string
//     contactNumber: string
//   }[]
//   waitingPeriod: number // in months
//   createdAt: Date
//   updatedAt: Date
// }

// const FuneralBenefitsSchema: Schema<IFuneralBenefits> = new Schema(
//   {
//     userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
//     name: { type: String, required: true },
//     description: { type: String, required: true },
//     price: { type: Number, required: true },
//     status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
//     startDate: { type: Date },
//     endDate: { type: Date },
//     coverageAmount: { type: Number, required: true },
//     beneficiaries: [
//       {
//         name: { type: String, required: true },
//         relationship: { type: String, required: true },
//         contactNumber: { type: String, required: true },
//       },
//     ],
//     waitingPeriod: { type: Number, required: true },
//   },
//   { timestamps: true },
// )

// // Indexes for efficient queries
// FuneralBenefitsSchema.index({ userId: 1 })
// FuneralBenefitsSchema.index({ status: 1 })

// export const FuneralBenefits =
//   mongoose.models.FuneralBenefits || model<IFuneralBenefits>("FuneralBenefits", FuneralBenefitsSchema)



import mongoose, { Schema, type Document, model } from "mongoose"

export interface IFuneralBenefits extends Document {
  userId: mongoose.Types.ObjectId
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: "active" | "inactive" | "pending"
  startDate: Date
  endDate: Date
  coverageAmount: number
  beneficiaries: {
    name: string
    relationship: string
    contactNumber: string
  }[]
  waitingPeriod: number // in months
  createdAt: Date
  updatedAt: Date
}

const FuneralBenefitsSchema: Schema<IFuneralBenefits> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    price: { type: Number, required: true },
    monthlyContribution: { type: Number, required: true },
    status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
    startDate: { type: Date },
    endDate: { type: Date },
    coverageAmount: { type: Number, required: true },
    beneficiaries: [
      {
        name: { type: String, required: true },
        relationship: { type: String, required: true },
        contactNumber: { type: String, required: true },
      },
    ],
    waitingPeriod: { type: Number, required: true },
  },
  { timestamps: true },
)

FuneralBenefitsSchema.index({ userId: 1 })
FuneralBenefitsSchema.index({ status: 1 })

export const FuneralBenefits =
  mongoose.models.FuneralBenefits || model<IFuneralBenefits>("FuneralBenefits", FuneralBenefitsSchema)
