// import mongoose, { Schema, type Document, model } from "mongoose"

// export interface IMonthlyGroceries extends Document {
//   userId: mongoose.Types.ObjectId
//   name: string
//   description: string
//   price: number
//   status: "active" | "inactive" | "pending"
//   startDate: Date
//   endDate: Date
//   groceryList: string[]
//   deliveryDate: number // Day of the month for delivery
//   preferredStores: string[]
//   createdAt: Date
//   updatedAt: Date
// }

// const MonthlyGroceriesSchema: Schema<IMonthlyGroceries> = new Schema(
//   {
//     userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
//     name: { type: String, required: true },
//     description: { type: String, required: true },
//     price: { type: Number, required: true },
//     status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
//     startDate: { type: Date },
//     endDate: { type: Date },
//     groceryList: [{ type: String }],
//     deliveryDate: { type: Number, min: 1, max: 31, required: true },
//     preferredStores: [{ type: String }],
//   },
//   { timestamps: true },
// )

// // Indexes for efficient queries
// MonthlyGroceriesSchema.index({ userId: 1 })
// MonthlyGroceriesSchema.index({ status: 1 })

// export const MonthlyGroceries =
//   mongoose.models.MonthlyGroceries || model<IMonthlyGroceries>("MonthlyGroceries", MonthlyGroceriesSchema)




import mongoose, { Schema, type Document, model } from "mongoose"

export interface IMonthlyGroceries extends Document {
  userId: mongoose.Types.ObjectId
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: "active" | "inactive" | "pending"
  startDate: Date
  endDate: Date
  groceryList: string[]
  deliveryDate: number // Day of the month for delivery
  preferredStores: string[]
  createdAt: Date
  updatedAt: Date
}

const MonthlyGroceriesSchema: Schema<IMonthlyGroceries> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    price: { type: Number, required: true },
    monthlyContribution: { type: Number, required: true },
    status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
    startDate: { type: Date },
    endDate: { type: Date },
    groceryList: [{ type: String }],
    deliveryDate: { type: Number, min: 1, max: 31, required: true },
    preferredStores: [{ type: String }],
  },
  { timestamps: true },
)

MonthlyGroceriesSchema.index({ userId: 1 })
MonthlyGroceriesSchema.index({ status: 1 })

export const MonthlyGroceries =
  mongoose.models.MonthlyGroceries || model<IMonthlyGroceries>("MonthlyGroceries", MonthlyGroceriesSchema)
