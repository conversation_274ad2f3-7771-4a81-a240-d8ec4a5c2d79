

// models/StokvelGroup.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface IStokvelGroup extends Document {
  name: string;
  description: string;
  members: mongoose.Types.ObjectId[];
  admin: mongoose.Types.ObjectId;
  locationId?: mongoose.Types.ObjectId; // NEW: Reference to Location (optional during migration)
  geolocation?: string; // DEPRECATED: Keep for migration, make optional
  totalSales: number;
  avgOrderValue: number;
  activeOrders: number;
  bulkOrderThreshold: number; // Minimum order amount for bulk purchasing
  pendingOrderAmount: number; // Current pending order total
  deliveryStatus: 'pending' | 'in-transit' | 'delivered';
  createdAt: Date;
  updatedAt: Date;
}

const StokvelGroupSchema: Schema<IStokvelGroup> = new Schema(
  {
    name: { type: String, required: true },
    description: { type: String, required: true },
    members: [{ type: mongoose.Types.ObjectId, ref: 'User' }],
    admin: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    locationId: { type: Schema.Types.ObjectId, ref: 'Location', required: false }, // NEW: Reference to Location
    geolocation: { type: String, required: false }, // DEPRECATED: Make optional for migration
    totalSales: { type: Number, default: 0 },
    avgOrderValue: { type: Number, default: 0 },
    activeOrders: { type: Number, default: 0 },
    bulkOrderThreshold: { type: Number, default: 1000 },
    pendingOrderAmount: { type: Number, default: 0 },
    deliveryStatus: { type: String, default: 'pending' },
  },
  { timestamps: true }
);

// Indexes for efficient queries
StokvelGroupSchema.index({ name: 1 });
StokvelGroupSchema.index({ admin: 1 });
StokvelGroupSchema.index({ members: 1 });
StokvelGroupSchema.index({ locationId: 1 }); // NEW: Index for location-based queries
StokvelGroupSchema.index({ geolocation: 1 }); // Keep for migration period

// Virtual for populated location with full hierarchy
StokvelGroupSchema.virtual('locationHierarchy', {
  ref: 'Location',
  localField: 'locationId',
  foreignField: '_id',
  justOne: true,
  populate: {
    path: 'townshipId',
    populate: {
      path: 'cityId',
      populate: {
        path: 'provinceId'
      }
    }
  }
});

// Ensure virtual fields are serialized
StokvelGroupSchema.set('toJSON', { virtuals: true });
StokvelGroupSchema.set('toObject', { virtuals: true });

// Export the model
export const StokvelGroup = mongoose.models.StokvelGroup || model<IStokvelGroup>('StokvelGroup', StokvelGroupSchema);
