// models/ShoppingCart.ts
import mongoose, { Schema, Document, Model, model } from 'mongoose';

export interface ICartItem {
  _id: string;
  product: mongoose.Types.ObjectId;
  quantity: number;
  price?: number;
}

export interface IShoppingCart extends Document {
  user: mongoose.Types.ObjectId;
  items: ICartItem[];
  total: number;
  groupId?: mongoose.Types.ObjectId;
  groupOrderId?: mongoose.Types.ObjectId;
  isFinalized: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Interface for model methods and statics
interface IShoppingCartModel extends Model<IShoppingCart> {
  // You can add custom model methods here if needed
  findByUser(userId: string): Promise<IShoppingCart | null>;
  findByGroup(groupId: string): Promise<IShoppingCart[]>;
}

const CartItemSchema: Schema = new Schema({
  _id: { type: String },
  product: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
  quantity: { type: Number, required: true, min: 1 },
  price: { type: Number, required: false },
});

const ShoppingCartSchema: Schema<IShoppingCart, IShoppingCartModel> = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    items: [CartItemSchema],
    total: { type: Number, required: true, default: 0 },
    groupId: { type: Schema.Types.ObjectId, ref: 'StokvelGroup', default: null },
    groupOrderId: { type: Schema.Types.ObjectId, ref: 'GroupOrder', default: null },
    isFinalized: { type: Boolean, default: false },
  },
  { timestamps: true }
);

// Add custom model methods
ShoppingCartSchema.statics.findByUser = async function(userId: string) {
  return this.findOne({ user: userId }).exec();
};

ShoppingCartSchema.statics.findByGroup = async function(groupId: string) {
  return this.find({ groupId }).exec();
};

// Indexes for efficient queries
ShoppingCartSchema.index({ user: 1 });
ShoppingCartSchema.index({ groupId: 1 });

// Prevent recompiling the model
let ShoppingCartModel: IShoppingCartModel;

try {
  // Try to retrieve existing model
  ShoppingCartModel = mongoose.model<IShoppingCart, IShoppingCartModel>('ShoppingCart')
} catch {
  // If model doesn't exist, create it
  ShoppingCartModel = mongoose.model<IShoppingCart, IShoppingCartModel>('ShoppingCart', ShoppingCartSchema)
}

export const ShoppingCart = ShoppingCartModel;
