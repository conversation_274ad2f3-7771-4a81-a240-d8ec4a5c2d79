// // models/index.ts
// import './User';
// import './StokvelGroup';
// import './Product';
// import './ProductCategory';
// import './ShoppingCart';
// import './Referral';

// export { User } from './User';
// export { StokvelGroup } from './StokvelGroup';
// export { Product } from './Product';
// export { ProductCategory } from './ProductCategory';
// export { ShoppingCart } from './ShoppingCart';
// export { Referral } from './Referral';



// models/index.ts
import '../lib/dbconnect';

// Import all models
// import './BaseSubscription';
import './City';
import './Delivery';
import './FuneralBenefits';
import './GrocerySchoolBundle';
import './GroupOrder';
import './Location';
import './LoanApplication';
import './MemberOrder';
import './MonthlyGroceries';
import './Product';
import './ProductArchive';
import './ProductCategory';
import './ProductCategoryArchive';
import './ProductRating';
import './Province';
import './Referral';
import './ShoppingCart';
import './StokvelGroup';
import './Township';
import './User';
import './Wishlist';
import './YearEndBundle';

// Export all models and their types
// export * from './BaseSubscription';
export * from './City';
export * from './Delivery';
export * from './FuneralBenefits';
export * from './GrocerySchoolBundle';
export * from './GroupOrder';
export * from './Location';
export * from './LoanApplication';
export * from './MemberOrder';
export * from './MonthlyGroceries';
export * from './Product';
export * from './ProductArchive';
export * from './ProductCategory';
export * from './ProductCategoryArchive';
export * from './ProductRating';
export * from './Province';
export * from './Referral';
export * from './ShoppingCart';
export * from './StokvelGroup';
export * from './Township';
export * from './User';
export * from './Wishlist';
export * from './YearEndBundle';