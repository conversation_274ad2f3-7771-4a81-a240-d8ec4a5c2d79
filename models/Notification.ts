// models/Notification.ts
import mongoose, { Schema, Document, Model } from 'mongoose';
import { 
  NotificationType, 
  NotificationPriority, 
  DeliveryChannel, 
  NotificationStatus,
  INotification 
} from '@/types/notifications';

// Notification type enum values
const NotificationTypeValues = [
  'order_confirmation',
  'order_status_update',
  'payment_confirmation',
  'payment_failed',
  'group_milestone',
  'group_invitation',
  'group_order_finalized',
  'shipping_update',
  'delivery_confirmation',
  'discount_available',
  'cart_reminder',
  'system_announcement',
  'account_update',
  'security_alert'
] as const;

// Priority enum values
const NotificationPriorityValues = [
  'low',
  'normal',
  'high',
  'urgent'
] as const;

// Delivery channel enum values
const DeliveryChannelValues = [
  'in_app',
  'email',
  'sms',
  'push',
  'webhook'
] as const;

// Status enum values
const NotificationStatusValues = [
  'pending',
  'sent',
  'delivered',
  'read',
  'failed',
  'expired'
] as const;

// Interface for model methods and statics
interface INotificationModel extends Model<INotification> {
  findByUserId(userId: string, options?: any): Promise<INotification[]>;
  findUnreadByUserId(userId: string): Promise<INotification[]>;
  markAsRead(notificationIds: string[]): Promise<number>;
  markAsDelivered(notificationId: string, channel: DeliveryChannel): Promise<INotification | null>;
  getUnreadCount(userId: string): Promise<number>;
  getAnalytics(startDate?: Date, endDate?: Date): Promise<any>;
  cleanupExpired(): Promise<number>;
}

// Notification schema definition
const NotificationSchema: Schema<INotification, INotificationModel> = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    type: {
      type: String,
      enum: NotificationTypeValues,
      required: true,
      index: true
    },
    priority: {
      type: String,
      enum: NotificationPriorityValues,
      default: 'normal',
      required: true,
      index: true
    },
    title: {
      type: String,
      required: true,
      maxlength: 200
    },
    message: {
      type: String,
      required: true,
      maxlength: 1000
    },
    data: {
      type: Schema.Types.Mixed,
      default: {}
    },
    channels: [{
      type: String,
      enum: DeliveryChannelValues,
      required: true
    }],
    status: {
      type: String,
      enum: NotificationStatusValues,
      default: 'pending',
      required: true,
      index: true
    },
    readAt: {
      type: Date,
      index: true
    },
    deliveredAt: {
      type: Date,
      index: true
    },
    expiresAt: {
      type: Date,
      index: true
    },
    actionUrl: {
      type: String,
      maxlength: 500
    },
    actionText: {
      type: String,
      maxlength: 50
    },
    imageUrl: {
      type: String,
      maxlength: 500
    },
    groupId: {
      type: Schema.Types.ObjectId,
      ref: 'Group',
      index: true
    },
    orderId: {
      type: Schema.Types.ObjectId,
      ref: 'GroupOrder',
      index: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual for checking if notification is read
NotificationSchema.virtual('isRead').get(function() {
  return !!this.readAt;
});

// Virtual for checking if notification is delivered
NotificationSchema.virtual('isDelivered').get(function() {
  return !!this.deliveredAt;
});

// Virtual for checking if notification is expired
NotificationSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

// Virtual for time since creation
NotificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now.getTime() - this.createdAt.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  return 'Just now';
});

// Pre-save middleware to set expiration
NotificationSchema.pre('save', function(next) {
  // Set default expiration if not set (30 days for most notifications)
  if (!this.expiresAt) {
    const expirationDays = this.priority === 'urgent' ? 7 : 
                          this.priority === 'high' ? 14 : 30;
    this.expiresAt = new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000);
  }

  // Set delivered timestamp when status changes to delivered
  if (this.isModified('status') && this.status === 'delivered' && !this.deliveredAt) {
    this.deliveredAt = new Date();
  }

  next();
});

// Static methods
NotificationSchema.statics.findByUserId = async function(
  userId: string, 
  options: any = {}
) {
  const {
    type,
    status,
    limit = 50,
    offset = 0,
    unreadOnly = false,
    includeExpired = false
  } = options;

  const query: any = { userId: new mongoose.Types.ObjectId(userId) };

  if (type) query.type = type;
  if (status) query.status = status;
  if (unreadOnly) query.readAt = { $exists: false };
  if (!includeExpired) {
    query.$or = [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ];
  }

  return this.find(query)
    .populate('groupId', 'name')
    .populate('orderId', 'orderNumber')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(offset);
};

NotificationSchema.statics.findUnreadByUserId = async function(userId: string) {
  return this.find({
    userId: new mongoose.Types.ObjectId(userId),
    readAt: { $exists: false },
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  })
    .populate('groupId', 'name')
    .populate('orderId', 'orderNumber')
    .sort({ createdAt: -1 });
};

NotificationSchema.statics.markAsRead = async function(notificationIds: string[]) {
  const objectIds = notificationIds.map(id => new mongoose.Types.ObjectId(id));
  
  const result = await this.updateMany(
    { 
      _id: { $in: objectIds },
      readAt: { $exists: false }
    },
    { 
      readAt: new Date(),
      status: 'read'
    }
  );

  return result.modifiedCount;
};

NotificationSchema.statics.markAsDelivered = async function(
  notificationId: string, 
  channel: DeliveryChannel
) {
  return this.findByIdAndUpdate(
    notificationId,
    {
      status: 'delivered',
      deliveredAt: new Date(),
      $addToSet: { deliveredChannels: channel }
    },
    { new: true }
  );
};

NotificationSchema.statics.getUnreadCount = async function(userId: string) {
  return this.countDocuments({
    userId: new mongoose.Types.ObjectId(userId),
    readAt: { $exists: false },
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

NotificationSchema.statics.getAnalytics = async function(startDate?: Date, endDate?: Date) {
  const matchStage: any = {};
  if (startDate && endDate) {
    matchStage.createdAt = { $gte: startDate, $lte: endDate };
  }

  const analytics = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalSent: { $sum: 1 },
        totalDelivered: {
          $sum: { $cond: [{ $ne: ['$deliveredAt', null] }, 1, 0] }
        },
        totalRead: {
          $sum: { $cond: [{ $ne: ['$readAt', null] }, 1, 0] }
        },
        totalFailed: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        }
      }
    }
  ]);

  const typeBreakdown = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$type',
        sent: { $sum: 1 },
        delivered: { $sum: { $cond: [{ $ne: ['$deliveredAt', null] }, 1, 0] } },
        read: { $sum: { $cond: [{ $ne: ['$readAt', null] }, 1, 0] } }
      }
    }
  ]);

  const result = analytics[0] || {
    totalSent: 0,
    totalDelivered: 0,
    totalRead: 0,
    totalFailed: 0
  };

  result.deliveryRate = result.totalSent > 0 ? 
    (result.totalDelivered / result.totalSent) * 100 : 0;
  result.readRate = result.totalDelivered > 0 ? 
    (result.totalRead / result.totalDelivered) * 100 : 0;
  result.failureRate = result.totalSent > 0 ? 
    (result.totalFailed / result.totalSent) * 100 : 0;

  result.typeBreakdown = typeBreakdown.reduce((acc, item) => {
    acc[item._id] = {
      sent: item.sent,
      delivered: item.delivered,
      read: item.read,
      readRate: item.delivered > 0 ? (item.read / item.delivered) * 100 : 0
    };
    return acc;
  }, {});

  return result;
};

NotificationSchema.statics.cleanupExpired = async function() {
  const result = await this.deleteMany({
    expiresAt: { $lt: new Date() },
    status: { $in: ['delivered', 'read', 'failed'] }
  });

  return result.deletedCount;
};

// Indexes for performance
NotificationSchema.index({ userId: 1, createdAt: -1 });
NotificationSchema.index({ userId: 1, readAt: 1 });
NotificationSchema.index({ userId: 1, status: 1 });
NotificationSchema.index({ type: 1, createdAt: -1 });
NotificationSchema.index({ priority: 1, createdAt: -1 });
NotificationSchema.index({ expiresAt: 1 });
NotificationSchema.index({ groupId: 1, createdAt: -1 });
NotificationSchema.index({ orderId: 1, createdAt: -1 });

// TTL index for automatic cleanup of expired notifications
NotificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Prevent recompiling the model
let NotificationModel: INotificationModel;

try {
  NotificationModel = mongoose.model<INotification, INotificationModel>('Notification');
} catch {
  NotificationModel = mongoose.model<INotification, INotificationModel>('Notification', NotificationSchema);
}

export const Notification = NotificationModel;
