// models/Product.ts

import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IProduct extends Document {
  name: string;
  description: string;
  price: number;
  category: Types.ObjectId;
  stock: number;
  image: string;
  averageRating?: number;
  totalRatings?: number;
  createdAt: Date;
  updatedAt: Date;
  // Add the virtual field type
  ratings?: Array<{
    rating: number;
    _id: Types.ObjectId;
  }>;
}

const ProductSchema: Schema<IProduct> = new Schema(
  {
    name: { 
      type: String, 
      required: true, 
      trim: true
    },  
    description: { type: String, required: true, trim: true },
    price: { type: Number, required: true, min: 0 },  
    category: { type: Schema.Types.ObjectId, ref: 'ProductCategory', required: true },
    stock: { type: Number, required: true, min: 0 },  
    image: { type: String, required: true, trim: true },  
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual populate for ratings
ProductSchema.virtual('ratings', {
  ref: 'Rating',
  localField: '_id',
  foreignField: 'productId'
});

// Virtual fields for rating statistics
ProductSchema.virtual('averageRating').get(function() {
  return (this.ratings ?? []).length > 0
    ? (this.ratings || []).reduce((acc: number, curr: { rating: number }) => acc + curr.rating, 0) / (this.ratings?.length || 1)
    : 0;
});

ProductSchema.virtual('totalRatings').get(function() {
  return this.ratings?.length || 0;
});

// Indexes
ProductSchema.index({ name: 1 });
ProductSchema.index({ category: 1 });
ProductSchema.index({ price: 1 });

const ProductModel = (mongoose.models?.Product || mongoose.model<IProduct>('Product', ProductSchema)) as mongoose.Model<IProduct>;

export const Product = ProductModel;
export type ProductModel = typeof ProductModel;