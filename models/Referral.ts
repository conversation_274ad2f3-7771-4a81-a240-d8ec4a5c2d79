// models/Referral.ts

import mongoose, { Schema, Document, model } from 'mongoose';
import { IUser } from './User';

export interface IReferral extends Document {
  referrerId: mongoose.Types.ObjectId | IUser;
  referredUserId: mongoose.Types.ObjectId | IUser;
  referralCode: string;
  createdAt: Date;
  earnings: {
    purchaseAmount: number;
    percentage: number;
    earningAmount: number;
  }[];
}

const ReferralSchema = new Schema<IReferral>({
  referrerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  referredUserId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  referralCode: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  earnings: [
    {
      purchaseAmount: { type: Number, required: true },
      percentage: { type: Number, default: 5 },
      earningAmount: { type: Number, required: true },
    },
  ],
});

ReferralSchema.index({ referrerId: 1 });
ReferralSchema.index({ referredUserId: 1 });
ReferralSchema.index({ referralCode: 1 });
ReferralSchema.index({ createdAt: -1 });

export const Referral = mongoose.models.Referral || model<IReferral>('Referral', ReferralSchema);

