import mongoose, { Schema, type Document, model } from "mongoose"

export interface ILoanApplication extends Document {
  userId: mongoose.Types.ObjectId
  name: string
  email: string
  phone: string
  loanAmount: number
  status: "pending" | "approved" | "rejected"
  applicationDate: Date
  approvalDate?: Date
  repaymentTerms?: string
  repaymentStartDate?: Date
  repaymentEndDate?: Date
  createdAt: Date
  updatedAt: Date
}

const LoanApplicationSchema: Schema<ILoanApplication> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    name: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: String, required: true },
    loanAmount: { type: Number, required: true },
    status: { type: String, enum: ["pending", "approved", "rejected"], default: "pending" },
    applicationDate: { type: Date, default: Date.now },
    approvalDate: { type: Date },
    repaymentTerms: { type: String },
    repaymentStartDate: { type: Date },
    repaymentEndDate: { type: Date },
  },
  { timestamps: true },
)

// Indexes for efficient queries
LoanApplicationSchema.index({ userId: 1 })
LoanApplicationSchema.index({ status: 1 })
LoanApplicationSchema.index({ applicationDate: 1 })

export const LoanApplication =
  mongoose.models.LoanApplication || model<ILoanApplication>("LoanApplication", LoanApplicationSchema)

