import mongoose, { Schema, Document, model, Types } from 'mongoose';
import { IProduct } from './Product';

export interface IProductArchive extends Document {
  originalProductId: Types.ObjectId;
  name: string;
  description: string;
  price: number;
  category: Types.ObjectId;
  stock: number;
  image: string;
  originalCreatedAt: Date;
  originalUpdatedAt: Date;
  archivedAt: Date;
  deletedBy?: Types.ObjectId; // Optional: track which user deleted the product
}

const ProductArchiveSchema: Schema<IProductArchive> = new Schema(
  {
    originalProductId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Product', 
      required: true 
    },
    name: { type: String, required: true },
    description: { type: String, required: true },
    price: { type: Number, required: true },
    category: { 
      type: Schema.Types.ObjectId, 
      ref: 'ProductCategory', 
      required: true 
    },
    stock: { type: Number, required: true },
    image: { type: String, required: true },
    originalCreatedAt: { type: Date, required: true },
    originalUpdatedAt: { type: Date, required: true },
    archivedAt: { 
      type: Date, 
      default: Date.now, 
      required: true 
    },
    deletedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    }
  }
);

ProductArchiveSchema.index({ originalProductId: 1 });
ProductArchiveSchema.index({ category: 1 });
ProductArchiveSchema.index({ archivedAt: -1 });

export const ProductArchive = mongoose.models.ProductArchive || model<IProductArchive>('ProductArchive', ProductArchiveSchema);
