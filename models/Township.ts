// models/Township.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface ITownship extends Document {
  name: string;
  cityId: mongoose.Types.ObjectId;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const TownshipSchema: Schema<ITownship> = new Schema(
  {
    name: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    cityId: { 
      type: Schema.Types.ObjectId, 
      ref: 'City', 
      required: true 
    },
    isActive: { 
      type: Boolean, 
      default: true 
    }
  },
  { timestamps: true }
);

// Compound unique index to prevent duplicate township names within the same city
TownshipSchema.index({ name: 1, cityId: 1 }, { unique: true });

// Additional indexes for efficient queries
// Note: name index is covered by compound unique index above
TownshipSchema.index({ cityId: 1 });
TownshipSchema.index({ isActive: 1 });
TownshipSchema.index({ cityId: 1, isActive: 1 });

// Virtual for populated city
TownshipSchema.virtual('city', {
  ref: 'City',
  localField: 'cityId',
  foreignField: '_id',
  justOne: true
});

// Ensure virtual fields are serialized
TownshipSchema.set('toJSON', { virtuals: true });
TownshipSchema.set('toObject', { virtuals: true });

// Export the model
export const Township = mongoose.models.Township || model<ITownship>('Township', TownshipSchema);
