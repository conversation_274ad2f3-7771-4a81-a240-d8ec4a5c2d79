// import mongoose, { Schema, type Document, model } from "mongoose"

// export interface IGrocerySchoolBundle extends Document {
//   userId: mongoose.Types.ObjectId
//   name: string
//   description: string
//   price: number
//   status: "active" | "inactive" | "pending"
//   startDate: Date
//   endDate: Date
//   bundleSize: "basic" | "standard" | "premium"
//   groceryItems: string[]
//   schoolItems: string[]
//   deliveryFrequency: "monthly" | "quarterly" | "annually"
//   createdAt: Date
//   updatedAt: Date
// }

// const GrocerySchoolBundleSchema: Schema<IGrocerySchoolBundle> = new Schema(
//   {
//     userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
//     name: { type: String, required: true },
//     description: { type: String, required: true },
//     price: { type: Number, required: true },
//     status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
//     startDate: { type: Date },
//     endDate: { type: Date },
//     bundleSize: { type: String, enum: ["basic", "standard", "premium"], required: true },
//     groceryItems: [{ type: String }],
//     schoolItems: [{ type: String }],
//     deliveryFrequency: { type: String, enum: ["monthly", "quarterly", "annually"], required: true },
//   },
//   { timestamps: true },
// )

// // Indexes for efficient queries
// GrocerySchoolBundleSchema.index({ userId: 1 })
// GrocerySchoolBundleSchema.index({ status: 1 })

// export const GrocerySchoolBundle =
//   mongoose.models.GrocerySchoolBundle || model<IGrocerySchoolBundle>("GrocerySchoolBundle", GrocerySchoolBundleSchema)


import mongoose, { Schema, type Document, model } from "mongoose"

export interface IGrocerySchoolBundle extends Document {
  userId: mongoose.Types.ObjectId
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: "active" | "inactive" | "pending"
  startDate: Date
  endDate: Date
  bundleSize: "basic" | "standard" | "premium"
  groceryItems: string[]
  schoolItems: string[]
  deliveryFrequency: "monthly" | "quarterly" | "annually"
  groceryAllocation: number
  schoolSuppliesAllocation: number
  createdAt: Date
  updatedAt: Date
}

const GrocerySchoolBundleSchema: Schema<IGrocerySchoolBundle> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    price: { type: Number, required: true },
    monthlyContribution: { type: Number, required: true },
    status: { type: String, enum: ["active", "inactive", "pending"], default: "pending" },
    startDate: { type: Date },
    endDate: { type: Date },
    bundleSize: { type: String, enum: ["basic", "standard", "premium"], required: true },
    groceryItems: [{ type: String }],
    schoolItems: [{ type: String }],
    deliveryFrequency: { type: String, enum: ["monthly", "quarterly", "annually"], required: true },
    groceryAllocation: { type: Number, required: true },
    schoolSuppliesAllocation: { type: Number, required: true },
  },
  { timestamps: true },
)

GrocerySchoolBundleSchema.index({ userId: 1 })
GrocerySchoolBundleSchema.index({ status: 1 })

export const GrocerySchoolBundle =
  mongoose.models.GrocerySchoolBundle || model<IGrocerySchoolBundle>("GrocerySchoolBundle", GrocerySchoolBundleSchema)
