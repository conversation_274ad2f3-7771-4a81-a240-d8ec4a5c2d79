import mongoose, { Schema, Document, Types, model } from 'mongoose';

export interface IProductCategoryArchive extends Document {
  originalCategoryId: Types.ObjectId;
  name: string;
  description: string;
  product_count: number;
  parent_category?: Types.ObjectId;
  originalCreatedAt: Date;
  originalUpdatedAt: Date;
  archivedAt: Date;
  deletedBy?: Types.ObjectId;
}

const ProductCategoryArchiveSchema: Schema<IProductCategoryArchive> = new Schema(
  {
    originalCategoryId: { 
      type: mongoose.Schema.Types.ObjectId, 
      required: true, 
      ref: 'ProductCategory' 
    },
    name: { type: String, required: true },
    description: { type: String, required: true },
    product_count: { type: Number, default: 0 },
    parent_category: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'ProductCategory' 
    },
    originalCreatedAt: { type: Date, required: true },
    originalUpdatedAt: { type: Date, required: true },
    archivedAt: { type: Date, default: Date.now },
    deletedBy: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'User' 
    }
  },
  { timestamps: true }
);

// Indexes for efficient queries
ProductCategoryArchiveSchema.index({ originalCategoryId: 1 });
ProductCategoryArchiveSchema.index({ name: 1 });
ProductCategoryArchiveSchema.index({ archivedAt: 1 });
ProductCategoryArchiveSchema.index({ deletedBy: 1 });

export const ProductCategoryArchive = mongoose.models.ProductCategoryArchive || 
  model<IProductCategoryArchive>('ProductCategoryArchive', ProductCategoryArchiveSchema);
