// import mongoose, { Schema, Document, Types } from 'mongoose';

// export interface IRating extends Document {
//   productId: Types.ObjectId;
//   userId: Types.ObjectId;
//   rating: number;
//   review?: string;
//   createdAt: Date;
//   updatedAt: Date;
// }

// const RatingSchema: Schema<IRating> = new Schema(
//   {
//     productId: {
//       type: Schema.Types.ObjectId,
//       ref: 'Product',
//       required: true
//     },
//     userId: {
//       type: Schema.Types.ObjectId,
//       ref: 'User',
//       required: true
//     },
//     rating: {
//       type: Number,
//       required: true,
//       min: 1,
//       max: 5
//     },
//     review: {
//       type: String,
//       trim: true
//     }
//   },
//   { 
//     timestamps: true,
//     toJSON: { virtuals: true },
//     toObject: { virtuals: true }
//   }
// );

// // Ensure a user can only rate a product once
// RatingSchema.index({ productId: 1, userId: 1 }, { unique: true });
// // Index for querying ratings by product
// RatingSchema.index({ productId: 1 });

// const RatingModel = (mongoose.models?.Rating || mongoose.model<IRating>('Rating', RatingSchema)) as mongoose.Model<IRating>;

// export const Rating = RatingModel;
// export type RatingModel = typeof RatingModel;


import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IRating extends Document {
  productId: Types.ObjectId;
  userId: Types.ObjectId;
  rating: number;
  createdAt: Date;
  updatedAt: Date;
}

const RatingSchema: Schema<IRating> = new Schema(
  {
    productId: {
      type: Schema.Types.ObjectId,
      ref: 'Product',
      required: true,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
      validate: {
        validator: Number.isInteger,
        message: 'Rating must be a whole number'
      }
    }
  },
  { 
    timestamps: true 
  }
);

// Compound index to ensure one rating per user per product
RatingSchema.index({ productId: 1, userId: 1 }, { unique: true });

// Add a pre-save middleware to validate rating value
RatingSchema.pre('save', function(next) {
  if (this.rating < 1 || this.rating > 5) {
    next(new Error('Rating must be between 1 and 5'));
  }
  next();
});

// Static method to calculate average rating for a product
RatingSchema.statics.getProductStats = async function(productId: Types.ObjectId) {
  const stats = await this.aggregate([
    { $match: { productId: new Types.ObjectId(productId) } },
    {
      $group: {
        _id: '$productId',
        averageRating: { $avg: '$rating' },
        totalRatings: { $sum: 1 }
      }
    }
  ]);
  
  return stats.length > 0 
    ? { averageRating: stats[0].averageRating, totalRatings: stats[0].totalRatings }
    : { averageRating: 0, totalRatings: 0 };
};

// Instance method to update product's rating stats after save/update
RatingSchema.methods.updateProductStats = async function() {
  const stats = await (this.constructor as any).getProductStats(this.productId);
  await mongoose.model('Product').findByIdAndUpdate(this.productId, {
    $set: {
      averageRating: stats.averageRating,
      totalRatings: stats.totalRatings
    }
  });
};

// Post-save middleware to update product stats
RatingSchema.post('save', async function(this: IRating & { updateProductStats: () => Promise<void> }) {
  await this.updateProductStats();
});

// Post-remove middleware to update product stats
RatingSchema.post(['deleteOne', 'deleteMany'], async function(this: IRating & { updateProductStats: () => Promise<void> }) {
  await this.updateProductStats();
});

const RatingModel = (mongoose.models?.Rating || mongoose.model<IRating>('Rating', RatingSchema)) as mongoose.Model<IRating> & {
  getProductStats(productId: Types.ObjectId): Promise<{ averageRating: number; totalRatings: number }>;
};

export const Rating = RatingModel;
export type RatingModel = typeof RatingModel;