// models/Province.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface IProvince extends Document {
  name: string;
  code: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ProvinceSchema: Schema<IProvince> = new Schema(
  {
    name: { 
      type: String, 
      required: true, 
      unique: true,
      trim: true,
      maxlength: 100
    },
    code: { 
      type: String, 
      required: true, 
      unique: true,
      trim: true,
      uppercase: true,
      maxlength: 10
    },
    isActive: { 
      type: Boolean, 
      default: true 
    }
  },
  { timestamps: true }
);

// Indexes for efficient queries
// Note: name and code indexes are automatically created by unique: true
ProvinceSchema.index({ isActive: 1 });
ProvinceSchema.index({ name: 1, isActive: 1 });

// Validation
ProvinceSchema.pre('save', function(next) {
  // Ensure code is uppercase
  if (this.code) {
    this.code = this.code.toUpperCase();
  }
  next();
});

// Export the model
export const Province = mongoose.models.Province || model<IProvince>('Province', ProvinceSchema);
