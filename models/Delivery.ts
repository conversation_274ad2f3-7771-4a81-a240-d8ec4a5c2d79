
// // models/Delivery.ts
// import mongoose, { Schema, Document, model } from 'mongoose';

// export interface IDelivery extends Document {
//   groupOrderId: mongoose.Types.ObjectId;
//   driverName: string;
//   deliveryVehicle: string;
//   estimatedDeliveryDate: Date;
//   deliveryStatus: 'pending' | 'in-transit' | 'delivered';
//   trackingDetails: string;
//   createdAt: Date;
//   updatedAt: Date;
// }

// const DeliverySchema: Schema<IDelivery> = new Schema(
//   {
//     groupOrderId: { type: mongoose.Types.ObjectId, ref: 'GroupOrder', required: true },
//     driverName: { type: String, required: true },
//     deliveryVehicle: { type: String, required: true },
//     estimatedDeliveryDate: { type: Date, required: true },
//     deliveryStatus: { type: String, default: 'pending' },
//     trackingDetails: { type: String, default: '' },
//   },
//   { timestamps: true }
// );

// // Export the model
// export const Delivery = mongoose.models.Delivery || model<IDelivery>('Delivery', DeliverySchema);


import mongoose, { Schema, Document, model, Types } from 'mongoose';

export interface IDelivery extends Document {
  groupOrderId: Types.ObjectId;  // Corrected type here
  driverName: string;
  deliveryVehicle: string;
  estimatedDeliveryDate: Date;
  deliveryStatus: 'pending' | 'in-transit' | 'delivered';
  trackingDetails: string;
  createdAt: Date;
  updatedAt: Date;
}

const DeliverySchema: Schema<IDelivery> = new Schema(
  {
    groupOrderId: { type: Schema.Types.ObjectId, ref: 'GroupOrder', required: true },  // Fixed here
    driverName: { type: String, required: true },
    deliveryVehicle: { type: String, required: true },
    estimatedDeliveryDate: { type: Date, required: true },
    deliveryStatus: { type: String, default: 'pending' },
    trackingDetails: { type: String, default: '' },
  },
  { timestamps: true }
);

// Export the model
export const Delivery = mongoose.models.Delivery || model<IDelivery>('Delivery', DeliverySchema);
