// models/City.ts

import mongoose, { Schema, Document, model } from 'mongoose';

export interface ICity extends Document {
  name: string;
  provinceId: mongoose.Types.ObjectId;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const CitySchema: Schema<ICity> = new Schema(
  {
    name: { 
      type: String, 
      required: true,
      trim: true,
      maxlength: 100
    },
    provinceId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Province', 
      required: true 
    },
    isActive: { 
      type: Boolean, 
      default: true 
    }
  },
  { timestamps: true }
);

// Compound unique index to prevent duplicate city names within the same province
CitySchema.index({ name: 1, provinceId: 1 }, { unique: true });

// Additional indexes for efficient queries
// Note: name index is covered by compound unique index above
CitySchema.index({ provinceId: 1 });
CitySchema.index({ isActive: 1 });
CitySchema.index({ provinceId: 1, isActive: 1 });

// Virtual for populated province
CitySchema.virtual('province', {
  ref: 'Province',
  localField: 'provinceId',
  foreignField: '_id',
  justOne: true
});

// Ensure virtual fields are serialized
CitySchema.set('toJSON', { virtuals: true });
CitySchema.set('toObject', { virtuals: true });

// Export the model
export const City = mongoose.models.City || model<ICity>('City', CitySchema);
