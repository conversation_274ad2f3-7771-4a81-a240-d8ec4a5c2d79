// models/ProductRating.ts

import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IProductRating extends Document {
  userId: mongoose.Types.ObjectId;
  productId: mongoose.Types.ObjectId;
  rating: number; // 1-5 stars
  review?: string;
  title?: string;
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  reportedCount: number;
  status: 'active' | 'hidden' | 'pending';
  createdAt: Date;
  updatedAt: Date;
}

export interface IProductRatingStats {
  productId: mongoose.Types.ObjectId;
  averageRating: number;
  totalRatings: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  lastUpdated: Date;
}

// Product Rating Schema
const ProductRatingSchema: Schema<IProductRating> = new Schema(
  {
    userId: { 
      type: Schema.Types.ObjectId, 
      ref: 'User', 
      required: true,
      index: true
    },
    productId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Product', 
      required: true,
      index: true
    },
    rating: { 
      type: Number, 
      required: true, 
      min: 1, 
      max: 5 
    },
    review: { 
      type: String, 
      maxlength: 1000 
    },
    title: { 
      type: String, 
      maxlength: 100 
    },
    isVerifiedPurchase: { 
      type: Boolean, 
      default: false 
    },
    helpfulVotes: { 
      type: Number, 
      default: 0 
    },
    reportedCount: { 
      type: Number, 
      default: 0 
    },
    status: {
      type: String,
      enum: ['active', 'hidden', 'pending'],
      default: 'active'
    }
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Product Rating Stats Schema
const ProductRatingStatsSchema: Schema<IProductRatingStats> = new Schema(
  {
    productId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Product', 
      required: true,
      unique: true,
      index: true
    },
    averageRating: { 
      type: Number, 
      default: 0,
      min: 0,
      max: 5
    },
    totalRatings: { 
      type: Number, 
      default: 0,
      min: 0
    },
    ratingDistribution: {
      1: { type: Number, default: 0 },
      2: { type: Number, default: 0 },
      3: { type: Number, default: 0 },
      4: { type: Number, default: 0 },
      5: { type: Number, default: 0 }
    },
    lastUpdated: { 
      type: Date, 
      default: Date.now 
    }
  },
  { 
    timestamps: true 
  }
);

// Indexes for efficient queries
ProductRatingSchema.index({ productId: 1, userId: 1 }, { unique: true }); // One rating per user per product
ProductRatingSchema.index({ productId: 1, status: 1, createdAt: -1 });
ProductRatingSchema.index({ userId: 1, createdAt: -1 });
ProductRatingSchema.index({ rating: 1 });
ProductRatingSchema.index({ isVerifiedPurchase: 1 });

// Virtual for rating age
ProductRatingSchema.virtual('ratingAge').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
});

// Static methods for ProductRating
ProductRatingSchema.statics.findByProduct = function(productId: string, status: string = 'active') {
  return this.find({ productId, status })
    .populate('userId', 'name')
    .sort({ createdAt: -1 });
};

ProductRatingSchema.statics.findByUser = function(userId: string) {
  return this.find({ userId })
    .populate('productId', 'name image')
    .sort({ createdAt: -1 });
};

ProductRatingSchema.statics.getAverageRating = async function(productId: string) {
  const result = await this.aggregate([
    { $match: { productId: new mongoose.Types.ObjectId(productId), status: 'active' } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalRatings: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        }
      }
    }
  ]);

  if (result.length === 0) {
    return {
      averageRating: 0,
      totalRatings: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    };
  }

  const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  result[0].ratingDistribution.forEach((rating: number) => {
    distribution[rating as keyof typeof distribution]++;
  });

  return {
    averageRating: Math.round(result[0].averageRating * 10) / 10,
    totalRatings: result[0].totalRatings,
    ratingDistribution: distribution
  };
};

// Instance methods
ProductRatingSchema.methods.markHelpful = function() {
  this.helpfulVotes += 1;
  return this.save();
};

ProductRatingSchema.methods.report = function() {
  this.reportedCount += 1;
  if (this.reportedCount >= 5) {
    this.status = 'hidden';
  }
  return this.save();
};

// Post-save middleware to update product rating stats
ProductRatingSchema.post('save', async function() {
  await updateProductRatingStats(this.productId);
});

ProductRatingSchema.post('deleteOne', { document: true }, async function() {
  await updateProductRatingStats(this.productId);
});

// Function to update product rating stats
async function updateProductRatingStats(productId: mongoose.Types.ObjectId) {
  try {
    const ProductRating = mongoose.model('ProductRating');
    const ProductRatingStats = mongoose.model('ProductRatingStats');
    
    const stats = await ProductRating.getAverageRating(productId.toString());
    
    await ProductRatingStats.findOneAndUpdate(
      { productId },
      {
        averageRating: stats.averageRating,
        totalRatings: stats.totalRatings,
        ratingDistribution: stats.ratingDistribution,
        lastUpdated: new Date()
      },
      { upsert: true, new: true }
    );

    // Also update the product document
    const Product = mongoose.model('Product');
    await Product.findByIdAndUpdate(productId, {
      averageRating: stats.averageRating,
      reviewCount: stats.totalRatings
    });
  } catch (error) {
    console.error('Error updating product rating stats:', error);
  }
}

// Export the models
export const ProductRating = mongoose.models.ProductRating || mongoose.model<IProductRating>('ProductRating', ProductRatingSchema);
export const ProductRatingStats = mongoose.models.ProductRatingStats || mongoose.model<IProductRatingStats>('ProductRatingStats', ProductRatingStatsSchema);

export type ProductRatingModel = typeof ProductRating;
export type ProductRatingStatsModel = typeof ProductRatingStats;
