// login-test.js
const fetch = require('node-fetch');

async function loginUser() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-client-type': 'web',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '@Admin2020',
        rememberMe: true
      }),
    });

    const data = await response.json();
    console.log('Login response:', data);
    return data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
}

loginUser();
