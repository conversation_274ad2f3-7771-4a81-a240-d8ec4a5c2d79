// get-groups.js
const fetch = require('node-fetch');

async function getUserGroups() {
  try {
    // First, get the user's ID from the /api/auth/me endpoint
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-client-type': 'web',
      },
      credentials: 'include',
    });

    const meData = await meResponse.json();
    console.log('User data:', meData);

    if (!meData.user || !meData.user._id) {
      console.error('Failed to get user ID');
      return;
    }

    const userId = meData.user._id;

    // Now get the user's groups
    const groupsResponse = await fetch(`http://localhost:3000/api/users/groups?userId=${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const groupsData = await groupsResponse.json();
    console.log('User groups:', groupsData);
    return groupsData;
  } catch (error) {
    console.error('Error getting user groups:', error);
    throw error;
  }
}

getUserGroups();
