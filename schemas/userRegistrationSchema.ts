import { z } from "zod"

export const userRegistrationSchema = z
  .object({
    email: z.string().email("A valid email is required"),
    existingUserId: z.string().default(""),
    name: z.string().min(2, "Name must be at least 2 characters").default(""),
    phone: z.string().min(10, "Phone number must be at least 10 digits").default(""),
    password: z.string().min(8, "Password must be at least 8 characters").optional(),
    confirmPassword: z.string().optional(),
  })
  .refine((data) => !data.password || data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  })

export type UserRegistrationFormType = z.infer<typeof userRegistrationSchema>
