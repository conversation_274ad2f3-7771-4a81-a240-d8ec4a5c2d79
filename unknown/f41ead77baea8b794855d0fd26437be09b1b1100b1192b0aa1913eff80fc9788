// app/api/analytics/overview/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { analyticsService } from '@/lib/services/analyticsService';
import { 
  GetAnalyticsRequest, 
  GetAnalyticsResponse,
  TimePeriod,
  DateRange 
} from '@/types/analytics';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  
  const _period = (searchParams.get('period') as TimePeriod) || 'month';
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  const type = searchParams.get('type') || 'overview';

  try {
    // Parse date range
    let dateRange: DateRange;
    
    if (startDate && endDate) {
      dateRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      };
      
      // Validate dates
      if (isNaN(dateRange.start.getTime()) || isNaN(dateRange.end.getTime())) {
        return NextResponse.json(
          { error: 'Invalid date format' },
          { headers: corsHeaders, status: 400 }
        );
      }
      
      if (dateRange.start >= dateRange.end) {
        return NextResponse.json(
          { error: 'Start date must be before end date' },
          { headers: corsHeaders, status: 400 }
        );
      }
    } else {
      // Default to last 30 days
      const now = new Date();
      dateRange = {
        start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        end: now
      };
    }

    // Get analytics data based on type
    let analyticsData: Record<string, unknown> = {};

    switch (type) {
      case 'overview':
        // Get all key metrics for overview
        const [revenueAnalytics, userAnalytics, orderAnalytics] = await Promise.all([
          analyticsService.getRevenueAnalytics(dateRange),
          analyticsService.getUserAnalytics(dateRange),
          analyticsService.getOrderAnalytics(dateRange)
        ]);

        analyticsData = {
          revenue: revenueAnalytics,
          users: userAnalytics,
          orders: orderAnalytics,
          summary: {
            totalRevenue: revenueAnalytics.totalRevenue,
            totalOrders: orderAnalytics.totalOrders,
            totalUsers: userAnalytics.totalUsers,
            averageOrderValue: orderAnalytics.averageOrderValue,
            revenueGrowth: revenueAnalytics.revenueGrowth,
            userGrowth: userAnalytics.userGrowthRate,
            orderCompletionRate: orderAnalytics.orderCompletionRate
          }
        };
        break;

      case 'revenue':
        analyticsData = await analyticsService.getRevenueAnalytics(dateRange);
        break;

      case 'users':
        analyticsData = await analyticsService.getUserAnalytics(dateRange);
        break;

      case 'orders':
        analyticsData = await analyticsService.getOrderAnalytics(dateRange);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid analytics type' },
          { headers: corsHeaders, status: 400 }
        );
    }

    // Format response
    const response: GetAnalyticsResponse = {
      metrics: [], // Would be populated with formatted metrics
      charts: [], // Would be populated with chart data
      summary: analyticsData,
      lastUpdated: new Date()
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body: GetAnalyticsRequest = await req.json();

    // Validate request
    if (!body.type) {
      return NextResponse.json(
        { error: 'Analytics type is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Set default date range if not provided
    let dateRange: DateRange;
    if (body.dateRange) {
      dateRange = {
        start: new Date(body.dateRange.start),
        end: new Date(body.dateRange.end)
      };
    } else {
      // Default based on period
      const now = new Date();
      const daysBack = {
        hour: 1,
        day: 7,
        week: 4 * 7,
        month: 30,
        quarter: 90,
        year: 365
      }[body.period] || 30;

      dateRange = {
        start: new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000),
        end: now
      };
    }

    // Get analytics data
    let analyticsData: Record<string, unknown> = {};

    switch (body.type) {
      case 'revenue':
        analyticsData = await analyticsService.getRevenueAnalytics(dateRange);
        break;

      case 'users':
        analyticsData = await analyticsService.getUserAnalytics(dateRange);
        break;

      case 'orders':
        analyticsData = await analyticsService.getOrderAnalytics(dateRange);
        break;

      default:
        return NextResponse.json(
          { error: 'Unsupported analytics type' },
          { headers: corsHeaders, status: 400 }
        );
    }

    // Convert to metrics format
    const metrics = convertToMetrics(analyticsData, body.type);
    const charts = convertToCharts(analyticsData, body.type);

    const response: GetAnalyticsResponse = {
      metrics,
      charts,
      summary: analyticsData,
      lastUpdated: new Date()
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error processing analytics request:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Helper functions
function convertToMetrics(data: Record<string, unknown>, type: string): unknown[] {
  const metrics = [];

  switch (type) {
    case 'revenue':
      metrics.push(
        {
          id: 'total_revenue',
          name: 'Total Revenue',
          type: 'revenue',
          value: data.totalRevenue || 0,
          change: data.revenueGrowth || 0,
          changePercentage: data.revenueGrowth || 0,
          trend: (data.revenueGrowth || 0) >= 0 ? 'up' : 'down',
          unit: 'ZAR',
          format: 'currency',
          description: 'Total revenue for the selected period',
          lastUpdated: new Date()
        },
        {
          id: 'average_order_value',
          name: 'Average Order Value',
          type: 'revenue',
          value: data.averageOrderValue || 0,
          unit: 'ZAR',
          format: 'currency',
          description: 'Average value per order',
          lastUpdated: new Date()
        }
      );
      break;

    case 'users':
      metrics.push(
        {
          id: 'total_users',
          name: 'Total Users',
          type: 'users',
          value: data.totalUsers || 0,
          unit: 'users',
          format: 'number',
          description: 'Total registered users',
          lastUpdated: new Date()
        },
        {
          id: 'active_users',
          name: 'Active Users',
          type: 'users',
          value: data.activeUsers || 0,
          change: data.userGrowthRate || 0,
          changePercentage: data.userGrowthRate || 0,
          trend: (data.userGrowthRate || 0) >= 0 ? 'up' : 'down',
          unit: 'users',
          format: 'number',
          description: 'Users active in the selected period',
          lastUpdated: new Date()
        }
      );
      break;

    case 'orders':
      metrics.push(
        {
          id: 'total_orders',
          name: 'Total Orders',
          type: 'orders',
          value: data.totalOrders || 0,
          unit: 'orders',
          format: 'number',
          description: 'Total orders in the selected period',
          lastUpdated: new Date()
        },
        {
          id: 'completion_rate',
          name: 'Order Completion Rate',
          type: 'conversion',
          value: data.orderCompletionRate || 0,
          unit: '%',
          format: 'percentage',
          description: 'Percentage of orders completed successfully',
          lastUpdated: new Date()
        }
      );
      break;
  }

  return metrics;
}

function convertToCharts(data: Record<string, unknown>, type: string): unknown[] {
  const charts = [];

  switch (type) {
    case 'revenue':
      if (data.revenueByPeriod) {
        charts.push({
          id: 'revenue_trend',
          title: 'Revenue Trend',
          type: 'line',
          data: data.revenueByPeriod,
          lastUpdated: new Date()
        });
      }
      
      if (data.revenueByCategory) {
        charts.push({
          id: 'revenue_by_category',
          title: 'Revenue by Category',
          type: 'pie',
          data: data.revenueByCategory,
          lastUpdated: new Date()
        });
      }
      break;

    case 'users':
      if (data.usersByPeriod) {
        charts.push({
          id: 'user_growth',
          title: 'User Growth',
          type: 'line',
          data: data.usersByPeriod,
          lastUpdated: new Date()
        });
      }
      break;

    case 'orders':
      if (data.ordersByPeriod) {
        charts.push({
          id: 'orders_trend',
          title: 'Orders Trend',
          type: 'line',
          data: data.ordersByPeriod,
          lastUpdated: new Date()
        });
      }
      
      if (data.ordersByStatus) {
        charts.push({
          id: 'orders_by_status',
          title: 'Orders by Status',
          type: 'doughnut',
          data: data.ordersByStatus,
          lastUpdated: new Date()
        });
      }
      break;
  }

  return charts;
}
