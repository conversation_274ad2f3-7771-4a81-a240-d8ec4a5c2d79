'use client'

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Clock } from 'lucide-react'

const flashSales = [
  {
    id: 1,
    title: "Wireless Earbuds",
    imageUrl: "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150&q=80",
    originalPrice: 1499,
    discountedPrice: 999,
    endTime: new Date(new Date().getTime() + 3 * 60 * 60 * 1000), // 3 hours from now
  },
  {
    id: 2,
    title: "Fitness Tracker",
    imageUrl: "https://images.unsplash.com/photo-1575311373937-040b8e1fd5b6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150&q=80",
    originalPrice: 999,
    discountedPrice: 599,
    endTime: new Date(new Date().getTime() + 5 * 60 * 60 * 1000), // 5 hours from now
  },
  {
    id: 3,
    title: "Portable Charger",
    imageUrl: "https://images.unsplash.com/photo-1609592431950-5c0658b242d4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150&q=80",
    originalPrice: 599,
    discountedPrice: 349,
    endTime: new Date(new Date().getTime() + 4 * 60 * 60 * 1000), // 4 hours from now
  },
]

function FlashSaleItem({ item }: { item: typeof flashSales[0] }) {
  const [timeLeft, setTimeLeft] = useState(getTimeLeft(item.endTime))

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(getTimeLeft(item.endTime))
    }, 1000)

    return () => clearInterval(timer)
  }, [item.endTime])

  function getTimeLeft(endTime: Date) {
    const difference = endTime.getTime() - new Date().getTime()
    if (difference <= 0) return { hours: 0, minutes: 0, seconds: 0 }

    const hours = Math.floor(difference / (1000 * 60 * 60))
    const minutes = Math.floor((difference / (1000 * 60)) % 60)
    const seconds = Math.floor((difference / 1000) % 60)

    return { hours, minutes, seconds }
  }

  return (
    <Card>
      <CardContent className="p-4">
        <Image
          src={item.imageUrl || "/placeholder.svg"}
          alt={item.title}
          width={150}
          height={150}
          className="mx-auto mb-4"
        />
        <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
        <div className="flex justify-between items-center mb-2">
          <span className="text-xl font-bold text-[#2A7C6C]">
            R{item.discountedPrice}
          </span>
          <span className="text-sm text-gray-500 line-through">
            R{item.originalPrice}
          </span>
        </div>
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <Clock className="w-4 h-4 mr-1" />
          <span>
            {timeLeft.hours.toString().padStart(2, '0')}:
            {timeLeft.minutes.toString().padStart(2, '0')}:
            {timeLeft.seconds.toString().padStart(2, '0')}
          </span>
        </div>
        <Button className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white">
          Add to Cart
        </Button>
      </CardContent>
    </Card>
  )
}

export function FlashSales() {
  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Flash Sales</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {flashSales.map((item) => (
          <FlashSaleItem key={item.id} item={item} />
        ))}
      </div>
    </section>
  )
}

