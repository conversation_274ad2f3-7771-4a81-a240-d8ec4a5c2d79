"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Clock } from "lucide-react";
import { motion } from "framer-motion";
import Image from "next/image";
import { useRouter } from "next/navigation";

// Define the offer type
interface LimitedOffer {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
  expiryDate: string;
  discount: string;
  originalPrice: number;
  discountedPrice: number;
}

// Sample data
const limitedTimeOffers: LimitedOffer[] = [
  {
    id: 1,
    title: "Premium Bluetooth Speaker",
    description: "Waterproof, 24hr battery life",
    imageUrl: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?q=80&w=2069&auto=format&fit=crop",
    expiryDate: "2023-08-10",
    discount: "30% OFF",
    originalPrice: 1999,
    discountedPrice: 1399,
  },
  {
    id: 2,
    title: "Smart Home Security Camera",
    description: "HD quality, motion detection",
    imageUrl: "https://images.unsplash.com/photo-1558002038-1055907df827?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-08-15",
    discount: "25% OFF",
    originalPrice: 2499,
    discountedPrice: 1874,
  },
  {
    id: 3,
    title: "Fitness Smartwatch",
    description: "Heart rate, sleep tracking",
    imageUrl: "https://images.unsplash.com/photo-1579586337278-3befd40fd17a?q=80&w=2072&auto=format&fit=crop",
    expiryDate: "2023-08-20",
    discount: "20% OFF",
    originalPrice: 3499,
    discountedPrice: 2799,
  },
  {
    id: 4,
    title: "Wireless Charging Pad",
    description: "Fast charging for all devices",
    imageUrl: "https://images.unsplash.com/photo-1618577608401-46f4f53ed3a4?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-08-25",
    discount: "35% OFF",
    originalPrice: 999,
    discountedPrice: 649,
  },
  {
    id: 5,
    title: "Noise Cancelling Headphones",
    description: "Premium sound quality",
    imageUrl: "https://images.unsplash.com/photo-1546435770-a3e426bf472b?q=80&w=2065&auto=format&fit=crop",
    expiryDate: "2023-08-30",
    discount: "40% OFF",
    originalPrice: 4999,
    discountedPrice: 2999,
  },
];

export function LimitedTimeOffers() {
  const router = useRouter();
  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? limitedTimeOffers.length - 1 : prevIndex - 1
    );
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === limitedTimeOffers.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handleViewOffer = (id: number) => {
    router.push(`/offers/${id}`);
  };

  // Calculate days remaining
  const daysRemaining = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Auto-scroll carousel
  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 5000);
    return () => clearInterval(interval);
  }, [currentIndex]);

  // Visible items calculation
  const getVisibleItems = () => {
    const items = [];
    const totalItems = limitedTimeOffers.length;
    
    for (let i = 0; i < 3; i++) {
      const index = (currentIndex + i) % totalItems;
      items.push(limitedTimeOffers[index]);
    }
    
    return items;
  };

  const visibleItems = getVisibleItems();

  return (
    <section className="mb-16">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900">Limited Time Offers</h2>
        <p className="text-gray-500 mt-2 md:mt-0">
          Exclusive deals available for a short time only
        </p>
      </div>

      <div className="relative">
        {/* Carousel navigation */}
        <div className="absolute -left-4 top-1/2 -translate-y-1/2 z-10">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handlePrev}
            className="rounded-full bg-white/80 backdrop-blur-sm border-gray-200 shadow-md hover:bg-white"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
        </div>

        <div className="absolute -right-4 top-1/2 -translate-y-1/2 z-10">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleNext}
            className="rounded-full bg-white/80 backdrop-blur-sm border-gray-200 shadow-md hover:bg-white"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>

        {/* Carousel */}
        <div 
          ref={carouselRef}
          className="overflow-hidden px-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {visibleItems.map((offer, index) => {
              const days = daysRemaining(offer.expiryDate);
              const isUrgent = days <= 3 && days > 0;
              
              return (
                <motion.div
                  key={`${offer.id}-${index}`}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="rounded-xl overflow-hidden shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-300"
                >
                  <div className="relative h-48">
                    <Image
                      src={offer.imageUrl}
                      alt={offer.title}
                      layout="fill"
                      objectFit="cover"
                    />
                    <Badge className="absolute top-3 left-3 z-10 bg-green-600 text-white">
                      {offer.discount}
                    </Badge>
                    {isUrgent && (
                      <Badge className="absolute top-3 right-3 z-10 bg-red-500 text-white">
                        Ends Soon!
                      </Badge>
                    )}
                  </div>
                  
                  <div className="p-4">
                    <h3 className="text-lg font-bold text-gray-900 mb-1">{offer.title}</h3>
                    <p className="text-sm text-gray-600 mb-3">{offer.description}</p>
                    
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-lg font-bold text-green-700">R{offer.discountedPrice.toLocaleString()}</span>
                      <span className="text-sm text-gray-500 line-through">R{offer.originalPrice.toLocaleString()}</span>
                    </div>
                    
                    <div className="flex items-center text-xs text-gray-500 mb-4">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>
                        {days > 0
                          ? `${days} day${days !== 1 ? 's' : ''} left`
                          : "Offer expired"}
                      </span>
                    </div>
                    
                    <Button 
                      className="w-full bg-green-600 hover:bg-green-700 text-white"
                      onClick={() => handleViewOffer(offer.id)}
                    >
                      View Offer
                    </Button>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
        
        {/* Carousel indicators */}
        <div className="flex justify-center mt-6 gap-2">
          {limitedTimeOffers.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex ? "bg-green-600 w-6" : "bg-gray-300"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
