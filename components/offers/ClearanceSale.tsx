import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Image from "next/image"

const clearanceItems = [
  {
    id: 1,
    title: "Bluetooth Speaker",
    imageUrl: "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150&q=80",
    originalPrice: 999,
    discountedPrice: 499,
    discount: 50,
  },
  {
    id: 2,
    title: "Leather Wallet",
    imageUrl: "https://images.unsplash.com/photo-1627123424574-724758594e93?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150&q=80",
    originalPrice: 399,
    discountedPrice: 199,
    discount: 50,
  },
  {
    id: 3,
    title: "Sunglasses",
    imageUrl: "https://images.unsplash.com/photo-1572635196237-14b3f281503f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150&q=80",
    originalPrice: 599,
    discountedPrice: 299,
    discount: 50,
  },
  {
    id: 4,
    title: "Travel Backpack",
    imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150&q=80",
    originalPrice: 1299,
    discountedPrice: 649,
    discount: 50,
  },
]

export function ClearanceSale() {
  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Clearance Sale</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {clearanceItems.map((item) => (
          <Card key={item.id}>
            <CardContent className="p-4">
              <Image
                src={item.imageUrl || "/placeholder.svg"}
                alt={item.title}
                width={150}
                height={150}
                className="mx-auto mb-2"
              />
              <h3 className="text-sm font-semibold mb-1">{item.title}</h3>
              <div className="flex justify-between items-center mb-2">
                <span className="text-lg font-bold text-[#2A7C6C]">
                  R{item.discountedPrice}
                </span>
                <span className="text-xs text-gray-500 line-through">
                  R{item.originalPrice}
                </span>
              </div>
              <div className="bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded mb-2">
                {item.discount}% OFF
              </div>
              <Button className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white text-sm">
                Add to Cart
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
}

