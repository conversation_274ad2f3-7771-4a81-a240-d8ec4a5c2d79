"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Copy, Check } from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/AuthContext";

interface FeaturedDealProps {
  title: string;
  subtitle: string;
  description: string;
  imageUrl: string;
  expiryDate: string;
  discount: string;
  code: string;
  originalPrice: number;
  discountedPrice: number;
}

export function FeaturedDeal({
  title,
  subtitle,
  description,
  imageUrl,
  expiryDate,
  discount,
  code,
  originalPrice,
  discountedPrice,
}: FeaturedDealProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [copied, setCopied] = useState(false);

  const handleCopyCode = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleClaimDeal = () => {
    if (!user) {
      router.push('/login');
    } else {
      // Navigate to product page or special offer page
      router.push('/offers/featured');
    }
  };

  // Calculate days remaining
  const daysRemaining = () => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const days = daysRemaining();
  const isUrgent = days <= 3 && days > 0;

  return (
    <section className="mb-16">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900">Featured Deal of the Day</h2>
        <p className="text-gray-500 mt-2 md:mt-0">
          Special offer with the biggest savings
        </p>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="rounded-xl overflow-hidden shadow-lg border border-gray-100"
      >
        <div className="grid grid-cols-1 lg:grid-cols-2">
          {/* Image Section */}
          <div className="relative h-64 lg:h-full">
            <Image
              src={imageUrl}
              alt={title}
              layout="fill"
              objectFit="cover"
              className="transition-transform duration-500 hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
            <Badge className="absolute top-4 left-4 z-10 bg-green-600 text-white px-3 py-1.5 text-sm font-medium">
              {discount}
            </Badge>
            {isUrgent && (
              <Badge className="absolute top-4 right-4 z-10 bg-red-500 text-white px-3 py-1.5 text-sm font-medium">
                Ends Soon!
              </Badge>
            )}
          </div>

          {/* Content Section */}
          <div className="p-6 lg:p-8 flex flex-col">
            <div className="flex-grow">
              <h3 className="text-2xl font-bold text-gray-900 mb-1">{title}</h3>
              <p className="text-lg text-gray-600 mb-4">{subtitle}</p>
              <p className="text-gray-600 mb-6">{description}</p>

              <div className="flex items-center gap-3 mb-6">
                <span className="text-3xl font-bold text-green-700">R{discountedPrice.toLocaleString()}</span>
                <span className="text-lg text-gray-500 line-through">R{originalPrice.toLocaleString()}</span>
                <Badge className="bg-green-100 text-green-800 hover:bg-green-200 ml-2">
                  Save R{(originalPrice - discountedPrice).toLocaleString()}
                </Badge>
              </div>

              <div className="flex items-center text-sm text-gray-500 mb-6">
                <Clock className="w-4 h-4 mr-2" />
                <span>
                  {days > 0
                    ? `Offer expires in ${days} day${days !== 1 ? 's' : ''}`
                    : "Offer has expired"}
                </span>
              </div>

              {/* Promo code */}
              <div className="w-full p-3 bg-gray-100 rounded-lg flex items-center justify-between mb-6">
                <div>
                  <span className="text-xs text-gray-500 block mb-1">Promo Code:</span>
                  <span className="font-mono text-lg font-semibold text-gray-800">{code}</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyCode}
                  className="border-green-300 text-green-700 hover:bg-green-50"
                >
                  {copied ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                  {copied ? "Copied" : "Copy Code"}
                </Button>
              </div>
            </div>

            <Button
              size="lg"
              onClick={handleClaimDeal}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              Claim This Deal
            </Button>
          </div>
        </div>
      </motion.div>
    </section>
  );
}
