import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Clock } from 'lucide-react'

interface Offer {
  id: number
  title: string
  description: string
  imageUrl: string
  expiryDate: string
}

interface OfferCardProps {
  offer: Offer
}

export function OfferCard({ offer }: OfferCardProps) {
  return (
    <Card className="overflow-hidden">
      <div className="relative h-48">
        <Image
          src={offer.imageUrl || "/placeholder.svg"}
          alt={offer.title}
          layout="fill"
          objectFit="cover"
        />
      </div>
      <CardContent className="p-4">
        <h3 className="text-lg font-semibold mb-2">{offer.title}</h3>
        <p className="text-gray-600 mb-4">{offer.description}</p>
        <div className="flex items-center text-sm text-gray-500">
          <Clock className="w-4 h-4 mr-1" />
          <span>Expires on {new Date(offer.expiryDate).toLocaleDateString()}</span>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white">
          Claim Offer
        </Button>
      </CardFooter>
    </Card>
  )
}

