'use client'

import { useState, useEffect } from "react"
import { Card, CardContent} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Clock } from 'lucide-react'

const dealOfTheDay = {
  id: 1,
  title: "Smart Watch X1",
  description: "Track your fitness and stay connected with our latest smartwatch.",
  imageUrl: "https://images.unsplash.com/photo-1546868871-7041f2a55e12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&h=300&q=80",
  originalPrice: 3999,
  discountedPrice: 2999,
  endTime: new Date(new Date().getTime() + 24 * 60 * 60 * 1000), // 24 hours from now
}

export function DealOfTheDay() {
  const [timeLeft, setTimeLeft] = useState(getTimeLeft())

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(getTimeLeft())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  function getTimeLeft() {
    const difference = dealOfTheDay.endTime.getTime() - new Date().getTime()
    if (difference <= 0) return { hours: 0, minutes: 0, seconds: 0 }

    const hours = Math.floor(difference / (1000 * 60 * 60))
    const minutes = Math.floor((difference / (1000 * 60)) % 60)
    const seconds = Math.floor((difference / 1000) % 60)

    return { hours, minutes, seconds }
  }

  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Deal of the Day</h2>
      <Card className="overflow-hidden">
        <div className="md:flex">
          <div className="md:w-1/2">
            <Image
              src={dealOfTheDay.imageUrl || "/placeholder.svg"}
              alt={dealOfTheDay.title}
              width={300}
              height={300}
              layout="responsive"
            />
          </div>
          <CardContent className="p-6 md:w-1/2">
            <h3 className="text-2xl font-semibold mb-2">{dealOfTheDay.title}</h3>
            <p className="text-gray-600 mb-4">{dealOfTheDay.description}</p>
            <div className="flex items-center mb-4">
              <span className="text-3xl font-bold text-[#2A7C6C] mr-2">
                R{dealOfTheDay.discountedPrice}
              </span>
              <span className="text-xl text-gray-500 line-through">
                R{dealOfTheDay.originalPrice}
              </span>
            </div>
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Clock className="w-4 h-4 mr-1" />
              <span>
                Ends in: {timeLeft.hours.toString().padStart(2, '0')}:
                {timeLeft.minutes.toString().padStart(2, '0')}:
                {timeLeft.seconds.toString().padStart(2, '0')}
              </span>
            </div>
            <Button className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white">
              Add to Cart
            </Button>
          </CardContent>
        </div>
      </Card>
    </section>
  )
}

