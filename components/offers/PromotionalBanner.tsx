"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import Link from "next/link";

interface PromotionalBannerProps {
  title: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  bgColor?: string;
  textColor?: string;
}

export function PromotionalBanner({
  title,
  description,
  buttonText,
  buttonLink,
  bgColor = "bg-green-600",
  textColor = "text-white",
}: PromotionalBannerProps) {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`${bgColor} ${textColor} rounded-xl overflow-hidden mb-16`}
    >
      <div className="container mx-auto px-6 py-12 md:py-16">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">{title}</h2>
          <p className="text-lg opacity-90 mb-8">{description}</p>
          <Button
            asChild
            size="lg"
            className="bg-white text-green-700 hover:bg-gray-100"
          >
            <Link href={buttonLink}>{buttonText}</Link>
          </Button>
        </div>
      </div>
    </motion.section>
  );
}
