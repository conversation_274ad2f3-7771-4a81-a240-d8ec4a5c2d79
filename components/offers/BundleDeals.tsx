import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

const bundleDeals = [
  {
    id: 1,
    title: "Home Office Bundle",
    items: ["Ergonomic Chair", "Adjustable Desk", "Desk Lamp"],
    imageUrl: "https://images.unsplash.com/photo-1518455027359-f3f8164ba6bd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&h=200&q=80",
    price: 7499,
    savings: 1500,
  },
  {
    id: 2,
    title: "Fitness Starter Kit",
    items: ["Yoga Mat", "Resistance Bands", "Water Bottle"],
    imageUrl: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&h=200&q=80",
    price: 599,
    savings: 150,
  },
]

export function BundleDeals() {
  return (
    <section className="mb-12">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Bundle Deals</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {bundleDeals.map((bundle) => (
          <Card key={bundle.id}>
            <CardContent className="p-6">
              <div className="flex items-center mb-4">
                <Image
                  src={bundle.imageUrl || "/placeholder.svg"}
                  alt={bundle.title}
                  width={100}
                  height={100}
                  className="rounded-lg mr-4"
                />
                <div>
                  <h3 className="text-xl font-semibold mb-2">{bundle.title}</h3>
                  <ul className="list-disc list-inside text-gray-600 mb-2">
                    {bundle.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <span className="text-2xl font-bold text-[#2A7C6C]">R{bundle.price}</span>
                  <p className="text-sm text-gray-500">Save R{bundle.savings}</p>
                </div>
                <Button className="bg-[#2A7C6C] hover:bg-[#236657] text-white">
                  Add to Cart
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
}

