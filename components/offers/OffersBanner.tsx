import {
    Carousel,
    // CarouselContent,
    // CarouselItem,
    // CarouselPrevious,
    // CarouselNext
  } from "@/components/ui/carousel"
  import Image from "next/image"
  
  const banners = [
    {
      id: 1,
      imageUrl: "https://images.unsplash.com/photo-1607082350899-7e105aa886ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=400&q=80",
      alt: "Summer Sale",
      title: "Summer Sale",
      description: "Up to 50% off on selected items",
    },
    {
      id: 2,
      imageUrl: "https://images.unsplash.com/photo-1483985988355-763728e1935b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=400&q=80",
      alt: "New Arrivals",
      title: "New Arrivals",
      description: "Check out our latest collection",
    },
    {
      id: 3,
      imageUrl: "https://images.unsplash.com/photo-1512909006721-3d6018887383?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&h=400&q=80",
      alt: "Free Shipping",
      title: "Free Shipping",
      description: "On orders over R1000",
    },
  ]
  
  export function OffersBanner() {
    return (
      <Carousel className="mb-12">
        {banners.map((banner) => (
          <div key={banner.id} className="relative h-[400px] w-full">
            <Image
              src={banner.imageUrl || "/placeholder.svg"}
              alt={banner.alt}
              layout="fill"
              objectFit="cover"
              className="rounded-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 flex flex-col justify-center items-center text-white">
              <h2 className="text-4xl font-bold mb-2">{banner.title}</h2>
              <p className="text-xl">{banner.description}</p>
            </div>
          </div>
        ))}
      </Carousel>
    )
  }
  
  