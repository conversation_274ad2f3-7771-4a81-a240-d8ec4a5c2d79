"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { EnhancedOfferCard } from "@/components/offers/EnhancedOfferCard";
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

// Define the offer type
interface Offer {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
  expiryDate: string;
  discount: string;
  category: string;
  code?: string;
  originalPrice?: number;
  discountedPrice?: number;
}

// Sample data with categories
const categoryOffers: Offer[] = [
  // Electronics
  {
    id: 1,
    title: "Smart TV Flash Sale",
    description: "Ultra HD 4K Smart TVs at unbeatable prices",
    imageUrl: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-08-31",
    discount: "25% OFF",
    category: "electronics",
    code: "SMART25",
    originalPrice: 12999,
    discountedPrice: 9749,
  },
  {
    id: 2,
    title: "Wireless Headphones",
    description: "Premium noise-cancelling headphones",
    imageUrl: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-09-15",
    discount: "20% OFF",
    category: "electronics",
    code: "AUDIO20",
    originalPrice: 2499,
    discountedPrice: 1999,
  },
  {
    id: 3,
    title: "Smartphone Accessories",
    description: "Cases, chargers, and more",
    imageUrl: "https://images.unsplash.com/photo-1583394838336-acd977736f90?q=80&w=2068&auto=format&fit=crop",
    expiryDate: "2023-10-01",
    discount: "Buy 2 Get 1 Free",
    category: "electronics",
  },

  // Fashion
  {
    id: 4,
    title: "Summer Collection",
    description: "Latest trends for the season",
    imageUrl: "https://images.unsplash.com/photo-1445205170230-053b83016050?q=80&w=2071&auto=format&fit=crop",
    expiryDate: "2023-08-15",
    discount: "30% OFF",
    category: "fashion",
    code: "SUMMER30",
    originalPrice: 1299,
    discountedPrice: 909,
  },
  {
    id: 5,
    title: "Designer Watches",
    description: "Luxury timepieces at special prices",
    imageUrl: "https://images.unsplash.com/photo-1524805444758-089113d48a6d?q=80&w=2088&auto=format&fit=crop",
    expiryDate: "2023-09-30",
    discount: "15% OFF",
    category: "fashion",
    code: "WATCH15",
    originalPrice: 4999,
    discountedPrice: 4249,
  },
  {
    id: 6,
    title: "Footwear Clearance",
    description: "End of season sale on all shoes",
    imageUrl: "https://images.unsplash.com/photo-1549298916-b41d501d3772?q=80&w=2012&auto=format&fit=crop",
    expiryDate: "2023-08-31",
    discount: "Up to 50% OFF",
    category: "fashion",
  },

  // Home & Living
  {
    id: 7,
    title: "Kitchen Appliances",
    description: "Modern appliances for your kitchen",
    imageUrl: "https://images.unsplash.com/photo-1556911220-bda9f7f7597e?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-09-15",
    discount: "20% OFF",
    category: "home",
    code: "KITCHEN20",
    originalPrice: 3499,
    discountedPrice: 2799,
  },
  {
    id: 8,
    title: "Bedroom Furniture",
    description: "Comfortable and stylish furniture",
    imageUrl: "https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-10-31",
    discount: "25% OFF",
    category: "home",
    code: "BED25",
    originalPrice: 8999,
    discountedPrice: 6749,
  },
  {
    id: 9,
    title: "Home Decor",
    description: "Accent pieces to elevate your space",
    imageUrl: "https://images.unsplash.com/photo-1513519245088-0e12902e5a38?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-09-30",
    discount: "Buy 1 Get 1 Half Price",
    category: "home",
  },

  // Beauty & Health
  {
    id: 10,
    title: "Skincare Bundle",
    description: "Complete skincare routine package",
    imageUrl: "https://images.unsplash.com/photo-1571781926291-c477ebfd024b?q=80&w=2088&auto=format&fit=crop",
    expiryDate: "2023-08-31",
    discount: "30% OFF",
    category: "beauty",
    code: "SKIN30",
    originalPrice: 1899,
    discountedPrice: 1329,
  },
  {
    id: 11,
    title: "Makeup Collection",
    description: "Premium makeup products",
    imageUrl: "https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?q=80&w=2087&auto=format&fit=crop",
    expiryDate: "2023-09-15",
    discount: "25% OFF",
    category: "beauty",
    code: "MAKEUP25",
    originalPrice: 2499,
    discountedPrice: 1874,
  },
  {
    id: 12,
    title: "Fitness Equipment",
    description: "Home workout essentials",
    imageUrl: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?q=80&w=2070&auto=format&fit=crop",
    expiryDate: "2023-10-15",
    discount: "20% OFF",
    category: "beauty",
    code: "FIT20",
    originalPrice: 5999,
    discountedPrice: 4799,
  },
];

// Define the categories
const categories = [
  { id: "all", name: "All Offers" },
  { id: "electronics", name: "Electronics" },
  { id: "fashion", name: "Fashion" },
  { id: "home", name: "Home & Living" },
  { id: "beauty", name: "Beauty & Health" },
];

export function CategoryOffers() {
  const [activeCategory, setActiveCategory] = useState("all");
  const [visibleCount, setVisibleCount] = useState(6);

  // Filter offers based on active category
  const filteredOffers = activeCategory === "all"
    ? categoryOffers
    : categoryOffers.filter(offer => offer.category === activeCategory);

  // Determine if we need to show the "View More" button
  const hasMoreOffers = filteredOffers.length > visibleCount;

  // Handle loading more offers
  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 3);
  };

  return (
    <section className="mb-16">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900">Special Offers by Category</h2>
        <p className="text-gray-500 mt-2 md:mt-0">
          Browse our exclusive deals across different categories
        </p>
      </div>

      <Tabs defaultValue="all" value={activeCategory} onValueChange={setActiveCategory} className="w-full">
        <TabsList className="mb-8 w-full max-w-full overflow-x-auto flex flex-nowrap justify-start md:justify-center p-1 bg-gray-100 rounded-full">
          {categories.map(category => (
            <TabsTrigger
              key={category.id}
              value={category.id}
              className="px-6 py-2 rounded-full data-[state=active]:bg-green-600 data-[state=active]:text-white"
            >
              {category.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map(category => (
          <TabsContent key={category.id} value={category.id} className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredOffers.slice(0, visibleCount).map(offer => (
                <EnhancedOfferCard key={offer.id} offer={offer} />
              ))}
            </div>

            {hasMoreOffers && (
              <div className="flex justify-center mt-8">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  className="border-green-300 text-green-700 hover:bg-green-50"
                >
                  View More Offers <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </section>
  );
}
