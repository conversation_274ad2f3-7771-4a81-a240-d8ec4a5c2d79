"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import Link from "next/link";

// Define the slide type
interface HeroSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  imageUrl: string;
  buttonText: string;
  buttonLink: string;
  discount: string;
}

// Sample data
const heroSlides: HeroSlide[] = [
  {
    id: 1,
    title: "Summer Sale",
    subtitle: "Up to 50% Off",
    description: "Discover amazing deals on our most popular products for the summer season.",
    imageUrl: "https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?q=80&w=2070&auto=format&fit=crop",
    buttonText: "Shop Now",
    buttonLink: "/products",
    discount: "50% OFF",
  },
  {
    id: 2,
    title: "New Arrivals",
    subtitle: "Fresh Collection",
    description: "Be the first to explore our latest products with special introductory prices.",
    imageUrl: "https://images.unsplash.com/photo-1607083206968-13611e3d76db?q=80&w=2115&auto=format&fit=crop",
    buttonText: "Explore",
    buttonLink: "/products/new",
    discount: "New Season",
  },
  {
    id: 3,
    title: "Exclusive Deals",
    subtitle: "Members Only",
    description: "Special offers exclusively for our loyal customers. Sign in to unlock these deals.",
    imageUrl: "https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?q=80&w=2115&auto=format&fit=crop",
    buttonText: "Sign In",
    buttonLink: "/login",
    discount: "Members Only",
  },
];

export function OffersHero() {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto-rotate slides
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === heroSlides.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative mb-16 rounded-xl overflow-hidden shadow-lg">
      <div className="h-[500px] relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="absolute inset-0"
          >
            {/* Background image */}
            <Image
              src={heroSlides[currentSlide].imageUrl}
              alt={heroSlides[currentSlide].title}
              layout="fill"
              objectFit="cover"
              priority
            />
            
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent"></div>
            
            {/* Content */}
            <div className="relative h-full flex items-center">
              <div className="container mx-auto px-6">
                <div className="max-w-lg">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <span className="inline-block px-4 py-1 mb-4 bg-green-600 text-white text-sm font-medium rounded-full">
                      {heroSlides[currentSlide].discount}
                    </span>
                  </motion.div>
                  
                  <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                    className="text-4xl md:text-5xl font-bold text-white mb-2"
                  >
                    {heroSlides[currentSlide].title}
                  </motion.h1>
                  
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.5 }}
                    className="text-xl text-white/90 mb-2"
                  >
                    {heroSlides[currentSlide].subtitle}
                  </motion.p>
                  
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                    className="text-white/80 mb-8"
                  >
                    {heroSlides[currentSlide].description}
                  </motion.p>
                  
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                  >
                    <Button
                      asChild
                      size="lg"
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <Link href={heroSlides[currentSlide].buttonLink}>
                        {heroSlides[currentSlide].buttonText}
                      </Link>
                    </Button>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
        
        {/* Slide indicators */}
        <div className="absolute bottom-6 left-0 right-0">
          <div className="flex justify-center gap-2">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentSlide ? "bg-white w-6" : "bg-white/50"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
