"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Copy, Check, Tag, ExternalLink } from "lucide-react";
import { motion } from "framer-motion";
import Image from "next/image";
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';

interface Offer {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
  expiryDate: string;
  discount: string;
  category: string;
  code?: string;
  originalPrice?: number;
  discountedPrice?: number;
}

interface EnhancedOfferCardProps {
  offer: Offer;
}

export function EnhancedOfferCard({ offer }: EnhancedOfferCardProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [copied, setCopied] = useState(false);

  const handleCopyCode = () => {
    if (offer.code) {
      navigator.clipboard.writeText(offer.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleClaimOffer = () => {
    if (!user) {
      router.push('/login');
    } else {
      // If user is logged in, navigate to the offer details page
      router.push(`/offers/${offer.id}`);
    }
  };

  const daysRemaining = () => {
    const today = new Date();
    const expiryDate = new Date(offer.expiryDate);
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const days = daysRemaining();
  const isUrgent = days <= 3 && days > 0;
  const isExpired = days <= 0;

  return (
    <motion.div
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="overflow-hidden h-full flex flex-col border-0 shadow-md hover:shadow-xl transition-shadow duration-300">
        <div className="relative">
          {/* Discount badge */}
          <Badge className="absolute top-4 left-4 z-10 bg-green-600 hover:bg-green-700 text-white px-3 py-1 text-sm font-medium">
            {offer.discount}
          </Badge>

          {/* Urgency badge */}
          {isUrgent && !isExpired && (
            <Badge className="absolute top-4 right-4 z-10 bg-red-500 hover:bg-red-600 text-white px-3 py-1 text-sm font-medium">
              Ends Soon!
            </Badge>
          )}

          {/* Expired badge */}
          {isExpired && (
            <Badge className="absolute top-4 right-4 z-10 bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 text-sm font-medium">
              Expired
            </Badge>
          )}

          {/* Image with gradient overlay */}
          <div className="relative h-48 w-full">
            <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent z-[1]"></div>
            <Image
              src={offer.imageUrl || "/placeholder.svg"}
              alt={offer.title}
              layout="fill"
              objectFit="cover"
            />
          </div>
        </div>

        <CardContent className="p-5 flex-grow">
          <h3 className="text-xl font-bold mb-2 text-gray-900">{offer.title}</h3>
          <p className="text-gray-600 mb-4">{offer.description}</p>

          {/* Price display if available */}
          {offer.originalPrice && offer.discountedPrice && (
            <div className="flex items-center gap-2 mb-4">
              <span className="text-lg font-bold text-green-700">R{offer.discountedPrice.toLocaleString()}</span>
              <span className="text-sm text-gray-500 line-through">R{offer.originalPrice.toLocaleString()}</span>
            </div>
          )}

          {/* Expiry date */}
          <div className="flex items-center text-sm text-gray-500 mb-2">
            <Clock className="w-4 h-4 mr-2" />
            <span>
              {isExpired
                ? "Offer has expired"
                : `Expires in ${days} day${days !== 1 ? 's' : ''}`}
            </span>
          </div>

          {/* Category tag */}
          <div className="flex items-center text-sm text-gray-500">
            <Tag className="w-4 h-4 mr-2" />
            <span className="capitalize">{offer.category}</span>
          </div>
        </CardContent>

        <CardFooter className="p-5 pt-0 flex flex-col gap-3">
          {/* Promo code if available */}
          {offer.code && (
            <div className="w-full p-2 bg-gray-100 rounded-md flex items-center justify-between mb-2">
              <div className="font-mono text-sm font-medium text-gray-800">{offer.code}</div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyCode}
                className="h-8 px-2 text-green-600 hover:text-green-700 hover:bg-green-100"
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                <span className="ml-1">{copied ? "Copied" : "Copy"}</span>
              </Button>
            </div>
          )}

          <Button
            className={`w-full ${isExpired ? 'bg-gray-400 hover:bg-gray-500' : 'bg-green-600 hover:bg-green-700'} text-white`}
            disabled={isExpired}
            onClick={handleClaimOffer}
          >
            {isExpired ? "Expired" : "Claim Offer"}
            <ExternalLink className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
