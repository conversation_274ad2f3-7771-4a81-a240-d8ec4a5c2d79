import { useEffect } from 'react'
import { CheckCircle } from 'lucide-react'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  productName: string
}

export function ConfirmationModal({ isOpen, onClose, productName }: ConfirmationModalProps) {
  // Auto-close after 3 seconds
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        onClose()
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [isOpen, onClose])

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="flex flex-row items-center gap-2">
          <CheckCircle className="h-6 w-6 text-green-500" />
          <div>
            <DialogTitle>Item Added to Cart</DialogTitle>
            <DialogDescription>
              {productName} has been added to your cart successfully.
            </DialogDescription>
          </div>
        </DialogHeader>
        <DialogFooter className="flex-row justify-between sm:justify-between">
          <Button variant="outline" onClick={onClose}>
            Continue Shopping
          </Button>
          <Button asChild className="bg-purple-600 hover:bg-purple-700">
            <Link href="/cart">View Cart</Link>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

