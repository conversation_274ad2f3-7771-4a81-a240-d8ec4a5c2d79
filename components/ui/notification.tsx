import React, { useState, useEffect } from 'react'
import { X } from 'lucide-react'

interface NotificationProps {
  title: string
  message: string
  type?: 'success' | 'error' | 'info'
  duration?: number
  onClose: () => void
}

export const Notification: React.FC<NotificationProps> = ({
  title,
  message,
  type = 'info',
  duration = 3000,
  onClose,
}) => {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      onClose()
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onClose])

  if (!isVisible) return null

  const bgColor = type === 'success' ? 'bg-green-100' : type === 'error' ? 'bg-red-100' : 'bg-blue-100'
  const textColor = type === 'success' ? 'text-green-800' : type === 'error' ? 'text-red-800' : 'text-blue-800'

  return (
    <div className={`fixed bottom-4 right-4 w-80 ${bgColor} ${textColor} p-4 rounded-md shadow-lg`}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-semibold">{title}</h3>
          <p className="text-sm mt-1">{message}</p>
        </div>
        <button onClick={() => { setIsVisible(false); onClose(); }} className="text-gray-500 hover:text-gray-700">
          <X size={18} />
        </button>
      </div>
    </div>
  )
}

