"use client";

import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { Check, ChevronDown, Search, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface Province {
  _id: string;
  name: string;
  code: string;
  isActive?: boolean;
}

interface EnhancedProvinceSelectProps {
  provinces: Province[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
}

export function EnhancedProvinceSelect({
  provinces,
  value,
  onValueChange,
  placeholder = "Select a province",
  disabled = false,
  className,
  isLoading = false,
}: EnhancedProvinceSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedProvince = provinces.find((province) => province._id === value);

  // Filter provinces based on search query
  const filteredProvinces = provinces.filter((province) =>
    province.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    province.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelect = (provinceId: string) => {
    onValueChange(provinceId);
    setOpen(false);
    setSearchQuery("");
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange("");
    setSearchQuery("");
  };

  const toggleOpen = () => {
    if (!disabled && !isLoading) {
      setOpen(!open);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open]);

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        onClick={toggleOpen}
        className={cn(
          "w-full justify-between h-12 text-left font-normal",
          !value && "text-muted-foreground",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        disabled={disabled || isLoading}
      >
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {selectedProvince ? (
            <>
              <div className="w-6 h-6 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-xs font-bold text-white">
                  {selectedProvince.code}
                </span>
              </div>
              <span className="truncate">{selectedProvince.name}</span>
              <Badge variant="secondary" className="ml-auto bg-emerald-100 text-emerald-700 border-emerald-200">
                {selectedProvince.code}
              </Badge>
            </>
          ) : (
            <span>{isLoading ? "Loading provinces..." : placeholder}</span>
          )}
        </div>
        <div className="flex items-center gap-1 flex-shrink-0">
          {selectedProvince && !disabled && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-gray-100"
              onClick={handleClear}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
          <ChevronDown className={cn("h-4 w-4 opacity-50 transition-transform", open && "rotate-180")} />
        </div>
      </Button>

      {open && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border rounded-md shadow-lg">
          {/* Search Header */}
          <div className="bg-gradient-to-r from-emerald-500 to-teal-500 p-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/70" />
              <Input
                placeholder="Search provinces..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white/20 border-white/30 text-white placeholder:text-white/70 focus:bg-white/30 focus:border-white/50"
                autoFocus
              />
            </div>
          </div>

          {/* Province List */}
          <div className="max-h-64 overflow-y-auto bg-white">
            {filteredProvinces.length === 0 ? (
              <div className="py-6 text-center text-sm text-gray-500">
                {searchQuery ? "No provinces found." : "No provinces available."}
              </div>
            ) : (
              <div className="p-1">
                {filteredProvinces.map((province) => (
                  <div
                    key={province._id}
                    onClick={() => handleSelect(province._id)}
                    className="flex items-center gap-3 p-3 cursor-pointer hover:bg-emerald-50 rounded-sm transition-colors"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-bold text-white">
                        {province.code}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 truncate">
                        {province.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        Province Code: {province.code}
                      </div>
                    </div>
                    <Check
                      className={cn(
                        "h-4 w-4 text-emerald-600",
                        value === province._id ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
