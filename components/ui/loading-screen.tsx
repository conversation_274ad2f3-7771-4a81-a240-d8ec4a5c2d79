
// components/ui/LoadingScreen.tsx
import type React from "react"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

interface LoadingScreenProps extends React.HTMLAttributes<HTMLDivElement> {
  projectName?: string
}

export function LoadingScreen({ projectName = "Stokvel", className, ...props }: LoadingScreenProps) {
  const [dots, setDots] = useState("")

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length >= 3 ? "" : prev + "."))
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <div
      className={cn("fixed inset-0 flex flex-col items-center justify-center bg-[#2A7C6C] text-white", className)}
      {...props}
    >
      <h1 className="text-4xl font-bold mb-4" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
        {projectName}
      </h1>
      <p className="text-xl" style={{ fontFamily: "Avenir, sans-serif" }}>
        loading{dots}
      </p>
    </div>
  )
}

