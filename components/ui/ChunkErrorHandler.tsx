"use client";

import { useEffect } from 'react';
import { initChunkError<PERSON>and<PERSON>, resetChunkErrorHandler, preloadCriticalChunks } from '@/lib/chunkErrorHandler';

export function ChunkErrorHandler() {
  useEffect(() => {
    // Initialize chunk error handling
    initChunkErrorHandler();
    
    // Preload critical chunks
    preloadCriticalChunks();
    
    // Reset error handler on route changes
    const handleRouteChange = () => {
      resetChunkErrorHandler();
    };

    // Listen for route changes (Next.js specific)
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', handleRouteChange);
      
      // Also listen for popstate (back/forward navigation)
      window.addEventListener('popstate', handleRouteChange);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('beforeunload', handleRouteChange);
        window.removeEventListener('popstate', handleRouteChange);
      }
    };
  }, []);

  // This component doesn't render anything
  return null;
}
