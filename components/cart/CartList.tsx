// components/cart/CartList.tsx
'use client'

import { useAppSelector } from "@/lib/redux/hooks"
import {
  selectDisplayItems,
  selectSubtotal
} from "@/lib/redux/features/cart/cartSlice"
import {
  useUpdateCartItemMutation,
  useRemoveFromCartMutation
} from "@/lib/redux/features/cart/cartApiSlice"
import { Button } from "@/components/ui/button"
import { Minus, Plus, X } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useAuth } from "@/context/AuthContext"
import { useState } from "react"

interface CartListProps {
  groupId: string;
}

export function CartList({ groupId }: CartListProps) {
  const { user } = useAuth();
  const userId = user?._id || '';

  // Redux state
  const displayItems = useAppSelector(selectDisplayItems);
  const subtotal = useAppSelector(selectSubtotal);

  // RTK Query mutations
  const [updateCartItem] = useUpdateCartItemMutation();
  const [removeFromCart] = useRemoveFromCartMutation();

  // Local state for optimistic UI updates
  const [isUpdating, setIsUpdating] = useState<Record<string, boolean>>({});
  const [isRemoving, setIsRemoving] = useState<Record<string, boolean>>({});

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Shopping Cart</h1>
        <p className="text-sm text-gray-500">Price</p>
      </div>

      <div className="divide-y">
        {displayItems.map((item) => {
          const isItemUpdating = isUpdating[item.productId] || false;
          const isItemRemoving = isRemoving[item.productId] || false;

          // Handle quantity update
          const handleUpdateQuantity = async (newQuantity: number) => {
            if (newQuantity < 1 || !userId) return;

            try {
              setIsUpdating(prev => ({ ...prev, [item.productId]: true }));

              await updateCartItem({
                userId,
                productId: item.productId,
                quantity: newQuantity,
                groupId
              });
            } catch (error) {
              console.error("Failed to update cart item quantity:", error);
            } finally {
              setIsUpdating(prev => ({ ...prev, [item.productId]: false }));
            }
          };

          // Handle item removal
          const handleRemoveItem = async () => {
            if (!userId) return;

            try {
              setIsRemoving(prev => ({ ...prev, [item.productId]: true }));

              await removeFromCart({
                userId,
                productId: item.productId,
                groupId
              });
            } catch (error) {
              console.error("Failed to remove item from cart:", error);
            } finally {
              setIsRemoving(prev => ({ ...prev, [item.productId]: false }));
            }
          };

          return (
            <div key={item._id} className="flex py-6 gap-6">
              <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md bg-gray-100">
                <Image
                  src={item.image ? `/api/images/${item.image}` : "/placeholder.svg"}
                  alt={item.name}
                  width={96}
                  height={96}
                  className="h-full w-full object-cover object-center"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/placeholder.svg";
                  }}
                />
              </div>

              <div className="flex flex-1 flex-col">
                <div className="flex justify-between text-base font-medium text-gray-900">
                  <h3>{item.name}</h3>
                  <p className="ml-4">R {(item.price || 0).toFixed(2)}</p>
                </div>
                <p className="mt-1 text-sm text-gray-500">SKU: #{item._id.slice(-6) || 'N/A'}</p>

                <div className="flex items-center mt-4 gap-4">
                  <div className="flex items-center gap-2">
                    <Button
                      size="icon"
                      variant="outline"
                      className="h-8 w-8"
                      onClick={() => handleUpdateQuantity(item.quantity - 1)}
                      disabled={isItemUpdating || item.quantity <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <span className="w-8 text-center">{item.quantity}</span>
                    <Button
                      size="icon"
                      variant="outline"
                      className="h-8 w-8"
                      onClick={() => handleUpdateQuantity(item.quantity + 1)}
                      disabled={isItemUpdating}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveItem}
                    disabled={isItemRemoving}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="flex justify-between items-center py-4 border-t">
        <Link
          href="/store"
          className="text-blue-600 hover:text-blue-800 font-medium flex items-center gap-2"
        >
          ← Continue Shopping
        </Link>
        <div className="text-right">
          <p className="text-gray-500">Subtotal</p>
          <p className="text-2xl font-semibold">R {subtotal.toFixed(2)}</p>
        </div>
      </div>
    </div>
  )
}
