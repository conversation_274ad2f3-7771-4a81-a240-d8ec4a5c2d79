'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useGetGroupOrdersQuery } from '@/lib/redux/features/cart/cartApiSlice'
import { formatCurrency } from '@/lib/utils'
import { Users, ShoppingBag, TrendingUp } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent } from '@/components/ui/card'

interface GroupOrderSummaryProps {
  groupId: string
}

/**
 * GroupOrderSummary component that shows a summary of the current group order
 */
export function GroupOrderSummary({ groupId }: GroupOrderSummaryProps) {
  const { data: groupOrders, isLoading } = useGetGroupOrdersQuery(groupId)
  
  // Get the current group order (should be the first one)
  const currentOrder = groupOrders?.[0]
  
  if (isLoading) {
    return (
      <Card className="mt-4">
        <CardContent className="pt-6">
          <div className="flex items-center gap-4 mb-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </CardContent>
      </Card>
    )
  }
  
  if (!currentOrder) {
    return null
  }
  
  // Count unique users contributing to the order
  const uniqueUsers = new Set(currentOrder.userContributions.map(c => c.userId)).size
  
  return (
    <Card className="mt-4 overflow-hidden">
      <CardContent className="pt-6">
        <h3 className="font-medium text-lg mb-4 flex items-center gap-2">
          <ShoppingBag className="h-5 w-5 text-purple-500" />
          Group Order Summary
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <motion.div 
            className="flex items-center gap-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <div className="bg-blue-100 p-2 rounded-full">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-sm text-gray-500">Contributors</div>
              <div className="font-medium">{uniqueUsers} {uniqueUsers === 1 ? 'member' : 'members'}</div>
            </div>
          </motion.div>
          
          <motion.div 
            className="flex items-center gap-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-green-100 p-2 rounded-full">
              <TrendingUp className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <div className="text-sm text-gray-500">Total Value</div>
              <div className="font-medium">{formatCurrency(currentOrder.totalOrderValue)}</div>
            </div>
          </motion.div>
          
          <motion.div 
            className="flex items-center gap-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="bg-purple-100 p-2 rounded-full">
              <ShoppingBag className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <div className="text-sm text-gray-500">Items</div>
              <div className="font-medium">
                {currentOrder.orderItems.reduce((sum, item) => sum + item.quantity, 0)} items
              </div>
            </div>
          </motion.div>
        </div>
        
        {currentOrder.userContributions.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-500 mb-2">Top Contributors</h4>
            <div className="space-y-2">
              {currentOrder.userContributions
                .sort((a, b) => b.totalSpent - a.totalSpent)
                .slice(0, 3)
                .map((contribution, index) => (
                  <motion.div 
                    key={contribution.userId}
                    className="flex items-center justify-between"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 + (index * 0.1) }}
                  >
                    <div className="flex items-center gap-2">
                      <div className="bg-gray-100 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <span className="font-medium">{contribution.userName}</span>
                    </div>
                    <span className="text-gray-600">{formatCurrency(contribution.totalSpent)}</span>
                  </motion.div>
                ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default GroupOrderSummary
