'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>Circle, ShoppingBag, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import Link from 'next/link'
import { useAuth } from '@/context/AuthContext'

interface OrderSuccessMessageProps {
  groupId: string
  orderId: string
}

/**
 * OrderSuccessMessage component that shows a success message after an order is placed
 */
export function OrderSuccessMessage({ groupId, orderId }: OrderSuccessMessageProps) {
  const { user } = useAuth()
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-2xl mx-auto py-12"
    >
      <Card className="border-green-100 shadow-lg">
        <CardContent className="pt-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 500 }}
            className="mx-auto bg-green-100 rounded-full p-4 w-20 h-20 flex items-center justify-center mb-6"
          >
            <CheckCircle className="h-10 w-10 text-green-600" />
          </motion.div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Order Placed Successfully!</h1>
          <p className="text-gray-600 mb-6">
            Thank you{user?.name ? `, ${user.name}` : ''}! Your group order has been placed and is being processed.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <div className="flex justify-between mb-2">
              <span className="text-gray-600">Order ID:</span>
              <span className="font-medium">{orderId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <span className="text-purple-600 font-medium">Processing</span>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild variant="outline" className="flex items-center gap-2">
              <Link href={`/groups/${groupId}/orders/${orderId}`}>
                <ShoppingBag className="h-4 w-4" />
                View Order Details
              </Link>
            </Button>
            
            <Button asChild className="bg-[#2A7C6C] hover:bg-[#236358] flex items-center gap-2">
              <Link href={`/groups/${groupId}/products`}>
                Continue Shopping
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default OrderSuccessMessage
