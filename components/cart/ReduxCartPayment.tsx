'use client'

import { useAppSelector } from "@/lib/redux/hooks"
import { selectSubtotal } from "@/lib/redux/features/cart/cartSlice"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { useRouter } from 'next/navigation'

export function ReduxCartPayment() {
  const subtotal = useAppSelector(selectSubtotal)
  const router = useRouter()
  const [cardNumber, setCardNumber] = useState('')
  const [expiryMonth, setExpiryMonth] = useState('')
  const [expiryYear, setExpiryYear] = useState('')
  const [cvv, setCvv] = useState('')
  const [cardholderName, setCardholderName] = useState('')

  const handleCheckout = () => {
    // Implement your checkout/payment logic here
    console.log('Processing payment...')
    router.push('/checkout/success')
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold">Payment Details</h2>
      
      <div className="flex justify-between items-center py-4 border-t border-b">
        <span className="text-lg">Order Total:</span>
        <span className="text-2xl font-bold">R {subtotal.toFixed(2)}</span>
      </div>
      
      <div className="space-y-4">
        <div>
          <label htmlFor="cardholderName" className="block text-sm font-medium mb-1">
            Cardholder Name
          </label>
          <Input
            id="cardholderName"
            value={cardholderName}
            onChange={(e) => setCardholderName(e.target.value)}
            placeholder="John Doe"
            required
          />
        </div>
        
        <div>
          <label htmlFor="cardNumber" className="block text-sm font-medium mb-1">
            Card Number
          </label>
          <Input
            id="cardNumber"
            value={cardNumber}
            onChange={(e) => setCardNumber(e.target.value.replace(/\D/g, ''))}
            placeholder="1234 5678 9012 3456"
            maxLength={16}
            required
          />
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label htmlFor="expiryMonth" className="block text-sm font-medium mb-1">
              Month
            </label>
            <Input
              id="expiryMonth"
              value={expiryMonth}
              onChange={(e) => setExpiryMonth(e.target.value.replace(/\D/g, ''))}
              placeholder="MM"
              maxLength={2}
              required
            />
          </div>
          
          <div>
            <label htmlFor="expiryYear" className="block text-sm font-medium mb-1">
              Year
            </label>
            <Input
              id="expiryYear"
              value={expiryYear}
              onChange={(e) => setExpiryYear(e.target.value.replace(/\D/g, ''))}
              placeholder="YY"
              maxLength={2}
              required
            />
          </div>
          
          <div>
            <label htmlFor="cvv" className="block text-sm font-medium mb-1">
              CVV
            </label>
            <Input
              id="cvv"
              value={cvv}
              onChange={(e) => setCvv(e.target.value.replace(/\D/g, ''))}
              placeholder="123"
              maxLength={3}
              required
            />
          </div>
        </div>
      </div>
      
      <Button 
        onClick={handleCheckout} 
        className="w-full bg-[#2A7C6C] hover:bg-[#236358]"
        disabled={!cardNumber || !expiryMonth || !expiryYear || !cvv || !cardholderName}
      >
        Pay R {subtotal.toFixed(2)}
      </Button>
      
      <p className="text-xs text-center text-gray-500 mt-4">
        Your payment information is secure and encrypted. We do not store your card details.
      </p>
    </div>
  )
}
