'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import { useAppSelector } from '@/lib/redux/hooks'
import { selectCartLoading } from '@/lib/redux/features/cart/cartSlice'

/**
 * CartLoadingIndicator component that shows a loading spinner during cart operations
 */
export function CartLoadingIndicator() {
  const isLoading = useAppSelector(selectCartLoading)
  
  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed top-4 right-4 bg-white shadow-md rounded-full p-2 z-50"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          >
            <Loader2 className="h-5 w-5 text-purple-600" />
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default CartLoadingIndicator
