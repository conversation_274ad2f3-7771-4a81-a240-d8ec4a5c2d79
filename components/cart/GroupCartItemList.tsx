import React from 'react';
import { CartItem } from '@/types/shoppingCart';

interface GroupCartItemListProps {
  items: CartItem[];
}

const GroupCartItemList: React.FC<GroupCartItemListProps> = ({ items }) => {
  return (
    <table className="table-auto w-full">
      <thead>
        <tr>
          <th className="px-4 py-2">Product</th>
          <th className="px-4 py-2">Buyer</th>
          <th className="px-4 py-2">Quantity</th>
          <th className="px-4 py-2">Price</th>
        </tr>
      </thead>
      <tbody>
        {items.map((item) => (
          <tr key={item._id}>
            <td className="border px-4 py-2">{item.product.name}</td>
            <td className="border px-4 py-2">{item.buyerName}</td>
            <td className="border px-4 py-2">{item.quantity}</td>
            <td className="border px-4 py-2">${item.price}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default GroupCartItemList;
