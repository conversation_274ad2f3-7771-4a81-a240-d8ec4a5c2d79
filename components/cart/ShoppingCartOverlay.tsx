"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useCart } from "@/lib/redux/hooks/useCart";
import { useAuth } from "@/context/AuthContext";

interface CartOverlayProps {
  groupId: string;
  onCheckoutComplete: () => void;
}

export function ShoppingCartOverlay({ groupId, onCheckoutComplete }: CartOverlayProps) {
  // We need auth context but don't use the user ID directly
  useAuth();

  // Use Redux cart hooks
  const {
    cartItems,
    updateCartItem,
    removeFromCart,
    totalItems,
    isLoading
  } = useCart(groupId);

  const [isOpen, setIsOpen] = useState(false);
  const [updatingItems, setUpdatingItems] = useState<Set<string>>(new Set());

  const handleUpdateQuantity = async (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) return;

    setUpdatingItems(prev => new Set(prev).add(productId));

    try {
      await updateCartItem(productId, newQuantity);
    } catch (error) {
      console.error('Failed to update cart item:', error);
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  };

  const handleRemoveItem = async (productId: string) => {
    setUpdatingItems(prev => new Set(prev).add(productId));

    try {
      await removeFromCart(productId);
    } catch (error) {
      console.error('Failed to remove cart item:', error);
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  };

  const handleCheckout = async () => {
    try {
      // Redirect to checkout page
      window.location.href = `/groups/${groupId}/checkout`;
      setIsOpen(false);
      onCheckoutComplete();
    } catch (error) {
      console.error("Error during checkout:", error);
    }
  };

  return (
    <>
      <Button variant="ghost" size="icon" onClick={() => setIsOpen(true)}>
        <span>View Cart ({totalItems})</span>
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Your Shopping Cart</DialogTitle>
          </DialogHeader>

          {isLoading ? (
            <div className="space-y-4">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ) : cartItems.length > 0 ? (
            <div className="space-y-4">
              {cartItems.map((item) => {
                const isUpdating = updatingItems.has(item._id as string);
                return (
                  <div key={item._id} className={`flex items-center justify-between ${isUpdating ? 'opacity-50' : ''}`}>
                    <span>{item.name}</span>
                    <Input
                      type="number"
                      min={1}
                      value={item.quantity}
                      disabled={isUpdating}
                      onChange={(e) => typeof item._id === 'string' && handleUpdateQuantity(item._id, parseInt(e.target.value))}
                    />
                    <Button
                      variant="destructive"
                      disabled={isUpdating}
                      onClick={() => typeof item._id === 'string' && handleRemoveItem(item._id)}
                    >
                      {isUpdating ? 'Removing...' : 'Remove'}
                    </Button>
                  </div>
                );
              })}
            </div>
          ) : (
            <p>Your cart is empty.</p>
          )}

          <DialogFooter>
            <Button variant="default" onClick={handleCheckout}>
              Proceed to Checkout
            </Button>
            <Button variant="secondary" onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
