'use client'

import React, { useState, useEffect } from 'react'
import { AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useAppSelector } from '@/lib/redux/hooks'
import { selectCartError } from '@/lib/redux/features/cart/cartSlice'

interface CartErrorBoundaryProps {
  children: React.ReactNode
  onRetry: () => void
}

export function CartErrorBoundary({ children, onRetry }: CartErrorBoundaryProps) {
  const cartError = useAppSelector(selectCartError)
  const [showError, setShowError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // Show error after a short delay to avoid flashing for transient errors
  useEffect(() => {
    if (cartError) {
      const timer = setTimeout(() => {
        setShowError(true)
      }, 500)
      
      return () => clearTimeout(timer)
    } else {
      setShowError(false)
    }
  }, [cartError])

  // Auto-retry a few times with increasing delays
  useEffect(() => {
    if (cartError && retryCount < 3) {
      const retryDelay = Math.pow(2, retryCount) * 1000 // Exponential backoff
      
      const timer = setTimeout(() => {
        onRetry()
        setRetryCount(prev => prev + 1)
      }, retryDelay)
      
      return () => clearTimeout(timer)
    }
  }, [cartError, retryCount, onRetry])

  // Reset retry count when error is cleared
  useEffect(() => {
    if (!cartError) {
      setRetryCount(0)
    }
  }, [cartError])

  if (showError && cartError) {
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error loading cart</AlertTitle>
          <AlertDescription>
            {cartError}
          </AlertDescription>
        </Alert>
        
        <div className="flex justify-center">
          <Button 
            variant="outline"
            onClick={() => {
              setRetryCount(0)
              onRetry()
            }}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
