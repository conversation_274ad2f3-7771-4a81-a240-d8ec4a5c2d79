'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle } from '@/components/ui/sheet';
import { GroupCart } from './GroupCart';
import { DiscountProgressBar } from './DiscountProgressBar';
import { useAppSelector } from '@/lib/redux/hooks';
import { selectTotalItems } from '@/lib/redux/features/cart/cartSlice';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

interface CartOverlayProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CartOverlay({ isOpen, onOpenChange }: CartOverlayProps) {
  const totalItems = useAppSelector(selectTotalItems);
  const { user } = useAuth();
  const router = useRouter();
  const [currentGroupId, setCurrentGroupId] = useState('');

  // Get the current group ID from localStorage
  useEffect(() => {
    const groupId = localStorage.getItem('currentGroupId') || '';
    setCurrentGroupId(groupId);
  }, []);

  // Handle checkout
  const handleCheckout = () => {
    onOpenChange(false);
    if (currentGroupId) {
      router.push(`/groups/${currentGroupId}/checkout`);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-md overflow-y-auto">
        <SheetHeader className="mb-4">
          <SheetTitle>Shopping Cart</SheetTitle>
        </SheetHeader>
        {user && currentGroupId ? (
          <div className="space-y-6">
            <GroupCart
              groupId={currentGroupId}
              onCheckout={handleCheckout}
            />

            {totalItems > 0 && (
              <DiscountProgressBar groupId={currentGroupId} />
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <p className="text-gray-500 mb-6">
              {!user
                ? "Please log in to view your cart."
                : "No group selected. Please join a group to shop."}
            </p>
            <Button
              className="bg-[#2A7C6C] hover:bg-[#236358] text-white"
              onClick={() => router.push(!user ? '/login' : '/')}
            >
              {!user ? "Log In" : "Browse Groups"}
            </Button>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
