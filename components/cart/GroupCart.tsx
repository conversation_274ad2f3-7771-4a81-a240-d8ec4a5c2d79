'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Minus, Plus, X, ShoppingBag, Trash2, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useGetShoppingCartQuery, useUpdateCartItemMutation, useRemoveFromCartMutation, useClearCartMutation } from '@/lib/redux/features/cart/cartApiSlice'
import { useAppSelector, useAppDispatch } from '@/lib/redux/hooks'
import { selectCart, selectTotalItems, selectSubtotal, selectCartError, setError, clearCartState } from '@/lib/redux/features/cart/cartSlice'
import { useAuth } from '@/context/AuthContext'
import { toast } from 'sonner'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'

interface GroupCartProps {
  groupId: string
  onCheckout?: () => void
}

export function GroupCart({ groupId, onCheckout }: GroupCartProps) {
  const { user } = useAuth()
  const userId = user?._id || ''
  const dispatch = useAppDispatch()

  // Reduced console logging for stability
  // console.log('GroupCart - groupId:', groupId)
  // console.log('GroupCart - userId:', userId)

  // Local state
  const [isConfirmingClear, setIsConfirmingClear] = useState(false)
  const [loadingItemIds, setLoadingItemIds] = useState<Record<string, boolean>>({})
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Redux state
  const cart = useAppSelector(selectCart)
  const totalItems = useAppSelector(selectTotalItems)
  const subtotal = useAppSelector(selectSubtotal)
  const cartError = useAppSelector(selectCartError)

  // RTK Query hooks
  const { isLoading, error, refetch } = useGetShoppingCartQuery({
    userId,
    groupId
  }, {
    skip: !userId,
    refetchOnMountOrArgChange: true
  })

  const [updateCartItem] = useUpdateCartItemMutation()
  const [removeFromCart] = useRemoveFromCartMutation()
  const [clearCart] = useClearCartMutation()

  // Show error toast when cart error occurs
  if (cartError) {
    toast.error(cartError)
    dispatch(clearCartState())
  }

  // Manual refresh function
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await refetch()
      toast.success('Cart refreshed')
    } catch (error) {
      console.error('Failed to refresh cart:', error)
      toast.error('Failed to refresh cart')
    } finally {
      setIsRefreshing(false)
    }
  }

  // Handle quantity update
  const handleUpdateQuantity = async (productId: string, currentQuantity: number, increment: boolean) => {
    if (!userId) return

    const newQuantity = increment ? currentQuantity + 1 : Math.max(1, currentQuantity - 1)

    // Set loading state for this item
    setLoadingItemIds(prev => ({ ...prev, [productId]: true }))

    try {
      console.log('Updating quantity:', { userId, productId, quantity: newQuantity, groupId })
      await updateCartItem({
        userId,
        productId,
        quantity: newQuantity,
        groupId
      }).unwrap()

      // Show success toast
      toast.success(`Quantity updated to ${newQuantity}`)
    } catch (error) {
      console.error('Failed to update quantity:', error)
      toast.error('Failed to update quantity. Please try again.')
      dispatch(setError('Failed to update quantity. Please try again.'))
    } finally {
      // Clear loading state
      setLoadingItemIds(prev => ({ ...prev, [productId]: false }))
    }
  }

  // Handle remove item
  const handleRemoveItem = async (productId: string) => {
    if (!userId) return

    // Set loading state for this item
    setLoadingItemIds(prev => ({ ...prev, [productId]: true }))

    try {
      console.log('Removing item with productId:', productId)
      await removeFromCart({
        userId,
        productId,
        groupId
      }).unwrap()

      // Show success toast
      toast.success('Item removed from cart')
    } catch (error) {
      console.error('Failed to remove item:', error)
      toast.error('Failed to remove item. Please try again.')
      dispatch(setError('Failed to remove item. Please try again.'))
    } finally {
      // Clear loading state
      setLoadingItemIds(prev => ({ ...prev, [productId]: false }))
    }
  }

  // Handle clear cart
  const handleClearCart = async () => {
    if (!userId) return

    try {
      await clearCart({
        userId,
        groupId
      }).unwrap()

      // Show success toast
      toast.success('Cart cleared successfully')
      setIsConfirmingClear(false)
    } catch (error) {
      console.error('Failed to clear cart:', error)
      toast.error('Failed to clear cart. Please try again.')
      dispatch(setError('Failed to clear cart. Please try again.'))
    }
  }

  // Handle retry
  const handleRetry = () => {
    dispatch(clearCartState())
    refetch()
  }

  // Show error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertTitle>Error loading cart</AlertTitle>
        <AlertDescription>
          {error.toString()}
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={handleRetry}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 w-48 bg-gray-200 animate-pulse rounded"></div>
        {[1, 2, 3].map(i => (
          <div key={i} className="flex items-center gap-4 py-4 border-b">
            <div className="h-20 w-20 bg-gray-200 animate-pulse rounded-md"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
              <div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
            </div>
            <div className="h-8 w-24 bg-gray-200 animate-pulse rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  // Show empty cart
  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <ShoppingBag className="h-16 w-16 text-[#2A7C6C] mb-4" />
        <h2 className="text-2xl font-semibold text-[#2A7C6C] mb-2">Your cart is empty</h2>
        <p className="text-gray-500 mb-6">Looks like you haven't added any products to your cart yet.</p>
        <Button
          className="bg-[#2A7C6C] hover:bg-[#236358] text-white"
          onClick={() => window.location.href = `/groups/${groupId}/products`}
        >
          View All Products
        </Button>
      </div>
    )
  }

  // Reduced debug logging for stability
  // console.log('Cart items:', cart.items)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-900">Your Cart ({totalItems} items)</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="flex items-center gap-1"
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <div className="space-y-6 divide-y">
        {cart.items.map((item) => {
          // Debug information for each item
          console.log('Rendering item:', item)

          const isItemLoading = loadingItemIds[item.productId] || false

          return (
            <div key={item.productId} className="flex items-center gap-4 py-4">
              <div className="h-20 w-20 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                <Image
                  src={item.image ? `/api/images/${item.image}` : '/placeholder.svg'}
                  alt={item.name || 'Product'}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    (e.target as HTMLImageElement).src = '/placeholder.svg';
                  }}
                />
              </div>

              <div className="flex-grow">
                <h3 className="font-medium text-gray-900">{item.name || 'Unknown Product'}</h3>
                <p className="text-sm text-gray-500">
                  R {typeof item.price === 'number' ? item.price.toFixed(2) : '0.00'}
                </p>
              </div>

              <div className="flex items-center gap-3">
                <Button
                  size="icon"
                  variant="outline"
                  className="h-8 w-8 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white"
                  onClick={() => handleUpdateQuantity(item.productId, item.quantity, false)}
                  disabled={isItemLoading || item.quantity <= 1}
                >
                  <Minus className="h-4 w-4" />
                </Button>

                <span className="w-8 text-center font-medium">{item.quantity}</span>

                <Button
                  size="icon"
                  variant="outline"
                  className="h-8 w-8 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white"
                  onClick={() => handleUpdateQuantity(item.productId, item.quantity, true)}
                  disabled={isItemLoading}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="text-right w-24">
                <p className="font-medium">
                  R {typeof item.subtotal === 'number' ? item.subtotal.toFixed(2) :
                     ((item.price || 0) * item.quantity).toFixed(2)}
                </p>
              </div>

              <Button
                size="icon"
                variant="ghost"
                className="h-8 w-8 text-gray-400 hover:bg-red-50 hover:text-red-500"
                onClick={() => handleRemoveItem(item.productId)}
                disabled={isItemLoading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )
        })}
      </div>

      <div className="border-t pt-4">
        <div className="flex justify-between text-lg font-semibold">
          <span>Subtotal</span>
          <span>R {subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between items-center mt-4">
          <p className="text-sm text-gray-500">Shipping and taxes calculated at checkout</p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
              onClick={() => setIsConfirmingClear(true)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Cart
            </Button>

            {onCheckout && (
              <Button
                size="sm"
                className="bg-[#2A7C6C] hover:bg-[#236358] text-white"
                onClick={onCheckout}
                disabled={totalItems === 0}
              >
                Checkout
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Clear Cart Confirmation Dialog */}
      <Dialog open={isConfirmingClear} onOpenChange={setIsConfirmingClear}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clear Shopping Cart</DialogTitle>
            <DialogDescription>
              Are you sure you want to clear your shopping cart? This action cannot be undone.
              All items in your cart will be removed.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setIsConfirmingClear(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleClearCart}
            >
              Clear Cart
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
