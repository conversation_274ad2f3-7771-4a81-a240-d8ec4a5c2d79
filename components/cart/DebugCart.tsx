'use client'

import React from 'react'
import { useAppSelector } from '@/lib/redux/hooks'
import { selectCart } from '@/lib/redux/features/cart/cartSlice'
import { useGetShoppingCartQuery } from '@/lib/redux/features/cart/cartApiSlice'
import { useAuth } from '@/context/AuthContext'

interface DebugCartProps {
  groupId: string
}

/**
 * DebugCart component for development purposes only
 * Shows raw cart data for debugging
 */
export function DebugCart({ groupId }: DebugCartProps) {
  const { user } = useAuth()
  const userId = user?._id || ''
  const cart = useAppSelector(selectCart)
  
  // Also fetch directly from API for comparison
  const { data: apiCart } = useGetShoppingCartQuery({
    userId,
    groupId
  }, {
    skip: !userId
  })
  
  if (process.env.NODE_ENV !== 'development') {
    return null
  }
  
  return (
    <div className="text-xs font-mono bg-gray-100 p-2 rounded overflow-auto max-h-64">
      <div className="mb-2">
        <strong>Redux Cart State:</strong>
        <pre>{JSON.stringify(cart, null, 2)}</pre>
      </div>
      
      <div>
        <strong>API Cart Response:</strong>
        <pre>{JSON.stringify(apiCart, null, 2)}</pre>
      </div>
    </div>
  )
}

export default DebugCart
