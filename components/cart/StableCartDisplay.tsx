'use client';

import { useState, memo, useCallback } from 'react';
import Image from 'next/image';
import { Minus, Plus, X, ShoppingBag, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCartContext } from '@/context/CartContext';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { motion, AnimatePresence } from 'framer-motion';

interface StableCartDisplayProps {
  groupId: string;
}

export const StableCartDisplay = memo(function StableCartDisplay({ groupId }: StableCartDisplayProps) {
  const [isConfirmingClear, setIsConfirmingClear] = useState(false);
  const [updatingItems, setUpdatingItems] = useState<Set<string>>(new Set());

  // Use stable cart data from context
  const {
    stableCartData,
    isLoading,
    updateCartItem,
    removeFromCart,
    clearCart,
    error
  } = useCartContext();

  const handleUpdateQuantity = useCallback(async (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) return;

    setUpdatingItems(prev => new Set(prev).add(productId));
    
    try {
      await updateCartItem(productId, newQuantity);
    } catch (error) {
      console.error('Failed to update cart item:', error);
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  }, [updateCartItem]);

  const handleRemoveItem = useCallback(async (productId: string) => {
    setUpdatingItems(prev => new Set(prev).add(productId));
    
    try {
      await removeFromCart(productId);
    } catch (error) {
      console.error('Failed to remove cart item:', error);
    } finally {
      setUpdatingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  }, [removeFromCart]);

  const handleClearCart = useCallback(async () => {
    try {
      await clearCart();
      setIsConfirmingClear(false);
    } catch (error) {
      console.error('Failed to clear cart:', error);
    }
  }, [clearCart]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  if (stableCartData.totalItems === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <ShoppingBag className="mx-auto h-16 w-16 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
        <p className="text-gray-500 mb-6">Start adding products to see them here</p>
        <Button>
          Continue Shopping
        </Button>
      </motion.div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Cart Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">
          Shopping Cart ({stableCartData.totalItems} items)
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsConfirmingClear(true)}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Clear Cart
        </Button>
      </div>

      {/* Cart Items */}
      <div className="space-y-4">
        <AnimatePresence>
          {stableCartData.items.map((item) => {
            const isUpdating = updatingItems.has(item.productId);
            
            return (
              <motion.div
                key={item._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`bg-white rounded-lg border p-4 ${isUpdating ? 'opacity-50' : ''}`}
              >
                <div className="flex items-center space-x-4">
                  {/* Product Image */}
                  <div className="flex-shrink-0">
                    <Image
                      src={item.image || '/placeholder-product.jpg'}
                      alt={item.name}
                      width={80}
                      height={80}
                      className="rounded-md object-cover"
                    />
                  </div>

                  {/* Product Details */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {item.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      R {item.price.toFixed(2)} each
                    </p>
                    <p className="text-lg font-semibold text-green-600">
                      R {item.subtotal.toFixed(2)}
                    </p>
                  </div>

                  {/* Quantity Controls */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUpdateQuantity(item.productId, item.quantity - 1)}
                      disabled={isUpdating || item.quantity <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    
                    <span className="w-12 text-center font-medium">
                      {item.quantity}
                    </span>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUpdateQuantity(item.productId, item.quantity + 1)}
                      disabled={isUpdating}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Remove Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveItem(item.productId)}
                    disabled={isUpdating}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Cart Summary */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="space-y-2">
          <div className="flex justify-between text-base">
            <span>Subtotal ({stableCartData.totalItems} items)</span>
            <span>R {stableCartData.subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-base">
            <span>Shipping</span>
            <span className="text-green-600">Free</span>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between text-lg font-semibold">
              <span>Total</span>
              <span>R {stableCartData.subtotal.toFixed(2)}</span>
            </div>
          </div>
        </div>

        <Button 
          className="w-full mt-4 bg-green-600 hover:bg-green-700"
          size="lg"
        >
          Proceed to Checkout
        </Button>
      </div>

      {/* Clear Cart Confirmation Dialog */}
      <Dialog open={isConfirmingClear} onOpenChange={setIsConfirmingClear}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clear Cart</DialogTitle>
          </DialogHeader>
          <p className="text-gray-600">
            Are you sure you want to remove all items from your cart? This action cannot be undone.
          </p>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmingClear(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleClearCart}
            >
              Clear Cart
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
});
