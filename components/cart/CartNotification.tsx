'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, ShoppingCart, X } from 'lucide-react'
import { useAppSelector, useAppDispatch } from '@/lib/redux/hooks'
import { selectIsNotificationVisible, selectTotalItems, hideNotification } from '@/lib/redux/features/cart/cartSlice'
import { Button } from '@/components/ui/button'
import { CartOverlay } from './CartOverlay'

/**
 * CartNotification component that shows a toast notification when items are added to cart
 * Uses framer-motion for smooth animations
 */
export function CartNotification() {
  // We don't need currentGroupId anymore as we're using the CartOverlay component
  // which handles the group ID internally
  const [isCartOpen, setIsCartOpen] = useState(false)
  const isVisible = useAppSelector(selectIsNotificationVisible)
  const totalItems = useAppSelector(selectTotalItems)
  const dispatch = useAppDispatch()

  // We no longer need to get the group ID from localStorage
  // as the CartOverlay component handles this internally

  // Auto-hide notification after 5 seconds
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        dispatch(hideNotification())
      }, 5000)

      return () => clearTimeout(timer)
    }
  }, [isVisible, dispatch])

  return (
    <>
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50 flex items-center gap-3 border border-green-100"
          >
            <div className="bg-green-100 rounded-full p-2">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Item added to cart</h3>
              <p className="text-sm text-gray-500">
                You now have {totalItems} {totalItems === 1 ? 'item' : 'items'} in your cart
              </p>
            </div>
            <div className="flex items-center gap-2 ml-2">
              <Button
                variant="outline"
                size="sm"
                className="text-purple-600 border-purple-200 hover:bg-purple-50"
                onClick={() => {
                  dispatch(hideNotification())
                  // Open cart overlay instead of navigating
                  setIsCartOpen(true)
                }}
              >
                <ShoppingCart className="h-4 w-4 mr-1" />
                View Cart
              </Button>
              <button
                onClick={() => dispatch(hideNotification())}
                className="text-gray-400 hover:text-gray-600 p-1"
                aria-label="Close notification"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cart Overlay */}
      <CartOverlay
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
      />
    </>
  )
}
