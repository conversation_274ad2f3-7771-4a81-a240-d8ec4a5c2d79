'use client'

import { ShoppingCart } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useAppSelector } from '@/lib/redux/hooks'
import { selectTotalItems, selectIsNotificationVisible } from '@/lib/redux/features/cart/cartSlice'
import { useState } from 'react'
import { CartOverlay } from './CartOverlay'
import { useAuth } from '@/context/AuthContext'
import { useRouter } from 'next/navigation'

export function CartIconWithOverlay() {
  const totalItems = useAppSelector(selectTotalItems)
  const isNotificationVisible = useAppSelector(selectIsNotificationVisible)
  const [isCartOpen, setIsCartOpen] = useState(false)
  const { user } = useAuth()
  const router = useRouter()

  // Handle cart button click
  const handleCartClick = () => {
    if (!user) {
      // Redirect to login if user is not logged in
      router.push('/login')
      return
    }

    // Open the cart overlay if user is logged in
    setIsCartOpen(true)
  }

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="relative"
        aria-label="Shopping cart"
        onClick={handleCartClick}
      >
        <ShoppingCart className="h-5 w-5" />
        {totalItems > 0 && (
          <span
            className={cn(
              "absolute right-0 top-0 h-4 w-4 rounded-full bg-purple-600 text-[10px] font-medium text-white flex items-center justify-center",
              isNotificationVisible && "animate-pulse"
            )}
          >
            {totalItems}
          </span>
        )}
      </Button>

      {/* Cart Overlay */}
      <CartOverlay
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
      />
    </>
  )
}
