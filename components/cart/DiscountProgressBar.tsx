'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useCalculateGroupDiscountQuery } from '@/lib/redux/features/cart/cartApiSlice'
import { useAppSelector } from '@/lib/redux/hooks'
import { selectSubtotal } from '@/lib/redux/features/cart/cartSlice'
import { formatCurrency } from '@/lib/utils'
import { Sparkles, Target, Award } from 'lucide-react'
import { Progress } from '@/components/ui/progress'

interface DiscountProgressBarProps {
  groupId: string
}

/**
 * DiscountProgressBar component that shows progress towards discount milestones
 */
export function DiscountProgressBar({ groupId }: DiscountProgressBarProps) {
  const subtotal = useAppSelector(selectSubtotal)
  const { data: discountInfo, isLoading } = useCalculateGroupDiscountQuery(groupId, {
    skip: subtotal <= 0
  })

  // Define discount tiers
  const discountTiers = [
    { threshold: 5000, discountPercentage: 10 },
    { threshold: 10000, discountPercentage: 15 },
    { threshold: 20000, discountPercentage: 20 }
  ]

  // Find the next tier to reach
  const currentTier = discountInfo?.appliedTier
  const nextTier = discountTiers.find(tier => tier.threshold > subtotal)

  // Calculate progress percentage
  const calculateProgress = () => {
    if (!nextTier) return 100 // Already at max tier

    const prevThreshold = currentTier?.threshold || 0
    const progress = ((subtotal - prevThreshold) / (nextTier.threshold - prevThreshold)) * 100
    return Math.min(Math.max(progress, 0), 100)
  }

  // If no items in cart, don't show anything
  if (subtotal <= 0) return null

  // If loading, show a skeleton
  if (isLoading) {
    return (
      <div className="mt-4 p-4 bg-gray-50 rounded-lg animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-2 bg-gray-200 rounded w-full mb-4"></div>
        <div className="h-6 bg-gray-200 rounded w-full"></div>
      </div>
    )
  }

  return (
    <div className="mt-4 p-4 bg-purple-50 rounded-lg border border-purple-100">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-purple-500" />
          <h3 className="font-medium text-purple-700">Group Discount Progress</h3>
        </div>
        {currentTier && (
          <div className="text-sm font-medium text-purple-700">
            {currentTier.discountPercentage}% discount applied!
          </div>
        )}
      </div>

      <Progress value={calculateProgress()} className="h-3 mb-3" />

      <div className="flex justify-between text-xs text-gray-500">
        {discountTiers.map((tier, _index) => (
          <div
            key={tier.threshold}
            className={`flex flex-col items-center ${subtotal >= tier.threshold ? 'text-purple-700 font-medium' : ''}`}
          >
            <div className="flex items-center gap-1">
              {subtotal >= tier.threshold ? (
                <Award className="h-3 w-3" />
              ) : (
                <Target className="h-3 w-3" />
              )}
              <span>{tier.discountPercentage}%</span>
            </div>
            <span>{formatCurrency(tier.threshold)}</span>
          </div>
        ))}
      </div>

      {nextTier && (
        <motion.div
          className="mt-2 text-center text-sm text-purple-700"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          Add {formatCurrency(nextTier.threshold - subtotal)} more to get {nextTier.discountPercentage}% discount!
        </motion.div>
      )}

      {discountInfo && discountInfo.discountAmount > 0 && (
        <motion.div
          className="mt-2 text-right text-sm font-medium text-green-600"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          You're saving {formatCurrency(discountInfo.discountAmount)}!
        </motion.div>
      )}
    </div>
  )
}

export default DiscountProgressBar
