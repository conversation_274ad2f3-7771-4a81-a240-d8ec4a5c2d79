"use client"

import React, { useEffect } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import type { StepComponentProps } from "./StepComponentProps"

const singleSavingPricingPlans = [
  {
    id: "basic",
    title: "Basic",
    price: 350,
    description: "Perfect for individuals starting their stockvel journey. Access to basic group buying features and community zones.",
    benefits: ["Access to store page", "Basic group buying features", "Community zones access"],
  },
  {
    id: "standard",
    title: "Standard",
    price: 500,
    description: "Ideal for active stockvel members. Enhanced group buying power and priority delivery scheduling.",
    benefits: ["Enhanced group buying power", "Priority delivery scheduling", "All Basic features"],
  },
  {
    id: "premium",
    title: "Premium",
    price: 1000,
    description: "Our ultimate stockvel experience. Maximum discounts, exclusive zones, and premium delivery options.",
    benefits: ["Maximum discounts", "Exclusive zones", "Premium delivery options", "All Standard features"],
  },
]

interface SingleSavingPricingPlansProps extends StepComponentProps {
  initialPlan?: string;
}

export function SingleSavingPricingPlans({ updateFormData, onNext, onPrev, initialPlan }: SingleSavingPricingPlansProps) {
  const [selectedPlan, setSelectedPlan] = React.useState<string | undefined>(initialPlan)

  const handlePlanSelect = React.useCallback((planId: string) => {
    const plan = singleSavingPricingPlans.find(p => p.id === planId)
    if (plan) {
      setSelectedPlan(planId)
      updateFormData('selectedPlan', planId)
      updateFormData('planTitle', plan.title)
      updateFormData('planPrice', plan.price.toString())
      updateFormData('benefits', plan.benefits)
    }
  }, [updateFormData])

  useEffect(() => {
    if (initialPlan) {
      const plan = singleSavingPricingPlans.find(p => p.id === initialPlan.toLowerCase())
      if (plan) {
        handlePlanSelect(plan.id)
      }
    }
  }, [initialPlan, handlePlanSelect])

  const selectedPlanData = selectedPlan 
    ? singleSavingPricingPlans.find(p => p.id === selectedPlan)
    : undefined

  if (!selectedPlanData) {
    return null
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto px-4 py-8"
    >
      <h2 className="text-2xl font-bold mb-6 text-center">Confirm Your Saving Plan</h2>
      
      <div className="max-w-2xl mx-auto">
        <div 
          className="border rounded-lg p-8 bg-white shadow-lg"
        >
          <div className="text-center mb-6">
            <h3 className="text-2xl font-bold text-primary mb-2">{selectedPlanData.title}</h3>
            <p className="text-4xl font-bold text-gray-900">
              R{selectedPlanData.price}
              <span className="text-base font-normal text-gray-500 ml-2">/month</span>
            </p>
          </div>
          
          <div className="mb-8">
            <p className="text-gray-600 text-center text-lg mb-6">{selectedPlanData.description}</p>
            
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-semibold mb-4 text-gray-900">Plan Benefits:</h4>
              <ul className="space-y-3">
                {selectedPlanData.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-center text-gray-700">
                    <svg 
                      className="w-5 h-5 text-primary mr-3 flex-shrink-0" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24" 
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth="2" 
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          </div>
      
          <div className="flex justify-between mt-8">
            <Button 
              variant="outline" 
              onClick={onPrev}
              className="w-[120px]"
            >
              Previous
            </Button>
            <Button 
              onClick={onNext}
              className="w-[120px]"
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
