"use client";

import { useState, useEffect, use<PERSON>emo, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormItem, FormControl, FormLabel, FormMessage } from "@/components/ui/form";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext";
import { Loader2, Mail, User, Phone, Lock } from "lucide-react";
import { debounce } from "lodash";
import { isValidEmail } from "@/lib/utils";
import { userRegistrationSchema, type UserRegistrationFormType } from "@/schemas/userRegistrationSchema";
import type { StepComponentProps } from "./StepComponentProps";

export function SingleSavingUserRegistration({ updateFormData, onNext, onPrev }: StepComponentProps) {
  const { signup } = useAuth();

  // Mock checkUserByEmail function since it's not available in useGroupSavings
  const checkUserByEmail = useCallback(async (email: string) => {
    console.log("Checking user by email:", email);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    // Return null to indicate user not found
    return null as unknown as { _id: string; name: string; phone: string; email: string; } | null;
  }, []);

  const form = useForm<UserRegistrationFormType>({
    resolver: zodResolver(userRegistrationSchema),
    defaultValues: {
      email: "",
      name: "",
      phone: "",
      existingUserId: "",
    },
  });

  const [isUserKnown, setIsUserKnown] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [checkingEmail, setCheckingEmail] = useState(false);
  const [isValidEmailEntered, setIsValidEmailEntered] = useState(false);
  const [emailCheckComplete, setEmailCheckComplete] = useState(false);
  const [emailMessage, setEmailMessage] = useState("");
  const [userCreated, setUserCreated] = useState(false);

  const debouncedCheckEmail = useMemo(
    () =>
      debounce(async (email: string) => {
        setCheckingEmail(true);
        setEmailCheckComplete(false);
        try {
          const result = await checkUserByEmail(email);
          if (result) {
            setIsUserKnown(true);
            form.setValue("existingUserId", result._id);
            form.setValue("name", result.name);
            form.setValue("phone", result.phone);
            setEmailMessage(`Welcome back, ${result.name}!`);
          } else {
            setIsUserKnown(false);
            form.setValue("existingUserId", "");
            setEmailMessage("This email is available for registration.");
          }
        } catch (err) {
          console.error("Error checking user by email:", err);
          setEmailMessage("An error occurred while checking the email.");
        } finally {
          setCheckingEmail(false);
          setEmailCheckComplete(true);
        }
      }, 500),
    [checkUserByEmail, form]
  );

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "email") {
        const email = value.email as string;
        const isValid = isValidEmail(email);
        setIsValidEmailEntered(isValid);
        setEmailCheckComplete(false);

        if (isValid) {
          setEmailMessage("Checking email...");
          debouncedCheckEmail(email);
        } else if (email) {
          setEmailMessage("Please enter a valid email address.");
        } else {
          setEmailMessage("");
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, debouncedCheckEmail]);

  const showRegistrationFields = !isUserKnown && isValidEmailEntered && emailCheckComplete;

  const onSubmit = async (data: UserRegistrationFormType) => {
    setSubmitting(true);

    try {
      if (!isUserKnown) {
        if (!data.name || !data.phone || !data.password || !data.confirmPassword) {
          form.setError("root", { message: "Please fill in all registration fields" });
          setSubmitting(false);
          return;
        }

        await signup(data.name, data.email, data.phone, data.password);

        const newUser = await checkUserByEmail(data.email);
        if (newUser) {
          data.existingUserId = newUser._id;
          setUserCreated(true);
          form.setValue("name", newUser.name);
          form.setValue("phone", newUser.phone);
        }
      }

      // Update parent's state using updateFormData with the form values
      updateFormData("email", data.email);
      updateFormData("name", data.name);
      updateFormData("phone", data.phone);
      updateFormData("userId", data.existingUserId);

      onNext();
    } catch (error) {
      console.error("Failed to process user registration:", error);
      form.setError("root", { message: (error as Error).message || "An error occurred during registration." });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{isUserKnown ? "Welcome Back" : "User Registration"}</CardTitle>
        <CardDescription>
          {isUserKnown ? "Please confirm your details" : "Enter your details to create an account"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <input type="hidden" {...form.register("existingUserId")} />
            {isUserKnown && (
              <>
                <input type="hidden" {...form.register("name")} />
                <input type="hidden" {...form.register("phone")} />
              </>
            )}

            <FormItem>
              <FormLabel>Email Address</FormLabel>
              {emailMessage && (
                <p className={`text-sm ${isValidEmailEntered ? "text-green-600" : "text-red-600"}`}>{emailMessage}</p>
              )}
              <FormControl>
                <div className="flex gap-2 items-center">
                  <div className="relative w-full">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <Input
                      placeholder="Enter your email"
                      {...form.register("email")}
                      className={`pl-10 ${isValidEmailEntered ? "border-green-500" : ""}`}
                      disabled={isUserKnown}
                    />
                  </div>
                  {isValidEmailEntered && checkingEmail && <Loader2 className="h-4 w-4 animate-spin" />}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>

            {(showRegistrationFields || userCreated) && (
              <>
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <Input
                        placeholder="Your full name"
                        {...form.register("name")}
                        className="pl-10"
                        disabled={userCreated}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>

                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <Input
                        placeholder="e.g. 0712345678"
                        {...form.register("phone")}
                        className="pl-10"
                        disabled={userCreated}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>

                {!userCreated && (
                  <>
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <Input type="password" {...form.register("password")} className="pl-10" />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>

                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <Input type="password" {...form.register("confirmPassword")} className="pl-10" />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </>
                )}
              </>
            )}

            {form.formState.errors.root && <p className="text-sm text-red-600">{form.formState.errors.root.message}</p>}
            <div className="flex justify-between mt-6">
              <Button type="button" onClick={onPrev} variant="outline" className="rounded-full px-6 py-2 text-sm">
                Back
              </Button>
              <Button
                type="submit"
                disabled={submitting}
                className="rounded-full bg-[#2A7C6C] px-6 py-2 text-sm text-white hover:bg-[#236358]"
              >
                {submitting ? <Loader2 className="h-4 w-4 animate-spin" /> : "Next"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
