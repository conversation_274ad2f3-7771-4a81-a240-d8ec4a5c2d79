"use client";

import React from "react"
import { useRouter } from "next/navigation"
import type { FormData, Beneficiary } from "./StepComponentProps"
import { SingleSavingPricingPlans } from "./SingleSavingPricingPlans"
import { SingleSavingUserRegistration } from "./SingleSavingUserRegistration"
import { SingleSavingFormSummary } from "./SingleSavingFormSummary"

type Step = "pricing" | "registration" | "summary"

interface SingleSavingMultiStepFormProps {
  onClose: () => void
  initialPlan?: string
}

const initialFormData: FormData = {
  investmentType: "single_saving",
  selectedPlan: "",
  userId: "",
  email: "",
  name: "",
  phone: "",
  existingUserId: "",
  planDuration: "",
  planPrice: "",
  planTitle: "",
  joiningFee: "",
  benefits: [],
  beneficiaries: [],
}

export function SingleSavingMultiStepForm({ onClose, initialPlan }: SingleSavingMultiStepFormProps) {
  const [currentStep, setCurrentStep] = React.useState<Step>("pricing")
  const [formData, setFormData] = React.useState<FormData>(initialFormData)
  const router = useRouter()

  const updateFormData = (key: keyof FormData, value: string | string[] | Beneficiary[]) => {
    setFormData((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  const handleNext = () => {
    switch (currentStep) {
      case "pricing":
        setCurrentStep("registration")
        break
      case "registration":
        setCurrentStep("summary")
        break
      case "summary":
        // Handle form submission
        console.log("Form submitted:", formData)
        router.push("/dashboard")
        break
    }
  }

  const handlePrev = () => {
    switch (currentStep) {
      case "registration":
        setCurrentStep("pricing")
        break
      case "summary":
        setCurrentStep("registration")
        break
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case "pricing":
        return (
          <SingleSavingPricingPlans
            formData={formData}
            updateFormData={updateFormData}
            onNext={handleNext}
            onPrev={handlePrev}
            initialPlan={initialPlan}
          />
        )
      case "registration":
        return (
          <SingleSavingUserRegistration
            formData={formData}
            updateFormData={updateFormData}
            onNext={handleNext}
            onPrev={handlePrev}
          />
        )
      case "summary":
        return (
          <SingleSavingFormSummary
            formData={formData}
            onClose={onClose}
            onPrev={handlePrev}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {renderStep()}
      </div>
    </div>
  )
}
