import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import type { FormSummaryProps } from "./StepComponentProps"

export function SingleSavingFormSummary({ formData, onClose, onPrev }: FormSummaryProps) {
  const handleSubmit = async () => {
    try {
      // Here you would typically make an API call to save the form data
      console.log("Submitting form data:", formData)
      onClose()
    } catch (error) {
      console.error("Error submitting form:", error)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Review Your Saving Plan</CardTitle>
        <CardDescription>Please review your information before confirming</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="font-semibold text-lg">Plan Details</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="font-medium">Plan Type</div>
            <div>{formData.planTitle}</div>
            <div className="font-medium">Monthly Amount</div>
            <div>R{formData.planPrice}</div>
            <div className="font-medium">Joining Fee</div>
            <div>R{formData.joiningFee}</div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-semibold text-lg">Personal Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="font-medium">Full Name</div>
            <div>{formData.name}</div>
            <div className="font-medium">Email</div>
            <div>{formData.email}</div>
            <div className="font-medium">Phone Number</div>
            <div>{formData.phone}</div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-semibold text-lg">Benefits</h3>
          <ul className="list-disc list-inside text-sm space-y-2">
            {formData.benefits.map((benefit, index) => (
              <li key={index}>{benefit}</li>
            ))}
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onPrev}>
          Previous
        </Button>
        <Button onClick={handleSubmit}>
          Confirm and Submit
        </Button>
      </CardFooter>
    </Card>
  )
}
