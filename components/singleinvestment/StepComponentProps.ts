// StepComponentProps.ts for Single Investment
export interface Beneficiary {
  name: string;
  relationship: string;
  contactNumber: string;
}

export interface FormData {
  [key: string]: string | string[] | Beneficiary[];
  investmentType: string;
  selectedPlan: string;
  userId: string;
  email: string;
  name: string;
  phone: string;
  existingUserId: string;
  planDuration: string;
  planPrice: string;
  planTitle: string;
  joiningFee: string;
  benefits: string[];
  beneficiaries: Beneficiary[];
}

export interface StepComponentProps {
  formData: FormData;
  updateFormData: (key: keyof FormData, value: string | string[] | Beneficiary[]) => void;
  onNext: () => void;
  onPrev: () => void;
}

export interface FormSummaryProps {
  formData: FormData;
  onClose: () => void;
  onPrev: () => void;
}
