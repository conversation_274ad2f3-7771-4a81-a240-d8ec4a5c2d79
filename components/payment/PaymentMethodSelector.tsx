'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { 
  CreditCard, 
  Truck, 
  Shield, 
  Zap, 
  Star,
  CheckCircle,
  Info
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export type PaymentMethod = 'payfast' | 'peach' | 'cod';

interface PaymentMethodSelectorProps {
  selectedMethod?: PaymentMethod;
  onMethodSelect: (method: PaymentMethod) => void;
  amount: number;
  currency?: string;
  className?: string;
}

export function PaymentMethodSelector({
  selectedMethod,
  onMethodSelect,
  amount,
  currency = 'ZAR',
  className = ''
}: PaymentMethodSelectorProps) {
  const [hoveredMethod, setHoveredMethod] = useState<PaymentMethod | null>(null);

  const paymentMethods = [
    {
      id: 'payfast' as PaymentMethod,
      name: 'PayFast',
      description: 'Secure online payment with cards, EFT, and more',
      icon: '🍑',
      iconComponent: CreditCard,
      features: ['Credit/Debit Cards', 'Instant EFT', 'SnapScan', 'Mobicred'],
      fees: '2.9% + R2.00',
      processingTime: 'Instant',
      security: 'PCI Compliant',
      popular: true,
      pros: ['Instant payment', 'Multiple payment options', 'Established provider'],
      cons: ['Higher fees', 'Online only'],
      color: 'from-blue-500 to-purple-600',
      bgColor: 'from-blue-50 to-purple-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'peach' as PaymentMethod,
      name: 'Peach Payments',
      description: 'Modern payment platform with 20+ payment methods',
      icon: '🍑',
      iconComponent: CreditCard,
      features: ['Cards', 'Payflex BNPL', 'Capitec Pay', 'Apple/Google Pay'],
      fees: '2.4% + R1.50',
      processingTime: 'Instant',
      security: '3D Secure + PCI DSS',
      recommended: true,
      pros: ['Lower fees', 'More payment methods', 'Modern platform', 'Group payments'],
      cons: ['Newer provider'],
      color: 'from-orange-500 to-pink-600',
      bgColor: 'from-orange-50 to-pink-50',
      borderColor: 'border-orange-200'
    },
    {
      id: 'cod' as PaymentMethod,
      name: 'Cash on Delivery',
      description: 'Pay with cash when your order is delivered',
      icon: '🚚',
      iconComponent: Truck,
      features: ['No upfront payment', 'Cash payment', 'Delivery tracking'],
      fees: 'R50 delivery fee',
      processingTime: '2-5 business days',
      security: 'No card details required',
      pros: ['No online payment needed', 'Pay on delivery', 'No card required'],
      cons: ['Delivery fee', 'Longer processing', 'Limited to delivery areas'],
      color: 'from-green-500 to-blue-600',
      bgColor: 'from-green-50 to-blue-50',
      borderColor: 'border-green-200',
      maxAmount: 5000
    }
  ];

  // Filter methods based on amount for COD
  const availableMethods = paymentMethods.filter(method => {
    if (method.id === 'cod' && method.maxAmount && amount > method.maxAmount) {
      return false;
    }
    return true;
  });

  const getMethodDetails = (method: typeof paymentMethods[0]) => {
    const IconComponent = method.iconComponent;
    
    return (
      <motion.div
        key={method.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-300 ${
          selectedMethod === method.id
            ? `${method.borderColor} bg-gradient-to-r ${method.bgColor} shadow-lg`
            : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-md'
        }`}
        onClick={() => onMethodSelect(method.id)}
        onMouseEnter={() => setHoveredMethod(method.id)}
        onMouseLeave={() => setHoveredMethod(null)}
      >
        {/* Selection Indicator */}
        {selectedMethod === method.id && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute top-4 right-4 w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center"
          >
            <CheckCircle className="h-4 w-4 text-white" />
          </motion.div>
        )}

        {/* Badges */}
        <div className="flex gap-2 mb-4">
          {method.popular && (
            <Badge className="bg-blue-100 text-blue-800 text-xs">
              <Star className="h-3 w-3 mr-1" />
              Popular
            </Badge>
          )}
          {method.recommended && (
            <Badge className="bg-green-100 text-green-800 text-xs">
              <CheckCircle className="h-3 w-3 mr-1" />
              Recommended
            </Badge>
          )}
        </div>

        {/* Header */}
        <div className="flex items-center gap-4 mb-4">
          <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${method.color} flex items-center justify-center text-2xl`}>
            {method.icon}
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900">{method.name}</h3>
            <p className="text-sm text-gray-600">{method.description}</p>
          </div>
        </div>

        {/* Features */}
        <div className="space-y-3 mb-4">
          <div className="flex flex-wrap gap-1">
            {method.features.map((feature, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {feature}
              </Badge>
            ))}
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Fees:</span>
              <span className="font-medium ml-1">{method.fees}</span>
            </div>
            <div>
              <span className="text-gray-600">Processing:</span>
              <span className="font-medium ml-1">{method.processingTime}</span>
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <Shield className="h-4 w-4 text-green-600" />
            <span className="text-gray-600">{method.security}</span>
          </div>
        </div>

        {/* Pros and Cons (shown on hover or selection) */}
        <AnimatePresence>
          {(hoveredMethod === method.id || selectedMethod === method.id) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t pt-4 space-y-2"
            >
              <div>
                <h4 className="text-sm font-semibold text-green-700 mb-1">Advantages:</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  {method.pros.map((pro, index) => (
                    <li key={index} className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      {pro}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="text-sm font-semibold text-orange-700 mb-1">Considerations:</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  {method.cons.map((con, index) => (
                    <li key={index} className="flex items-center gap-1">
                      <Info className="h-3 w-3 text-orange-500" />
                      {con}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* COD Amount Limit Warning */}
        {method.id === 'cod' && method.maxAmount && amount > method.maxAmount && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-700">
              Cash on Delivery is not available for orders above R{method.maxAmount.toFixed(2)}
            </p>
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl">
            <CreditCard className="h-6 w-6" />
            Choose Payment Method
          </CardTitle>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}
            </p>
          </div>
        </CardHeader>

        <CardContent>
          <RadioGroup
            value={selectedMethod}
            onValueChange={(value) => onMethodSelect(value as PaymentMethod)}
            className="space-y-4"
          >
            {availableMethods.map((method) => (
              <div key={method.id} className="relative">
                <RadioGroupItem
                  value={method.id}
                  id={method.id}
                  className="sr-only"
                />
                <Label htmlFor={method.id} className="cursor-pointer">
                  {getMethodDetails(method)}
                </Label>
              </div>
            ))}
          </RadioGroup>

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-5 w-5 text-green-600" />
              <span className="font-semibold text-gray-900">Secure Payments</span>
            </div>
            <p className="text-sm text-gray-600">
              All payment methods are secured with bank-level encryption and comply with industry security standards.
              Your payment information is never stored on our servers.
            </p>
          </div>

          {/* Method Comparison */}
          {availableMethods.length > 1 && (
            <div className="mt-6">
              <h3 className="font-semibold text-gray-900 mb-3">Quick Comparison</h3>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Method</th>
                      <th className="text-left py-2">Fees</th>
                      <th className="text-left py-2">Processing</th>
                      <th className="text-left py-2">Best For</th>
                    </tr>
                  </thead>
                  <tbody>
                    {availableMethods.map((method) => (
                      <tr key={method.id} className="border-b">
                        <td className="py-2 font-medium">{method.name}</td>
                        <td className="py-2">{method.fees}</td>
                        <td className="py-2">{method.processingTime}</td>
                        <td className="py-2 text-gray-600">
                          {method.id === 'payfast' && 'Traditional online payments'}
                          {method.id === 'peach' && 'Modern, feature-rich payments'}
                          {method.id === 'cod' && 'No upfront payment needed'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
