"use client"

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export function PrivacySettings() {
  const [profileVisibility, setProfileVisibility] = useState(true)
  const [activityVisibility, setActivityVisibility] = useState(false)
  const [dataSharing, setDataSharing] = useState(true)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Implement privacy settings update logic here
    console.log('Updating privacy settings:', { profileVisibility, activityVisibility, dataSharing })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex items-center justify-between">
        <Label htmlFor="profile-visibility">Profile Visibility</Label>
        <Switch
          id="profile-visibility"
          checked={profileVisibility}
          onCheckedChange={setProfileVisibility}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label htmlFor="activity-visibility">Activity Visibility</Label>
        <Switch
          id="activity-visibility"
          checked={activityVisibility}
          onCheckedChange={setActivityVisibility}
        />
      </div>
      <div className="flex items-center justify-between">
        <Label htmlFor="data-sharing">Data Sharing</Label>
        <Switch
          id="data-sharing"
          checked={dataSharing}
          onCheckedChange={setDataSharing}
        />
      </div>
      <Button type="submit">Update Privacy Settings</Button>
    </form>
  )
}

