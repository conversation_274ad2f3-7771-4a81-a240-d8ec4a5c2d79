"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

export function PersonalInfoForm() {
  const [name, setName] = useState('<PERSON>')
  const [email, setEmail] = useState('<EMAIL>')
  const [phone, setPhone] = useState('+27 ************')
  const [bio, setBio] = useState('I am a member of the Stokvel community.')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Implement update logic here
    console.log('Updating personal info:', { name, email, phone, bio })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid w-full gap-1.5">
        <Label htmlFor="name">Full Name</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>
      <div className="grid w-full gap-1.5">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      <div className="grid w-full gap-1.5">
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          type="tel"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
        />
      </div>
      <div className="grid w-full gap-1.5">
        <Label htmlFor="bio">Bio</Label>
        <Textarea
          id="bio"
          value={bio}
          onChange={(e) => setBio(e.target.value)}
          rows={4}
        />
      </div>
      <Button type="submit">Update Personal Information</Button>
    </form>
  )
}

