"use client"

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"

export function NotificationSettings() {
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [smsNotifications, setSmsNotifications] = useState(false)
  const [pushNotifications, setPushNotifications] = useState(true)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Implement notification settings update logic here
    console.log('Updating notification settings:', { emailNotifications, smsNotifications, pushNotifications })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex items-center space-x-2">
        <Checkbox
          id="email-notifications"
          checked={emailNotifications}
          onCheckedChange={(checked) => setEmailNotifications(checked as boolean)}
        />
        <Label htmlFor="email-notifications">Email Notifications</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox
          id="sms-notifications"
          checked={smsNotifications}
          onCheckedChange={(checked) => setSmsNotifications(checked as boolean)}
        />
        <Label htmlFor="sms-notifications">SMS Notifications</Label>
      </div>
      <div className="flex items-center space-x-2">
        <Checkbox
          id="push-notifications"
          checked={pushNotifications}
          onCheckedChange={(checked) => setPushNotifications(checked as boolean)}
        />
        <Label htmlFor="push-notifications">Push Notifications</Label>
      </div>
      <Button type="submit">Update Notification Preferences</Button>
    </form>
  )
}

