"use client"

import { motion } from "framer-motion"
import { 
  HelpCircle, 
  Clock, 
  MessageCircle, 
  Phone,
  ArrowRight
} from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const quickFAQs = [
  {
    question: "How quickly do you respond to messages?",
    answer: "We typically respond to emails within 2-4 hours during business hours, and live chat is available for immediate assistance Monday through Friday."
  },
  {
    question: "Can I schedule a call with your team?",
    answer: "Absolutely! You can schedule a call through our contact form or by calling our direct line. We're happy to discuss your needs at a time that works for you."
  },
  {
    question: "Do you offer support in multiple languages?",
    answer: "Yes, our team can assist in English, Afrikaans, and several other South African languages. Let us know your preference when you contact us."
  },
  {
    question: "What information should I include in my message?",
    answer: "Please include your name, contact details, and a detailed description of your question or issue. The more information you provide, the better we can assist you."
  }
]

export function ContactFAQ() {
  return (
    <section className="py-20 px-4 md:px-6">
      <div className="container mx-auto max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 
            className="text-4xl md:text-5xl font-bold text-[#2F4858] mb-6"
            style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
          >
            Quick Answers
          </h2>
          <p 
            className="text-gray-600 text-lg md:text-xl max-w-2xl mx-auto"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Common questions about getting in touch with our team
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white/70 backdrop-blur-sm rounded-3xl border border-gray-200/50 shadow-xl p-8"
        >
          <Accordion type="single" collapsible className="w-full space-y-4">
            {quickFAQs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <AccordionItem 
                  value={`item-${index}`} 
                  className="bg-white/50 rounded-2xl border border-gray-200/30 shadow-sm hover:shadow-md transition-all duration-300"
                >
                  <AccordionTrigger
                    className="text-left text-lg font-bold text-[#2F4858] hover:text-[#2A7C6C] px-6 py-4 hover:no-underline transition-colors duration-300"
                    style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="h-6 w-6 bg-gradient-to-br from-[#2A7C6C] to-[#1E5A4F] rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                        <HelpCircle className="h-3 w-3 text-white" />
                      </div>
                      <span className="text-left">{faq.question}</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent
                    className="text-gray-700 text-base leading-relaxed px-6 pb-4"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  >
                    <div className="pl-9">
                      {faq.answer}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </motion.div>
            ))}
          </Accordion>

          {/* CTA to Full FAQ */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-8 text-center"
          >
            <p 
              className="text-gray-600 mb-4"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Need more detailed answers?
            </p>
            <Button
              className="bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#236358] hover:to-[#164239] text-white rounded-2xl px-8 py-3 font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Visit Full FAQ
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </motion.div>
        </motion.div>

        {/* Contact Preference Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid md:grid-cols-3 gap-6 mt-16"
        >
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200/50">
            <MessageCircle className="h-8 w-8 text-blue-600 mb-4" />
            <h3 
              className="font-bold text-gray-800 mb-2"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              Prefer Chat?
            </h3>
            <p 
              className="text-gray-600 text-sm"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Get instant answers through our live chat feature
            </p>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-teal-50 p-6 rounded-2xl border border-green-200/50">
            <Phone className="h-8 w-8 text-green-600 mb-4" />
            <h3 
              className="font-bold text-gray-800 mb-2"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              Need to Talk?
            </h3>
            <p 
              className="text-gray-600 text-sm"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Call us directly for personalized assistance
            </p>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-2xl border border-purple-200/50">
            <Clock className="h-8 w-8 text-purple-600 mb-4" />
            <h3 
              className="font-bold text-gray-800 mb-2"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              Take Your Time?
            </h3>
            <p 
              className="text-gray-600 text-sm"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Send us an email with all the details
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
