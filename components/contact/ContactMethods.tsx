"use client"

import { motion } from "framer-motion"
import {
  MessageCircle,
  Phone,
  Mail,
  Clock,
  Zap,
  Users,
  HeadphonesIcon,
  ChatBubbleIcon
} from "lucide-react"

// Custom WhatsApp icon component
const WhatsAppIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
  </svg>
)
import { Button } from "@/components/ui/button"

const contactMethods = [
  {
    icon: WhatsAppIcon,
    title: "WhatsApp Chat",
    description: "Get instant help via WhatsApp",
    action: "Start WhatsApp",
    availability: "Available now",
    gradient: "from-green-500 to-green-600",
    delay: 0.1,
    href: "https://wa.me/2727658079493?text=Hi%20Stokvel%20Team!%20I%20need%20help%20with..."
  },
  {
    icon: Phone,
    title: "Phone Support",
    description: "Speak directly with our experts",
    action: "Call Now",
    availability: "Mon-Fri, 8AM-6PM",
    gradient: "from-blue-500 to-teal-600",
    delay: 0.2,
    href: "tel:+2727658079493"
  },
  {
    icon: Mail,
    title: "Email Support",
    description: "Send us a detailed message",
    action: "Send Email",
    availability: "24/7 response",
    gradient: "from-purple-500 to-pink-600",
    delay: 0.3
  },
  {
    icon: Users,
    title: "Community Forum",
    description: "Connect with other members",
    action: "Join Discussion",
    availability: "Always active",
    gradient: "from-orange-500 to-red-600",
    delay: 0.4
  }
]

export function ContactMethods() {
  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
      {contactMethods.map((method, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: method.delay }}
          className="group relative"
        >
          <div className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
            {/* Gradient Background on Hover */}
            <div className={`absolute inset-0 bg-gradient-to-br ${method.gradient} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500`} />
            
            <div className="relative z-10">
              {/* Icon */}
              <div className={`h-16 w-16 bg-gradient-to-br ${method.gradient} rounded-2xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                <method.icon className="h-8 w-8 text-white" />
              </div>

              {/* Content */}
              <h3 
                className="text-xl md:text-2xl font-bold text-[#2F4858] mb-3 group-hover:text-[#2A7C6C] transition-colors duration-300"
                style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
              >
                {method.title}
              </h3>
              
              <p 
                className="text-gray-600 mb-4 leading-relaxed"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                {method.description}
              </p>

              {/* Availability */}
              <div className="flex items-center mb-6">
                <div className="h-2 w-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                <span className="text-sm text-gray-500" style={{ fontFamily: 'Avenir, sans-serif' }}>
                  {method.availability}
                </span>
              </div>

              {/* Action Button */}
              {method.href ? (
                <a href={method.href} target="_blank" rel="noopener noreferrer" className="block">
                  <Button
                    className={`w-full bg-gradient-to-r ${method.gradient} hover:opacity-90 text-white rounded-2xl py-3 font-semibold transition-all duration-300 shadow-lg hover:shadow-xl`}
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  >
                    {method.action}
                  </Button>
                </a>
              ) : (
                <Button
                  className={`w-full bg-gradient-to-r ${method.gradient} hover:opacity-90 text-white rounded-2xl py-3 font-semibold transition-all duration-300 shadow-lg hover:shadow-xl`}
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  {method.action}
                </Button>
              )}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}
