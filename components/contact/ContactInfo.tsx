"use client"

import { motion } from "framer-motion"
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  MessageCircle,
  Users,
  Star,
  Award,
  Shield
} from 'lucide-react'
import { Button } from "@/components/ui/button"

const contactDetails = [
  {
    icon: MapPin,
    label: "Office Location",
    value: "Bloemfontein, Free State, South Africa",
    action: "Get Directions",
    gradient: "from-red-500 to-pink-600"
  },
  {
    icon: Phone,
    label: "Phone Support",
    value: "+27 27 658 079 493",
    action: "Call Now",
    href: "tel:+2727658079493",
    gradient: "from-green-500 to-teal-600"
  },
  {
    icon: Mail,
    label: "Email Support",
    value: "<EMAIL>",
    action: "Send Email",
    href: "mailto:<EMAIL>",
    gradient: "from-blue-500 to-indigo-600"
  },
  {
    icon: Clock,
    label: "Support Hours",
    value: "Mon-Fri: 8AM-6PM SAST",
    action: "View Schedule",
    gradient: "from-purple-500 to-violet-600"
  }
]

const stats = [
  { icon: Users, value: "10,000+", label: "Happy Members" },
  { icon: Star, value: "4.9/5", label: "Customer Rating" },
  { icon: Award, value: "99%", label: "Issue Resolution" },
  { icon: Shield, value: "24/7", label: "Security Monitoring" }
]

export function ContactInfo() {
  return (
    <motion.div
      initial={{ opacity: 0, x: -30 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-8"
    >
      {/* Header */}
      <div>
        <h2
          className="text-3xl md:text-4xl font-bold text-[#2F4858] mb-4"
          style={{
            fontFamily: 'ClashDisplay-Variable, sans-serif',
            letterSpacing: '-0.02em'
          }}
        >
          Ready to Connect?
        </h2>
        <p
          className="text-gray-600 text-lg leading-relaxed"
          style={{ fontFamily: 'Avenir, sans-serif' }}
        >
          We're here to help you succeed in your community shopping journey.
          Choose the best way to reach us and we'll get back to you quickly.
        </p>
      </div>

      {/* Contact Methods */}
      <div className="space-y-6">
        {contactDetails.map((detail, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="group bg-white/70 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`h-12 w-12 bg-gradient-to-br ${detail.gradient} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  <detail.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3
                    className="font-semibold text-[#2F4858] mb-1"
                    style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                  >
                    {detail.label}
                  </h3>
                  {detail.href ? (
                    <a
                      href={detail.href}
                      className="text-gray-600 hover:text-[#2A7C6C] transition-colors font-medium"
                      style={{ fontFamily: 'Avenir, sans-serif' }}
                    >
                      {detail.value}
                    </a>
                  ) : (
                    <p
                      className="text-gray-600"
                      style={{ fontFamily: 'Avenir, sans-serif' }}
                    >
                      {detail.value}
                    </p>
                  )}
                </div>
              </div>

              <Button
                size="sm"
                variant="outline"
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 border-gray-300 hover:border-[#2A7C6C] hover:text-[#2A7C6C]"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                {detail.action}
              </Button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Stats Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-gradient-to-r from-[#2A7C6C]/10 to-[#7FDBCA]/10 p-8 rounded-3xl border border-[#2A7C6C]/20"
      >
        <h3
          className="text-xl font-bold text-[#2F4858] mb-6 text-center"
          style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
        >
          Why Our Members Love Our Support
        </h3>

        <div className="grid grid-cols-2 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
              className="text-center"
            >
              <div className="h-10 w-10 bg-[#2A7C6C] rounded-xl flex items-center justify-center mx-auto mb-3">
                <stat.icon className="h-5 w-5 text-white" />
              </div>
              <div
                className="text-2xl font-bold text-[#2A7C6C] mb-1"
                style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
              >
                {stat.value}
              </div>
              <div
                className="text-sm text-gray-600"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Quick Action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="text-center"
      >
        <p
          className="text-gray-600 mb-4"
          style={{ fontFamily: 'Avenir, sans-serif' }}
        >
          Need immediate assistance?
        </p>
        <a
          href="https://wa.me/2727658079493?text=Hi%20Stokvel%20Team!%20I%20need%20help%20with..."
          target="_blank"
          rel="noopener noreferrer"
        >
          <Button
            className="bg-gradient-to-r from-[#25D366] to-[#128C7E] hover:from-[#20BA5A] hover:to-[#0F7A6B] text-white rounded-2xl px-8 py-3 font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            Chat on WhatsApp
          </Button>
        </a>
      </motion.div>
    </motion.div>
  )
}

