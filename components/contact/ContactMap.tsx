"use client"

import { motion } from "framer-motion"
import { MapPin, Navigation, Clock, Phone } from "lucide-react"
import { Button } from "@/components/ui/button"

export function ContactMap() {
  return (
    <section className="py-20 px-4 md:px-6 bg-gradient-to-r from-[#2A7C6C]/5 to-[#7FDBCA]/5">
      <div className="container mx-auto max-w-6xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 
            className="text-4xl md:text-5xl font-bold text-[#2F4858] mb-6"
            style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
          >
            Visit Our Office
          </h2>
          <p 
            className="text-gray-600 text-lg md:text-xl max-w-2xl mx-auto"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Located in the heart of Bloemfontein, we're always happy to meet in person
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Map Placeholder */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative"
          >
            <div className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl border border-gray-200/50 shadow-xl">
              {/* Interactive Map Placeholder */}
              <div className="relative h-80 bg-gradient-to-br from-[#2A7C6C]/20 to-[#7FDBCA]/20 rounded-2xl overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="h-16 w-16 text-[#2A7C6C] mx-auto mb-4" />
                    <h3 
                      className="text-xl font-bold text-[#2F4858] mb-2"
                      style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                    >
                      Bloemfontein Office
                    </h3>
                    <p className="text-gray-600" style={{ fontFamily: 'Avenir, sans-serif' }}>
                      Interactive map coming soon
                    </p>
                  </div>
                </div>
                
                {/* Floating location pin */}
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                >
                  <div className="h-8 w-8 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                    <MapPin className="h-5 w-5 text-white" />
                  </div>
                </motion.div>
              </div>

              {/* Map Actions */}
              <div className="flex gap-4 mt-6">
                <Button
                  className="flex-1 bg-[#2A7C6C] hover:bg-[#236358] text-white rounded-2xl py-3"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  <Navigation className="h-4 w-4 mr-2" />
                  Get Directions
                </Button>
                <a href="tel:+2727658079493">
                  <Button
                    variant="outline"
                    className="flex-1 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white rounded-2xl py-3"
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    Call Office
                  </Button>
                </a>
              </div>
            </div>
          </motion.div>

          {/* Office Information */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-8"
          >
            <div className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl border border-gray-200/50 shadow-lg">
              <h3 
                className="text-2xl font-bold text-[#2F4858] mb-6"
                style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
              >
                Office Details
              </h3>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="h-12 w-12 bg-gradient-to-br from-[#2A7C6C] to-[#1E5A4F] rounded-xl flex items-center justify-center flex-shrink-0">
                    <MapPin className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 
                      className="font-semibold text-[#2F4858] mb-1"
                      style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                    >
                      Address
                    </h4>
                    <p className="text-gray-600" style={{ fontFamily: 'Avenir, sans-serif' }}>
                      Bloemfontein, Free State<br />
                      South Africa
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 
                      className="font-semibold text-[#2F4858] mb-1"
                      style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                    >
                      Office Hours
                    </h4>
                    <div className="text-gray-600 space-y-1" style={{ fontFamily: 'Avenir, sans-serif' }}>
                      <p>Monday - Friday: 8:00 AM - 6:00 PM</p>
                      <p>Saturday: 9:00 AM - 2:00 PM</p>
                      <p>Sunday: Closed</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Phone className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 
                      className="font-semibold text-[#2F4858] mb-1"
                      style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                    >
                      Direct Line
                    </h4>
                    <a
                      href="tel:+2727658079493"
                      className="text-[#2A7C6C] hover:text-[#236358] transition-colors font-semibold"
                      style={{ fontFamily: 'Avenir, sans-serif' }}
                    >
                      +27 27 658 079 493
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Visit Notice */}
            <div className="bg-gradient-to-r from-[#2A7C6C]/10 to-[#7FDBCA]/10 p-6 rounded-2xl border border-[#2A7C6C]/20">
              <h4 
                className="font-semibold text-[#2F4858] mb-2"
                style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
              >
                Planning a Visit?
              </h4>
              <p 
                className="text-gray-600 text-sm"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                We recommend calling ahead to ensure someone is available to assist you. 
                Walk-ins are welcome during business hours!
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
