'use client'

import { useState } from 'react'
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Send,
  User,
  Mail,
  MessageSquare,
  Tag,
  CheckCircle,
  Loader2
} from 'lucide-react'

export function ContactForm() {
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    subject: '',
    category: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    console.log('Form submitted:', formState)
    setIsSubmitting(false)
    setIsSubmitted(true)

    // Reset form after 3 seconds
    setTimeout(() => {
      setFormState({ name: '', email: '', subject: '', category: '', message: '' })
      setIsSubmitted(false)
    }, 3000)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormState({
      ...formState,
      [e.target.name]: e.target.value
    })
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormState({
      ...formState,
      [name]: value
    })
  }

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl border border-gray-200/50 shadow-xl text-center"
      >
        <div className="h-16 w-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle className="h-8 w-8 text-white" />
        </div>
        <h3
          className="text-2xl font-bold text-[#2F4858] mb-4"
          style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
        >
          Message Sent Successfully!
        </h3>
        <p
          className="text-gray-600"
          style={{ fontFamily: 'Avenir, sans-serif' }}
        >
          Thank you for reaching out. We'll get back to you within 2-4 hours during business hours.
        </p>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 30 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-white/70 backdrop-blur-sm p-8 rounded-3xl border border-gray-200/50 shadow-xl"
    >
      <div className="mb-8">
        <h2
          className="text-3xl md:text-4xl font-bold text-[#2F4858] mb-4"
          style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
        >
          Send us a Message
        </h2>
        <p
          className="text-gray-600"
          style={{ fontFamily: 'Avenir, sans-serif' }}
        >
          Fill out the form below and we'll get back to you as soon as possible.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
        >
          <label
            htmlFor="name"
            className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
            style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
          >
            <User className="h-4 w-4 mr-2 text-[#2A7C6C]" />
            Full Name
          </label>
          <Input
            type="text"
            id="name"
            name="name"
            value={formState.name}
            onChange={handleChange}
            required
            placeholder="Enter your full name"
            className="w-full h-12 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          />
        </motion.div>

        {/* Email Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <label
            htmlFor="email"
            className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
            style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
          >
            <Mail className="h-4 w-4 mr-2 text-[#2A7C6C]" />
            Email Address
          </label>
          <Input
            type="email"
            id="email"
            name="email"
            value={formState.email}
            onChange={handleChange}
            required
            placeholder="<EMAIL>"
            className="w-full h-12 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          />
        </motion.div>

        {/* Subject and Category Row */}
        <div className="grid md:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <label
              htmlFor="subject"
              className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              <MessageSquare className="h-4 w-4 mr-2 text-[#2A7C6C]" />
              Subject
            </label>
            <Input
              type="text"
              id="subject"
              name="subject"
              value={formState.subject}
              onChange={handleChange}
              required
              placeholder="Brief subject line"
              className="w-full h-12 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.4 }}
          >
            <label
              className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              <Tag className="h-4 w-4 mr-2 text-[#2A7C6C]" />
              Category
            </label>
            <Select value={formState.category} onValueChange={(value) => handleSelectChange('category', value)}>
              <SelectTrigger className="w-full h-12 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] bg-white/50">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Inquiry</SelectItem>
                <SelectItem value="support">Technical Support</SelectItem>
                <SelectItem value="billing">Billing Question</SelectItem>
                <SelectItem value="partnership">Partnership</SelectItem>
                <SelectItem value="feedback">Feedback</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </motion.div>
        </div>

        {/* Message Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.5 }}
        >
          <label
            htmlFor="message"
            className="flex items-center text-sm font-semibold text-[#2F4858] mb-3"
            style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
          >
            <MessageSquare className="h-4 w-4 mr-2 text-[#2A7C6C]" />
            Message
          </label>
          <Textarea
            id="message"
            name="message"
            value={formState.message}
            onChange={handleChange}
            required
            placeholder="Tell us how we can help you..."
            className="w-full h-32 rounded-2xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/50 text-gray-800 resize-none"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          />
        </motion.div>

        {/* Submit Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full h-14 bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#236358] hover:to-[#164239] text-white rounded-2xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                Sending Message...
              </>
            ) : (
              <>
                <Send className="h-5 w-5 mr-2" />
                Send Message
              </>
            )}
          </Button>
        </motion.div>

        {/* Form Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.7 }}
          className="text-center pt-4"
        >
          <p
            className="text-sm text-gray-500"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            We typically respond within 2-4 hours during business hours
          </p>
        </motion.div>
      </form>
    </motion.div>
  )
}

