import { ShoppingBag, Users, TrendingUp } from 'lucide-react'

const impactStats = [
  {
    icon: ShoppingBag,
    value: "500,000+",
    label: "Groceries Delivered"
  },
  {
    icon: Users,
    value: "100,000+",
    label: "Active Members"
  },
  {
    icon: TrendingUp,
    value: "30%",
    label: "Average Savings"
  }
]

export function OurImpact() {
  return (
    <section className="py-24 px-4 md:px-6 bg-[#F8FAF9]">
      <div className="container mx-auto">
        <h2 
          className="text-[#2F4858] text-4xl md:text-5xl lg:text-6xl mb-12 text-center"
          style={{ 
            fontFamily: 'ClashDisplay-Variable, sans-serif',
            letterSpacing: '-0.02em' 
          }}
        >
          Our Impact
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          {impactStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="h-16 w-16 bg-[#2A7C6C]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <stat.icon className="h-8 w-8 text-[#2A7C6C]" />
              </div>
              <h3 
                className="text-[#2F4858] text-3xl md:text-4xl mb-2"
                style={{ 
                  fontFamily: 'ClashDisplay-Variable, sans-serif',
                  letterSpacing: '-0.02em' 
                }}
              >
                {stat.value}
              </h3>
              <p 
                className="text-gray-600 text-lg"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                {stat.label}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

