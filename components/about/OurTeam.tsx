import Image from "next/image"

const teamMembers = [
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Founder & CEO",
    image: "/team/thabo-molefe.jpg"
  },
  {
    name: "<PERSON><PERSON> Ndlovu",
    role: "COO",
    image: "/team/lerato-ndlovu.jpg"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "CTO",
    image: "/team/sipho-nkosi.jpg"
  },
  {
    name: "<PERSON><PERSON><PERSON> D<PERSON>",
    role: "Head of Community Relations",
    image: "/team/nomsa-dlamini.jpg"
  }
]

export function OurTeam() {
  return (
    <section className="py-24 px-4 md:px-6">
      <div className="container mx-auto">
        <h2 
          className="text-[#2F4858] text-4xl md:text-5xl lg:text-6xl mb-12 text-center"
          style={{ 
            fontFamily: 'ClashDisplay-Variable, sans-serif',
            letterSpacing: '-0.02em' 
          }}
        >
          Meet Our Team
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <div key={index} className="text-center">
              <div className="relative w-48 h-48 mx-auto mb-4">
                <Image
                  src={member.image || "/placeholder.svg"}
                  alt={member.name}
                  fill
                  className="object-cover rounded-full"
                />
              </div>
              <h3 
                className="text-[#2F4858] text-xl mb-1"
                style={{ 
                  fontFamily: 'ClashDisplay-Variable, sans-serif',
                  letterSpacing: '-0.02em' 
                }}
              >
                {member.name}
              </h3>
              <p 
                className="text-gray-600"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                {member.role}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

