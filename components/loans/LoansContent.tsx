

// "use client"

// import { LoanHeroSlider } from "./LoanHeroSlider"
// import { Check } from "lucide-react"
// import Image from "next/image"

// export function LoansContent() {
//   return (
//     <div>
//       <LoanHeroSlider />
//       <div className="bg-white py-16">
//         <div className="container mx-auto px-4">
//           <div className="flex flex-col md:flex-row items-center justify-between">
//             <div className="md:w-1/2 mb-8 md:mb-0">
//               <Image
//                 src="/headers/bank.png"
//                 alt="Stokvel Loan Benefits"
//                 width={600}
//                 height={400}
//                 className="rounded-lg shadow-lg object-cover"
//               />
//             </div>
//             <div className="md:w-1/2 md:pl-12">
//               <h2
//                 className="mb-6 text-3xl font-bold text-[#2F4858]"
//                 style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
//               >
//                 Benefits of our Interest-Free Loans
//               </h2>
//               <ul className="space-y-4">
//                 {[
//                   "No interest charges",
//                   "Flexible repayment terms",
//                   "Quick approval process",
//                   "Available to all active Stokvel members",
//                 ].map((benefit, index) => (
//                   <li key={index} className="flex items-center">
//                     <div className="flex-shrink-0 w-6 h-6 rounded-full bg-[#2A7C6C] flex items-center justify-center mr-3">
//                       <Check className="w-4 h-4 text-white" />
//                     </div>
//                     <span className="text-lg text-gray-700" style={{ fontFamily: "Avenir, sans-serif" }}>
//                       {benefit}
//                     </span>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   )
// }




"use client"

import { LoanHeroSlider } from "./LoanHeroSlider"
import { HowItWorks } from "./HowItWorks"
import { Check } from "lucide-react"
import Image from "next/image"

export function LoansContent() {
  return (
    <div>
      <LoanHeroSlider />
      <div className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <Image
                src="/headers/slider7.png"
                alt="Stokvel Loan Benefits"
                width={600}
                height={400}
                className="rounded-lg shadow-lg object-cover"
              />
            </div>
            <div className="md:w-1/2 md:pl-12">
              <h2
                className="mb-6 text-3xl font-bold text-[#2F4858]"
                style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
              >
                Benefits of our Interest-Free Loans
              </h2>
              <ul className="space-y-4">
                {[
                  "No interest charges",
                  "Flexible repayment terms",
                  "Quick approval process",
                  "Available to all active Stokvel members",
                ].map((benefit, index) => (
                  <li key={index} className="flex items-center">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-[#2A7C6C] flex items-center justify-center mr-3">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-lg text-gray-700" style={{ fontFamily: "Avenir, sans-serif" }}>
                      {benefit}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
      <HowItWorks />
    </div>
  )
}

