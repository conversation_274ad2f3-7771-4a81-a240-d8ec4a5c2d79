"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { LoansApplicationModal } from "../modals/LoansApplicationModal"
import { motion, AnimatePresence } from "framer-motion"

const slides = [
  {
    id: 1,
    title: "Interest-Free Loans\nfor Your Needs",
    subtitle:
      "Access quick and easy loans without the burden of interest. Our Stokvel community supports your financial growth.",
    image: "/headers/slider5.png",
    number: "01",
  },
  {
    id: 2,
    title: "Flexible Repayment\nTailored for You",
    subtitle: "Enjoy flexible repayment terms that suit your financial situation. We're here to help you succeed.",
    image: "/headers/slider7.png",
    number: "02",
  },
  {
    id: 3,
    title: "Quick Approval\nFast Support",
    subtitle:
      "Get the funds you need when you need them. Our quick approval process ensures timely financial assistance.",
    image: "/headers/slider3.png",
    number: "03",
  },
]

export function LoanHeroSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [])

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
  }, [])

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }, [])

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }, [])

  return (
    <section className="relative min-h-[80vh] overflow-hidden bg-white">
      <div className="relative h-[80vh] w-full">
        <AnimatePresence initial={false}>
          {slides.map(
            (slide, index) =>
              index === currentSlide && (
                <motion.div
                  key={slide.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                  className="absolute inset-0"
                >
                  <Image
                    src={slide.image || "/placeholder.svg"}
                    alt={`Loan Slide ${index + 1}`}
                    layout="fill"
                    objectFit="cover"
                    priority={index === 0}
                  />
                  <div className="absolute inset-0 bg-white bg-opacity-70" />
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                    className="absolute right-0 top-0 select-none text-[20rem] font-bold text-gray-100 opacity-50"
                  >
                    {slide.number}
                  </motion.div>

                  <div className="container relative mx-auto h-full px-4 md:px-6">
                    <div className="flex h-full items-center">
                      <div className="max-w-2xl">
                        <motion.h1
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.3, duration: 0.5 }}
                          className="mb-8 max-w-3xl text-4xl font-normal text-[#2F4858] md:text-6xl lg:text-7xl"
                          style={{
                            fontFamily: "ClashDisplay-Variable, sans-serif",
                            letterSpacing: "-0.02em",
                            lineHeight: "1.1",
                          }}
                        >
                          {slide.title}
                        </motion.h1>
                        <motion.p
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.4, duration: 0.5 }}
                          className="text-muted-foreground mb-8 max-w-lg text-lg"
                          style={{ fontFamily: "Avenir, sans-serif" }}
                        >
                          {slide.subtitle}
                        </motion.p>

                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.5, duration: 0.5 }}
                        >
                          <Button
                            className="rounded-full bg-[#2A7C6C] px-8 py-6 text-lg text-white hover:bg-[#236358]"
                            style={{ fontFamily: "Avenir, sans-serif" }}
                            onClick={() => setIsModalOpen(true)}
                          >
                            Apply for a Loan
                          </Button>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ),
          )}
        </AnimatePresence>
      </div>

      {/* Pagination Dots */}
      <div className="absolute bottom-8 left-1/2 flex -translate-x-1/2 gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`h-2 w-2 rounded-full transition-all ${
              index === currentSlide ? "w-8 bg-[#2A7C6C]" : "bg-gray-300 hover:bg-gray-400"
            }`}
            aria-label={`Go to loan slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 p-2 text-[#2F4858] hover:text-[#2A7C6C]"
        aria-label="Previous loan slide"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 p-2 text-[#2F4858] hover:text-[#2A7C6C]"
        aria-label="Next loan slide"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Modal */}
      <LoansApplicationModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </section>
  )
}

