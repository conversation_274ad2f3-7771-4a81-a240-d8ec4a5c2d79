// import { Check } from "lucide-react"

// const steps = [
//   {
//     title: "Become a Member",
//     description: "Register for a Stokvel account to join our community.",
//     cta: "Register Now",
//   },
//   {
//     title: "Choose a Subscription",
//     description: "Select a subscription plan of at least R1000 from our offerings.",
//     cta: "View Plans",
//   },
//   {
//     title: "Maintain Membership",
//     description: "Consistently meet your subscription payments for at least 9 months.",
//     cta: "Learn More",
//   },
//   {
//     title: "Qualify for a Loan",
//     description: "Become eligible for a loan up to 5 times your total investment.",
//     cta: "Apply for a Loan",
//   },
// ]

// export function HowItWorks() {
//   return (
//     <section className="bg-gray-50 py-16">
//       <div className="container mx-auto px-4">
//         <h2
//           className="text-3xl font-bold text-[#2F4858] mb-12 text-center"
//           style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
//         >
//           How It Works
//         </h2>
//         <div className="relative">
//           {/* Timeline */}
//           <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-[#2A7C6C] h-full"></div>

//           {/* Steps */}
//           {steps.map((step, index) => (
//             <div
//               key={index}
//               className={`relative mb-12 ${index % 2 === 0 ? "md:ml-auto md:pl-8" : "md:mr-auto md:pr-8"} md:w-1/2`}
//             >
//               <div className="bg-white p-6 rounded-lg shadow-lg">
//                 <div className="flex items-center mb-4">
//                   <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#2A7C6C] flex items-center justify-center mr-3">
//                     <Check className="w-5 h-5 text-white" />
//                   </div>
//                   <h3
//                     className="text-xl font-semibold text-[#2F4858]"
//                     style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
//                   >
//                     {step.title}
//                   </h3>
//                 </div>
//                 <p className="text-gray-600 mb-4" style={{ fontFamily: "Avenir, sans-serif" }}>
//                   {step.description}
//                 </p>
//                 <button
//                   className="text-[#2A7C6C] font-semibold hover:underline"
//                   style={{ fontFamily: "Avenir, sans-serif" }}
//                 >
//                   {step.cta}
//                 </button>
//               </div>
//               {/* Timeline dot */}
//               <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-4 h-4 rounded-full bg-[#2A7C6C] border-4 border-white"></div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </section>
//   )
// }



import { UserPlus, CreditCard, Calendar, PiggyBank } from "lucide-react"

const steps = [
  {
    title: "Become a Member",
    description: "Register for a Stokvel account to join our community.",
    cta: "Register Now",
    icon: UserPlus,
  },
  {
    title: "Choose a Subscription",
    description: "Select a subscription plan of at least R1000 from our offerings.",
    cta: "View Plans",
    icon: CreditCard,
  },
  {
    title: "Maintain Membership",
    description: "Consistently meet your subscription payments for at least 9 months.",
    cta: "Learn More",
    icon: Calendar,
  },
  {
    title: "Qualify for a Loan",
    description: "Become eligible for a loan up to 5 times your total investment.",
    cta: "Apply for a Loan",
    icon: PiggyBank,
  },
]

export function HowItWorks() {
  return (
    <section className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        <h2
          className="text-3xl font-bold text-[#2F4858] mb-12 text-center"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          How It Works
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#2A7C6C] flex items-center justify-center mb-4">
                <step.icon className="w-8 h-8 text-white" />
              </div>
              <h3
                className="text-xl font-semibold text-[#2F4858] mb-2"
                style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
              >
                {step.title}
              </h3>
              <p className="text-gray-600 mb-4" style={{ fontFamily: "Avenir, sans-serif" }}>
                {step.description}
              </p>
              <button
                className="mt-auto text-[#2A7C6C] font-semibold hover:underline"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                {step.cta}
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

