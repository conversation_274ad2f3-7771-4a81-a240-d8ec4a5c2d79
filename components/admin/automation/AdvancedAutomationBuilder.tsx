"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Trash2,
  Save,
  Play,
  Pause,
  Settings,
  Zap,
  Clock,
  Mail,
  Bell,
  Database,
  Webhook,
  Calendar,
  Filter,
  ArrowRight,
  Copy,
  Edit,
  Eye,
  MoreHorizontal,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

// Types for automation builder
interface AutomationRule {
  id?: string;
  name: string;
  description: string;
  trigger: AutomationTrigger;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  isActive: boolean;
  priority: number;
}

interface AutomationTrigger {
  type: 'schedule' | 'event' | 'threshold' | 'manual';
  config: Record<string, unknown>;
}

interface AutomationCondition {
  id: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in_range';
  value: unknown;
  logicalOperator?: 'AND' | 'OR';
}

interface AutomationAction {
  id: string;
  type: 'email' | 'notification' | 'update_record' | 'create_task' | 'api_call' | 'report_generation';
  config: Record<string, unknown>;
}

export function AdvancedAutomationBuilder() {
  const [automationRule, setAutomationRule] = useState<AutomationRule>({
    name: '',
    description: '',
    trigger: {
      type: 'event',
      config: {}
    },
    conditions: [],
    actions: [],
    isActive: true,
    priority: 1
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);

  // Available trigger types
  const triggerTypes = [
    { value: 'schedule', label: 'Schedule', icon: Clock, description: 'Run at specific times' },
    { value: 'event', label: 'Event', icon: Zap, description: 'Trigger on system events' },
    { value: 'threshold', label: 'Threshold', icon: Filter, description: 'Trigger when metrics cross thresholds' },
    { value: 'manual', label: 'Manual', icon: Play, description: 'Run manually when needed' }
  ];

  // Available action types
  const actionTypes = [
    { value: 'email', label: 'Send Email', icon: Mail, description: 'Send email notifications' },
    { value: 'notification', label: 'Push Notification', icon: Bell, description: 'Send push notifications' },
    { value: 'update_record', label: 'Update Record', icon: Database, description: 'Update database records' },
    { value: 'create_task', label: 'Create Task', icon: CheckCircle, description: 'Create tasks or reminders' },
    { value: 'api_call', label: 'API Call', icon: Webhook, description: 'Call external APIs' },
    { value: 'report_generation', label: 'Generate Report', icon: Calendar, description: 'Generate and send reports' }
  ];

  // Available fields for conditions
  const availableFields = [
    { value: 'revenue', label: 'Revenue', type: 'number' },
    { value: 'order_count', label: 'Order Count', type: 'number' },
    { value: 'customer_count', label: 'Customer Count', type: 'number' },
    { value: 'inventory_level', label: 'Inventory Level', type: 'number' },
    { value: 'churn_rate', label: 'Churn Rate', type: 'number' },
    { value: 'conversion_rate', label: 'Conversion Rate', type: 'number' },
    { value: 'customer_segment', label: 'Customer Segment', type: 'string' },
    { value: 'product_category', label: 'Product Category', type: 'string' },
    { value: 'order_status', label: 'Order Status', type: 'string' },
    { value: 'time_of_day', label: 'Time of Day', type: 'time' },
    { value: 'day_of_week', label: 'Day of Week', type: 'string' }
  ];

  // Available operators
  const operators = [
    { value: 'equals', label: 'Equals', types: ['string', 'number', 'time'] },
    { value: 'not_equals', label: 'Not Equals', types: ['string', 'number', 'time'] },
    { value: 'greater_than', label: 'Greater Than', types: ['number', 'time'] },
    { value: 'less_than', label: 'Less Than', types: ['number', 'time'] },
    { value: 'contains', label: 'Contains', types: ['string'] },
    { value: 'in_range', label: 'In Range', types: ['number', 'time'] }
  ];

  // Add condition
  const addCondition = () => {
    const newCondition: AutomationCondition = {
      id: `condition_${Date.now()}`,
      field: 'revenue',
      operator: 'greater_than',
      value: 0,
      logicalOperator: automationRule.conditions.length > 0 ? 'AND' : undefined
    };

    setAutomationRule(prev => ({
      ...prev,
      conditions: [...prev.conditions, newCondition]
    }));
  };

  // Remove condition
  const removeCondition = (id: string) => {
    setAutomationRule(prev => ({
      ...prev,
      conditions: prev.conditions.filter(c => c.id !== id)
    }));
  };

  // Update condition
  const updateCondition = (id: string, updates: Partial<AutomationCondition>) => {
    setAutomationRule(prev => ({
      ...prev,
      conditions: prev.conditions.map(c =>
        c.id === id ? { ...c, ...updates } : c
      )
    }));
  };

  // Add action
  const addAction = () => {
    const newAction: AutomationAction = {
      id: `action_${Date.now()}`,
      type: 'email',
      config: {}
    };

    setAutomationRule(prev => ({
      ...prev,
      actions: [...prev.actions, newAction]
    }));
  };

  // Remove action
  const removeAction = (id: string) => {
    setAutomationRule(prev => ({
      ...prev,
      actions: prev.actions.filter(a => a.id !== id)
    }));
  };

  // Update action
  const updateAction = (id: string, updates: Partial<AutomationAction>) => {
    setAutomationRule(prev => ({
      ...prev,
      actions: prev.actions.map(a =>
        a.id === id ? { ...a, ...updates } : a
      )
    }));
  };

  // Save automation rule
  const saveAutomationRule = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');

      const response = await fetch('/api/admin/automation/rules', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(automationRule)
      });

      if (response.ok) {
        const result = await response.json();
        setAutomationRule(prev => ({ ...prev, id: result.data.id }));
        // Show success message
      }
    } catch (error) {
      console.error('Error saving automation rule:', error);
    } finally {
      setSaving(false);
    }
  };

  // Test automation rule
  const testAutomationRule = async () => {
    setTesting(true);
    try {
      const token = localStorage.getItem('token');

      const response = await fetch('/api/admin/automation/test', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ rule: automationRule })
      });

      if (response.ok) {
        const result = await response.json();
        // Show test results
        console.log('Test results:', result);
      }
    } catch (error) {
      console.error('Error testing automation rule:', error);
    } finally {
      setTesting(false);
    }
  };

  // Get field type
  const getFieldType = (fieldValue: string): string => {
    const field = availableFields.find(f => f.value === fieldValue);
    return field?.type || 'string';
  };

  // Get available operators for field type
  const getAvailableOperators = (fieldType: string) => {
    return operators.filter(op => op.types.includes(fieldType));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center">
            <Settings className="h-8 w-8 mr-3 text-blue-600" />
            Automation Builder
          </h1>
          <p className="text-muted-foreground">
            Create intelligent workflows with triggers, conditions, and actions
          </p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={testAutomationRule} disabled={testing}>
            <Play className="h-4 w-4 mr-2" />
            {testing ? 'Testing...' : 'Test Rule'}
          </Button>
          <Button onClick={saveAutomationRule} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Rule'}
          </Button>
        </div>
      </div>

      {/* Automation Builder Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="trigger">Trigger</TabsTrigger>
          <TabsTrigger value="conditions">Conditions</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Automation Rule Configuration</CardTitle>
              <CardDescription>Basic information about your automation rule</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Rule Name</Label>
                  <Input
                    id="name"
                    value={automationRule.name}
                    onChange={(e) => setAutomationRule(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter rule name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select
                    value={automationRule.priority.toString()}
                    onValueChange={(value) => setAutomationRule(prev => ({ ...prev, priority: parseInt(value) }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">High (1)</SelectItem>
                      <SelectItem value="2">Medium (2)</SelectItem>
                      <SelectItem value="3">Low (3)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={automationRule.description}
                  onChange={(e) => setAutomationRule(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what this automation rule does"
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={automationRule.isActive}
                  onCheckedChange={(checked) => setAutomationRule(prev => ({ ...prev, isActive: checked }))}
                />
                <Label htmlFor="active">Enable this automation rule</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trigger Tab */}
        <TabsContent value="trigger" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Trigger Configuration</CardTitle>
              <CardDescription>Define when this automation should run</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {triggerTypes.map((trigger) => {
                  const Icon = trigger.icon;
                  const isSelected = automationRule.trigger.type === trigger.value;

                  return (
                    <Card
                      key={trigger.value}
                      className={`cursor-pointer transition-colors ${
                        isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                      }`}
                      onClick={() => setAutomationRule(prev => ({
                        ...prev,
                        trigger: { type: trigger.value as any, config: {} }
                      }))}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <Icon className={`h-6 w-6 ${isSelected ? 'text-primary' : 'text-muted-foreground'}`} />
                          <div>
                            <h3 className="font-medium">{trigger.label}</h3>
                            <p className="text-sm text-muted-foreground">{trigger.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Trigger-specific configuration */}
              {automationRule.trigger.type === 'schedule' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Schedule Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Frequency</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select frequency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Time</Label>
                        <Input type="time" />
                      </div>

                      <div className="space-y-2">
                        <Label>Timezone</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select timezone" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Africa/Johannesburg">South Africa (SAST)</SelectItem>
                            <SelectItem value="UTC">UTC</SelectItem>
                            <SelectItem value="America/New_York">Eastern Time</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {automationRule.trigger.type === 'event' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Event Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Event Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select event type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="order_created">Order Created</SelectItem>
                          <SelectItem value="customer_registered">Customer Registered</SelectItem>
                          <SelectItem value="payment_completed">Payment Completed</SelectItem>
                          <SelectItem value="inventory_low">Inventory Low</SelectItem>
                          <SelectItem value="customer_churned">Customer Churned</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
              )}

              {automationRule.trigger.type === 'threshold' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Threshold Configuration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Metric</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select metric" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="revenue">Revenue</SelectItem>
                            <SelectItem value="order_count">Order Count</SelectItem>
                            <SelectItem value="customer_count">Customer Count</SelectItem>
                            <SelectItem value="churn_rate">Churn Rate</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Operator</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select operator" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="greater_than">Greater Than</SelectItem>
                            <SelectItem value="less_than">Less Than</SelectItem>
                            <SelectItem value="equals">Equals</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Value</Label>
                        <Input type="number" placeholder="Enter threshold value" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Conditions Tab */}
        <TabsContent value="conditions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Conditions</CardTitle>
                  <CardDescription>Define when this automation should execute</CardDescription>
                </div>
                <Button onClick={addCondition}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Condition
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {automationRule.conditions.map((condition, index) => (
                  <Card key={condition.id} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Condition {index + 1}</h4>
                      <div className="flex items-center gap-2">
                        {index > 0 && (
                          <Select
                            value={condition.logicalOperator || 'AND'}
                            onValueChange={(value) => updateCondition(condition.id, { logicalOperator: value as 'AND' | 'OR' })}
                          >
                            <SelectTrigger className="w-20">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="AND">AND</SelectItem>
                              <SelectItem value="OR">OR</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCondition(condition.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label>Field</Label>
                        <Select
                          value={condition.field}
                          onValueChange={(value) => updateCondition(condition.id, { field: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {availableFields.map(field => (
                              <SelectItem key={field.value} value={field.value}>
                                {field.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Operator</Label>
                        <Select
                          value={condition.operator}
                          onValueChange={(value) => updateCondition(condition.id, { operator: value as any })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getAvailableOperators(getFieldType(condition.field)).map(operator => (
                              <SelectItem key={operator.value} value={operator.value}>
                                {operator.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Value</Label>
                        {getFieldType(condition.field) === 'number' ? (
                          <Input
                            type="number"
                            value={condition.value as string}
                            onChange={(e) => updateCondition(condition.id, { value: parseFloat(e.target.value) || 0 })}
                            placeholder="Enter value"
                          />
                        ) : getFieldType(condition.field) === 'time' ? (
                          <Input
                            type="time"
                            value={condition.value as string}
                            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
                          />
                        ) : (
                          <Input
                            value={condition.value as string}
                            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
                            placeholder="Enter value"
                          />
                        )}
                      </div>

                      <div className="flex items-end">
                        <Badge variant="outline" className="mb-2">
                          {getFieldType(condition.field)}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))}

                {automationRule.conditions.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Filter className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No conditions added yet. Click "Add Condition" to get started.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Actions Tab */}
        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Actions</CardTitle>
                  <CardDescription>Define what happens when conditions are met</CardDescription>
                </div>
                <Button onClick={addAction}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Action
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {automationRule.actions.map((action, index) => {
                  const actionType = actionTypes.find(t => t.value === action.type);
                  const Icon = actionType?.icon || Zap;

                  return (
                    <Card key={action.id} className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <Icon className="h-5 w-5 text-primary" />
                          <h4 className="font-medium">Action {index + 1}: {actionType?.label}</h4>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAction(action.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label>Action Type</Label>
                          <Select
                            value={action.type}
                            onValueChange={(value) => updateAction(action.id, { type: value as any, config: {} })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {actionTypes.map(type => {
                                const TypeIcon = type.icon;
                                return (
                                  <SelectItem key={type.value} value={type.value}>
                                    <div className="flex items-center">
                                      <TypeIcon className="h-4 w-4 mr-2" />
                                      {type.label}
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Action-specific configuration */}
                        {action.type === 'email' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Recipients</Label>
                              <Input
                                placeholder="<EMAIL>, <EMAIL>"
                                value={(action.config.recipients as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, recipients: e.target.value }
                                })}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Subject</Label>
                              <Input
                                placeholder="Email subject"
                                value={(action.config.subject as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, subject: e.target.value }
                                })}
                              />
                            </div>
                            <div className="space-y-2 md:col-span-2">
                              <Label>Message</Label>
                              <Textarea
                                placeholder="Email message content"
                                value={(action.config.message as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, message: e.target.value }
                                })}
                                rows={3}
                              />
                            </div>
                          </div>
                        )}

                        {action.type === 'notification' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Title</Label>
                              <Input
                                placeholder="Notification title"
                                value={(action.config.title as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, title: e.target.value }
                                })}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Priority</Label>
                              <Select
                                value={(action.config.priority as string) || 'normal'}
                                onValueChange={(value) => updateAction(action.id, {
                                  config: { ...action.config, priority: value }
                                })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="low">Low</SelectItem>
                                  <SelectItem value="normal">Normal</SelectItem>
                                  <SelectItem value="high">High</SelectItem>
                                  <SelectItem value="urgent">Urgent</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2 md:col-span-2">
                              <Label>Message</Label>
                              <Textarea
                                placeholder="Notification message"
                                value={(action.config.message as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, message: e.target.value }
                                })}
                                rows={2}
                              />
                            </div>
                          </div>
                        )}

                        {action.type === 'api_call' && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>URL</Label>
                                <Input
                                  placeholder="https://api.example.com/webhook"
                                  value={(action.config.url as string) || ''}
                                  onChange={(e) => updateAction(action.id, {
                                    config: { ...action.config, url: e.target.value }
                                  })}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Method</Label>
                                <Select
                                  value={(action.config.method as string) || 'POST'}
                                  onValueChange={(value) => updateAction(action.id, {
                                    config: { ...action.config, method: value }
                                  })}
                                >
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="GET">GET</SelectItem>
                                    <SelectItem value="POST">POST</SelectItem>
                                    <SelectItem value="PUT">PUT</SelectItem>
                                    <SelectItem value="DELETE">DELETE</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label>Headers (JSON)</Label>
                              <Textarea
                                placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'
                                value={(action.config.headers as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, headers: e.target.value }
                                })}
                                rows={2}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>Body (JSON)</Label>
                              <Textarea
                                placeholder='{"message": "Automation triggered", "data": "{{context}}"}'
                                value={(action.config.body as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, body: e.target.value }
                                })}
                                rows={3}
                              />
                            </div>
                          </div>
                        )}

                        {action.type === 'report_generation' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Report Type</Label>
                              <Select
                                value={(action.config.reportType as string) || ''}
                                onValueChange={(value) => updateAction(action.id, {
                                  config: { ...action.config, reportType: value }
                                })}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select report type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="revenue">Revenue Report</SelectItem>
                                  <SelectItem value="customers">Customer Report</SelectItem>
                                  <SelectItem value="inventory">Inventory Report</SelectItem>
                                  <SelectItem value="performance">Performance Report</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label>Format</Label>
                              <Select
                                value={(action.config.format as string) || 'pdf'}
                                onValueChange={(value) => updateAction(action.id, {
                                  config: { ...action.config, format: value }
                                })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="pdf">PDF</SelectItem>
                                  <SelectItem value="excel">Excel</SelectItem>
                                  <SelectItem value="csv">CSV</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2 md:col-span-2">
                              <Label>Recipients</Label>
                              <Input
                                placeholder="<EMAIL>, <EMAIL>"
                                value={(action.config.recipients as string) || ''}
                                onChange={(e) => updateAction(action.id, {
                                  config: { ...action.config, recipients: e.target.value }
                                })}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </Card>
                  );
                })}

                {automationRule.actions.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No actions added yet. Click "Add Action" to get started.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Rule Summary */}
      {(automationRule.name || automationRule.conditions.length > 0 || automationRule.actions.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              Rule Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Rule Name:</span>
                <span>{automationRule.name || 'Unnamed Rule'}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-medium">Trigger:</span>
                <Badge variant="outline">
                  {triggerTypes.find(t => t.value === automationRule.trigger.type)?.label || 'Not configured'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-medium">Conditions:</span>
                <span>{automationRule.conditions.length} condition(s)</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-medium">Actions:</span>
                <span>{automationRule.actions.length} action(s)</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-medium">Status:</span>
                <Badge variant={automationRule.isActive ? 'default' : 'secondary'}>
                  {automationRule.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}