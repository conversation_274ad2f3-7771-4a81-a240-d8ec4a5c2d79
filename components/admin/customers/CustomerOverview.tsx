import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, UserPlus, ShoppingCart, TrendingUp } from 'lucide-react'

const overviewItems = [
  {
    title: "Total Customers",
    value: "5,678",
    icon: Users,
    trend: "+12.5%",
    trendLabel: "from last month",
  },
  {
    title: "New Customers",
    value: "245",
    icon: UserPlus,
    trend: "+18.2%",
    trendLabel: "from last month",
  },
  {
    title: "Active Customers",
    value: "3,890",
    icon: ShoppingCart,
    trend: "+5.4%",
    trendLabel: "from last month",
  },
  {
    title: "Average Order Value",
    value: "R 567.89",
    icon: TrendingUp,
    trend: "+15.7%",
    trendLabel: "from last month",
  },
]

export function CustomerOverview() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {overviewItems.map((item, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {item.title}
            </CardTitle>
            <item.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{item.value}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500">{item.trend}</span> {item.trendLabel}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

