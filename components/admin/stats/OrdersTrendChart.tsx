"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card"
import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

const data = [
  { date: "2023-05-01", orders: 12 },
  { date: "2023-05-02", orders: 19 },
  { date: "2023-05-03", orders: 15 },
  { date: "2023-05-04", orders: 22 },
  { date: "2023-05-05", orders: 28 },
  { date: "2023-05-06", orders: 25 },
  { date: "2023-05-07", orders: 30 },
]

export function OrdersTrendChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Orders Trend</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <XAxis 
              dataKey="date" 
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value}`}
            />
            <Tooltip />
            <Line 
              type="monotone" 
              dataKey="orders" 
              stroke="#2A7C6C" 
              strokeWidth={2} 
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

