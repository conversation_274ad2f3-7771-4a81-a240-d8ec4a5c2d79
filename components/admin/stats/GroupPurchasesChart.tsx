"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, XAxis, YAxis, Tooltip, CartesianGrid } from "recharts"

const data = [
  { name: "Mamelodi Buyers", purchases: 150000 },
  { name: "Soweto Savers", purchases: 180000 },
  { name: "Tembisa Team", purchases: 120000 },
  { name: "Midrand Grocers", purchases: 200000 },
  { name: "Centurion Circle", purchases: 160000 },
].sort((a, b) => b.purchases - a.purchases) // Sort in descending order

export function GroupPurchasesChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Purchasing Groups</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data} layout="vertical" margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" horizontal={false} />
            <XAxis type="number" tickFormatter={(value) => `R ${value / 1000}k`} />
            <YAxis dataKey="name" type="category" width={100} />
            <Tooltip 
              formatter={(value) => `R ${value.toLocaleString()}`} 
              labelFormatter={(label) => `Group: ${label}`}
            />
            <Bar dataKey="purchases" fill="#2A7C6C" barSize={20} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

