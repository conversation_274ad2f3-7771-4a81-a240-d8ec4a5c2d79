import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Package, DollarSign, ShoppingCart, TrendingUp } from 'lucide-react'

const stats = [
  {
    title: "Total Products",
    value: "1,234",
    icon: Package,
    description: "+20% from last month",
    trend: "up",
  },
  {
    title: "Total Revenue",
    value: "R 123,456",
    icon: DollarSign,
    description: "+15% from last month",
    trend: "up",
  },
  {
    title: "Total Orders",
    value: "5,678",
    icon: ShoppingCart,
    description: "+10% from last month",
    trend: "up",
  },
  {
    title: "Avg. Order Value",
    value: "R 789",
    icon: TrendingUp,
    description: "+5% from last month",
    trend: "up",
  },
]

export function ProductStats() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

