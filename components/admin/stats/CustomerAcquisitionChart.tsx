"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

const data = [
  { month: "Jan", customers: 100 },
  { month: "Feb", customers: 120 },
  { month: "Mar", customers: 150 },
  { month: "Apr", customers: 180 },
  { month: "May", customers: 220 },
  { month: "Jun", customers: 270 },
]

export function CustomerAcquisitionChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer Acquisition</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <XAxis 
              dataKey="month" 
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value}`}
            />
            <Tooltip />
            <Line 
              type="monotone" 
              dataKey="customers" 
              stroke="#2A7C6C" 
              strokeWidth={2} 
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

