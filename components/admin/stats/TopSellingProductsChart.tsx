"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, XAxis, <PERSON>A<PERSON><PERSON>, Tooltip } from "recharts"

const data = [
  { name: "Maize Meal", sales: 120 },
  { name: "Cooking Oil", sales: 98 },
  { name: "Rice", sales: 86 },
  { name: "<PERSON>", sales: 75 },
  { name: "Bread", sales: 65 },
]

export function TopSellingProductsChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Selling Products</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <XAxis
              dataKey="name"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value}`}
            />
            <Tooltip />
            <Bar dataKey="sales" fill="#2A7C6C" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

