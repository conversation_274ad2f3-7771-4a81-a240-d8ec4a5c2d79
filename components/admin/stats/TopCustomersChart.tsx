"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from "recharts"

const data = [
  { name: "<PERSON>", spent: 2345.67 },
  { name: "<PERSON>", spent: 1987.54 },
  { name: "<PERSON>", spent: 1876.23 },
  { name: "<PERSON>", spent: 1654.89 },
  { name: "<PERSON>", spent: 1543.21 },
]

export function TopCustomersChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Customers by Spend</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <XAxis
              dataKey="name"
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis
              stroke="#888888"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `R ${value}`}
            />
            <Tooltip />
            <Bar dataKey="spent" fill="#2A7C6C" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

