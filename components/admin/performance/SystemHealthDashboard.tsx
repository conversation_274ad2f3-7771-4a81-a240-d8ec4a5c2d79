"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Server, 
  Database, 
  Zap, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Cpu,
  HardDrive,
  Wifi,
  RefreshCw,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';

// Types for system health data
interface SystemMetric {
  name: string;
  value: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  threshold: number;
  trend: 'up' | 'down' | 'stable';
  lastUpdated: string;
}

interface PerformanceData {
  responseTime: number;
  throughput: number;
  errorRate: number;
  uptime: number;
  activeUsers: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
}

interface SystemAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
}

export function SystemHealthDashboard() {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([]);
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [historicalData, setHistoricalData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch system health data
  const fetchSystemHealth = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/admin/performance/system', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch system health data');
      }

      const result = await response.json();
      if (result.success) {
        setSystemMetrics(result.data.metrics);
        setPerformanceData(result.data.performance);
        setAlerts(result.data.alerts);
        setHistoricalData(result.data.historical);
      }
    } catch (error) {
      console.error('Error fetching system health:', error);
      // Mock data for development
      setSystemMetrics([
        {
          name: 'Response Time',
          value: 145,
          unit: 'ms',
          status: 'healthy',
          threshold: 200,
          trend: 'stable',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'CPU Usage',
          value: 65,
          unit: '%',
          status: 'warning',
          threshold: 80,
          trend: 'up',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Memory Usage',
          value: 78,
          unit: '%',
          status: 'warning',
          threshold: 85,
          trend: 'up',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Disk Usage',
          value: 45,
          unit: '%',
          status: 'healthy',
          threshold: 90,
          trend: 'stable',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Error Rate',
          value: 0.2,
          unit: '%',
          status: 'healthy',
          threshold: 1,
          trend: 'down',
          lastUpdated: new Date().toISOString()
        },
        {
          name: 'Uptime',
          value: 99.9,
          unit: '%',
          status: 'healthy',
          threshold: 99,
          trend: 'stable',
          lastUpdated: new Date().toISOString()
        }
      ]);

      setPerformanceData({
        responseTime: 145,
        throughput: 1250,
        errorRate: 0.2,
        uptime: 99.9,
        activeUsers: 342,
        memoryUsage: 78,
        cpuUsage: 65,
        diskUsage: 45
      });

      setAlerts([
        {
          id: '1',
          type: 'warning',
          title: 'High CPU Usage',
          message: 'CPU usage has been above 60% for the last 10 minutes',
          timestamp: new Date().toISOString(),
          resolved: false
        }
      ]);

      // Mock historical data
      const mockHistorical = Array.from({ length: 24 }, (_, i) => ({
        time: `${23 - i}:00`,
        responseTime: 120 + Math.random() * 50,
        cpuUsage: 50 + Math.random() * 30,
        memoryUsage: 60 + Math.random() * 25,
        throughput: 1000 + Math.random() * 500
      }));
      setHistoricalData(mockHistorical);
    } finally {
      setLoading(false);
    }
  };

  // Refresh system health data
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSystemHealth();
    setRefreshing(false);
  };

  useEffect(() => {
    fetchSystemHealth();
    
    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchSystemHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'healthy':
        return { color: 'text-green-600 bg-green-50', icon: CheckCircle };
      case 'warning':
        return { color: 'text-yellow-600 bg-yellow-50', icon: AlertTriangle };
      case 'critical':
        return { color: 'text-red-600 bg-red-50', icon: AlertTriangle };
      default:
        return { color: 'text-gray-600 bg-gray-50', icon: Activity };
    }
  };

  // Get metric icon
  const getMetricIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'response time':
        return Clock;
      case 'cpu usage':
        return Cpu;
      case 'memory usage':
        return Server;
      case 'disk usage':
        return HardDrive;
      case 'error rate':
        return AlertTriangle;
      case 'uptime':
        return Activity;
      default:
        return Activity;
    }
  };

  // Get trend display
  const getTrendDisplay = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-green-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-300 rounded-full" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Health</h1>
          <p className="text-muted-foreground">
            Real-time system performance monitoring and alerts
          </p>
        </div>
        
        <Button 
          variant="outline" 
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <div className="space-y-2">
          {alerts.map((alert) => {
            const { color } = getStatusDisplay(alert.type);
            return (
              <div key={alert.id} className={`flex items-center p-3 rounded-lg border ${color}`}>
                <AlertTriangle className="h-5 w-5 mr-3" />
                <div className="flex-1">
                  <p className="font-medium">{alert.title}</p>
                  <p className="text-sm opacity-80">{alert.message}</p>
                </div>
                <Badge variant={alert.type === 'critical' ? 'destructive' : 'secondary'}>
                  {alert.type}
                </Badge>
              </div>
            );
          })}
        </div>
      )}

      {/* System Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {systemMetrics.map((metric, index) => {
          const { color, icon: StatusIcon } = getStatusDisplay(metric.status);
          const MetricIcon = getMetricIcon(metric.name);
          const progressValue = (metric.value / metric.threshold) * 100;
          
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
                <MetricIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metric.value.toFixed(metric.name === 'Error Rate' ? 1 : 0)}{metric.unit}
                </div>
                <Progress value={Math.min(progressValue, 100)} className="mt-2" />
                <div className="flex items-center justify-between mt-2">
                  <div className={`flex items-center px-2 py-1 rounded-full ${color}`}>
                    <StatusIcon className="h-3 w-3 mr-1" />
                    <span className="text-xs font-medium">{metric.status}</span>
                  </div>
                  {getTrendDisplay(metric.trend)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Threshold: {metric.threshold}{metric.unit}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Performance Charts */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Response Time Trend</CardTitle>
                <CardDescription>Last 24 hours</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={historicalData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="responseTime" 
                      stroke="#8884d8" 
                      strokeWidth={2}
                      name="Response Time (ms)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Throughput</CardTitle>
                <CardDescription>Requests per minute</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={historicalData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey="throughput" 
                      stroke="#82ca9d" 
                      fill="#82ca9d"
                      name="Throughput (req/min)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>CPU & Memory Usage</CardTitle>
                <CardDescription>Resource utilization over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={historicalData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="cpuUsage" 
                      stroke="#ff7300" 
                      strokeWidth={2}
                      name="CPU Usage (%)"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="memoryUsage" 
                      stroke="#387908" 
                      strokeWidth={2}
                      name="Memory Usage (%)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {performanceData && (
              <Card>
                <CardHeader>
                  <CardTitle>Current Performance</CardTitle>
                  <CardDescription>Real-time system status</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Active Users</span>
                    <span className="text-2xl font-bold">{performanceData.activeUsers}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Throughput</span>
                    <span className="text-2xl font-bold">{performanceData.throughput} req/min</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Uptime</span>
                    <span className="text-2xl font-bold">{performanceData.uptime}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Error Rate</span>
                    <span className="text-2xl font-bold">{performanceData.errorRate}%</span>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
