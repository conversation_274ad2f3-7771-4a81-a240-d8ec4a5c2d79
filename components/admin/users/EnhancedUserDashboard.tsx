"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Search, 
  Filter, 
  Download, 
  UserPlus,
  AlertTriangle,
  Star,
  Activity,
  DollarSign,
  ShoppingCart,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Eye,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';

// Types for user management
interface UserAnalytics {
  userId: string;
  email: string;
  name: string;
  role: string;
  registrationDate: Date;
  lastLoginDate?: Date;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lifetimeValue: number;
  groupsJoined: number;
  activityScore: number;
  engagementLevel: 'high' | 'medium' | 'low' | 'inactive';
  riskScore: number;
  churnProbability: number;
  behaviorSegment: string;
  acquisitionChannel: string;
}

interface UserSegment {
  id: string;
  name: string;
  description: string;
  userCount: number;
  averageValue: number;
  growthRate: number;
  characteristics: {
    averageAge: number;
    averageLifetimeValue: number;
    averageOrderValue: number;
    mostPopularCategories: string[];
    retentionRate: number;
    conversionRate: number;
  };
}

export function EnhancedUserDashboard() {
  const [users, setUsers] = useState<UserAnalytics[]>([]);
  const [segments, setSegments] = useState<UserSegment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSegment, setSelectedSegment] = useState('all');
  const [selectedUser, setSelectedUser] = useState<UserAnalytics | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch user data
  const fetchUserData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const [usersResponse, segmentsResponse] = await Promise.all([
        fetch('/api/admin/users/analytics', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('/api/admin/users/segments', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (usersResponse.ok && segmentsResponse.ok) {
        const usersData = await usersResponse.json();
        const segmentsData = await segmentsResponse.json();
        
        setUsers(usersData.data || []);
        setSegments(segmentsData.data || []);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Mock data for development
      setUsers(generateMockUsers());
      setSegments(generateMockSegments());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  // Filter users based on search and segment
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSegment = selectedSegment === 'all' || user.behaviorSegment === selectedSegment;
    return matchesSearch && matchesSegment;
  });

  // Calculate overview metrics
  const overviewMetrics = {
    totalUsers: users.length,
    activeUsers: users.filter(u => u.engagementLevel !== 'inactive').length,
    highValueUsers: users.filter(u => u.lifetimeValue > 10000).length,
    atRiskUsers: users.filter(u => u.churnProbability > 0.7).length,
    averageLifetimeValue: users.reduce((sum, u) => sum + u.lifetimeValue, 0) / users.length || 0,
    totalRevenue: users.reduce((sum, u) => sum + u.totalSpent, 0)
  };

  // Get engagement level color
  const getEngagementColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-orange-100 text-orange-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get risk level color
  const getRiskColor = (score: number) => {
    if (score > 70) return 'text-red-600';
    if (score > 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  };

  // Generate chart data for user segments
  const segmentChartData = segments.map(segment => ({
    name: segment.name,
    users: segment.userCount,
    value: segment.averageValue
  }));

  // Generate engagement distribution data
  const engagementData = [
    { name: 'High', value: users.filter(u => u.engagementLevel === 'high').length, color: '#10b981' },
    { name: 'Medium', value: users.filter(u => u.engagementLevel === 'medium').length, color: '#f59e0b' },
    { name: 'Low', value: users.filter(u => u.engagementLevel === 'low').length, color: '#f97316' },
    { name: 'Inactive', value: users.filter(u => u.engagementLevel === 'inactive').length, color: '#ef4444' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Comprehensive user analytics, segmentation, and engagement tracking
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.activeUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {((overviewMetrics.activeUsers / overviewMetrics.totalUsers) * 100).toFixed(1)}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Value</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.highValueUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              CLV > {formatCurrency(10000)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">At Risk</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{overviewMetrics.atRiskUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              High churn probability
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg CLV</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(overviewMetrics.averageLifetimeValue)}</div>
            <p className="text-xs text-muted-foreground">
              +8% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(overviewMetrics.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              From user purchases
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">User List</TabsTrigger>
          <TabsTrigger value="segments">Segments</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>User Segments</CardTitle>
                <CardDescription>Distribution of users across behavior segments</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={segmentChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="users" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Engagement Levels</CardTitle>
                <CardDescription>User engagement distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={engagementData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {engagementData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedSegment} onValueChange={setSelectedSegment}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by segment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Segments</SelectItem>
                {segments.map(segment => (
                  <SelectItem key={segment.id} value={segment.name}>
                    {segment.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>Users ({filteredUsers.length})</CardTitle>
              <CardDescription>Detailed user information and analytics</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Engagement</TableHead>
                    <TableHead>Orders</TableHead>
                    <TableHead>Lifetime Value</TableHead>
                    <TableHead>Risk Score</TableHead>
                    <TableHead>Segment</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.slice(0, 10).map((user) => (
                    <TableRow key={user.userId}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getEngagementColor(user.engagementLevel)}>
                          {user.engagementLevel}
                        </Badge>
                      </TableCell>
                      <TableCell>{user.totalOrders}</TableCell>
                      <TableCell>{formatCurrency(user.lifetimeValue)}</TableCell>
                      <TableCell>
                        <span className={getRiskColor(user.riskScore)}>
                          {user.riskScore.toFixed(0)}%
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{user.behaviorSegment}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm" onClick={() => setSelectedUser(user)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Segments Tab */}
        <TabsContent value="segments" className="space-y-4">
          <div className="grid gap-4">
            {segments.map((segment) => (
              <Card key={segment.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{segment.name}</CardTitle>
                      <CardDescription>{segment.description}</CardDescription>
                    </div>
                    <Badge variant={segment.growthRate > 0 ? 'default' : 'destructive'}>
                      {segment.growthRate > 0 ? '+' : ''}{segment.growthRate}%
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{segment.userCount.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Users</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{formatCurrency(segment.averageValue)}</div>
                      <div className="text-sm text-muted-foreground">Avg Value</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{segment.characteristics.retentionRate}%</div>
                      <div className="text-sm text-muted-foreground">Retention</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{segment.characteristics.conversionRate}%</div>
                      <div className="text-sm text-muted-foreground">Conversion</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Mock data generators
function generateMockUsers(): UserAnalytics[] {
  const segments = ['Champion', 'Loyal Customer', 'Potential Loyalist', 'New Customer', 'At Risk'];
  const engagementLevels: ('high' | 'medium' | 'low' | 'inactive')[] = ['high', 'medium', 'low', 'inactive'];
  const channels = ['organic', 'social', 'email', 'paid', 'referral'];

  return Array.from({ length: 50 }, (_, i) => ({
    userId: `user_${i + 1}`,
    email: `user${i + 1}@example.com`,
    name: `User ${i + 1}`,
    role: 'customer',
    registrationDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    lastLoginDate: Math.random() > 0.1 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) : undefined,
    totalOrders: Math.floor(Math.random() * 20),
    totalSpent: Math.random() * 15000,
    averageOrderValue: 500 + Math.random() * 2000,
    lifetimeValue: 1000 + Math.random() * 20000,
    groupsJoined: Math.floor(Math.random() * 10),
    activityScore: Math.random() * 100,
    engagementLevel: engagementLevels[Math.floor(Math.random() * engagementLevels.length)],
    riskScore: Math.random() * 100,
    churnProbability: Math.random(),
    behaviorSegment: segments[Math.floor(Math.random() * segments.length)],
    acquisitionChannel: channels[Math.floor(Math.random() * channels.length)]
  }));
}

function generateMockSegments(): UserSegment[] {
  return [
    {
      id: 'champions',
      name: 'Champions',
      description: 'High-value customers with frequent purchases and high engagement',
      userCount: 150,
      averageValue: 15000,
      growthRate: 12,
      characteristics: {
        averageAge: 35,
        averageLifetimeValue: 15000,
        averageOrderValue: 2500,
        mostPopularCategories: ['Electronics', 'Home'],
        retentionRate: 95,
        conversionRate: 35
      }
    },
    {
      id: 'loyal',
      name: 'Loyal Customers',
      description: 'Regular customers with consistent purchase behavior',
      userCount: 280,
      averageValue: 8000,
      growthRate: 8,
      characteristics: {
        averageAge: 38,
        averageLifetimeValue: 8000,
        averageOrderValue: 1500,
        mostPopularCategories: ['Clothing', 'Electronics'],
        retentionRate: 85,
        conversionRate: 25
      }
    },
    {
      id: 'potential',
      name: 'Potential Loyalists',
      description: 'Recent customers showing promise for loyalty',
      userCount: 320,
      averageValue: 3500,
      growthRate: 15,
      characteristics: {
        averageAge: 32,
        averageLifetimeValue: 3500,
        averageOrderValue: 800,
        mostPopularCategories: ['Books', 'Clothing'],
        retentionRate: 65,
        conversionRate: 18
      }
    },
    {
      id: 'at-risk',
      name: 'At Risk',
      description: 'Customers showing signs of potential churn',
      userCount: 89,
      averageValue: 2000,
      growthRate: -12,
      characteristics: {
        averageAge: 42,
        averageLifetimeValue: 2000,
        averageOrderValue: 600,
        mostPopularCategories: ['Home', 'Books'],
        retentionRate: 35,
        conversionRate: 8
      }
    }
  ];
}
