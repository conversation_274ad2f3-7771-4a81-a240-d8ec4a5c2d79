import React from "react"
import { useFormContext } from "react-hook-form"
import Image from "next/image"
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice"

export function PreviewStep() {
  const { watch } = useFormContext()
  const { data: categories = [] } = useGetCategoriesQuery()
  const values = watch()

  const getCategoryName = (categoryId: string) => {
    const category = categories.find((cat) => cat._id === categoryId)
    return category ? category.name : "Unknown Category"
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Product Preview</h3>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="font-semibold">Name:</p>
          <p>{values.name}</p>
        </div>
        <div>
          <p className="font-semibold">Description:</p>
          <p>{values.description}</p>
        </div>
        <div>
          <p className="font-semibold">Price:</p>
          <p>${values.price}</p>
        </div>
        <div>
          <p className="font-semibold">Category:</p>
          <p>{getCategoryName(values.category)}</p>
        </div>
        <div>
          <p className="font-semibold">Stock:</p>
          <p>{values.stock}</p>
        </div>
      </div>
      {values.image && (
        <div>
          <p className="font-semibold">Image Preview:</p>
          <Image
            src={URL.createObjectURL(values.image)}
            alt={values.name || "Product preview"}
            width={300}
            height={300}
            className="max-w-full h-auto max-h-64 mt-2 rounded-lg object-cover"
            unoptimized
          />
        </div>
      )}
    </div>
  )
}
