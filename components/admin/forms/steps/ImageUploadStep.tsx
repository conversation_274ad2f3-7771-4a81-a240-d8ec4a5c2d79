import React, { useCallback } from "react"
import { useFormContext } from "react-hook-form"
import { useDropzone } from "react-dropzone"
import Image from "next/image"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"

export function ImageUploadStep() {
  const { control, setValue, watch } = useFormContext()
  const image = watch("image")

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      setValue("image", acceptedFiles[0], { shouldValidate: true })
    },
    [setValue],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/jpeg": [],
      "image/png": [],
      "image/webp": [],
    },
    maxSize: 5000000,
    multiple: false,
  })

  return (
    <FormField
      control={control}
      name="image"
      render={() => (
        <FormItem>
          <FormLabel>Product Image</FormLabel>
          <FormControl>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer ${
                isDragActive ? "border-primary" : "border-gray-300"
              }`}
            >
              <input {...getInputProps()} />
              {isDragActive ? (
                <p>Drop the image here ...</p>
              ) : (
                <p>Drag &lsquo;n&lsquo; drop an image here, or click to select one</p>
              )}
            </div>
          </FormControl>
          {image && (
            <div className="mt-4">
              <Image
                src={URL.createObjectURL(image)}
                alt="Preview"
                width={200}
                height={200}
                className="rounded-lg object-cover"
                unoptimized
              />
            </div>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
