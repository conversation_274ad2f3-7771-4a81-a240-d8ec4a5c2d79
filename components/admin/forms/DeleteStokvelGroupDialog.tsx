"use client"

import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useGroups } from "@/lib/redux/hooks/useGroups"
import { StokvelGroup } from "@/lib/frontendGroupUtilities"

interface DeleteStokvelGroupDialogProps {
  group: StokvelGroup
  onClose: () => void
}

export function DeleteStokvelGroupDialog({ group, onClose }: DeleteStokvelGroupDialogProps) {
  const [open, setOpen] = useState(true)
  const { removeGroup } = useGroups()

  const handleDelete = async () => {
    try {
      await removeGroup(group._id)
      setOpen(false)
      onClose()
    } catch (error) {
      console.error("Failed to delete group:", error)
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the
            Stokvel group {group.name} and remove all associated data.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

