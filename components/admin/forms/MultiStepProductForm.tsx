// "use client"

// import React, { useState } from "react"
// import { useForm, FormProvider } from "react-hook-form"
// import { z } from "zod"
// import { zodResolver } from "@hookform/resolvers/zod"
// import { Button } from "@/components/ui/button"
// import { useCreateProduct } from "@/lib/frontendProductUtilities"
// import { BasicInfoStep } from "./steps/BasicInfoStep" 
// import { ImageUploadStep } from "./steps/ImageUploadStep"
// import { PreviewStep } from "./steps/PreviewStep" 

// const productSchema = z.object({
//   name: z.string().min(2, "Name must be at least 2 characters."),
//   description: z.string().min(10, "Description must be at least 10 characters."),
//   price: z.number().positive("Price must be a positive number."),
//   category: z.string().nonempty("Category is required."),
//   stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
//   image: z
//     .instanceof(File)
//     .refine((file) => file.size <= 5000000, `Max image size is 5MB.`)
//     .refine(
//       (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
//       "Only .jpg, .png, and .webp formats are supported.",
//     ),
// })

// type ProductFormType = z.infer<typeof productSchema>

// export function MultiStepProductForm({ onSuccess }: { onSuccess?: () => void }) {
//   const [step, setStep] = useState(0)
//   const createProductMutation = useCreateProduct()
//   const methods = useForm<ProductFormType>({
//     resolver: zodResolver(productSchema),
//     defaultValues: {
//       name: "",
//       description: "",
//       price: 0,
//       category: "",
//       stock: 0,
//     },
//   })

//   const onSubmit = async (data: ProductFormType) => {
//     try {
//       const formData = new FormData()
//       formData.append("name", data.name)
//       formData.append("description", data.description)
//       formData.append("price", data.price.toString())
//       formData.append("category", data.category)
//       formData.append("stock", data.stock.toString())
//       formData.append("image", data.image)

//       await createProductMutation.mutateAsync(formData)
//       methods.reset()
//       onSuccess?.()
//     } catch (error) {
//       console.error("Failed to create Product", error)
//     }
//   }

//   const steps = [
//     { title: "Basic Information", component: BasicInfoStep },
//     { title: "Image Upload", component: ImageUploadStep },
//     { title: "Preview", component: PreviewStep },
//   ]

//   const CurrentStep = steps[step].component

//   return (
//     <FormProvider {...methods}>
//       <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
//         <div className="space-y-8">
//           <CurrentStep />
//         </div>
//         <div className="flex justify-between">
//           {step > 0 && (
//             <Button type="button" onClick={() => setStep((prev) => prev - 1)}>
//               Previous
//             </Button>
//           )}
//           {step < steps.length - 1 ? (
//             <Button type="button" onClick={() => setStep((prev) => prev + 1)}>
//               Next
//             </Button>
//           ) : (
//             <Button type="submit" disabled={createProductMutation.isLoading}>
//               {createProductMutation.isLoading ? "Creating..." : "Create Product"}
//             </Button>
//           )}
//         </div>
//       </form>
//     </FormProvider>
//   )
// }



// "use client"

// import React, { useState } from "react"
// import { useForm, FormProvider } from "react-hook-form"
// import { z } from "zod"
// import { zodResolver } from "@hookform/resolvers/zod"
// import { Button } from "@/components/ui/button"
// import { useCreateProductMutation } from "@/hooks/useCreateProductMutation"
// import { BasicInfoStep } from "./steps/BasicInfoStep"
// import { ImageUploadStep } from "./steps/ImageUploadStep"
// import { PreviewStep } from "./steps/PreviewStep"

// const productSchema = z.object({
//   name: z.string().min(2, "Name must be at least 2 characters."),
//   description: z.string().min(10, "Description must be at least 10 characters."),
//   price: z.number().positive("Price must be a positive number."),
//   category: z.string().nonempty("Category is required."),
//   stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
//   image: z
//     .instanceof(File)
//     .refine((file) => file.size <= 5000000, `Max image size is 5MB.`)
//     .refine(
//       (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
//       "Only .jpg, .png, and .webp formats are supported.",
//     ),
// })

// type ProductFormType = z.infer<typeof productSchema>

// export function MultiStepProductForm({ onSuccess }: { onSuccess?: () => void }) {
//   const [step, setStep] = useState(0)
//   const createProductMutation = useCreateProductMutation()
//   const methods = useForm<ProductFormType>({
//     resolver: zodResolver(productSchema),
//     defaultValues: {
//       name: "",
//       description: "",
//       price: 0,
//       category: "",
//       stock: 0,
//     },
//   })

//   const onSubmit = async (data: ProductFormType) => {
//     try {
//       const formData = new FormData()
//       formData.append("name", data.name)
//       formData.append("description", data.description)
//       formData.append("price", data.price.toString())
//       formData.append("category", data.category)
//       formData.append("stock", data.stock.toString())
//       formData.append("image", data.image)

//       await createProductMutation.mutateAsync(formData)
//       methods.reset()
//       onSuccess?.()
//     } catch (error) {
//       console.error("Failed to create Product", error)
//     }
//   }

//   const steps = [
//     { title: "Basic Information", component: BasicInfoStep },
//     { title: "Image Upload", component: ImageUploadStep },
//     { title: "Preview", component: PreviewStep },
//   ]

//   const CurrentStep = steps[step].component

//   return (
//     <FormProvider {...methods}>
//       <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
//         <div className="space-y-8">
//           <CurrentStep />
//         </div>
//         <div className="flex justify-between">
//           {step > 0 && (
//             <Button type="button" onClick={() => setStep((prev) => prev - 1)}>
//               Previous
//             </Button>
//           )}
//           {step < steps.length - 1 ? (
//             <Button type="button" onClick={() => setStep((prev) => prev + 1)}>
//               Next
//             </Button>
//           ) : (
//             <Button type="submit" disabled={createProductMutation.isLoading}>
//               {createProductMutation.isLoading ? "Creating..." : "Create Product"}
//             </Button>
//           )}
//         </div>
//       </form>
//     </FormProvider>
//   )
// }


// "use client"

// import React, { useState } from "react"
// import { useForm, FormProvider } from "react-hook-form"
// import { z } from "zod"
// import { zodResolver } from "@hookform/resolvers/zod"
// import { Button } from "@/components/ui/button"
// import { useCreateProductMutation } from "@/hooks/useCreateProductMutation"
// import { BasicInfoStep } from "./steps/BasicInfoStep"
// import { ImageUploadStep } from "./steps/ImageUploadStep"
// import { PreviewStep } from "./steps/PreviewStep"

// const productSchema = z.object({
//   name: z.string().min(2, "Name must be at least 2 characters."),
//   description: z.string().min(10, "Description must be at least 10 characters."),
//   price: z.number().positive("Price must be a positive number."),
//   category: z.string().nonempty("Category is required."),
//   stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
//   image: z
//     .instanceof(File)
//     .refine((file) => file.size <= 5000000, `Max image size is 5MB.`)
//     .refine(
//       (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
//       "Only .jpg, .png, and .webp formats are supported.",
//     ),
// })

// type ProductFormType = z.infer<typeof productSchema>

// export function MultiStepProductForm({ onSuccess }: { onSuccess?: () => void }) {
//   const [step, setStep] = useState(0)
//   const createProductMutation = useCreateProductMutation()
//   const methods = useForm<ProductFormType>({
//     resolver: zodResolver(productSchema),
//     defaultValues: {
//       name: "",
//       description: "",
//       price: 0,
//       category: "",
//       stock: 0,
//     },
//   })

//   const onSubmit = async (data: ProductFormType) => {
//     try {
//       const formData = new FormData()
//       formData.append("name", data.name)
//       formData.append("description", data.description)
//       formData.append("price", data.price.toString())
//       formData.append("category", data.category)
//       formData.append("stock", data.stock.toString())
//       formData.append("image", data.image)

//       await createProductMutation.mutateAsync(formData)
//       methods.reset()
//       onSuccess?.()
//     } catch (error) {
//       console.error("Failed to create Product", error)
//     }
//   }

//   const steps = [
//     {
//       title: "Basic Information",
//       component: BasicInfoStep,
//       fields: ["name", "description", "price", "category", "stock"],
//     },
//     { title: "Image Upload", component: ImageUploadStep, fields: ["image"] },
//     { title: "Preview", component: PreviewStep, fields: [] },
//   ]

//   const CurrentStep = steps[step].component

//   const handleNext = async () => {
//     const fieldsToValidate = steps[step].fields
//     const isStepValid = await methods.trigger(fieldsToValidate as any)
//     if (isStepValid) {
//       setStep((prev) => prev + 1)
//     }
//   }

//   const handlePrevious = () => {
//     setStep((prev) => prev - 1)
//   }

//   return (
//     <FormProvider {...methods}>
//       <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
//         <div className="space-y-8">
//           <CurrentStep />
//         </div>
//         <div className="flex justify-between">
//           {step > 0 && (
//             <Button type="button" onClick={handlePrevious}>
//               Previous
//             </Button>
//           )}
//           {step < steps.length - 1 ? (
//             <Button type="button" onClick={handleNext}>
//               Next
//             </Button>
//           ) : (
//             <Button type="submit" disabled={createProductMutation.isLoading}>
//               {createProductMutation.isLoading ? "Creating..." : "Create Product"}
//             </Button>
//           )}
//         </div>
//       </form>
//     </FormProvider>
//   )
// }



// "use client"

// import type React from "react"
// import { useState } from "react"
// import { useForm, FormProvider } from "react-hook-form"
// import { z } from "zod"
// import { zodResolver } from "@hookform/resolvers/zod"
// import { Button } from "@/components/ui/button"
// import { useCreateProductMutation } from "@/hooks/useCreateProductMutation"
// import { BasicInfoStep } from "./steps/BasicInfoStep"
// import { ImageUploadStep } from "./steps/ImageUploadStep"
// import { PreviewStep } from "./steps/PreviewStep"

// const productSchema = z.object({
//   name: z.string().min(2, "Name must be at least 2 characters."),
//   description: z.string().min(10, "Description must be at least 10 characters."),
//   price: z.number().positive("Price must be a positive number."),
//   category: z.string().nonempty("Category is required."),
//   stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
//   image: z
//     .instanceof(File)
//     .refine((file) => file.size <= 5000000, `Max image size is 5MB.`)
//     .refine(
//       (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
//       "Only .jpg, .png, and .webp formats are supported.",
//     ),
// })

// type ProductFormType = z.infer<typeof productSchema>

// export function MultiStepProductForm({ onSuccess }: { onSuccess?: () => void }) {
//   const [step, setStep] = useState(0)
//   const createProductMutation = useCreateProductMutation()
//   const methods = useForm<ProductFormType>({
//     resolver: zodResolver(productSchema),
//     defaultValues: {
//       name: "",
//       description: "",
//       price: 0,
//       category: "",
//       stock: 0,
//     },
//   })

//   const onSubmit = async (data: ProductFormType) => {
//     try {
//       const formData = new FormData()
//       formData.append("name", data.name)
//       formData.append("description", data.description)
//       formData.append("price", data.price.toString())
//       formData.append("category", data.category)
//       formData.append("stock", data.stock.toString())
//       formData.append("image", data.image)

//       await createProductMutation.mutateAsync(formData)
//       methods.reset()
//       onSuccess?.()
//     } catch (error) {
//       console.error("Failed to create Product", error)
//     }
//   }

//   const steps = [
//     {
//       title: "Basic Information",
//       component: BasicInfoStep,
//       fields: ["name", "description", "price", "category", "stock"],
//     },
//     { title: "Image Upload", component: ImageUploadStep, fields: ["image"] },
//     { title: "Preview", component: PreviewStep, fields: [] },
//   ]

//   const CurrentStep = steps[step].component

//   const handleNext = async () => {
//     const fieldsToValidate = steps[step].fields
//     const isStepValid = await methods.trigger(fieldsToValidate as any)
//     if (isStepValid) {
//       setStep((prev) => prev + 1)
//     }
//   }

//   const handlePrevious = () => {
//     setStep((prev) => prev - 1)
//   }

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault()
//     methods.handleSubmit(onSubmit)()
//   }

//   return (
//     <FormProvider {...methods}>
//       <form onSubmit={handleSubmit} className="space-y-8">
//         <div className="space-y-8">
//           <CurrentStep />
//         </div>
//         <div className="flex justify-between">
//           {step > 0 && (
//             <Button type="button" onClick={handlePrevious}>
//               Previous
//             </Button>
//           )}
//           {step < steps.length - 1 ? (
//             <Button type="button" onClick={handleNext}>
//               Next
//             </Button>
//           ) : (
//             <Button type="submit" disabled={createProductMutation.isLoading}>
//               {createProductMutation.isLoading ? "Creating..." : "Create Product"}
//             </Button>
//           )}
//         </div>
//       </form>
//     </FormProvider>
//   )
// }



// "use client";

// import React, { useState } from "react";
// import { useForm, FormProvider } from "react-hook-form";
// import { z } from "zod";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Button } from "@/components/ui/button";
// import { useCreateProductMutation } from "@/hooks/useCreateProductMutation";
// import { BasicInfoStep } from "./steps/BasicInfoStep";
// import { ImageUploadStep } from "./steps/ImageUploadStep";
// import { PreviewStep } from "./steps/PreviewStep";

// const productSchema = z.object({
//   name: z.string().min(2, "Name must be at least 2 characters."),
//   description: z.string().min(10, "Description must be at least 10 characters."),
//   price: z.number().positive("Price must be a positive number."),
//   category: z.string().nonempty("Category is required."),
//   stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
//   image: z
//     .instanceof(File)
//     .refine((file) => file.size <= 5_000_000, "Max image size is 5MB.")
//     .refine(
//       (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
//       "Only .jpg, .png, and .webp formats are supported."
//     ),
// });

// type ProductFormType = z.infer<typeof productSchema>;

// export function MultiStepProductForm({ onSuccess }: { onSuccess?: () => void }) {
//   const [step, setStep] = useState(0);
//   const createProductMutation = useCreateProductMutation();

//   const methods = useForm<ProductFormType>({
//     resolver: zodResolver(productSchema),
//     defaultValues: {
//       name: "",
//       description: "",
//       price: 0,
//       category: "",
//       stock: 0,
//       // image is undefined by default; user will pick it
//     },
//   });

//   const onSubmit = async (data: ProductFormType) => {
//     try {
//       const formData = new FormData();
//       formData.append("name", data.name);
//       formData.append("description", data.description);
//       formData.append("price", data.price.toString());
//       formData.append("category", data.category);
//       formData.append("stock", data.stock.toString());
//       formData.append("image", data.image);

//       await createProductMutation.mutateAsync(formData);
//       methods.reset();
//       onSuccess?.();
//     } catch (error) {
//       console.error("Failed to create Product", error);
//     }
//   };

//   const steps = [
//     {
//       title: "Basic Information",
//       component: BasicInfoStep,
//       fields: ["name", "description", "price", "category", "stock"],
//     },
//     { title: "Image Upload", component: ImageUploadStep, fields: ["image"] },
//     { title: "Preview", component: PreviewStep, fields: [] },
//   ];

//   const CurrentStep = steps[step].component;

//   // Validate only the fields relevant to the current step
//   const handleNext = async () => {
//     const fieldsToValidate = steps[step].fields;
//     const isStepValid = await methods.trigger(fieldsToValidate as any);
//     if (isStepValid) {
//       setStep((prev) => prev + 1);
//     }
//   };

//   const handlePrevious = () => {
//     setStep((prev) => prev - 1);
//   };

//   // Instead of relying on <form onSubmit>, we’ll do everything manually
//   return (
//     <FormProvider {...methods}>
//       {/* We keep the form for styling/layout but remove the onSubmit prop */}
//       <form 
//         // Remove onSubmit to prevent auto-submission on Enter
//         onSubmit={(e) => e.preventDefault()} 
//         className="space-y-8"
//         // OPTIONAL: If pressing Enter is still submitting, you can 
//         // also explicitly stop it:
//         // onKeyDown={(e) => {
//         //   if (e.key === "Enter") e.preventDefault();
//         // }}
//       >
//         <div className="space-y-8">
//           <CurrentStep />
//         </div>

//         <div className="flex justify-between">
//           {step > 0 && (
//             <Button type="button" onClick={handlePrevious}>
//               Previous
//             </Button>
//           )}

//           {step < steps.length - 1 ? (
//             <Button type="button" onClick={handleNext}>
//               Next
//             </Button>
//           ) : (
//             // On the final step, manually call `methods.handleSubmit`
//             <Button
//               type="button"
//               onClick={() => methods.handleSubmit(onSubmit)()}
//               disabled={createProductMutation.isLoading}
//             >
//               {createProductMutation.isLoading ? "Creating..." : "Create Product"}
//             </Button>
//           )}
//         </div>
//       </form>
//     </FormProvider>
//   );
// }



// "use client";

// import React, { useState } from "react";
// import { useForm, FormProvider } from "react-hook-form";
// import { z } from "zod";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Button } from "@/components/ui/button";
// import { useCreateProductMutation } from "@/hooks/useCreateProductMutation";
// import { BasicInfoStep } from "./steps/BasicInfoStep";
// import { ImageUploadStep } from "./steps/ImageUploadStep";
// import { PreviewStep } from "./steps/PreviewStep";

// const productSchema = z.object({
//   name: z.string().min(2, "Name must be at least 2 characters."),
//   description: z.string().min(10, "Description must be at least 10 characters."),
//   price: z.number().positive("Price must be a positive number."),
//   category: z.string().nonempty("Category is required."),
//   stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
//   image: z
//     .instanceof(File)
//     .refine((file) => file.size <= 5_000_000, "Max image size is 5MB.")
//     .refine(
//       (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
//       "Only .jpg, .png, and .webp formats are supported."
//     ),
// });

// type ProductFormType = z.infer<typeof productSchema>;

// // StepDefinition ensures each step's `fields` are valid keys of ProductFormType
// interface StepDefinition {
//   title: string;
//   component: React.ComponentType;
//   fields: (keyof ProductFormType)[];
// }

// export function MultiStepProductForm({ onSuccess }: { onSuccess?: () => void }) {
//   const [step, setStep] = useState(0);
//   const createProductMutation = useCreateProductMutation();

//   const methods = useForm<ProductFormType>({
//     resolver: zodResolver(productSchema),
//     defaultValues: {
//       name: "",
//       description: "",
//       price: 0,
//       category: "",
//       stock: 0,
//     },
//   });

//   // Our steps now have a precise type
//   const steps: StepDefinition[] = [
//     {
//       title: "Basic Information",
//       component: BasicInfoStep,
//       fields: ["name", "description", "price", "category", "stock"],
//     },
//     { title: "Image Upload", component: ImageUploadStep, fields: ["image"] },
//     { title: "Preview", component: PreviewStep, fields: [] },
//   ];

//   const CurrentStep = steps[step].component;

//   const onSubmit = async (data: ProductFormType) => {
//     try {
//       const formData = new FormData();
//       formData.append("name", data.name);
//       formData.append("description", data.description);
//       formData.append("price", data.price.toString());
//       formData.append("category", data.category);
//       formData.append("stock", data.stock.toString());
//       formData.append("image", data.image);

//       await createProductMutation.mutateAsync(formData);
//       methods.reset();
//       onSuccess?.();
//     } catch (error) {
//       console.error("Failed to create Product", error);
//     }
//   };

//   const handleNext = async () => {
//     // Now `fieldsToValidate` is (keyof ProductFormType)[]
//     const fieldsToValidate = steps[step].fields;
//     // No `as any` needed, because it matches the `trigger` signature
//     const isStepValid = await methods.trigger(fieldsToValidate);
//     if (isStepValid) {
//       setStep((prev) => prev + 1);
//     }
//   };

//   const handlePrevious = () => {
//     setStep((prev) => prev - 1);
//   };

//   return (
//     <FormProvider {...methods}>
//       <form
//         onSubmit={(e) => e.preventDefault()}
//         className="space-y-8"
//       >
//         <div className="space-y-8">
//           <CurrentStep />
//         </div>

//         <div className="flex justify-between">
//           {step > 0 && (
//             <Button type="button" onClick={handlePrevious}>
//               Previous
//             </Button>
//           )}

//           {step < steps.length - 1 ? (
//             <Button type="button" onClick={handleNext}>
//               Next
//             </Button>
//           ) : (
//             <Button
//               type="button"
//               onClick={() => methods.handleSubmit(onSubmit)()}
//               disabled={createProductMutation.isLoading}
//             >
//               {createProductMutation.isLoading ? "Creating..." : "Create Product"}
//             </Button>
//           )}
//         </div>
//       </form>
//     </FormProvider>
//   );
// }



"use client";

import React, { useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { useCreateProductMutation } from "@/hooks/useCreateProductMutation";
import { BasicInfoStep } from "./steps/BasicInfoStep";
import { ImageUploadStep } from "./steps/ImageUploadStep";
import { PreviewStep } from "./steps/PreviewStep";

/**
 * 1) Conditionally define the `image` field in the schema:
 *    - On the server: z.any(), so there's no reference to the browser File API
 *    - In the browser: z.instanceof(File) with your size/type checks
 */
const fileField =
  typeof window === "undefined"
    ? z.any() // No File reference on the server
    : z
        .instanceof(File)
        .refine((file) => file.size <= 5_000_000, "Max image size is 5MB.")
        .refine(
          (file) => ["image/jpeg", "image/png", "image/webp"].includes(file.type),
          "Only .jpg, .png, and .webp formats are supported."
        );

/**
 * 2) Build the main product schema with the conditional fileField
 */
const productSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().min(10, "Description must be at least 10 characters."),
  price: z.number().positive("Price must be a positive number."),
  category: z.string().nonempty("Category is required."),
  stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
  image: fileField,
});

type ProductFormType = z.infer<typeof productSchema>;

/**
 * StepDefinition helps typed steps:
 *  - "fields": all form keys relevant to that step
 */
interface StepDefinition {
  title: string;
  component: React.ComponentType;
  fields: (keyof ProductFormType)[];
}

export function MultiStepProductForm({ onSuccess }: { onSuccess?: () => void }) {
  const [step, setStep] = useState(0);
  const createProductMutation = useCreateProductMutation();

  // 3) React Hook Form setup with the updated productSchema
  const methods = useForm<ProductFormType>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      price: 0,
      category: "",
      stock: 0,
      // "image" defaults to undefined until the user uploads a file
    },
  });

  // 4) Define your steps with an array of StepDefinition
  const steps: StepDefinition[] = [
    {
      title: "Basic Information",
      component: BasicInfoStep,
      fields: ["name", "description", "price", "category", "stock"],
    },
    { title: "Image Upload", component: ImageUploadStep, fields: ["image"] },
    { title: "Preview", component: PreviewStep, fields: [] },
  ];

  const CurrentStep = steps[step].component;

  /**
   * Submit handler for the final step:
   */
  const onSubmit = async (data: ProductFormType) => {
    try {
      // Build a FormData object for the image + other fields
      const formData = new FormData();
      formData.append("name", data.name);
      formData.append("description", data.description);
      formData.append("price", data.price.toString());
      formData.append("category", data.category);
      formData.append("stock", data.stock.toString());
      formData.append("image", data.image as File);

      await createProductMutation.mutateAsync(formData);
      methods.reset();
      onSuccess?.(); // e.g. close dialog or show success
    } catch (error) {
      console.error("Failed to create Product", error);
    }
  };

  /**
   * Step navigation: Validate only the current step's fields
   */
  const handleNext = async () => {
    const fieldsToValidate = steps[step].fields;
    const isStepValid = await methods.trigger(fieldsToValidate);
    if (isStepValid) {
      setStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    setStep((prev) => prev - 1);
  };

  return (
    <FormProvider {...methods}>
      {/* We disable default browser form submission with e.preventDefault() */}
      <form onSubmit={(e) => e.preventDefault()} className="space-y-8">
        <div className="space-y-8">
          <CurrentStep />
        </div>

        <div className="flex justify-between">
          {step > 0 && (
            <Button type="button" onClick={handlePrevious}>
              Previous
            </Button>
          )}

          {step < steps.length - 1 ? (
            <Button type="button" onClick={handleNext}>
              Next
            </Button>
          ) : (
            // On final step, call handleSubmit -> onSubmit
            <Button
              type="button"
              onClick={() => methods.handleSubmit(onSubmit)()}
              disabled={createProductMutation.isLoading}
            >
              {createProductMutation.isLoading ? "Creating..." : "Create Product"}
            </Button>
          )}
        </div>
      </form>
    </FormProvider>
  );
}
