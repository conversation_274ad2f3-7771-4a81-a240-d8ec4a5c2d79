"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useCreateCategoryMutation } from "@/lib/redux/features/categories/categoriesApiSlice";

const productCategorySchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().min(10, "Description must be at least 10 characters."),
});

type ProductCategoryFormType = z.infer<typeof productCategorySchema>;

export function AddProductCategoryForm({ onSuccess }: { onSuccess?: () => void }) {
  const [createCategory, { isLoading }] = useCreateCategoryMutation();
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const form = useForm<ProductCategoryFormType>({
    resolver: zodResolver(productCategorySchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const onSubmit = async (data: ProductCategoryFormType) => {
    try {
      setSubmitting(true);
      await createCategory(data);
      setSuccess(true);
      form.reset();
      setTimeout(() => {
        onSuccess?.();
      }, 3000);
    } catch (error) {
      console.error("Failed to create ProductCategory", error);
      setSuccess(false);
    } finally {
      setSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="p-4">
        <h2 className="text-xl font-semibold mb-2">Product Category Created!</h2>
        <p className="text-sm text-gray-700">
          Your new Product Category was created successfully. This window will
          close in 3 seconds...
        </p>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter category name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter category description" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading || submitting}>
            {submitting ? "Saving..." : "Create Category"}
          </Button>
        </div>
      </form>
    </Form>
  );
}

