// // // components/admin/AddProductModal.tsx

// // "use client"

// // import { useState } from 'react'
// // import { Button } from "@/components/ui/button"
// // import {
// //   Dialog,
// //   DialogContent,
// //   DialogDescription,
// //   DialogHeader,
// //   DialogTitle,
// //   DialogTrigger,
// // } from "@/components/ui/dialog"
// // import { ProductFormWizard } from "./ProductFormWizard"

// // export function AddProductModal() {
// //   const [open, setOpen] = useState(false)

// //   return (
// //     <Dialog open={open} onOpenChange={setOpen}>
// //       <DialogTrigger asChild>
// //         <Button variant="default">Add New Product</Button>
// //       </DialogTrigger>
// //       <DialogContent className="sm:max-w-[600px]">
// //         <DialogHeader>
// //           <DialogTitle>Add New Product</DialogTitle>
// //           <DialogDescription>
// //             Fill in the details of the new product. Navigate through the steps to complete all information.
// //           </DialogDescription>
// //         </DialogHeader>
// //         <ProductFormWizard />
// //       </DialogContent>
// //     </Dialog>
// //   )
// // }



// "use client"

// import { useState } from 'react'
// import { Button } from "@/components/ui/button"
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@/components/ui/dialog"
// import { ProductFormWizard } from "./ProductFormWizard"

// export function AddProductModal() {
//   const [open, setOpen] = useState(false)

//   const handleSuccess = () => {
//     setOpen(false)
//   }

//   return (
//     <Dialog open={open} onOpenChange={setOpen}>
//       <DialogTrigger asChild>
//         <Button variant="default">Add New Product</Button>
//       </DialogTrigger>
//       <DialogContent className="sm:max-w-[600px]">
//         <DialogHeader>
//           <DialogTitle>Add New Product</DialogTitle>
//           <DialogDescription>
//             Fill in the details of the new product. Navigate through the steps to complete all information.
//           </DialogDescription>
//         </DialogHeader>
//         <ProductFormWizard onSuccess={handleSuccess} />
//       </DialogContent>
//     </Dialog>
//   )
// }



// "use client"

// import { useState } from 'react'
// import { Button } from "@/components/ui/button"
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@/components/ui/dialog"
// import { ProductFormWizard } from "./ProductFormWizard"
// import { Notification } from "@/components/ui/notification"

// export function AddProductModal() {
//   const [open, setOpen] = useState(false)
//   const [showNotification, setShowNotification] = useState(false)

//   const handleSuccess = () => {
//     setOpen(false)
//     setShowNotification(true)
//   }

//   return (
//     <>
//       <Dialog open={open} onOpenChange={setOpen}>
//         <DialogTrigger asChild>
//           <Button variant="default">Add New Product</Button>
//         </DialogTrigger>
//         <DialogContent className="sm:max-w-[600px]">
//           <DialogHeader>
//             <DialogTitle>Add New Product</DialogTitle>
//             <DialogDescription>
//               Fill in the details of the new product. Navigate through the steps to complete all information.
//             </DialogDescription>
//           </DialogHeader>
//           <ProductFormWizard onSuccess={handleSuccess} />
//         </DialogContent>
//       </Dialog>
//       {showNotification && (
//         <Notification
//           title="Product Created"
//           message="The product has been successfully created."
//           type="success"
//           onClose={() => setShowNotification(false)}
//         />
//       )}
//     </>
//   )
// }




// "use client"

// import { useState } from "react"
// import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
// import { Button } from "@/components/ui/button"
// import { AddProductForm } from "./AddProductForm"

// export function AddProductModal() {
//   const [open, setOpen] = useState(false)

//   return (
//     <Dialog open={open} onOpenChange={setOpen}>
//       <DialogTrigger asChild>
//         <Button variant="outline">Add New Product</Button>
//       </DialogTrigger>
//       <DialogContent className="sm:max-w-[600px]">
//         <DialogHeader>
//           <DialogTitle>Add New Product</DialogTitle>
//         </DialogHeader>
//         <AddProductForm onSuccess={() => setOpen(false)} />
//       </DialogContent>
//     </Dialog>
//   )
// }



"use client"

import { useState } from "react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { MultiStepProductForm } from "./MultiStepProductForm"

export function AddProductModal() {
  const [open, setOpen] = useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Add New Product</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Product</DialogTitle>
        </DialogHeader>
        <MultiStepProductForm onSuccess={() => setOpen(false)} />
      </DialogContent>
    </Dialog>
  )
}

