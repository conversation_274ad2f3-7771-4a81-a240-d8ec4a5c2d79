"use client"

// No React imports needed
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Product } from "@/types/product"
import { useDeleteProductMutation } from "@/lib/redux/features/products/productsApiSlice"
import { toast } from 'sonner';

interface DeleteProductModalProps {
  product: Product;
  onClose: () => void;
}

export function DeleteProductModal({ product, onClose }: DeleteProductModalProps) {
  const [deleteProduct, { isLoading: isDeleting }] = useDeleteProductMutation();

  const handleDelete = async () => {
    try {
      // Ensure toast is imported and available
      if (typeof toast === 'undefined') {
        console.error('Sonner toast is not imported correctly');
        alert(`Archiving "${product.name}"`);
        return;
      }

      // Delete the product using RTK Query mutation
      await deleteProduct(product._id);

      // Debugging log
      console.log('Attempting to show success toast');

      // Enhanced success toast with fallback
      const toastResult = toast.success('Product Archived Successfully', {
        description: (
          <div className="space-y-1">
            <p>
              <strong>&quot;{product.name}&quot;</strong> has been moved to the archive.
            </p>
            <p className="text-xs text-muted-foreground">
              You can find this product in the Archive section.
            </p>
            <div className="flex items-center text-xs text-blue-600 mt-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Archived products can be restored later
            </div>
          </div>
        ),
        duration: 5000,
        position: 'top-right',
      });

      // Additional debugging
      console.log('Toast result:', toastResult);

      // Close the modal
      onClose();
    } catch (error) {
      // Enhanced error toast
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      console.error('Product archival error:', errorMessage);

      // Fallback toast with multiple methods
      try {
        toast.error('Failed to Archive Product', {
          description: (
            <div className="space-y-1">
              <p>Unable to archive &quot;{product.name}&quot;.</p>
              <p className="text-xs text-muted-foreground">
                {errorMessage}
              </p>
              <div className="flex items-center text-xs text-red-600 mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Please check your network and try again
              </div>
            </div>
          ),
          duration: 4000,
          position: 'top-right',
        });
      } catch (toastError) {
        console.error('Failed to show toast:', toastError);
        alert(`Failed to archive "${product.name}": ${errorMessage}`);
      }
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Product</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the product &quot;{product.name}&quot;?
            <span className="block mt-2 text-red-500 font-semibold">
              This action cannot be undone.
            </span>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? 'Archiving...' : 'Delete Product'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
