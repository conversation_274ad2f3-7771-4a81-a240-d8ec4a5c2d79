"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { useGroups } from "@/lib/redux/hooks/useGroups"
import { StokvelGroup } from "@/lib/frontendGroupUtilities"

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  geolocation: z.string().min(2, {
    message: "Geolocation must be at least 2 characters.",
  }),
  totalSales: z.number().min(0, {
    message: "Total sales must be a positive number.",
  }),
  avgOrderValue: z.number().min(0, {
    message: "Average order value must be a positive number.",
  }),
  activeOrders: z.number().min(0, {
    message: "Active orders must be a positive number.",
  }),
})

interface EditStokvelGroupModalProps {
  group: StokvelGroup
  onClose: () => void
}

export function EditStokvelGroupModal({ group, onClose }: EditStokvelGroupModalProps) {
  const [open, setOpen] = useState(true)
  const { updateGroup } = useGroups()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: group.name,
      geolocation: group.geolocation,
      totalSales: group.totalSales || 0,
      avgOrderValue: group.avgOrderValue || 0,
      activeOrders: group.activeOrders || 0,
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await updateGroup({ id: group._id, updateData: values })
      setOpen(false)
      onClose()
    } catch (error) {
      console.error("Failed to update group:", error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Stokvel Group</DialogTitle>
          <DialogDescription>
            Make changes to the Stokvel group here. Click save when you&quot;re done.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter group name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="geolocation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter location" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="totalSales"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Sales</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="Enter total sales" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="avgOrderValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Average Order Value</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="Enter average order value" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="activeOrders"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Active Orders</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="Enter number of active orders" {...field} onChange={e => field.onChange(parseInt(e.target.value, 10))} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit">Save changes</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

