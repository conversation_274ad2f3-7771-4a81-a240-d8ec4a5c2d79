// // components/admin/AddStokvelGroupModal.tsx
// "use client";

// import { useState } from "react";
// import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
// import { Button } from "@/components/ui/button";
// import { StokvelGroupForm } from "./StokvelGroupForm"; 

// /**
//  * This component opens a dialog with a StokvelGroup form.
//  */
// export function AddStokvelGroupModal() {
//   const [open, setOpen] = useState(false);

//   return (
//     <Dialog open={open} onOpenChange={setOpen}>
//       <DialogTrigger asChild>
//         <Button className="bg-[#2A7C6C] hover:bg-[#236358]">
//           Add New Group
//         </Button>
//       </DialogTrigger>
//       <DialogContent className="sm:max-w-[600px]">
//         <DialogHeader>
//           <DialogTitle>Add New Stokvel Group</DialogTitle>
//         </DialogHeader>

//         {/**
//          * The form is placed here so that once the dialog opens, 
//          * the user can fill in the group details and submit.
//          */}
//         <StokvelGroupForm onSuccess={() => setOpen(false)} />
//       </DialogContent>
//     </Dialog>
//   );
// }




// components/admin/AddStokvelGroupModal.tsx
"use client";

import { useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { StokvelGroupForm } from "./StokvelGroupForm"; 
import { useAuthStatus } from "@/hooks/useAuthStatus"; // Import the hook

/**
 * This component opens a dialog with a StokvelGroup form.
 */
export function AddStokvelGroupModal() {
  const [open, setOpen] = useState(false);
  const { user, isLoading } = useAuthStatus(); // Destructure from the hook

  // If authentication is loading, don't render the trigger
  if (isLoading) {
    return <Button disabled>Loading...</Button>;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-[#2A7C6C] hover:bg-[#236358]">
          Add New Group
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Stokvel Group</DialogTitle>
        </DialogHeader>

        {/* Only render the form if the user is authenticated */}
        {user ? (
          <StokvelGroupForm onSuccess={() => setOpen(false)} />
        ) : (
          <p className="text-red-500">You must be logged in to create a group.</p>
        )}
      </DialogContent>
    </Dialog>
  );
}
