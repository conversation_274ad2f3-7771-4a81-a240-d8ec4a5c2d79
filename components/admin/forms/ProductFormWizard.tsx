// components/admin/forms/ProductFormWizard.tsx
"use client";

import React, { useState, useEffect } from "react";
import { useF<PERSON>, SubmitHandler, FieldPath } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Image from "next/image";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Upload, Loader2 } from "lucide-react";

import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";

/**
 * 1) Define Zod schema for form validation
 *    - Use z.instanceof(File) for file arrays
 */
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().min(10, "Description must be at least 10 characters."),
  category: z.string().min(1, "Please select a category."),
  price: z.number().min(0, "Price must be a positive number."),
  compareAtPrice: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  stock: z.number().int().min(0, "Stock must be a positive integer."),
  weight: z.number().min(0).optional(),
  variants: z
    .array(z.object({ name: z.string(), values: z.array(z.string()) }))
    .optional(),
  taxable: z.boolean(),
  shippingRequired: z.boolean(),
  images: z
    .array(z.instanceof(File))
    .min(1, { message: "At least one product image is required." }),
});

/**
 * 2) Type for React Hook Form
 */
type FormValues = z.infer<typeof formSchema>;

/**
 * 3) The wizard form
 */
export function ProductFormWizard({ onSuccess }: { onSuccess?: () => void }) {
  const [step, setStep] = useState(1);
  const [images, setImages] = useState<File[]>([]);
  const [submitting, setSubmitting] = useState(false);

  const { data: categories = [] } = useGetCategoriesQuery();

  // Initialize react-hook-form with zod
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      category: "",
      price: 0,
      stock: 0,
      taxable: true,
      shippingRequired: true,
      images: [],
    },
  });

  /**
   * 4) Load from local storage on mount,
   *    parse the numeric/boolean fields,
   *    then reset the form with the data.
   */
  useEffect(() => {
    const savedData = localStorage.getItem("productFormData");
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);

        // Convert numeric fields from strings to number
        if (parsed.price) parsed.price = parseFloat(parsed.price);
        if (parsed.compareAtPrice) parsed.compareAtPrice = parseFloat(parsed.compareAtPrice);
        if (parsed.cost) parsed.cost = parseFloat(parsed.cost);
        if (parsed.stock) parsed.stock = parseInt(parsed.stock, 10);
        if (parsed.weight) parsed.weight = parseFloat(parsed.weight);

        // Convert boolean fields from string
        if (typeof parsed.taxable === "string") {
          parsed.taxable = parsed.taxable === "true";
        }
        if (typeof parsed.shippingRequired === "string") {
          parsed.shippingRequired = parsed.shippingRequired === "true";
        }

        // If images were persisted, we can't directly store them as `File`.
        // Typically, you can't restore File objects from localStorage.
        // So we skip that part or handle differently.
        if (parsed.images) {
          parsed.images = [];
        }

        form.reset(parsed); // Set all fields with the loaded data
      } catch (err) {
        console.error("Error parsing saved form data:", err);
      }
    }

    const subscription = form.watch((value) => {
      // We do not need to destructure images at all
      localStorage.setItem(
        "productFormData",
        JSON.stringify({
          ...value,            // spread entire form
          images: [],          // explicitly override images
        })
      );
    });


    // 5) Watch all fields, store them in localStorage whenever they change
// const subscription = form.watch((value) => {
//   -  const { images, ...rest } = value;
//   +  const { images: _unusedImages, ...rest } = value;

//     // We don't store actual File objects in localStorage
//     // We'll store just the rest of the fields
//     localStorage.setItem("productFormData", JSON.stringify(rest));
//   });


    return () => subscription.unsubscribe();
  }, [form]);

  /**
   * 6) On final submit
   *    Build a FormData object, POST to /api/products/create (example).
   */
  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    try {
      setSubmitting(true);

      // Combine everything into FormData
      const formData = new FormData();
      formData.append("name", data.name);
      formData.append("description", data.description);
      formData.append("category", data.category);
      formData.append("price", data.price.toString());
      if (data.compareAtPrice) formData.append("compareAtPrice", data.compareAtPrice.toString());
      if (data.cost) formData.append("cost", data.cost.toString());
      if (data.sku) formData.append("sku", data.sku);
      if (data.barcode) formData.append("barcode", data.barcode);
      formData.append("stock", data.stock.toString());
      formData.append("taxable", data.taxable ? "true" : "false");
      formData.append("shippingRequired", data.shippingRequired ? "true" : "false");
      if (data.weight) formData.append("weight", data.weight.toString());

      // Append images
      data.images.forEach((file) => {
        formData.append("images", file);
      });

      // POST to an API route, e.g. /api/products/create
      const res = await fetch("/api/products/create", {
        method: "POST",
        body: formData,
      });
      if (!res.ok) {
        const errorBody = await res.json();
        throw new Error(errorBody.error || "Failed to create product");
      }

      const result = await res.json();
      console.log("Product created successfully:", result);

      // Clear local storage so user doesn't see old data
      localStorage.removeItem("productFormData");

      // If the parent wants to close a modal or do something
      onSuccess?.();
    } catch (err) {
      console.error("Error creating product:", err);
      // handle your error UI
    } finally {
      setSubmitting(false);
    }
  };

  /**
   * 7) Handle user selecting images in step 4
   */
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const newImages = Array.from(files);
      setImages((prev) => [...prev, ...newImages]);

      // Also set in the form
      form.setValue("images", [...form.getValues("images"), ...newImages]);
    }
  };

  /**
   * 8) Reusable function for text input fields
   */
  const renderFormField = <T extends FieldPath<FormValues>>(
    name: T,
    label: string,
    placeholder: string,
    type: string = "text",
    description?: string
  ) => (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              type={type}
              placeholder={placeholder}
              {...field}
              onChange={(e) => {
                // Convert string to number if needed
                const val = type === "number" ? parseFloat(e.target.value) : e.target.value;
                field.onChange(val);
              }}
              value={
                typeof field.value === "string" || typeof field.value === "number"
                  ? field.value
                  : ""
              }
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );

  /**
   * 9) Render the fields for each step
   */
  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-4">
            {renderFormField("name", "Product Name", "Enter product name")}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product Description</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter product description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-white">
                      {categories.map((cat) => (
                        <SelectItem key={cat._id} value={cat._id}>
                          {cat.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            {renderFormField("price", "Price", "Enter price", "number")}
            {renderFormField(
              "compareAtPrice",
              "Compare at Price",
              "Enter compare at price",
              "number",
              "To show a markdown, enter a higher price here."
            )}
            {renderFormField(
              "cost",
              "Cost per item",
              "Enter cost per item",
              "number",
              "Customers won't see this price."
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            {renderFormField("sku", "SKU (Stock Keeping Unit)", "Enter SKU")}
            {renderFormField("barcode", "Barcode (ISBN, UPC, GTIN, etc.)", "Enter barcode")}
            {renderFormField("stock", "Stock", "Enter stock", "number")}
            <Separator />
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="taxable"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Charge tax on this product
                      </FormLabel>
                      <FormDescription>Tax will be calculated at checkout.</FormDescription>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="shippingRequired"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        This product requires shipping
                      </FormLabel>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="images">Product Images</Label>
              <div className="mt-2 flex items-center gap-4">
                <Label
                  htmlFor="image-upload"
                  className="flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 text-center"
                >
                  <Upload className="h-8 w-8 text-gray-400" />
                  <span className="mt-2 text-sm font-medium text-gray-600">
                    Upload Image
                  </span>
                  <Input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    multiple
                    className="hidden"
                    onChange={handleImageUpload}
                  />
                </Label>
                {images.map((image, index) => (
                  <Card key={index} className="h-32 w-32 overflow-hidden relative">
                    <CardContent className="p-0">
                      <Image
                        src={URL.createObjectURL(image)}
                        alt={`Product image ${index + 1}`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 128px) 100vw, 128px"
                        priority={index === 0} // Priority loading for first image
                      />
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            <FormMessage>{form.formState.errors.images?.message}</FormMessage>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(
          onSubmit,
          (errors) => console.log("Form errors:", errors)
        )}
        className="space-y-8"
      >
        {renderStep()}

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => setStep((prev) => Math.max(1, prev - 1))}
            disabled={step === 1}
          >
            Previous
          </Button>

          {step < 4 ? (
            <Button
              type="button"
              onClick={() => setStep((prev) => Math.min(4, prev + 1))}
            >
              Next
            </Button>
          ) : (
            <Button type="submit" disabled={submitting}>
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Product"
              )}
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
}
