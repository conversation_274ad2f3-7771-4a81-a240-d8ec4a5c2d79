"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Loader2, CheckCircle, XCircle } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { cn } from "@/lib/utils"

const formSchema = z.object({
  groupName: z.string().min(2, {
    message: "Group name must be at least 2 characters.",
  }),
  location: z.string().min(2, {
    message: "Location must be at least 2 characters.",
  }),
  memberCount: z.number().min(1, {
    message: "Group must have at least 1 member.",
  }),
  groupType: z.enum(["residential", "business", "community"], {
    required_error: "Please select a group type.",
  }),
  description: z.string().optional(),
})

type FormState = "idle" | "submitting" | "success" | "error"

export function OrderGroupForm() {
  const [formState, setFormState] = useState<FormState>("idle")
  const [isOpen, setIsOpen] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      groupName: "",
      location: "",
      memberCount: 1,
      groupType: "residential",
      description: "",
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    setFormState("submitting")
    form.clearErrors() // Clear any existing errors
    // Simulate API call
    setTimeout(() => {
      console.log(values)
      // Simulate success (80% of the time) or error (20% of the time)
      if (Math.random() > 0.2) {
        setFormState("success")
      } else {
        setFormState("error")
        // Simulate server-side validation error
        form.setError("groupName", {
          type: "manual",
          message: "This group name is already taken."
        })
      }
    }, 2000)
  }

  const resetForm = () => {
    setFormState("idle")
    form.reset()
  }

  const closeDialog = () => {
    setIsOpen(false)
    resetForm()
  }

  const formContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="groupName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Group Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter group name" {...field} />
              </FormControl>
              <FormDescription>
                This is the name that will be used to identify the order group.
              </FormDescription>
              {form.formState.errors[field.name] && (
                <FormMessage className="text-red-500 text-sm mt-1" />
              )}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location</FormLabel>
              <FormControl>
                <Input placeholder="Enter group location" {...field} />
              </FormControl>
              <FormDescription>
                The primary location or area where this group operates.
              </FormDescription>
              {form.formState.errors[field.name] && (
                <FormMessage className="text-red-500 text-sm mt-1" />
              )}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="memberCount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Number of Members</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="Enter number of members" 
                  {...field} 
                  onChange={(e) => field.onChange(parseInt(e.target.value, 10))}
                />
              </FormControl>
              <FormDescription>
                The initial number of members in this group.
              </FormDescription>
              {form.formState.errors[field.name] && (
                <FormMessage className="text-red-500 text-sm mt-1" />
              )}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="groupType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Group Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a group type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent className={cn("bg-green-100")}>
                  <SelectItem value="residential" className="text-green-900">Residential</SelectItem>
                  <SelectItem value="business" className="text-green-900">Business</SelectItem>
                  <SelectItem value="community" className="text-green-900">Community</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                The type of group this is, which may affect buying patterns and discounts.
              </FormDescription>
              {form.formState.errors[field.name] && (
                <FormMessage className="text-red-500 text-sm mt-1" />
              )}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Enter a brief description of the group" 
                  className="resize-none" 
                  {...field} 
                />
              </FormControl>
              <FormDescription>
                Any additional information about the group.
              </FormDescription>
              {form.formState.errors[field.name] && (
                <FormMessage className="text-red-500 text-sm mt-1" />
              )}
            </FormItem>
          )}
        />
        <Button type="submit" disabled={formState === "submitting"} className="w-full">
          {formState === "submitting" ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            "Create Order Group"
          )}
        </Button>
      </form>
    </Form>
  )

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>Create New Group</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Order Group</DialogTitle>
          <DialogDescription>
            Fill in the details to create a new order group.
          </DialogDescription>
        </DialogHeader>
        {formState === "idle" && formContent}
        {formState === "success" && (
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <CheckCircle className="w-16 h-16 text-green-500" />
            <h3 className="text-2xl font-semibold">Order Group Created Successfully</h3>
            <p>Your new order group has been created and is ready for use.</p>
            <Button onClick={closeDialog}>Close</Button>
          </div>
        )}
        {formState === "error" && (
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <XCircle className="w-16 h-16 text-red-500" />
            <h3 className="text-2xl font-semibold">Error Creating Order Group</h3>
            <p>There was an error creating the order group. Please try again.</p>
            <Button onClick={() => setFormState("idle")}>Try Again</Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

