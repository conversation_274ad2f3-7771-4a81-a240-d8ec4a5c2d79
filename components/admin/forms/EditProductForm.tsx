"use client"

import React, { useState } from "react"
import { useF<PERSON>, FormProvider } from "react-hook-form"
import { z } from "zod"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { useUpdateProduct, UpdateProductInput } from "@/lib/frontendProductUtilities"
import { BasicInfoStep } from "./steps/BasicInfoStep"
import { ImageUploadStep } from "./steps/ImageUploadStep"
import { PreviewStep } from "./steps/PreviewStep"
import { Product } from "@/types/product"

const productSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  description: z.string().min(10, "Description must be at least 10 characters."),
  price: z.number().positive("Price must be a positive number."),
  category: z.string().nonempty("Category is required."),
  stock: z.number().int().nonnegative("Stock must be a non-negative integer."),
  image: z
    .instanceof(File)
    .optional()
    .refine(
      (file) => !file || file.size <= 5000000, 
      `Max image size is 5MB.`
    )
    .refine(
      (file) => !file || ["image/jpeg", "image/png", "image/webp"].includes(file.type),
      "Only .jpg, .png, and .webp formats are supported.",
    ),
})

type ProductFormType = z.infer<typeof productSchema>

export function EditProductForm({ 
  product, 
  onSuccess 
}: { 
  product: Product, 
  onSuccess?: () => void 
}) {
  const [step, setStep] = useState(0)
  const updateProductMutation = useUpdateProduct()
  const methods = useForm<ProductFormType>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: product.name,
      description: product.description,
      price: product.price,
      category: product.category._id,
      stock: product.stock,
    },
  })

  const onSubmit = async (data: ProductFormType) => {
    try {
      const updateInput: UpdateProductInput = {
        id: product._id,
        updateData: {
          name: data.name,
          description: data.description,
          price: data.price,
          category: data.category,
          stock: data.stock,
        }
      }

      // Convert File to string (upload to server or use existing logic)
      if (data.image) {
        const formData = new FormData()
        formData.append('image', data.image)
        
        try {
          // Use the existing image upload logic from create product
          const response = await fetch('/api/images/upload', {
            method: 'POST',
            body: formData
          })
          
          if (!response.ok) {
            throw new Error('Image upload failed')
          }
          
          const { filename } = await response.json()
          updateInput.updateData.image = filename
        } catch (uploadError) {
          console.error('Image upload error:', uploadError)
          // Optionally handle upload failure
          return
        }
      }

      await updateProductMutation.mutateAsync(updateInput)
      methods.reset()
      onSuccess?.()
    } catch (error) {
      console.error("Failed to update Product", error)
    }
  }

  const steps = [
    { title: "Basic Information", component: BasicInfoStep },
    { title: "Image Upload", component: ImageUploadStep },
    { title: "Preview", component: PreviewStep },
  ]

  const CurrentStep = steps[step].component

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
        <div className="space-y-8">
          <CurrentStep />
        </div>
        <div className="flex justify-between">
          {step > 0 && (
            <Button type="button" onClick={() => setStep((prev) => prev - 1)}>
              Previous
            </Button>
          )}
          {step < steps.length - 1 ? (
            <Button type="button" onClick={() => setStep((prev) => prev + 1)}>
              Next
            </Button>
          ) : (
            <Button type="submit" disabled={updateProductMutation.isPending}>
              {updateProductMutation.isPending ? "Updating..." : "Update Product"}
            </Button>
          )}
        </div>
      </form>
    </FormProvider>
  )
}
