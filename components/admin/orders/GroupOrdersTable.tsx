// // "use client"

// // import { useState } from "react"
// // import {
// //   Table,
// //   TableBody,
// //   TableCell,
// //   TableHead,
// //   TableHeader,
// //   TableRow,
// // } from "@/components/ui/table"
// // import { Button } from "@/components/ui/button"
// // import {
// //   Select,
// //   SelectContent,
// //   SelectItem,
// //   SelectTrigger,
// //   SelectValue,
// // } from "@/components/ui/select"

// // const groups = [
// //   { id: "1", name: "Mamelodi Buyers" },
// //   { id: "2", name: "Soweto Savers" },
// //   { id: "3", name: "Tembisa Team" },
// //   { id: "4", name: "Midrand Grocers" },
// //   { id: "5", name: "Centurion Circle" },
// // ]

// // const groupOrders = [
// //   {
// //     id: "GRP001",
// //     customer: "Mamelodi Buyers",
// //     date: "2023-05-01",
// //     total: "R 12,345.67",
// //     status: "Processing",
// //     members: 15,
// //   },
// //   {
// //     id: "GRP002",
// //     customer: "Soweto Savers",
// //     date: "2023-05-02",
// //     total: "R 9,876.54",
// //     status: "Completed",
// //     members: 12,
// //   },
// //   {
// //     id: "GRP003",
// //     customer: "Tembisa Team",
// //     date: "2023-05-03",
// //     total: "R 15,678.90",
// //     status: "Shipped",
// //     members: 18,
// //   },
// //   {
// //     id: "GRP004",
// //     customer: "Midrand Grocers",
// //     date: "2023-05-04",
// //     total: "R 8,765.43",
// //     status: "Processing",
// //     members: 10,
// //   },
// //   {
// //     id: "GRP005",
// //     customer: "Centurion Circle",
// //     date: "2023-05-05",
// //     total: "R 11,223.34",
// //     status: "Completed",
// //     members: 14,
// //   },
// // ]

// // export function GroupOrdersTable() {
// //   const [selectedGroup, setSelectedGroup] = useState<string | undefined>(
// //     undefined
// //   )

// //   const filteredOrders = selectedGroup
// //     ? groupOrders.filter((order) => order.customer === selectedGroup)
// //     : groupOrders

// //   return (
// //     <div className="space-y-4">
// //       <div className="flex justify-between items-center">
// //         <h2 className="text-2xl font-semibold tracking-tight">Group Orders</h2>
// //         <Select onValueChange={setSelectedGroup}>
// //           <SelectTrigger className="w-[180px]">
// //             <SelectValue placeholder="Select group" />
// //           </SelectTrigger>
// //           <SelectContent>
// //             <SelectItem value="">All Groups</SelectItem>
// //             {groups.map((group) => (
// //               <SelectItem key={group.id} value={group.name}>
// //                 {group.name}
// //               </SelectItem>
// //             ))}
// //           </SelectContent>
// //         </Select>
// //       </div>
// //       <div className="rounded-md border">
// //         <Table>
// //           <TableHeader>
// //             <TableRow>
// //               <TableHead className="w-[100px]">Order ID</TableHead>
// //               <TableHead>Group</TableHead>
// //               <TableHead>Date</TableHead>
// //               <TableHead>Total</TableHead>
// //               <TableHead>Status</TableHead>
// //               <TableHead>Members</TableHead>
// //               <TableHead className="text-right">Actions</TableHead>
// //             </TableRow>
// //           </TableHeader>
// //           <TableBody>
// //             {filteredOrders.map((order) => (
// //               <TableRow key={order.id}>
// //                 <TableCell className="font-medium">{order.id}</TableCell>
// //                 <TableCell>{order.customer}</TableCell>
// //                 <TableCell>{order.date}</TableCell>
// //                 <TableCell>{order.total}</TableCell>
// //                 <TableCell>{order.status}</TableCell>
// //                 <TableCell>{order.members}</TableCell>
// //                 <TableCell className="text-right">
// //                   <Button variant="ghost">View Details</Button>
// //                 </TableCell>
// //               </TableRow>
// //             ))}
// //           </TableBody>
// //         </Table>
// //       </div>
// //     </div>
// //   )
// // }




// "use client"

// import { useState } from "react"
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table"
// import { Button } from "@/components/ui/button"
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select"

// const groups = [
//   { id: "1", name: "Mamelodi Buyers" },
//   { id: "2", name: "Soweto Savers" },
//   { id: "3", name: "Tembisa Team" },
//   { id: "4", name: "Midrand Grocers" },
//   { id: "5", name: "Centurion Circle" },
// ]

// const groupOrders = [
//   {
//     id: "GRP001",
//     customer: "Mamelodi Buyers",
//     date: "2023-05-01",
//     total: "R 12,345.67",
//     status: "Processing",
//     members: 15,
//   },
//   {
//     id: "GRP002",
//     customer: "Soweto Savers",
//     date: "2023-05-02",
//     total: "R 9,876.54",
//     status: "Completed",
//     members: 12,
//   },
//   {
//     id: "GRP003",
//     customer: "Tembisa Team",
//     date: "2023-05-03",
//     total: "R 15,678.90",
//     status: "Shipped",
//     members: 18,
//   },
//   {
//     id: "GRP004",
//     customer: "Midrand Grocers",
//     date: "2023-05-04",
//     total: "R 8,765.43",
//     status: "Processing",
//     members: 10,
//   },
//   {
//     id: "GRP005",
//     customer: "Centurion Circle",
//     date: "2023-05-05",
//     total: "R 11,223.34",
//     status: "Completed",
//     members: 14,
//   },
// ]

// export function GroupOrdersTable() {
//   const [selectedGroup, setSelectedGroup] = useState<string>("all")

//   const filteredOrders = selectedGroup === "all"
//     ? groupOrders
//     : groupOrders.filter((order) => order.customer === selectedGroup)

//   return (
//     <div className="space-y-4">
//       <div className="flex justify-between items-center">
//         <h2 className="text-2xl font-semibold tracking-tight">Group Orders</h2>
//         <Select value={selectedGroup} onValueChange={setSelectedGroup}>
//           <SelectTrigger className="w-[180px]">
//             <SelectValue placeholder="Select group" />
//           </SelectTrigger>
//           <SelectContent>
//             <SelectItem value="all">All Groups</SelectItem>
//             {groups.map((group) => (
//               <SelectItem key={group.id} value={group.name}>
//                 {group.name}
//               </SelectItem>
//             ))}
//           </SelectContent>
//         </Select>
//       </div>
//       <div className="rounded-md border">
//         <Table>
//           <TableHeader>
//             <TableRow>
//               <TableHead className="w-[100px]">Order ID</TableHead>
//               <TableHead>Group</TableHead>
//               <TableHead>Date</TableHead>
//               <TableHead>Total</TableHead>
//               <TableHead>Status</TableHead>
//               <TableHead>Members</TableHead>
//               <TableHead className="text-right">Actions</TableHead>
//             </TableRow>
//           </TableHeader>
//           <TableBody>
//             {filteredOrders.map((order) => (
//               <TableRow key={order.id}>
//                 <TableCell className="font-medium">{order.id}</TableCell>
//                 <TableCell>{order.customer}</TableCell>
//                 <TableCell>{order.date}</TableCell>
//                 <TableCell>{order.total}</TableCell>
//                 <TableCell>{order.status}</TableCell>
//                 <TableCell>{order.members}</TableCell>
//                 <TableCell className="text-right">
//                   <Button variant="ghost">View Details</Button>
//                 </TableCell>
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </div>
//     </div>
//   )
// }



"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { OrderGroupForm } from "@/components/admin/forms/OrderGroupForm"

const groups = [
  { id: "1", name: "Mamelodi Buyers" },
  { id: "2", name: "Soweto Savers" },
  { id: "3", name: "Tembisa Team" },
  { id: "4", name: "Midrand Grocers" },
  { id: "5", name: "Centurion Circle" },
]

const groupOrders = [
  {
    id: "GRP001",
    customer: "Mamelodi Buyers",
    date: "2023-05-01",
    total: "R 12,345.67",
    status: "Processing",
    members: 15,
  },
  {
    id: "GRP002",
    customer: "Soweto Savers",
    date: "2023-05-02",
    total: "R 9,876.54",
    status: "Completed",
    members: 12,
  },
  {
    id: "GRP003",
    customer: "Tembisa Team",
    date: "2023-05-03",
    total: "R 15,678.90",
    status: "Shipped",
    members: 18,
  },
  {
    id: "GRP004",
    customer: "Midrand Grocers",
    date: "2023-05-04",
    total: "R 8,765.43",
    status: "Processing",
    members: 10,
  },
  {
    id: "GRP005",
    customer: "Centurion Circle",
    date: "2023-05-05",
    total: "R 11,223.34",
    status: "Completed",
    members: 14,
  },
]

export function GroupOrdersTable() {
  const [selectedGroup, setSelectedGroup] = useState<string>("all")

  const filteredOrders = selectedGroup === "all"
    ? groupOrders
    : groupOrders.filter((order) => order.customer === selectedGroup)

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold tracking-tight">Group Orders</h2>
        <div className="flex items-center space-x-2">
          <Select value={selectedGroup} onValueChange={setSelectedGroup}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select group" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Groups</SelectItem>
              {groups.map((group) => (
                <SelectItem key={group.id} value={group.name}>
                  {group.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <OrderGroupForm />
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Order ID</TableHead>
              <TableHead>Group</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Members</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOrders.map((order) => (
              <TableRow key={order.id}>
                <TableCell className="font-medium">{order.id}</TableCell>
                <TableCell>{order.customer}</TableCell>
                <TableCell>{order.date}</TableCell>
                <TableCell>{order.total}</TableCell>
                <TableCell>{order.status}</TableCell>
                <TableCell>{order.members}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost">View Details</Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

