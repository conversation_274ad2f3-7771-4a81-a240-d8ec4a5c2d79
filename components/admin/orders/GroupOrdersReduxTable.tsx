'use client'

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { OrderGroupForm } from "@/components/admin/forms/OrderGroupForm"
import { useGetAllStokvelGroupsQuery as useGetAllGroupsQuery } from "@/lib/redux/features/groups/groupsApiSlice"
import { useGetAllGroupOrdersQuery } from "@/lib/redux/features/cart/cartApiSlice"
import { Skeleton } from "@/components/ui/skeleton"

export function GroupOrdersReduxTable() {
  const { data: groups, isLoading: isLoadingGroups } = useGetAllGroupsQuery();
  const { data: orders, isLoading: isLoadingOrders } = useGetAllGroupOrdersQuery();

  const [selectedGroup, setSelectedGroup] = useState<string>("all");

  // Handle loading state
  if (isLoadingGroups || isLoadingOrders) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-40" />
          <Skeleton className="h-10 w-64" />
        </div>
        <div className="rounded-md border">
          <div className="h-[400px] relative">
            <Skeleton className="absolute inset-0" />
          </div>
        </div>
      </div>
    );
  }

  // Handle no data
  if (!groups || !orders) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium">No orders found</h3>
        <p className="text-muted-foreground">Start shopping to create orders</p>
      </div>
    );
  }

  // Transform orders to match the expected format
  const transformedOrders = orders.map(order => ({
    id: order._id.substring(0, 8),
    customer: groups.find(g => g._id === order.groupId)?.name || 'Unknown Group',
    date: new Date(order.orderPlacedAt).toLocaleDateString(),
    total: `R ${order.totalOrderValue.toFixed(2)}`,
    status: order.status,
    members: order.userContributions.length
  }));

  // Filter orders by selected group
  const filteredOrders = selectedGroup === "all"
    ? transformedOrders
    : transformedOrders.filter((order) => {
        const group = groups.find(g => g.name === order.customer);
        return group && group._id === selectedGroup;
      });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Group Orders</h2>
        <div className="flex items-center space-x-4">
          <Select
            value={selectedGroup}
            onValueChange={setSelectedGroup}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by group" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Groups</SelectItem>
              {groups.map((group) => (
                <SelectItem key={group._id} value={group._id}>
                  {group.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <OrderGroupForm />
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Order ID</TableHead>
              <TableHead>Group</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Members</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOrders.length > 0 ? (
              filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>{order.customer}</TableCell>
                  <TableCell>{order.date}</TableCell>
                  <TableCell>{order.total}</TableCell>
                  <TableCell>{order.status}</TableCell>
                  <TableCell>{order.members}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No orders found for the selected group
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
