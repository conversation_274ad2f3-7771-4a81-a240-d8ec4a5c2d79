"use client"

import { TrendingUp, Users, ShoppingBag, DollarSign } from 'lucide-react'

const stats = [
  {
    title: "Total Revenue",
    value: "R242.65K",
    description: "From the running month",
    trend: "+12.5%",
    icon: DollarSign,
    color: "from-violet-500 to-purple-500"
  },
  {
    title: "Total Orders",
    value: "17,347",
    description: "Daily orders this month",
    trend: "+8.2%",
    icon: ShoppingBag,
    color: "from-blue-500 to-cyan-500"
  },
  {
    title: "Total Customers",
    value: "74.86%",
    description: "+6.04% from last month",
    trend: "+6.04%",
    icon: Users,
    color: "from-emerald-500 to-teal-500"
  },
  {
    title: "Average Sale",
    value: "R1,245",
    description: "Per transaction",
    trend: "+3.2%",
    icon: TrendingUp,
    color: "from-orange-500 to-red-500"
  }
]

export function DashboardStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <div
          key={index}
          className={`relative overflow-hidden rounded-xl bg-gradient-to-br ${stat.color} p-6 text-white shadow-lg`}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium opacity-75">{stat.title}</p>
              <p className="text-2xl font-semibold mt-2">{stat.value}</p>
            </div>
            <div className="rounded-full bg-white/20 p-2">
              <stat.icon className="h-6 w-6" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className="font-medium text-white">{stat.description}</span>
          </div>
          <div className="absolute bottom-0 right-0 opacity-10">
            <stat.icon className="h-24 w-24 -mb-6 -mr-6" />
          </div>
        </div>
      ))}
    </div>
  )
}

