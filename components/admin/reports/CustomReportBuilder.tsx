"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Plus, 
  Trash2, 
  Save, 
  Play, 
  Calendar as CalendarIcon,
  Clock,
  FileText,
  BarChart3,
  Pie<PERSON><PERSON>,
  LineChart,
  Table,
  Download,
  Mail,
  Settings,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';

// Types for report builder
interface ReportConfig {
  id?: string;
  name: string;
  description: string;
  type: 'revenue' | 'customers' | 'inventory' | 'performance' | 'custom';
  format: 'pdf' | 'excel' | 'csv' | 'json';
  schedule?: ReportSchedule;
  filters: ReportFilters;
  metrics: string[];
  visualizations: VisualizationConfig[];
  recipients: string[];
}

interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  timezone: string;
}

interface ReportFilters {
  dateRange: {
    from: Date;
    to: Date;
    type: 'fixed' | 'rolling';
    rollingPeriod?: number;
  };
  categories: string[];
  products: string[];
  customers: string[];
  groups: string[];
  status: string[];
}

interface VisualizationConfig {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'area' | 'table' | 'metric';
  title: string;
  dataSource: string;
  xAxis?: string;
  yAxis?: string;
  groupBy?: string;
  aggregation?: 'sum' | 'avg' | 'count' | 'max' | 'min';
  position: { row: number; col: number; width: number; height: number };
}

export function CustomReportBuilder() {
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    name: '',
    description: '',
    type: 'revenue',
    format: 'pdf',
    filters: {
      dateRange: {
        from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        to: new Date(),
        type: 'rolling',
        rollingPeriod: 30
      },
      categories: [],
      products: [],
      customers: [],
      groups: [],
      status: []
    },
    metrics: [],
    visualizations: [],
    recipients: []
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [availableMetrics, setAvailableMetrics] = useState<string[]>([]);
  const [availableDataSources, setAvailableDataSources] = useState<string[]>([]);
  const [saving, setSaving] = useState(false);
  const [generating, setGenerating] = useState(false);

  // Available options
  const reportTypes = [
    { value: 'revenue', label: 'Revenue Analysis' },
    { value: 'customers', label: 'Customer Analytics' },
    { value: 'inventory', label: 'Inventory Report' },
    { value: 'performance', label: 'Performance Metrics' },
    { value: 'custom', label: 'Custom Report' }
  ];

  const formatOptions = [
    { value: 'pdf', label: 'PDF Document' },
    { value: 'excel', label: 'Excel Spreadsheet' },
    { value: 'csv', label: 'CSV File' },
    { value: 'json', label: 'JSON Data' }
  ];

  const visualizationTypes = [
    { value: 'line', label: 'Line Chart', icon: LineChart },
    { value: 'bar', label: 'Bar Chart', icon: BarChart3 },
    { value: 'pie', label: 'Pie Chart', icon: PieChart },
    { value: 'area', label: 'Area Chart', icon: LineChart },
    { value: 'table', label: 'Data Table', icon: Table },
    { value: 'metric', label: 'Key Metric', icon: FileText }
  ];

  const aggregationOptions = [
    { value: 'sum', label: 'Sum' },
    { value: 'avg', label: 'Average' },
    { value: 'count', label: 'Count' },
    { value: 'max', label: 'Maximum' },
    { value: 'min', label: 'Minimum' }
  ];

  useEffect(() => {
    // Load available metrics and data sources
    setAvailableMetrics([
      'Total Revenue',
      'Order Count',
      'Customer Count',
      'Average Order Value',
      'Conversion Rate',
      'Customer Lifetime Value',
      'Product Sales',
      'Category Performance',
      'Group Order Value',
      'Discount Effectiveness'
    ]);

    setAvailableDataSources([
      'Orders',
      'Customers',
      'Products',
      'Categories',
      'Groups',
      'Payments',
      'Analytics'
    ]);
  }, []);

  // Handle form updates
  const updateReportConfig = (field: keyof ReportConfig, value: any) => {
    setReportConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateFilters = (field: keyof ReportFilters, value: any) => {
    setReportConfig(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [field]: value
      }
    }));
  };

  // Add visualization
  const addVisualization = () => {
    const newViz: VisualizationConfig = {
      id: `viz_${Date.now()}`,
      type: 'bar',
      title: 'New Chart',
      dataSource: 'Orders',
      xAxis: 'date',
      yAxis: 'revenue',
      aggregation: 'sum',
      position: { row: 0, col: 0, width: 6, height: 4 }
    };

    setReportConfig(prev => ({
      ...prev,
      visualizations: [...prev.visualizations, newViz]
    }));
  };

  // Remove visualization
  const removeVisualization = (id: string) => {
    setReportConfig(prev => ({
      ...prev,
      visualizations: prev.visualizations.filter(viz => viz.id !== id)
    }));
  };

  // Update visualization
  const updateVisualization = (id: string, updates: Partial<VisualizationConfig>) => {
    setReportConfig(prev => ({
      ...prev,
      visualizations: prev.visualizations.map(viz =>
        viz.id === id ? { ...viz, ...updates } : viz
      )
    }));
  };

  // Save report configuration
  const saveReportConfig = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/admin/reports/config', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reportConfig)
      });

      if (response.ok) {
        const result = await response.json();
        setReportConfig(prev => ({ ...prev, id: result.data.id }));
        // Show success message
      }
    } catch (error) {
      console.error('Error saving report config:', error);
    } finally {
      setSaving(false);
    }
  };

  // Generate report preview
  const generatePreview = async () => {
    setGenerating(true);
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/admin/reports/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          config: reportConfig,
          preview: true
        })
      });

      if (response.ok) {
        const result = await response.json();
        // Handle preview display
      }
    } catch (error) {
      console.error('Error generating preview:', error);
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Custom Report Builder</h1>
          <p className="text-muted-foreground">
            Create and customize reports with drag-and-drop widgets
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={generatePreview} disabled={generating}>
            <Eye className="h-4 w-4 mr-2" />
            {generating ? 'Generating...' : 'Preview'}
          </Button>
          <Button onClick={saveReportConfig} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Report'}
          </Button>
        </div>
      </div>

      {/* Report Builder Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="filters">Filters</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="visualizations">Charts</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Report Configuration</CardTitle>
              <CardDescription>Basic information about your report</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Report Name</Label>
                  <Input
                    id="name"
                    value={reportConfig.name}
                    onChange={(e) => updateReportConfig('name', e.target.value)}
                    placeholder="Enter report name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="type">Report Type</Label>
                  <Select value={reportConfig.type} onValueChange={(value) => updateReportConfig('type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {reportTypes.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={reportConfig.description}
                  onChange={(e) => updateReportConfig('description', e.target.value)}
                  placeholder="Describe what this report covers"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="format">Export Format</Label>
                  <Select value={reportConfig.format} onValueChange={(value) => updateReportConfig('format', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {formatOptions.map(format => (
                        <SelectItem key={format.value} value={format.value}>
                          {format.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="recipients">Email Recipients</Label>
                  <Input
                    id="recipients"
                    value={reportConfig.recipients.join(', ')}
                    onChange={(e) => updateReportConfig('recipients', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                    placeholder="<EMAIL>, <EMAIL>"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Metrics Tab */}
        <TabsContent value="metrics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Select Metrics</CardTitle>
              <CardDescription>Choose the metrics to include in your report</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableMetrics.map(metric => (
                  <div key={metric} className="flex items-center space-x-2">
                    <Checkbox
                      id={metric}
                      checked={reportConfig.metrics.includes(metric)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateReportConfig('metrics', [...reportConfig.metrics, metric]);
                        } else {
                          updateReportConfig('metrics', reportConfig.metrics.filter(m => m !== metric));
                        }
                      }}
                    />
                    <Label htmlFor={metric} className="text-sm font-medium">
                      {metric}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Schedule Tab */}
        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Report Scheduling</CardTitle>
              <CardDescription>Set up automated report generation and delivery</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enable-schedule"
                  checked={!!reportConfig.schedule}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateReportConfig('schedule', {
                        frequency: 'weekly',
                        time: '09:00',
                        timezone: 'Africa/Johannesburg'
                      });
                    } else {
                      updateReportConfig('schedule', undefined);
                    }
                  }}
                />
                <Label htmlFor="enable-schedule">Enable automatic report generation</Label>
              </div>

              {reportConfig.schedule && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div className="space-y-2">
                    <Label>Frequency</Label>
                    <Select
                      value={reportConfig.schedule.frequency}
                      onValueChange={(value) => updateReportConfig('schedule', {
                        ...reportConfig.schedule,
                        frequency: value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Time</Label>
                    <Input
                      type="time"
                      value={reportConfig.schedule.time}
                      onChange={(e) => updateReportConfig('schedule', {
                        ...reportConfig.schedule,
                        time: e.target.value
                      })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Timezone</Label>
                    <Select
                      value={reportConfig.schedule.timezone}
                      onValueChange={(value) => updateReportConfig('schedule', {
                        ...reportConfig.schedule,
                        timezone: value
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Africa/Johannesburg">South Africa (SAST)</SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                        <SelectItem value="America/New_York">Eastern Time</SelectItem>
                        <SelectItem value="Europe/London">London Time</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Visualizations Tab */}
        <TabsContent value="visualizations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Charts & Visualizations</CardTitle>
                  <CardDescription>Add charts and tables to your report</CardDescription>
                </div>
                <Button onClick={addVisualization}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Chart
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportConfig.visualizations.map((viz, index) => (
                  <Card key={viz.id} className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="font-medium">Chart {index + 1}</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeVisualization(viz.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Chart Type</Label>
                        <Select
                          value={viz.type}
                          onValueChange={(value) => updateVisualization(viz.id, { type: value as any })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {visualizationTypes.map(type => (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center">
                                  <type.icon className="h-4 w-4 mr-2" />
                                  {type.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Title</Label>
                        <Input
                          value={viz.title}
                          onChange={(e) => updateVisualization(viz.id, { title: e.target.value })}
                          placeholder="Chart title"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Data Source</Label>
                        <Select
                          value={viz.dataSource}
                          onValueChange={(value) => updateVisualization(viz.id, { dataSource: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {availableDataSources.map(source => (
                              <SelectItem key={source} value={source}>
                                {source}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </Card>
                ))}

                {reportConfig.visualizations.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No charts added yet. Click "Add Chart" to get started.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
