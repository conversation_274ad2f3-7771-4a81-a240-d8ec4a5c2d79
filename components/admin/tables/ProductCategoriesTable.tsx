"use client"

import React, { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Edit2Icon, TrashIcon, EyeIcon, LoaderIcon } from 'lucide-react'
import { useGetCategoriesQuery, useUpdateCategoryMutation, useDeleteCategoryMutation } from '@/lib/redux/features/categories/categoriesApiSlice'

// Extended interface for ProductCategory
export interface ExtendedProductCategory {
  _id: string
  name: string
  description?: string
  is_active: boolean
  product_count?: number
  parent_category?: string
  createdAt: Date
  updatedAt: Date
}

export function ProductCategoriesTable() {
  const { data: categories = [], isLoading } = useGetCategoriesQuery()
  const [updateCategoryMutation] = useUpdateCategoryMutation()
  const [deleteCategoryMutation] = useDeleteCategoryMutation()

  const [editingCategory, setEditingCategory] = useState<{
    id: string,
    name: string,
    description?: string
  } | null>(null)

  const [deletingCategory, setDeletingCategory] = useState<{
    id: string,
    name: string
  } | null>(null)

  const [selectedCategory, setSelectedCategory] = useState<ExtendedProductCategory | null>(null)

  // New state for loading actions
  const [isUpdating, setIsUpdating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Function to format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!editingCategory) return

    try {
      setIsUpdating(true)
      await updateCategoryMutation({
        id: editingCategory.id,
        updateData: {
          name: editingCategory.name,
          description: editingCategory.description
        }
      })

      toast.success('Category Updated', {
        description: `Category "${editingCategory.name}" has been successfully updated.`
      })

      setEditingCategory(null)
    } catch (error) {
      toast.error('Failed to Update Category', {
        description: error instanceof Error ? error.message : 'An unknown error occurred'
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDeleteConfirmation = async () => {
    if (!deletingCategory) return

    try {
      setIsDeleting(true)
      // Use deleteCategoryMutation
      await deleteCategoryMutation(deletingCategory.id)

      toast.success('Category Archived', {
        description: `Category "${deletingCategory.name}" has been successfully archived.`
      })

      setDeletingCategory(null)
    } catch (error) {
      toast.error('Failed to Archive Category', {
        description: error instanceof Error ? error.message : 'An unknown error occurred'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoading) {
    return <div>Loading categories...</div>
  }

  if (!categories || categories.length === 0) {
    return <div>No categories found.</div>
  }

  return (
    <>
      {/* Edit Category Dialog */}
      <Dialog
        open={!!editingCategory}
        onOpenChange={() => setEditingCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update the details of the selected category.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit} className="space-y-4">
            <div>
              <Label htmlFor="categoryName">Category Name</Label>
              <Input
                id="categoryName"
                value={editingCategory?.name || ''}
                onChange={(e) => setEditingCategory(prev =>
                  prev ? { ...prev, name: e.target.value } : null
                )}
                required
              />
            </div>
            <div>
              <Label htmlFor="categoryDescription">Description (Optional)</Label>
              <Input
                id="categoryDescription"
                value={editingCategory?.description || ''}
                onChange={(e) => setEditingCategory(prev =>
                  prev ? { ...prev, description: e.target.value } : null
                )}
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditingCategory(null)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Category Confirmation Dialog */}
      <Dialog
        open={!!deletingCategory}
        onOpenChange={() => setDeletingCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the category &quot;{deletingCategory?.name}&quot;?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeletingCategory(null)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirmation}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Confirm Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Category Dialog */}
      <Dialog
        open={!!selectedCategory}
        onOpenChange={() => setSelectedCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedCategory?.name}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <p><strong>Description:</strong> {selectedCategory?.description}</p>
            <p><strong>Product Count:</strong> {selectedCategory?.product_count ?? 0}</p>
            <p><strong>Status:</strong> {selectedCategory?.is_active ? 'Active' : 'Inactive'}</p>
            <p><strong>Parent Category:</strong> {selectedCategory?.parent_category || 'None'}</p>
            <p><strong>Created At:</strong> {selectedCategory && formatDate(selectedCategory.createdAt)}</p>
            <p><strong>Updated At:</strong> {selectedCategory && formatDate(selectedCategory.updatedAt)}</p>
          </div>
        </DialogContent>
      </Dialog>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Products Count</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Updated At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {categories.map((category) => (
            <TableRow key={category._id}>
              <TableCell>{category.name}</TableCell>
              <TableCell>{category.description}</TableCell>
              <TableCell>{category.product_count ?? 0}</TableCell>
              <TableCell>{category.is_active ? 'Active' : 'Inactive'}</TableCell>
              <TableCell>{formatDate(category.updatedAt)}</TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedCategory(category as unknown as ExtendedProductCategory)}
                  >
                    <EyeIcon className="h-4 w-4 mr-2" /> View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingCategory({
                      id: category._id,
                      name: category.name,
                      description: category.description
                    })}
                  >
                    <Edit2Icon className="h-4 w-4 mr-2" /> Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => setDeletingCategory({
                      id: category._id,
                      name: category.name
                    })}
                  >
                    <TrashIcon className="h-4 w-4 mr-2" /> Delete
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </>
  )
}
