"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Share } from 'lucide-react'

const stores = [
  {
    name: "Solaris Sparkle",
    location: "Johannesburg, Gauteng",
    quantity: "102",
    amount: "R12,500"
  },
  {
    name: "Crimson Dusk",
    location: "Pretoria, Gauteng",
    quantity: "214",
    amount: "R7,850"
  },
  {
    name: "Indigo Zephyr",
    location: "Cape Town, Western Cape",
    quantity: "143",
    amount: "R16,400"
  },
  {
    name: "Roseate Crest",
    location: "Durban, KwaZulu-Natal",
    quantity: "185",
    amount: "R23,640"
  }
]

export function TopStoresTable() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Top Performing Stores</CardTitle>
        <Button variant="outline" size="sm">
          <Share className="mr-2 h-4 w-4" />
          Share
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Store Name</TableHead>
              <TableHead>Location</TableHead>
              <TableHead className="text-right">Quantity</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {stores.map((store) => (
              <TableRow key={store.name}>
                <TableCell className="font-medium">{store.name}</TableCell>
                <TableCell>{store.location}</TableCell>
                <TableCell className="text-right">{store.quantity}</TableCell>
                <TableCell className="text-right">{store.amount}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

