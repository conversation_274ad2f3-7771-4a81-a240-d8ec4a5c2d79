"use client";

import { useState } from "react";
import Image from "next/image";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Eye, Trash2Icon } from "lucide-react";
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { Product } from "@/types/product";
import { EditProductModal } from "@/components/admin/forms/EditProductModal";
import { DeleteProductModal } from "@/components/admin/forms/DeleteProductModal";

export function ProductsTable() {
  const { data: products = [], isLoading, isError } = useGetAllProductsQuery();
  const { data: categories = [] } = useGetCategoriesQuery();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);

  if (isLoading) return <div>Loading products...</div>;
  if (isError) return <div>Error loading products</div>;

  // Updated: Handles populated ProductCategory objects or ID strings.
  const getCategoryName = (category: Product["category"]) => {
    if (typeof category === "string") {
      // When category is a string ID, find its name.
      const foundCategory = categories.find((cat) => cat._id === category);
      return foundCategory ? foundCategory.name : "Unknown Category";
    } else {
      // When category is a populated object.
      return category.name;
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Image</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Stock</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product._id}>
              <TableCell>
                <Image
                  src={`/api/images/${product.image}`}
                  alt={product.name}
                  width={50}
                  height={50}
                  className="object-cover rounded-md"
                />
              </TableCell>
              <TableCell className="font-medium">{product.name}</TableCell>
              <TableCell>{getCategoryName(product.category)}</TableCell>
              <TableCell>R {product.price.toFixed(2)}</TableCell>
              <TableCell>{product.stock}</TableCell>
              <TableCell>{formatDate(product.createdAt)}</TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-white shadow-sm" onClick={() => setSelectedProduct(product)}>
                        <Eye className="h-4 w-4 text-black" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>{selectedProduct?.name}</DialogTitle>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="aspect-square relative">
                          <Image
                            src={selectedProduct ? `/api/images/${selectedProduct.image}` : ""}
                            alt={selectedProduct?.name || ""}
                            fill
                            className="object-cover rounded-md"
                          />
                        </div>
                        <p>
                          <strong>Description:</strong> {selectedProduct?.description}
                        </p>
                        <p>
                          <strong>Category:</strong> {selectedProduct && getCategoryName(selectedProduct.category)}
                        </p>
                        <p>
                          <strong>Price:</strong> R {selectedProduct?.price.toFixed(2)}
                        </p>
                        <p>
                          <strong>Stock:</strong> {selectedProduct?.stock}
                        </p>
                        <p>
                          <strong>Created At:</strong> {selectedProduct && formatDate(selectedProduct.createdAt)}
                        </p>
                        <p>
                          <strong>Updated At:</strong> {selectedProduct && formatDate(selectedProduct.updatedAt)}
                        </p>
                      </div>
                    </DialogContent>
                  </Dialog>
                  <EditProductModal product={product} />
                  <Button
                    size="sm"
                    className="bg-white shadow-sm"
                    onClick={() => setProductToDelete(product)}
                  >
                    <Trash2Icon className="h-4 w-4 text-black" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {productToDelete && (
        <DeleteProductModal
          product={productToDelete}
          onClose={() => setProductToDelete(null)}
        />
      )}
    </div>
  );
}
