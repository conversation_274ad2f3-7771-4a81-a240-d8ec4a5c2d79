"use client"

import React, { useState, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { useGetArchivedProductsQuery, useRestoreArchivedProductMutation } from '@/lib/redux/features/products/productsApiSlice'
import Image from 'next/image'
import { formatCurrency } from '@/lib/utils'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { ArchiveRestoreIcon, Eye } from 'lucide-react'
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice"
import type { Product } from "@/types/product"

// Extend Product type to include archived information
interface ArchivedProduct extends Product {
  archivedAt?: string | Date;
}

// Utility function to validate and normalize image URL
const getValidImageUrl = (imageUrl?: string | null): string => {
  if (!imageUrl) return '/placeholder-product.png'

  try {
    // Check if it's a relative path or a full URL
    if (imageUrl.startsWith('/') || imageUrl.startsWith('http')) {
      return imageUrl
    }

    // If it looks like a filename, assume it's in the public directory
    return `/api/images/${imageUrl}`
  } catch {
    return '/placeholder-product.png'
  }
}

export function ArchivedProductsTable() {
  const {
    data: archivedProducts = [],
    isLoading,
    isError
  } = useGetArchivedProductsQuery();
  const [restoreProduct] = useRestoreArchivedProductMutation();
  const { data: categories = [] } = useGetCategoriesQuery();
  const [selectedProduct, setSelectedProduct] = useState<ArchivedProduct | null>(null);

  // Ensure archivedProducts is an array with valid products
  const safeArchivedProducts = useMemo(() => {
    if (!Array.isArray(archivedProducts)) {
      console.warn('❗ archivedProducts is not an array');
      return [];
    }

    // Filter out products without required properties
    const validProducts = archivedProducts.filter((product): product is ArchivedProduct =>
      product &&
      typeof product._id === 'string' &&
      typeof product.name === 'string'
    );

    console.log('✅ Valid Products:', validProducts.length);
    return validProducts;
  }, [archivedProducts]);

  // More verbose error and loading states
  if (isLoading) {
    console.log('⏳ ArchivedProductsTable: Loading state');
    return <div>Loading archived products...</div>;
  }

  if (isError) {
    console.error('❌ ArchivedProductsTable: Error loading archived products');
    return <div>Error loading archived products</div>;
  }

  if (safeArchivedProducts.length === 0) {
    console.warn('🚫 ArchivedProductsTable: No valid archived products found');
    return <div>No archived products found</div>;
  }

  // Reuse category name logic from ProductsTable
  const getCategoryName = (category: Product["category"]) => {
    if (typeof category === "string") {
      const foundCategory = categories.find((cat) => cat._id === category);
      return foundCategory ? foundCategory.name : "Unknown Category";
    } else {
      return category?.name || "Uncategorized";
    }
  };

  const formatDate = (date: Date | string | undefined | null) => {
    // Handle undefined, null, or empty object
    if (date == null || (typeof date === 'object' && Object.keys(date).length === 0)) return 'N/A';

    // If it's already a Date object, use it
    if (date instanceof Date) return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    // If it's a string, try to parse it
    if (typeof date === 'string') {
      const parsedDate = new Date(date);
      // Check if the parsed date is valid
      return !isNaN(parsedDate.getTime())
        ? parsedDate.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })
        : 'Invalid Date';
    }

    // If it's an object or any other type, return 'Invalid Date'
    return 'Invalid Date';
  };

  const handleRestoreConfirmation = async (product: ArchivedProduct) => {
    console.log('Restore confirmation clicked', product)
    try {
      await restoreProduct(product._id)
      toast.success('Product successfully restored', {
        description: `"${product.name}" has been moved back to the active products list.`
      })
    } catch (error) {
      toast.error('Failed to restore product', {
        description: error instanceof Error ? error.message : 'An unknown error occurred'
      })
    }
  }

  // Render table only if we have valid products
  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Image</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Stock</TableHead>
            <TableHead>Archived At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {safeArchivedProducts.map((product) => (
            <TableRow key={product._id}>
              <TableCell>
                <div className="relative h-16 w-16 overflow-hidden rounded-md">
                  <Image
                    src={getValidImageUrl(product.image)}
                    alt={product.name || 'Product Image'}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      const imgElement = e.target as HTMLImageElement;
                      imgElement.onerror = null; // Prevent infinite loop
                      imgElement.src = '/placeholder-product.png';
                    }}
                  />
                </div>
              </TableCell>
              <TableCell className="font-medium">{product.name}</TableCell>
              <TableCell>{getCategoryName(product.category)}</TableCell>
              <TableCell>{formatCurrency(product.price)}</TableCell>
              <TableCell>{product.stock}</TableCell>
              <TableCell>{formatDate(product.archivedAt)}</TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        size="sm"
                        className="bg-white shadow-sm"
                        onClick={() => setSelectedProduct(product)}
                      >
                        <Eye className="h-4 w-4 text-black" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>{selectedProduct?.name}</DialogTitle>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="aspect-square relative">
                          <Image
                            src={getValidImageUrl(selectedProduct?.image)}
                            alt={selectedProduct?.name || ""}
                            fill
                            className="object-cover rounded-md"
                          />
                        </div>
                        <p>
                          <strong>Description:</strong> {selectedProduct?.description}
                        </p>
                        <p>
                          <strong>Category:</strong> {selectedProduct && getCategoryName(selectedProduct.category)}
                        </p>
                        <p>
                          <strong>Archived At:</strong> {selectedProduct && formatDate(selectedProduct.archivedAt)}
                        </p>
                      </div>
                    </DialogContent>
                  </Dialog>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleRestoreConfirmation(product)}
                  >
                    <ArchiveRestoreIcon className="h-4 w-4 mr-2" />
                    Restore
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
