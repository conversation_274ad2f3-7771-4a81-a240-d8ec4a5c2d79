// "use client";

// import { useState } from "react";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { ArrowUpDown, Search } from 'lucide-react';

// interface GroupMembersTableProps {
//   members: { id: string; totalOrders: string; totalSpent: string }[];
// }

// const GroupMembersTable: React.FC<GroupMembersTableProps> = ({ members }) => {
//   console.log(members);

//   const [sortColumn, setSortColumn] = useState("");
//   const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

//   const sortMembers = (column: string) => {
//     if (column === sortColumn) {
//       setSortDirection(sortDirection === "asc" ? "desc" : "asc");
//     } else {
//       setSortColumn(column);
//       setSortDirection("asc");
//     }
//   };

//   return (
//     <div className="space-y-4">
//       <div className="flex justify-between items-center">
//         <h2 className="text-2xl font-semibold tracking-tight">Group Members</h2>
//         <div className="flex items-center space-x-2">
//           <Input placeholder="Search members..." className="max-w-sm" />
//           <Button variant="outline" size="icon">
//             <Search className="h-4 w-4" />
//           </Button>
//         </div>
//       </div>
//       <div className="rounded-md border">
//         <Table>
//           <TableHeader>
//             <TableRow>
//               <TableHead className="w-[100px]">Member ID</TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("totalOrders")}>Total Orders
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("totalSpent")}>Total Spent
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead className="text-right">Actions</TableHead>
//             </TableRow>
//           </TableHeader>
//           <TableBody>
//             {members.map((member, index) => (
//               <TableRow key={index}>
//                 <TableCell className="font-medium">{member.id}</TableCell>
//                 <TableCell>{member.totalOrders}</TableCell>
//                 <TableCell>{member.totalSpent}</TableCell>
//                 <TableCell className="text-right">
//                 </TableCell>
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </div>
//     </div>
//   );
// };

// export default GroupMembersTable;



// "use client";

// import { useState } from "react";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { ArrowUpDown, Search } from 'lucide-react';

// // Update the interface to accept string members or objects
// interface GroupMembersTableProps {
//   members: string[] | { id: string; totalOrders: string; totalSpent: string }[];
// }

// const GroupMembersTable: React.FC<GroupMembersTableProps> = ({ members }) => {
//   console.log(members);

//   const [sortColumn, setSortColumn] = useState("");
//   const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
//   const [searchTerm, setSearchTerm] = useState("");

//   const sortMembers = (column: string) => {
//     if (column === sortColumn) {
//       setSortDirection(sortDirection === "asc" ? "desc" : "asc");
//     } else {
//       setSortColumn(column);
//       setSortDirection("asc");
//     }
//   };

//   // Handle different member formats
//   const normalizedMembers = members.map(member => {
//     if (typeof member === 'string') {
//       // If member is a string (ID), create default object structure
//       return {
//         id: member,
//         totalOrders: "0", // Default value
//         totalSpent: "R0.00" // Default value with currency
//       };
//     }
//     return member; // Already in correct format
//   });

//   // Filter members based on search term
//   const filteredMembers = normalizedMembers.filter(member =>
//     member.id.toLowerCase().includes(searchTerm.toLowerCase())
//   );

//   // Sort members based on selected column
//   const sortedMembers = [...filteredMembers].sort((a, b) => {
//     if (!sortColumn) return 0;
    
//     const aVal = a[sortColumn as keyof typeof a];
//     const bVal = b[sortColumn as keyof typeof b];
    
//     if (sortColumn === "totalSpent") {
//       // Remove currency symbol for numeric comparison
//       const aNum = parseFloat(aVal.toString().replace(/[^0-9.-]+/g, ""));
//       const bNum = parseFloat(bVal.toString().replace(/[^0-9.-]+/g, ""));
//       return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
//     }
    
//     if (sortColumn === "totalOrders") {
//       const aNum = parseInt(aVal.toString());
//       const bNum = parseInt(bVal.toString());
//       return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
//     }
    
//     // String comparison
//     const compare = aVal.toString().localeCompare(bVal.toString());
//     return sortDirection === "asc" ? compare : -compare;
//   });

//   return (
//     <div className="space-y-4">
//       <div className="flex justify-between items-center">
//         <h2 className="text-2xl font-semibold tracking-tight">Group Members</h2>
//         <div className="flex items-center space-x-2">
//           <Input 
//             placeholder="Search members..." 
//             className="max-w-sm" 
//             value={searchTerm}
//             onChange={(e) => setSearchTerm(e.target.value)}
//           />
//           <Button variant="outline" size="icon">
//             <Search className="h-4 w-4" />
//           </Button>
//         </div>
//       </div>
//       <div className="rounded-md border">
//         <Table>
//           <TableHeader>
//             <TableRow>
//               <TableHead className="w-[100px]">Member ID</TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("totalOrders")}>
//                   Total Orders
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("totalSpent")}>
//                   Total Spent
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead className="text-right">Actions</TableHead>
//             </TableRow>
//           </TableHeader>
//           <TableBody>
//             {sortedMembers.length > 0 ? (
//               sortedMembers.map((member, index) => (
//                 <TableRow key={index}>
//                   <TableCell className="font-medium">{member.id}</TableCell>
//                   <TableCell>{member.totalOrders}</TableCell>
//                   <TableCell>{member.totalSpent}</TableCell>
//                   <TableCell className="text-right">
//                     <Button variant="outline" size="sm">View Profile</Button>
//                   </TableCell>
//                 </TableRow>
//               ))
//             ) : (
//               <TableRow>
//                 <TableCell colSpan={4} className="text-center py-6">
//                   No members found
//                 </TableCell>
//               </TableRow>
//             )}
//           </TableBody>
//         </Table>
//       </div>
//     </div>
//   );
// };

// export default GroupMembersTable;


// "use client";

// import { useState, useEffect } from "react";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { ArrowUpDown, Search } from 'lucide-react';

// // Updated interface to include user details
// interface GroupMember {
//   userId: string;
//   name: string;
//   email: string;
//   totalOrders?: string;
//   totalSpent?: string;
//   joinedAt?: string;
// }

// interface GroupMembersTableProps {
//   groupId: string;
// }

// const GroupMembersTable: React.FC<GroupMembersTableProps> = ({ groupId }) => {
//   const [members, setMembers] = useState<GroupMember[]>([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const [sortColumn, setSortColumn] = useState("");
//   const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
//   const [searchTerm, setSearchTerm] = useState("");

//   useEffect(() => {
//     const fetchMembers = async () => {
//       try {
//         setIsLoading(true);
//         const response = await fetch(`/api/groups/${groupId}/all-members`);
//         if (!response.ok) {
//           throw new Error('Failed to fetch members');
//         }
//         const data = await response.json();
//         setMembers(data);
//       } catch (error) {
//         console.error('Error fetching members:', error);
//       } finally {
//         setIsLoading(false);
//       }
//     };

//     if (groupId) {
//       fetchMembers();
//     }
//   }, [groupId]);

//   const sortMembers = (column: string) => {
//     if (column === sortColumn) {
//       setSortDirection(sortDirection === "asc" ? "desc" : "asc");
//     } else {
//       setSortColumn(column);
//       setSortDirection("asc");
//     }
//   };

//   // Filter members based on search term
//   const filteredMembers = members.filter(member =>
//     member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
//     member.email.toLowerCase().includes(searchTerm.toLowerCase())
//   );

//   // Sort members based on selected column
//   const sortedMembers = [...filteredMembers].sort((a, b) => {
//     if (!sortColumn) return 0;
    
//     const aVal = a[sortColumn as keyof typeof a] || '';
//     const bVal = b[sortColumn as keyof typeof b] || '';
    
//     if (sortColumn === "totalSpent") {
//       // Remove currency symbol for numeric comparison
//       const aNum = parseFloat(aVal.toString().replace(/[^0-9.-]+/g, "") || "0");
//       const bNum = parseFloat(bVal.toString().replace(/[^0-9.-]+/g, "") || "0");
//       return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
//     }
    
//     if (sortColumn === "totalOrders") {
//       const aNum = parseInt(aVal.toString() || "0");
//       const bNum = parseInt(bVal.toString() || "0");
//       return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
//     }
    
//     // String comparison for other columns
//     const compare = String(aVal).localeCompare(String(bVal));
//     return sortDirection === "asc" ? compare : -compare;
//   });

//   return (
//     <div className="space-y-4">
//       <div className="flex justify-between items-center">
//         <h2 className="text-2xl font-semibold tracking-tight">Group Members</h2>
//         <div className="flex items-center space-x-2">
//           <Input 
//             placeholder="Search members..." 
//             className="max-w-sm" 
//             value={searchTerm}
//             onChange={(e) => setSearchTerm(e.target.value)}
//           />
//           <Button variant="outline" size="icon">
//             <Search className="h-4 w-4" />
//           </Button>
//         </div>
//       </div>
//       <div className="rounded-md border">
//         <Table>
//           <TableHeader>
//             <TableRow>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("name")}>
//                   Name
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("email")}>
//                   Email
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("totalOrders")}>
//                   Total Orders
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("totalSpent")}>
//                   Total Spent
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => sortMembers("joinedAt")}>
//                   Joined
//                   <ArrowUpDown className="ml-2 h-4 w-4" />
//                 </Button>
//               </TableHead>
//               <TableHead className="text-right">Actions</TableHead>
//             </TableRow>
//           </TableHeader>
//           <TableBody>
//             {isLoading ? (
//               <TableRow>
//                 <TableCell colSpan={6} className="text-center py-6">
//                   Loading members...
//                 </TableCell>
//               </TableRow>
//             ) : sortedMembers.length > 0 ? (
//               sortedMembers.map((member, index) => (
//                 <TableRow key={index}>
//                   <TableCell className="font-medium">{member.name || 'N/A'}</TableCell>
//                   <TableCell>{member.email || 'N/A'}</TableCell>
//                   <TableCell>{member.totalOrders || '0'}</TableCell>
//                   <TableCell>{member.totalSpent || 'R0.00'}</TableCell>
//                   <TableCell>{member.joinedAt ? new Date(member.joinedAt).toLocaleDateString() : 'N/A'}</TableCell>
//                   <TableCell className="text-right">
//                     <Button variant="outline" size="sm">View Profile</Button>
//                   </TableCell>
//                 </TableRow>
//               ))
//             ) : (
//               <TableRow>
//                 <TableCell colSpan={6} className="text-center py-6">
//                   No members found
//                 </TableCell>
//               </TableRow>
//             )}
//           </TableBody>
//         </Table>
//       </div>
//     </div>
//   );
// };

// export default GroupMembersTable;


"use client";

import { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowUpDown, Search, AlertCircle, Loader2 } from 'lucide-react';
import { useGetGroupMembersQuery, type GroupMember } from '@/lib/redux/features/groups/groupsApiSlice';

// GroupMember interface is now imported from the API slice

interface GroupMembersTableProps {
  groupId: string;
}

const GroupMembersTable: React.FC<GroupMembersTableProps> = ({ groupId }) => {
  // Redux query hook for fetching group members
  const {
    data: members = [],
    isLoading,
    error,
    refetch
  } = useGetGroupMembersQuery(groupId, {
    skip: !groupId, // Skip the query if groupId is not provided
  });

  const [sortColumn, setSortColumn] = useState("");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [searchTerm, setSearchTerm] = useState("");

  const sortMembers = (column: string) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // Memoized filtered and sorted members for better performance
  const sortedMembers = useMemo(() => {
    // Filter members based on search term
    const filteredMembers = members.filter(member =>
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.phone.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort members based on selected column
    return [...filteredMembers].sort((a, b) => {
      if (!sortColumn) return 0;

      const aVal = a[sortColumn as keyof typeof a] || '';
      const bVal = b[sortColumn as keyof typeof b] || '';

      if (sortColumn === "totalSpent") {
        // Remove currency symbol for numeric comparison
        const aNum = parseFloat(aVal.toString().replace(/[^0-9.-]+/g, "") || "0");
        const bNum = parseFloat(bVal.toString().replace(/[^0-9.-]+/g, "") || "0");
        return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
      }

      if (sortColumn === "totalOrders") {
        const aNum = parseInt(aVal.toString() || "0");
        const bNum = parseInt(bVal.toString() || "0");
        return sortDirection === "asc" ? aNum - bNum : bNum - aNum;
      }

      // String comparison for other columns
      const compare = String(aVal).localeCompare(String(bVal));
      return sortDirection === "asc" ? compare : -compare;
    });
  }, [members, searchTerm, sortColumn, sortDirection]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold tracking-tight">Group Members</h2>
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Search members..."
            className="max-w-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Button variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
          {error && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Retry'
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load group members. Please try again.
            {error && 'error' in error && (
              <span className="block text-sm mt-1">
                {error.error || 'Unknown error occurred'}
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("name")}>
                  Name
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("email")}>
                  Email
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("phone")}>
                  Phone
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("totalOrders")}>
                  Orders
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("totalSpent")}>
                  Total Spent
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" onClick={() => sortMembers("joinedAt")}>
                  Joined
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-6">
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Loading members...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-6">
                  <div className="flex flex-col items-center space-y-2">
                    <AlertCircle className="h-8 w-8 text-muted-foreground" />
                    <span className="text-muted-foreground">Failed to load members</span>
                    <Button variant="outline" size="sm" onClick={() => refetch()}>
                      Try Again
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : sortedMembers.length > 0 ? (
              sortedMembers.map((member) => (
                <TableRow key={member.userId}>
                  <TableCell className="font-medium">{member.name || 'N/A'}</TableCell>
                  <TableCell>{member.email || 'N/A'}</TableCell>
                  <TableCell>{member.phone || 'N/A'}</TableCell>
                  <TableCell>{member.totalOrders || '0'}</TableCell>
                  <TableCell>{member.totalSpent || 'R0.00'}</TableCell>
                  <TableCell>{member.joinedAt ? new Date(member.joinedAt).toLocaleDateString() : 'N/A'}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">View Profile</Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-6">
                  <div className="flex flex-col items-center space-y-2">
                    <span className="text-muted-foreground">
                      {searchTerm ? 'No members match your search' : 'No members found'}
                    </span>
                    {searchTerm && (
                      <Button variant="outline" size="sm" onClick={() => setSearchTerm('')}>
                        Clear Search
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default GroupMembersTable;