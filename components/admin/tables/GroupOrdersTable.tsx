import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { MoreHorizontal, ArrowUpDown } from 'lucide-react';

interface GroupOrdersTableProps {
  orders: FrontendGroupOrder[];
}

interface FrontendGroupOrder {
    id: string;
    userContributions: { userName: string }[];
    totalOrderValue: number;
    orderPlacedAt: Date;
    status: string;
    paymentStatus: string;
}

const GroupOrdersTable: React.FC<GroupOrdersTableProps> = ({ orders }) => {
    const [sortColumn, setSortColumn] = useState<string>("");
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
    const [searchTerm, setSearchTerm] = useState<string>("");

    const handleSort = (column: string) => {
        if (sortColumn === column) {
            setSortDirection(sortDirection === "asc" ? "desc" : "asc");
        } else {
            setSortColumn(column);
            setSortDirection("asc");
        }
    };

    const filteredOrders = orders.filter((order: FrontendGroupOrder) =>
        order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.userContributions.some((contribution) =>
            contribution.userName.toLowerCase().includes(searchTerm.toLowerCase())
        )
    );

    const sortedOrders = filteredOrders.sort((a: FrontendGroupOrder, b: FrontendGroupOrder) => {
        let aValue: string | number;
        let bValue: string | number;

        switch (sortColumn) {
            case 'id':
                aValue = a.id;
                bValue = b.id;
                break;
            case 'orderPlacedAt':
                aValue = a.orderPlacedAt.getTime();
                bValue = b.orderPlacedAt.getTime();
                break;
            case 'totalOrderValue':
                aValue = a.totalOrderValue;
                bValue = b.totalOrderValue;
                break;
            case 'status':
                aValue = a.status;
                bValue = b.status;
                break;
            case 'paymentStatus':
                aValue = a.paymentStatus;
                bValue = b.paymentStatus;
                break;
            default:
                aValue = '';
                bValue = '';
        }

        return sortDirection === 'asc' ? (aValue > bValue ? 1 : -1) : (aValue < bValue ? 1 : -1);
    });

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-semibold tracking-tight">Group Orders</h2>
                <div className="flex items-center space-x-2">
                    <Input
                        placeholder="Search orders..."
                        className="max-w-sm"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button variant="outline" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                    </Button>
                </div>
            </div>
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[100px]">Order ID</TableHead>
                            <TableHead>Customer</TableHead>
                            <TableHead>
                                <Button variant="ghost" onClick={() => handleSort('orderPlacedAt')}>Date<ArrowUpDown className="ml-2 h-4 w-4" /></Button>
                            </TableHead>
                            <TableHead>
                                <Button variant="ghost" onClick={() => handleSort('totalOrderValue')}>Total<ArrowUpDown className="ml-2 h-4 w-4" /></Button>
                            </TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Payment</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedOrders.map((order) => (
                            <TableRow key={order.id}>
                                <TableCell className="font-medium">{order.id}</TableCell>
                                <TableCell>{order.userContributions.map(contribution => contribution.userName).join(', ')}</TableCell>
                                <TableCell>{new Date(order.orderPlacedAt).toLocaleDateString()}</TableCell>
                                <TableCell>{order.totalOrderValue}</TableCell>
                                <TableCell>{order.status}</TableCell>
                                <TableCell>{order.paymentStatus}</TableCell>
                                <TableCell className="text-right">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <span className="sr-only">Open menu</span>
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                            <DropdownMenuItem>View details</DropdownMenuItem>
                                            <DropdownMenuItem>Update status</DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem>Delete order</DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
};

export default GroupOrdersTable;
