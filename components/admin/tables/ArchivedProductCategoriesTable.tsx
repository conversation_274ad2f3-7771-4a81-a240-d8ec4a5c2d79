"use client"

import React, { useState } from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { LoaderIcon, RotateCcwIcon } from 'lucide-react'
import { 
  useGetArchivedProductCategories, 
  useRestoreArchivedProductCategory 
} from '@/lib/frontendProductCategoryUtilities'

export function ArchivedProductCategoriesTable() {
  const { 
    data: archivedCategories, 
    isLoading, 
    error 
  } = useGetArchivedProductCategories();

  const { 
    mutate: restoreCategory, 
    isPending: isRestoring 
  } = useRestoreArchivedProductCategory();

  const [restoringCategory, setRestoringCategory] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const handleRestore = (categoryId: string, categoryName: string) => {
    setRestoringCategory({ id: categoryId, name: categoryName });
  }

  const confirmRestore = () => {
    if (!restoringCategory) return;

    restoreCategory(restoringCategory.id, {
      onSuccess: () => {
        toast.success('Category Restored', {
          description: `Category "${restoringCategory.name}" has been successfully restored.`
        });
        setRestoringCategory(null);
      },
      onError: (error) => {
        toast.error('Failed to Restore Category', {
          description: error.message || 'An unknown error occurred'
        });
        setRestoringCategory(null);
      }
    });
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoaderIcon className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-destructive p-4">
        Failed to load archived categories: {error.message}
      </div>
    );
  }

  if (!archivedCategories || archivedCategories.length === 0) {
    return (
      <div className="text-center text-muted-foreground p-4">
        No archived categories found.
      </div>
    );
  }

  return (
    <>
      {/* Restore Confirmation Dialog */}
      <Dialog 
        open={!!restoringCategory} 
        onOpenChange={() => setRestoringCategory(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Restore Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to restore the category &ldquo;{restoringCategory?.name}&rdquo;?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setRestoringCategory(null)}
              disabled={isRestoring}
            >
              Cancel
            </Button>
            <Button 
              type="button" 
              onClick={confirmRestore}
              disabled={isRestoring}
            >
              {isRestoring ? (
                <>
                  <LoaderIcon className="h-4 w-4 mr-2 animate-spin" /> 
                  Restoring...
                </>
              ) : (
                <>
                  <RotateCcwIcon className="h-4 w-4 mr-2" />
                  Restore
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Archived Categories Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Product Count</TableHead>
            <TableHead>Archived At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {archivedCategories.map((category) => (
            <TableRow key={category._id}>
              <TableCell>{category.name}</TableCell>
              <TableCell>{category.description || 'No description'}</TableCell>
              <TableCell>{category.product_count || 0}</TableCell>
              <TableCell>
                {new Date(category.archivedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </TableCell>
              <TableCell>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleRestore(category._id, category.name)}
                >
                  <RotateCcwIcon className="h-4 w-4 mr-2" />
                  Restore
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </>
  );
}
