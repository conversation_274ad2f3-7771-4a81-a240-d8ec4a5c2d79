"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Download } from 'lucide-react'

const data = [
  { day: "Sun", sales: 32000, orders: 24000 },
  { day: "Mon", sales: 38000, orders: 28000 },
  { day: "Tue", sales: 45000, orders: 35000 },
  { day: "Wed", sales: 35000, orders: 25000 },
  { day: "Thu", sales: 42000, orders: 32000 },
  { day: "Fri", sales: 28000, orders: 20000 },
  { day: "Sat", sales: 40000, orders: 30000 },
]

export function SalesChart() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Weekly Sales Overview</CardTitle>
          <CardDescription>Daily sales and orders comparison</CardDescription>
        </div>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="sales" 
                stroke="#2A7C6C" 
                strokeWidth={2}
                name="Sales"
              />
              <Line 
                type="monotone" 
                dataKey="orders" 
                stroke="#94A3B8" 
                strokeWidth={2}
                name="Orders"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

