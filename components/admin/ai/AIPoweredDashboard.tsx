"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  Lightbulb,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  ShoppingCart,
  BarChart3,
  Settings,
  Play,
  Pause,
  RefreshCw,
  Star,
  ThumbsUp,
  ThumbsDown,
  Eye,
  ArrowRight,
  Sparkles,
  Cpu,
  Activity
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, PieChart, Pie, Cell } from 'recharts';

// Types for AI Dashboard
interface AIRecommendation {
  id: string;
  type: 'product' | 'pricing' | 'marketing' | 'inventory' | 'customer' | 'operational';
  priority: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  description: string;
  reasoning: string[];
  expectedImpact: {
    revenue: { min: number; max: number; unit: string };
    customers: { min: number; max: number; unit: string };
    efficiency: { min: number; max: number; unit: string };
    description: string;
  };
  confidence: number;
  estimatedROI: number;
  timeframe: string;
  category: string;
  status: 'pending' | 'in_progress' | 'completed' | 'dismissed';
  createdAt: Date;
}

interface PredictiveInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'opportunity' | 'risk';
  title: string;
  description: string;
  prediction: {
    metric: string;
    currentValue: number;
    predictedValue: number;
    changePercentage: number;
  };
  confidence: number;
  timeHorizon: string;
  affectedMetrics: string[];
  recommendedActions: string[];
}

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  executionCount: number;
  successRate: number;
  lastExecuted?: Date;
}

interface SmartAlert {
  id: string;
  type: 'performance' | 'security' | 'business' | 'system';
  severity: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  createdAt: Date;
  resolvedAt?: Date;
}

export function AIPoweredDashboard() {
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [insights, setInsights] = useState<PredictiveInsight[]>([]);
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [smartAlerts, setSmartAlerts] = useState<SmartAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // Fetch AI data
  const fetchAIData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      const [recsResponse, insightsResponse, automationResponse, alertsResponse] = await Promise.all([
        fetch('/api/admin/ai/recommendations', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('/api/admin/ai/insights', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('/api/admin/ai/automation', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch('/api/admin/ai/alerts', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (recsResponse.ok && insightsResponse.ok && automationResponse.ok && alertsResponse.ok) {
        const [recsData, insightsData, automationData, alertsData] = await Promise.all([
          recsResponse.json(),
          insightsResponse.json(),
          automationResponse.json(),
          alertsResponse.json()
        ]);

        setRecommendations(recsData.data || []);
        setInsights(insightsData.data || []);
        setAutomationRules(automationData.data || []);
        setSmartAlerts(alertsData.data || []);
      }
    } catch (error) {
      console.error('Error fetching AI data:', error);
      // Mock data for development
      setRecommendations(generateMockRecommendations());
      setInsights(generateMockInsights());
      setAutomationRules(generateMockAutomationRules());
      setSmartAlerts(generateMockSmartAlerts());
    } finally {
      setLoading(false);
    }
  };

  // Refresh AI data
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAIData();
    setRefreshing(false);
  };

  // Generate new recommendations
  const generateRecommendations = async () => {
    try {
      const token = localStorage.getItem('token');

      const response = await fetch('/api/admin/ai/recommendations', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'generate' })
      });

      if (response.ok) {
        await fetchAIData();
      }
    } catch (error) {
      console.error('Error generating recommendations:', error);
    }
  };

  useEffect(() => {
    fetchAIData();
  }, []);

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get insight type icon
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend': return TrendingUp;
      case 'anomaly': return AlertTriangle;
      case 'opportunity': return Target;
      case 'risk': return TrendingDown;
      default: return BarChart3;
    }
  };

  // Get insight type color
  const getInsightColor = (type: string) => {
    switch (type) {
      case 'trend': return 'text-blue-600 bg-blue-50';
      case 'anomaly': return 'text-red-600 bg-red-50';
      case 'opportunity': return 'text-green-600 bg-green-50';
      case 'risk': return 'text-orange-600 bg-orange-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  };

  // Calculate overview metrics
  const overviewMetrics = {
    totalRecommendations: recommendations.length,
    highPriorityRecs: recommendations.filter(r => r.priority === 'critical' || r.priority === 'high').length,
    implementedRecs: recommendations.filter(r => r.status === 'completed').length,
    totalPredictedImpact: recommendations.reduce((sum, r) => sum + r.expectedImpact.revenue.max, 0),
    activeAutomations: automationRules.filter(r => r.isActive).length,
    unresolvedAlerts: smartAlerts.filter(a => !a.resolvedAt).length,
    averageConfidence: recommendations.reduce((sum, r) => sum + r.confidence, 0) / recommendations.length || 0,
    averageROI: recommendations.reduce((sum, r) => sum + r.estimatedROI, 0) / recommendations.length || 0
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center">
            <Brain className="h-8 w-8 mr-3 text-purple-600" />
            AI-Powered Admin
          </h1>
          <p className="text-muted-foreground">
            Intelligent recommendations, predictive insights, and automated workflows
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={generateRecommendations}>
            <Sparkles className="h-4 w-4 mr-2" />
            Generate Insights
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Recommendations</CardTitle>
            <Lightbulb className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.totalRecommendations}</div>
            <p className="text-xs text-muted-foreground">
              {overviewMetrics.highPriorityRecs} high priority
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Predicted Impact</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(overviewMetrics.totalPredictedImpact)}</div>
            <p className="text-xs text-muted-foreground">
              Potential monthly revenue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Automations</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.activeAutomations}</div>
            <p className="text-xs text-muted-foreground">
              Running workflows
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Confidence</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.averageConfidence.toFixed(0)}%</div>
            <p className="text-xs text-muted-foreground">
              Average confidence score
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Smart Alerts */}
      {smartAlerts.filter(a => !a.resolvedAt).length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-semibold flex items-center">
            <Zap className="h-5 w-5 mr-2 text-yellow-500" />
            Smart Alerts
          </h3>
          {smartAlerts.filter(a => !a.resolvedAt).slice(0, 3).map((alert) => (
            <Card key={alert.id} className={`border-l-4 ${
              alert.severity === 'critical' ? 'border-l-red-500' :
              alert.severity === 'warning' ? 'border-l-yellow-500' : 'border-l-blue-500'
            }`}>
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant={alert.severity === 'critical' ? 'destructive' : 'secondary'}>
                        {alert.severity}
                      </Badge>
                      <Badge variant="outline">{alert.type}</Badge>
                    </div>
                    <h4 className="font-medium">{alert.title}</h4>
                    <p className="text-sm text-muted-foreground mt-1">{alert.message}</p>
                  </div>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Recommendation Categories</CardTitle>
                <CardDescription>Distribution of AI recommendations by type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Product', value: recommendations.filter(r => r.type === 'product').length, color: '#8884d8' },
                        { name: 'Pricing', value: recommendations.filter(r => r.type === 'pricing').length, color: '#82ca9d' },
                        { name: 'Marketing', value: recommendations.filter(r => r.type === 'marketing').length, color: '#ffc658' },
                        { name: 'Customer', value: recommendations.filter(r => r.type === 'customer').length, color: '#ff7300' }
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {[
                        { name: 'Product', value: recommendations.filter(r => r.type === 'product').length, color: '#8884d8' },
                        { name: 'Pricing', value: recommendations.filter(r => r.type === 'pricing').length, color: '#82ca9d' },
                        { name: 'Marketing', value: recommendations.filter(r => r.type === 'marketing').length, color: '#ffc658' },
                        { name: 'Customer', value: recommendations.filter(r => r.type === 'customer').length, color: '#ff7300' }
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>AI Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators for AI systems</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Average Confidence</span>
                    <span>{overviewMetrics.averageConfidence.toFixed(0)}%</span>
                  </div>
                  <Progress value={overviewMetrics.averageConfidence} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Implementation Rate</span>
                    <span>{((overviewMetrics.implementedRecs / overviewMetrics.totalRecommendations) * 100).toFixed(0)}%</span>
                  </div>
                  <Progress value={(overviewMetrics.implementedRecs / overviewMetrics.totalRecommendations) * 100} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Average ROI</span>
                    <span>{overviewMetrics.averageROI.toFixed(0)}%</span>
                  </div>
                  <Progress value={Math.min(overviewMetrics.averageROI, 200)} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{overviewMetrics.implementedRecs}</div>
                    <div className="text-sm text-muted-foreground">Implemented</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{overviewMetrics.activeAutomations}</div>
                    <div className="text-sm text-muted-foreground">Automations</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">AI Recommendations</h3>
            <Button onClick={generateRecommendations}>
              <Sparkles className="h-4 w-4 mr-2" />
              Generate New
            </Button>
          </div>

          <div className="grid gap-4">
            {recommendations.map((recommendation) => (
              <Card key={recommendation.id} className="relative">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={getPriorityColor(recommendation.priority)}>
                          {recommendation.priority}
                        </Badge>
                        <Badge variant="outline">{recommendation.type}</Badge>
                        <Badge variant="secondary">{recommendation.confidence}% confidence</Badge>
                      </div>
                      <CardTitle className="text-lg">{recommendation.title}</CardTitle>
                      <CardDescription>{recommendation.description}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button size="sm">
                        <Play className="h-4 w-4 mr-2" />
                        Implement
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-lg font-bold text-green-600">
                        {formatCurrency(recommendation.expectedImpact.revenue.min)} - {formatCurrency(recommendation.expectedImpact.revenue.max)}
                      </div>
                      <div className="text-sm text-muted-foreground">Expected Revenue</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-lg font-bold text-blue-600">{recommendation.estimatedROI}%</div>
                      <div className="text-sm text-muted-foreground">Estimated ROI</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="text-lg font-bold text-purple-600">{recommendation.timeframe}</div>
                      <div className="text-sm text-muted-foreground">Implementation Time</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">AI Reasoning:</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                      {recommendation.reasoning.map((reason, index) => (
                        <li key={index}>{reason}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Predictive Insights</h3>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Predictions
            </Button>
          </div>

          <div className="grid gap-4">
            {insights.map((insight) => {
              const Icon = getInsightIcon(insight.type);
              return (
                <Card key={insight.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${getInsightColor(insight.type)}`}>
                          <Icon className="h-5 w-5" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{insight.title}</CardTitle>
                          <CardDescription>{insight.description}</CardDescription>
                        </div>
                      </div>
                      <Badge variant="outline">{insight.confidence}% confidence</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-medium mb-3">Prediction Details</h4>
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Current Value:</span>
                            <span className="font-medium">{insight.prediction.currentValue.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Predicted Value:</span>
                            <span className="font-medium">{insight.prediction.predictedValue.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Change:</span>
                            <span className={`font-medium flex items-center ${
                              insight.prediction.changePercentage > 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {insight.prediction.changePercentage > 0 ? (
                                <TrendingUp className="h-4 w-4 mr-1" />
                              ) : (
                                <TrendingDown className="h-4 w-4 mr-1" />
                              )}
                              {Math.abs(insight.prediction.changePercentage)}%
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Time Horizon:</span>
                            <span className="font-medium">{insight.timeHorizon}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Recommended Actions</h4>
                        <ul className="space-y-2">
                          {insight.recommendedActions.map((action, index) => (
                            <li key={index} className="flex items-start gap-2 text-sm">
                              <ArrowRight className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
                              <span>{action}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Automation Tab */}
        <TabsContent value="automation" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Smart Automation</h3>
            <Button>
              <Settings className="h-4 w-4 mr-2" />
              Create Rule
            </Button>
          </div>

          <div className="grid gap-4">
            {automationRules.map((rule) => (
              <Card key={rule.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg flex items-center gap-2">
                        {rule.isActive ? (
                          <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                        ) : (
                          <div className="h-2 w-2 bg-gray-400 rounded-full"></div>
                        )}
                        {rule.name}
                      </CardTitle>
                      <CardDescription>{rule.description}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        {rule.isActive ? (
                          <>
                            <Pause className="h-4 w-4 mr-2" />
                            Pause
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-2" />
                            Start
                          </>
                        )}
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{rule.executionCount}</div>
                      <div className="text-sm text-muted-foreground">Executions</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{rule.successRate.toFixed(0)}%</div>
                      <div className="text-sm text-muted-foreground">Success Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {rule.lastExecuted ? (
                          <span className="text-sm">
                            {new Date(rule.lastExecuted).toLocaleDateString()}
                          </span>
                        ) : (
                          'Never'
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">Last Run</div>
                    </div>
                    <div className="text-center">
                      <Badge variant={rule.isActive ? 'default' : 'secondary'}>
                        {rule.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Mock data generators
function generateMockRecommendations(): AIRecommendation[] {
  return [
    {
      id: 'rec_1',
      type: 'product',
      priority: 'high',
      title: 'Expand Electronics Category',
      description: 'AI analysis suggests significant opportunity in electronics category expansion',
      reasoning: [
        'Electronics category shows 35% higher conversion rate than average',
        'Customer demand analysis indicates 60% unmet demand in smart home devices',
        'Competitor analysis shows market gap in mid-range electronics'
      ],
      expectedImpact: {
        revenue: { min: 25000, max: 45000, unit: 'ZAR/month' },
        customers: { min: 150, max: 300, unit: 'new customers' },
        efficiency: { min: 15, max: 25, unit: '% improvement' },
        description: 'Expected 20-35% revenue increase in electronics category'
      },
      confidence: 85,
      estimatedROI: 180,
      timeframe: '2-3 months',
      category: 'Product Strategy',
      status: 'pending',
      createdAt: new Date()
    },
    {
      id: 'rec_2',
      type: 'pricing',
      priority: 'medium',
      title: 'Optimize Group Discount Tiers',
      description: 'AI analysis suggests current discount structure is suboptimal',
      reasoning: [
        'Current 5-person tier shows 23% lower utilization than optimal',
        'Price elasticity analysis indicates opportunity for tier restructuring',
        'Customer behavior data shows preference for 3-person and 7-person groups'
      ],
      expectedImpact: {
        revenue: { min: 8000, max: 15000, unit: 'ZAR/month' },
        customers: { min: 50, max: 120, unit: 'additional participants' },
        efficiency: { min: 10, max: 18, unit: '% improvement' },
        description: 'Improved group formation rates and higher average order values'
      },
      confidence: 78,
      estimatedROI: 145,
      timeframe: '2-4 weeks',
      category: 'Pricing Strategy',
      status: 'pending',
      createdAt: new Date()
    },
    {
      id: 'rec_3',
      type: 'marketing',
      priority: 'high',
      title: 'Launch Targeted Re-engagement Campaign',
      description: 'AI identifies 89 high-value customers at risk of churning',
      reasoning: [
        'Churn prediction model identifies customers with 70%+ churn probability',
        'These customers represent R180,000 in potential lost revenue',
        'Historical data shows 65% success rate with targeted campaigns'
      ],
      expectedImpact: {
        revenue: { min: 45000, max: 75000, unit: 'ZAR retained' },
        customers: { min: 35, max: 58, unit: 'customers retained' },
        efficiency: { min: 20, max: 35, unit: '% churn reduction' },
        description: 'Significant reduction in customer churn and revenue retention'
      },
      confidence: 92,
      estimatedROI: 220,
      timeframe: '1-2 weeks',
      category: 'Customer Retention',
      status: 'pending',
      createdAt: new Date()
    }
  ];
}

function generateMockInsights(): PredictiveInsight[] {
  return [
    {
      id: 'insight_1',
      type: 'trend',
      title: 'Revenue Growth Acceleration Predicted',
      description: 'AI models predict 18% revenue growth over the next 30 days based on current trends',
      prediction: {
        metric: 'Monthly Revenue',
        currentValue: 125000,
        predictedValue: 147500,
        changePercentage: 18
      },
      confidence: 82,
      timeHorizon: '30 days',
      affectedMetrics: ['Revenue', 'Order Volume', 'Customer Acquisition'],
      recommendedActions: [
        'Increase inventory for high-demand categories',
        'Scale marketing campaigns for optimal ROI',
        'Prepare customer service for increased volume'
      ]
    },
    {
      id: 'insight_2',
      type: 'risk',
      title: 'Customer Churn Risk Identified',
      description: 'AI models identify 89 customers at high risk of churning in the next 30 days',
      prediction: {
        metric: 'Customer Churn Rate',
        currentValue: 5.2,
        predictedValue: 7.8,
        changePercentage: 50
      },
      confidence: 88,
      timeHorizon: '30 days',
      affectedMetrics: ['Customer Retention', 'Revenue', 'Lifetime Value'],
      recommendedActions: [
        'Launch targeted re-engagement campaign',
        'Provide personalized customer support',
        'Offer loyalty incentives to at-risk customers'
      ]
    },
    {
      id: 'insight_3',
      type: 'opportunity',
      title: 'High Demand Predicted for Home Category',
      description: 'AI forecasting indicates 45% increase in home category demand',
      prediction: {
        metric: 'Home Category Demand',
        currentValue: 100,
        predictedValue: 145,
        changePercentage: 45
      },
      confidence: 75,
      timeHorizon: '30 days',
      affectedMetrics: ['Inventory Turnover', 'Revenue', 'Customer Satisfaction'],
      recommendedActions: [
        'Increase home category inventory by 40%',
        'Negotiate better supplier terms for volume',
        'Prepare targeted marketing campaigns'
      ]
    }
  ];
}

function generateMockAutomationRules(): AutomationRule[] {
  return [
    {
      id: 'auto_1',
      name: 'Low Stock Alert & Reorder',
      description: 'Automatically alert and suggest reorders when inventory falls below threshold',
      isActive: true,
      executionCount: 45,
      successRate: 94,
      lastExecuted: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    },
    {
      id: 'auto_2',
      name: 'Customer Churn Prevention',
      description: 'Send personalized offers to customers identified as at-risk',
      isActive: true,
      executionCount: 23,
      successRate: 78,
      lastExecuted: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago
    },
    {
      id: 'auto_3',
      name: 'Weekly Performance Report',
      description: 'Generate and email weekly performance reports to stakeholders',
      isActive: true,
      executionCount: 12,
      successRate: 100,
      lastExecuted: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
    },
    {
      id: 'auto_4',
      name: 'Price Optimization Monitor',
      description: 'Monitor competitor prices and suggest adjustments',
      isActive: false,
      executionCount: 8,
      successRate: 67,
      lastExecuted: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 1 week ago
    }
  ];
}

function generateMockSmartAlerts(): SmartAlert[] {
  return [
    {
      id: 'alert_1',
      type: 'business',
      severity: 'warning',
      title: 'Unusual Order Pattern Detected',
      message: 'AI detected 35% increase in order cancellations in electronics category',
      createdAt: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
    },
    {
      id: 'alert_2',
      type: 'performance',
      severity: 'info',
      title: 'System Performance Optimization',
      message: 'AI suggests database query optimization for 15% performance improvement',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    },
    {
      id: 'alert_3',
      type: 'security',
      severity: 'critical',
      title: 'Suspicious Activity Pattern',
      message: 'AI detected potential fraudulent activity from IP range 192.168.1.x',
      createdAt: new Date(Date.now() - 45 * 60 * 1000) // 45 minutes ago
    }
  ];
}