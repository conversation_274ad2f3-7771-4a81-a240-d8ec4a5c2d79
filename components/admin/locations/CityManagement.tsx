// components/admin/locations/CityManagement.tsx

"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Plus, MoreHorizontal, Edit, Trash2, Eye, EyeOff, Building } from "lucide-react";
import { 
  useGetProvincesQuery,
  useGetCitiesByProvinceQuery 
} from "@/lib/redux/features/locations/locationsApiSlice";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { EnhancedProvinceSelect } from "@/components/ui/enhanced-province-select";
import type { City, CreateCityData, UpdateCityData } from "@/types/locations";

export function CityManagement() {
  const [selectedProvinceId, setSelectedProvinceId] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCity, setEditingCity] = useState<City | null>(null);
  const [createForm, setCreateForm] = useState<CreateCityData>({ name: "", provinceId: "" });
  const [editForm, setEditForm] = useState<UpdateCityData>({ id: "", name: "" });

  const { data: provincesData, isLoading: provincesLoading } = useGetProvincesQuery();
  const { data: citiesData, isLoading: citiesLoading } = useGetCitiesByProvinceQuery(
    selectedProvinceId,
    { skip: !selectedProvinceId }
  );
  const { createCity, updateCity, deleteCity } = useLocations();
  const { toast } = useToast();

  const provinces = provincesData?.provinces || [];
  const cities = citiesData?.cities || [];

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!createForm.name.trim() || !createForm.provinceId) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createCity(createForm);
      toast({
        title: "Success",
        description: "City created successfully.",
      });
      setCreateForm({ name: "", provinceId: "" });
      setIsCreateDialogOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to create city.",
        variant: "destructive",
      });
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editForm.name?.trim() && editForm.isActive === undefined) {
      toast({
        title: "Validation Error",
        description: "Please make at least one change.",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateCity({ ...editForm, provinceId: selectedProvinceId });
      toast({
        title: "Success",
        description: "City updated successfully.",
      });
      setEditForm({ id: "", name: "" });
      setEditingCity(null);
      setIsEditDialogOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to update city.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (city: City) => {
    setEditingCity(city);
    setEditForm({
      id: city._id,
      name: city.name,
      isActive: city.isActive,
    });
    setIsEditDialogOpen(true);
  };

  const handleToggleActive = async (city: City) => {
    try {
      await updateCity({
        id: city._id,
        isActive: !city.isActive,
        provinceId: selectedProvinceId,
      });
      toast({
        title: "Success",
        description: `City ${!city.isActive ? 'activated' : 'deactivated'} successfully.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to update city status.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (city: City) => {
    if (!confirm(`Are you sure you want to delete "${city.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteCity(selectedProvinceId, city._id);
      toast({
        title: "Success",
        description: "City deleted successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to delete city.",
        variant: "destructive",
      });
    }
  };

  const selectedProvince = provinces.find(p => p._id === selectedProvinceId);

  if (provincesLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Province Selection */}
      <div className="space-y-2">
        <Label>Select Province</Label>
        <EnhancedProvinceSelect
          provinces={provinces}
          value={selectedProvinceId}
          onValueChange={setSelectedProvinceId}
          placeholder="Choose a province to manage its cities"
          isLoading={provincesLoading}
        />
      </div>

      {selectedProvinceId && (
        <>
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Building className="h-5 w-5" />
                Cities in {selectedProvince?.name} ({cities.length})
              </h3>
              <p className="text-sm text-muted-foreground">
                Manage cities in {selectedProvince?.name} province
              </p>
            </div>
            
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#2A7C6C] hover:bg-[#236358]">
                  <Plus className="h-4 w-4 mr-2" />
                  Add City
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New City</DialogTitle>
                  <DialogDescription>
                    Add a new city to {selectedProvince?.name} province.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">City Name</Label>
                      <Input
                        id="name"
                        placeholder="e.g., Johannesburg"
                        value={createForm.name}
                        onChange={(e) => setCreateForm(prev => ({ 
                          ...prev, 
                          name: e.target.value,
                          provinceId: selectedProvinceId 
                        }))}
                        required
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]">
                      Create City
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Table */}
          {citiesLoading ? (
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Province</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cities.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        No cities found in {selectedProvince?.name}. Create your first city to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    cities.map((city) => (
                      <TableRow key={city._id}>
                        <TableCell className="font-medium">{city.name}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{selectedProvince?.name}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={city.isActive ? "default" : "secondary"}>
                            {city.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(city.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleEdit(city)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleActive(city)}>
                                {city.isActive ? (
                                  <>
                                    <EyeOff className="mr-2 h-4 w-4" />
                                    Deactivate
                                  </>
                                ) : (
                                  <>
                                    <Eye className="mr-2 h-4 w-4" />
                                    Activate
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleDelete(city)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit City</DialogTitle>
            <DialogDescription>
              Update city information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">City Name</Label>
                <Input
                  id="edit-name"
                  value={editForm.name || ""}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]">
                Update City
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
