// components/admin/locations/LocationManagement.tsx

"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Plus, MoreH<PERSON>zon<PERSON>, Edit, Trash2, Eye, EyeOff, MapPin } from "lucide-react";
import { 
  useGetProvincesQuery,
  useGetCitiesByProvinceQuery,
  useGetTownshipsByCityQuery,
  useGetLocationsByTownshipQuery 
} from "@/lib/redux/features/locations/locationsApiSlice";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { EnhancedProvinceSelect } from "@/components/ui/enhanced-province-select";
import type { Location, CreateLocationData, UpdateLocationData } from "@/types/locations";

export function LocationManagement() {
  const [selectedProvinceId, setSelectedProvinceId] = useState("");
  const [selectedCityId, setSelectedCityId] = useState("");
  const [selectedTownshipId, setSelectedTownshipId] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const [createForm, setCreateForm] = useState<CreateLocationData>({ name: "", townshipId: "", description: "" });
  const [editForm, setEditForm] = useState<UpdateLocationData>({ id: "", name: "", description: "" });

  const { data: provincesData, isLoading: provincesLoading } = useGetProvincesQuery();
  const { data: citiesData, isLoading: citiesLoading } = useGetCitiesByProvinceQuery(
    selectedProvinceId,
    { skip: !selectedProvinceId }
  );
  const { data: townshipsData, isLoading: townshipsLoading } = useGetTownshipsByCityQuery(
    selectedCityId,
    { skip: !selectedCityId }
  );
  const { data: locationsData, isLoading: locationsLoading } = useGetLocationsByTownshipQuery(
    selectedTownshipId,
    { skip: !selectedTownshipId }
  );
  const { createLocation, updateLocation, deleteLocation } = useLocations();
  const { toast } = useToast();

  const provinces = provincesData?.provinces || [];
  const cities = citiesData?.cities || [];
  const townships = townshipsData?.townships || [];
  const locations = locationsData?.locations || [];

  const handleProvinceChange = (provinceId: string) => {
    setSelectedProvinceId(provinceId);
    setSelectedCityId("");
    setSelectedTownshipId("");
  };

  const handleCityChange = (cityId: string) => {
    setSelectedCityId(cityId);
    setSelectedTownshipId("");
  };

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!createForm.name.trim() || !createForm.townshipId) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createLocation(createForm);
      toast({
        title: "Success",
        description: "Location created successfully.",
      });
      setCreateForm({ name: "", townshipId: "", description: "" });
      setIsCreateDialogOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to create location.",
        variant: "destructive",
      });
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editForm.name?.trim() && !editForm.description?.trim() && editForm.isActive === undefined) {
      toast({
        title: "Validation Error",
        description: "Please make at least one change.",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateLocation({ ...editForm, townshipId: selectedTownshipId });
      toast({
        title: "Success",
        description: "Location updated successfully.",
      });
      setEditForm({ id: "", name: "", description: "" });
      setEditingLocation(null);
      setIsEditDialogOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to update location.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (location: Location) => {
    setEditingLocation(location);
    setEditForm({
      id: location._id,
      name: location.name,
      description: location.description || "",
      isActive: location.isActive,
    });
    setIsEditDialogOpen(true);
  };

  const handleToggleActive = async (location: Location) => {
    try {
      await updateLocation({
        id: location._id,
        isActive: !location.isActive,
        townshipId: selectedTownshipId,
      });
      toast({
        title: "Success",
        description: `Location ${!location.isActive ? 'activated' : 'deactivated'} successfully.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to update location status.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (location: Location) => {
    if (!confirm(`Are you sure you want to delete "${location.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteLocation(selectedTownshipId, location._id);
      toast({
        title: "Success",
        description: "Location deleted successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to delete location.",
        variant: "destructive",
      });
    }
  };

  const selectedProvince = provinces.find(p => p._id === selectedProvinceId);
  const selectedCity = cities.find(c => c._id === selectedCityId);
  const selectedTownship = townships.find(t => t._id === selectedTownshipId);

  if (provincesLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Location Selection */}
      <div className="grid gap-4 md:grid-cols-3">
        <div className="space-y-2">
          <Label>Select Province</Label>
          <EnhancedProvinceSelect
            provinces={provinces}
            value={selectedProvinceId}
            onValueChange={handleProvinceChange}
            placeholder="Choose a province"
            isLoading={provincesLoading}
          />
        </div>

        <div className="space-y-2">
          <Label>Select City</Label>
          <Select 
            value={selectedCityId} 
            onValueChange={handleCityChange}
            disabled={!selectedProvinceId || citiesLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose a city" />
            </SelectTrigger>
            <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
              {cities.map((city) => (
                <SelectItem
                  key={city._id}
                  value={city._id}
                  className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                >
                  {city.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Select Township</Label>
          <Select 
            value={selectedTownshipId} 
            onValueChange={setSelectedTownshipId}
            disabled={!selectedCityId || townshipsLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose a township" />
            </SelectTrigger>
            <SelectContent>
              {townships.map((township) => (
                <SelectItem key={township._id} value={township._id}>
                  {township.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {selectedTownshipId && (
        <>
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Locations in {selectedTownship?.name} ({locations.length})
              </h3>
              <p className="text-sm text-muted-foreground">
                Manage locations in {selectedTownship?.name}, {selectedCity?.name}, {selectedProvince?.name}
              </p>
            </div>
            
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#2A7C6C] hover:bg-[#236358]">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Location
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Location</DialogTitle>
                  <DialogDescription>
                    Add a new location to {selectedTownship?.name}, {selectedCity?.name}.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Location Name</Label>
                      <Input
                        id="name"
                        placeholder="e.g., Orlando East"
                        value={createForm.name}
                        onChange={(e) => setCreateForm(prev => ({ 
                          ...prev, 
                          name: e.target.value,
                          townshipId: selectedTownshipId 
                        }))}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description (Optional)</Label>
                      <Textarea
                        id="description"
                        placeholder="Brief description of the location..."
                        value={createForm.description}
                        onChange={(e) => setCreateForm(prev => ({ 
                          ...prev, 
                          description: e.target.value 
                        }))}
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]">
                      Create Location
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Table */}
          {locationsLoading ? (
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Township</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {locations.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No locations found in {selectedTownship?.name}. Create your first location to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    locations.map((location) => (
                      <TableRow key={location._id}>
                        <TableCell className="font-medium">{location.name}</TableCell>
                        <TableCell className="max-w-xs truncate">
                          {location.description || <span className="text-muted-foreground">No description</span>}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{selectedTownship?.name}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={location.isActive ? "default" : "secondary"}>
                            {location.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(location.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleEdit(location)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleActive(location)}>
                                {location.isActive ? (
                                  <>
                                    <EyeOff className="mr-2 h-4 w-4" />
                                    Deactivate
                                  </>
                                ) : (
                                  <>
                                    <Eye className="mr-2 h-4 w-4" />
                                    Activate
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleDelete(location)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Location</DialogTitle>
            <DialogDescription>
              Update location information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Location Name</Label>
                <Input
                  id="edit-name"
                  value={editForm.name || ""}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={editForm.description || ""}
                  onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]">
                Update Location
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
