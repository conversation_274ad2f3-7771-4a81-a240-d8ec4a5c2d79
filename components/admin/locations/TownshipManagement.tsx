// components/admin/locations/TownshipManagement.tsx

"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Plus, MoreHorizontal, Edit, Trash2, Eye, EyeOff, Home } from "lucide-react";
import { 
  useGetProvincesQuery,
  useGetCitiesByProvinceQuery,
  useGetTownshipsByCityQuery 
} from "@/lib/redux/features/locations/locationsApiSlice";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { EnhancedProvinceSelect } from "@/components/ui/enhanced-province-select";
import type { Township, CreateTownshipData, UpdateTownshipData } from "@/types/locations";

export function TownshipManagement() {
  const [selectedProvinceId, setSelectedProvinceId] = useState("");
  const [selectedCityId, setSelectedCityId] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingTownship, setEditingTownship] = useState<Township | null>(null);
  const [createForm, setCreateForm] = useState<CreateTownshipData>({ name: "", cityId: "" });
  const [editForm, setEditForm] = useState<UpdateTownshipData>({ id: "", name: "" });

  const { data: provincesData, isLoading: provincesLoading } = useGetProvincesQuery();
  const { data: citiesData, isLoading: citiesLoading } = useGetCitiesByProvinceQuery(
    selectedProvinceId,
    { skip: !selectedProvinceId }
  );
  const { data: townshipsData, isLoading: townshipsLoading } = useGetTownshipsByCityQuery(
    selectedCityId,
    { skip: !selectedCityId }
  );
  const { createTownship, updateTownship, deleteTownship } = useLocations();
  const { toast } = useToast();

  const provinces = provincesData?.provinces || [];
  const cities = citiesData?.cities || [];
  const townships = townshipsData?.townships || [];

  const handleProvinceChange = (provinceId: string) => {
    setSelectedProvinceId(provinceId);
    setSelectedCityId("");
  };

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!createForm.name.trim() || !createForm.cityId) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      await createTownship(createForm);
      toast({
        title: "Success",
        description: "Township created successfully.",
      });
      setCreateForm({ name: "", cityId: "" });
      setIsCreateDialogOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to create township.",
        variant: "destructive",
      });
    }
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editForm.name?.trim() && editForm.isActive === undefined) {
      toast({
        title: "Validation Error",
        description: "Please make at least one change.",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateTownship({ ...editForm, cityId: selectedCityId });
      toast({
        title: "Success",
        description: "Township updated successfully.",
      });
      setEditForm({ id: "", name: "" });
      setEditingTownship(null);
      setIsEditDialogOpen(false);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to update township.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (township: Township) => {
    setEditingTownship(township);
    setEditForm({
      id: township._id,
      name: township.name,
      isActive: township.isActive,
    });
    setIsEditDialogOpen(true);
  };

  const handleToggleActive = async (township: Township) => {
    try {
      await updateTownship({
        id: township._id,
        isActive: !township.isActive,
        cityId: selectedCityId,
      });
      toast({
        title: "Success",
        description: `Township ${!township.isActive ? 'activated' : 'deactivated'} successfully.`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to update township status.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (township: Township) => {
    if (!confirm(`Are you sure you want to delete "${township.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteTownship(selectedCityId, township._id);
      toast({
        title: "Success",
        description: "Township deleted successfully.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to delete township.",
        variant: "destructive",
      });
    }
  };

  const selectedProvince = provinces.find(p => p._id === selectedProvinceId);
  const selectedCity = cities.find(c => c._id === selectedCityId);

  if (provincesLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Province Selection */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label>Select Province</Label>
          <EnhancedProvinceSelect
            provinces={provinces}
            value={selectedProvinceId}
            onValueChange={handleProvinceChange}
            placeholder="Choose a province"
            isLoading={provincesLoading}
          />
        </div>

        <div className="space-y-2">
          <Label>Select City</Label>
          <Select 
            value={selectedCityId} 
            onValueChange={setSelectedCityId}
            disabled={!selectedProvinceId || citiesLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose a city" />
            </SelectTrigger>
            <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
              {cities.map((city) => (
                <SelectItem
                  key={city._id}
                  value={city._id}
                  className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                >
                  {city.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {selectedCityId && (
        <>
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Home className="h-5 w-5" />
                Townships in {selectedCity?.name} ({townships.length})
              </h3>
              <p className="text-sm text-muted-foreground">
                Manage townships in {selectedCity?.name}, {selectedProvince?.name}
              </p>
            </div>
            
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#2A7C6C] hover:bg-[#236358]">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Township
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Township</DialogTitle>
                  <DialogDescription>
                    Add a new township to {selectedCity?.name}, {selectedProvince?.name}.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Township Name</Label>
                      <Input
                        id="name"
                        placeholder="e.g., Soweto"
                        value={createForm.name}
                        onChange={(e) => setCreateForm(prev => ({ 
                          ...prev, 
                          name: e.target.value,
                          cityId: selectedCityId 
                        }))}
                        required
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]">
                      Create Township
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Table */}
          {townshipsLoading ? (
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>City</TableHead>
                    <TableHead>Province</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {townships.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No townships found in {selectedCity?.name}. Create your first township to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    townships.map((township) => (
                      <TableRow key={township._id}>
                        <TableCell className="font-medium">{township.name}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{selectedCity?.name}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{selectedProvince?.name}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={township.isActive ? "default" : "secondary"}>
                            {township.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(township.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleEdit(township)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleActive(township)}>
                                {township.isActive ? (
                                  <>
                                    <EyeOff className="mr-2 h-4 w-4" />
                                    Deactivate
                                  </>
                                ) : (
                                  <>
                                    <Eye className="mr-2 h-4 w-4" />
                                    Activate
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleDelete(township)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Township</DialogTitle>
            <DialogDescription>
              Update township information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Township Name</Label>
                <Input
                  id="edit-name"
                  value={editForm.name || ""}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]">
                Update Township
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
