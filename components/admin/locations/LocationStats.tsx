// components/admin/locations/LocationStats.tsx

"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, Building, Home, Map, TrendingUp, Users, Activity, Globe, Layers, Navigation, Zap, Database, Loader2 } from "lucide-react";
import { useLocationStats } from "@/lib/redux/hooks/useLocations";
import { useGetAllStokvelGroupsQuery } from "@/lib/redux/features/groups/groupsApiSlice";
import { useSeedLocationsMutation, useMigrateGroupsMutation } from "@/lib/redux/features/locations/locationsApiSlice";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { useState } from "react";

export function LocationStats() {
  const { stats, isLoading: statsLoading, error: statsError } = useLocationStats();
  const { data: groupsData, isLoading: groupsLoading } = useGetAllStokvelGroupsQuery();
  const [seedLocations, { isLoading: isSeedingLoading }] = useSeedLocationsMutation();
  const [migrateGroups, { isLoading: isMigratingLoading }] = useMigrateGroupsMutation();
  const { toast } = useToast();
  const [isSeeding, setIsSeeding] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);

  const groups = groupsData || [];
  const groupsWithLocation = groups.filter(group => group.locationId);
  const groupsWithoutLocation = groups.filter(group => !group.locationId && group.geolocation);

  const handleSeedData = async () => {
    if (isSeeding || isSeedingLoading) return;

    setIsSeeding(true);
    try {
      const result = await seedLocations().unwrap();
      toast({
        title: "Success",
        description: result.message || "South African location data seeded successfully!",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to seed location data",
        variant: "destructive",
      });
    } finally {
      setIsSeeding(false);
    }
  };

  const handleMigrateGroups = async () => {
    if (isMigrating || isMigratingLoading) return;

    if (groupsWithoutLocation.length === 0) {
      toast({
        title: "No Migration Needed",
        description: "All groups are already using the new location system.",
      });
      return;
    }

    setIsMigrating(true);
    try {
      const result = await migrateGroups().unwrap();
      toast({
        title: "Success",
        description: `${result.message} Migrated: ${result.summary.migrated}, Created: ${result.summary.created}`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.data?.error || "Failed to migrate group locations",
        variant: "destructive",
      });
    } finally {
      setIsMigrating(false);
    }
  };

  if (statsLoading || groupsLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (statsError) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            Failed to load location statistics
          </div>
        </CardContent>
      </Card>
    );
  }

  const statCards = [
    {
      title: "Provinces",
      value: stats?.provinces || 0,
      description: "South African provinces",
      icon: Globe,
      gradient: "from-blue-500 to-cyan-500",
      bgGradient: "from-blue-50 to-cyan-50",
      level: "Level 1"
    },
    {
      title: "Cities",
      value: stats?.cities || 0,
      description: "Cities & municipalities",
      icon: Building,
      gradient: "from-emerald-500 to-teal-500",
      bgGradient: "from-emerald-50 to-teal-50",
      level: "Level 2"
    },
    {
      title: "Townships",
      value: stats?.townships || 0,
      description: "Townships & suburbs",
      icon: Layers,
      gradient: "from-orange-500 to-red-500",
      bgGradient: "from-orange-50 to-red-50",
      level: "Level 3"
    },
    {
      title: "Locations",
      value: stats?.locations || 0,
      description: "Specific locations",
      icon: Navigation,
      gradient: "from-purple-500 to-pink-500",
      bgGradient: "from-purple-50 to-pink-50",
      level: "Level 4"
    },
  ];

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-white/20 shadow-lg">
          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <Activity className="h-3 w-3 text-white" />
          </div>
          <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-0">
            System Overview
          </Badge>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Location Hierarchy Statistics</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Monitor the complete location system structure and group migration progress across South Africa.
        </p>
      </div>

      {/* Enhanced Location Hierarchy Stats */}
      <div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {statCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={stat.title}
                className="group hover:scale-105 transition-transform duration-300"
              >
                <Card className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br ${stat.bgGradient} relative overflow-hidden`}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className={`w-full h-full bg-gradient-to-br ${stat.gradient}`} />
                  </div>

                  <CardContent className="p-6 relative">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <Badge variant="outline" className="mb-2 text-xs">
                          {stat.level}
                        </Badge>
                        <h3 className="font-bold text-gray-900 text-lg">{stat.title}</h3>
                      </div>
                      <div className={`w-12 h-12 bg-gradient-to-br ${stat.gradient} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    {/* Value */}
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                      <p className="text-sm text-gray-600">{stat.description}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            );
          })}
        </div>
      </div>

      {/* Group Migration Status */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Group Migration Status</h3>
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Groups
              </CardTitle>
              <div className="p-2 rounded-full bg-gray-100">
                <Users className="h-4 w-4 text-gray-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{groups.length}</div>
              <p className="text-xs text-muted-foreground">
                All Stokvel groups
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Migrated Groups
              </CardTitle>
              <div className="p-2 rounded-full bg-green-100">
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{groupsWithLocation.length}</div>
              <p className="text-xs text-muted-foreground">
                Using new location system
              </p>
              {groups.length > 0 && (
                <div className="mt-2">
                  <div className="text-xs text-green-600 font-medium">
                    {Math.round((groupsWithLocation.length / groups.length) * 100)}% migrated
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Legacy Groups
              </CardTitle>
              <div className="p-2 rounded-full bg-yellow-100">
                <Activity className="h-4 w-4 text-yellow-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{groupsWithoutLocation.length}</div>
              <p className="text-xs text-muted-foreground">
                Using old geolocation
              </p>
              {groupsWithoutLocation.length > 0 && (
                <div className="mt-2">
                  <div className="text-xs text-yellow-600 font-medium">
                    Needs migration
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Migration Progress */}
      {groups.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Migration Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Groups migrated to new location system</span>
                <span className="font-medium">
                  {groupsWithLocation.length} of {groups.length}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${groups.length > 0 ? (groupsWithLocation.length / groups.length) * 100 : 0}%` 
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>0%</span>
                <span>100%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            <Button
              onClick={handleSeedData}
              disabled={isSeeding || isSeedingLoading}
              className="h-auto p-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 justify-start"
              variant="outline"
            >
              <div className="flex items-center gap-3 w-full">
                {(isSeeding || isSeedingLoading) ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Database className="h-4 w-4" />
                )}
                <div className="text-left">
                  <div className="font-medium text-sm">
                    {(isSeeding || isSeedingLoading) ? 'Seeding Data...' : 'Seed South African Data'}
                  </div>
                  <div className="text-xs opacity-80">
                    Populate provinces, cities, and townships
                  </div>
                </div>
              </div>
            </Button>

            <Button
              onClick={handleMigrateGroups}
              disabled={isMigrating || isMigratingLoading || groupsWithoutLocation.length === 0}
              className="h-auto p-4 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white border-0 justify-start"
              variant="outline"
            >
              <div className="flex items-center gap-3 w-full">
                {(isMigrating || isMigratingLoading) ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <TrendingUp className="h-4 w-4" />
                )}
                <div className="text-left">
                  <div className="font-medium text-sm">
                    {(isMigrating || isMigratingLoading) ? 'Migrating Groups...' : 'Migrate Legacy Groups'}
                  </div>
                  <div className="text-xs opacity-80">
                    {groupsWithoutLocation.length === 0
                      ? 'All groups already migrated'
                      : `Convert ${groupsWithoutLocation.length} groups to location hierarchy`
                    }
                  </div>
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
