"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Heart, 
  Share2, 
  Edit3, 
  Trash2, 
  Eye,
  ShoppingCart,
  Star,
  Calendar,
  Package
} from "lucide-react";
import { motion } from "framer-motion";
import { IWishlist } from "@/models/Wishlist";
import { 
  formatWishlistValue, 
  formatWishlistDate,
  getPriorityConfig,
  getWishlistStats
} from "@/types/wishlist";
import { 
  useDeleteWishlistMutation,
  useRemoveFromWishlistMutation
} from "@/lib/redux/features/wishlist/wishlistApiSlice";
import { toast } from "sonner";

interface WishlistCardProps {
  wishlist: IWishlist;
  userId: string;
  onEdit?: (wishlistId: string) => void;
  onView?: (wishlistId: string) => void;
  showActions?: boolean;
}

export function WishlistCard({ 
  wishlist, 
  userId,
  onEdit, 
  onView,
  showActions = true 
}: WishlistCardProps) {
  const [deleteWishlist] = useDeleteWishlistMutation();
  const [removeFromWishlist] = useRemoveFromWishlistMutation();
  const [isLoading, setIsLoading] = useState(false);

  const stats = getWishlistStats(wishlist as any);

  const handleDeleteWishlist = async () => {
    const confirmed = window.confirm(`Are you sure you want to delete "${wishlist.name}"?`);
    if (!confirmed) return;

    setIsLoading(true);
    try {
      await deleteWishlist({
        wishlistId: wishlist._id,
        userId
      }).unwrap();
      toast.success('Wishlist deleted successfully');
    } catch (error) {
      console.error('Failed to delete wishlist:', error);
      toast.error('Failed to delete wishlist');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveItem = async (productId: string) => {
    try {
      await removeFromWishlist({
        wishlistId: wishlist._id,
        userId,
        productId
      }).unwrap();
      toast.success('Item removed from wishlist');
    } catch (error) {
      console.error('Failed to remove item:', error);
      toast.error('Failed to remove item');
    }
  };

  const handleShare = async () => {
    if (wishlist.shareCode) {
      const shareUrl = `${window.location.origin}/wishlist/shared/${wishlist.shareCode}`;
      try {
        await navigator.clipboard.writeText(shareUrl);
        toast.success('Share link copied to clipboard');
      } catch (error) {
        toast.error('Failed to copy share link');
      }
    } else {
      toast.error('This wishlist is not public');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="hover:shadow-lg transition-shadow duration-300">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <Heart className="h-5 w-5 text-red-500" />
                {wishlist.name}
              </CardTitle>
              {wishlist.description && (
                <p className="text-sm text-gray-600 mt-1">
                  {wishlist.description}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {wishlist.isPublic && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Public
                </Badge>
              )}
              <Badge variant="outline">
                {stats.totalItems} items
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {formatWishlistDate(wishlist.createdAt)}
            </div>
            <div className="flex items-center gap-1">
              <Package className="h-4 w-4" />
              {stats.inStockItems} in stock
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Wishlist Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {formatWishlistValue(stats.totalValue)}
              </div>
              <div className="text-xs text-gray-600">Total Value</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {stats.priorityCounts.high}
              </div>
              <div className="text-xs text-gray-600">High Priority</div>
            </div>
          </div>

          {/* Recent Items Preview */}
          {wishlist.items.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Recent Items</h4>
              <div className="space-y-2">
                {wishlist.items.slice(0, 3).map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div 
                        className={`w-2 h-2 rounded-full bg-${getPriorityConfig(item.priority).color}-500`}
                      />
                      <span className="text-gray-600">
                        {typeof item.product === 'object' && 'name' in item.product 
                          ? item.product.name 
                          : 'Product'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {typeof item.product === 'object' && 'price' in item.product
                          ? formatWishlistValue(item.product.price)
                          : 'N/A'}
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveItem(
                          typeof item.product === 'object' && '_id' in item.product 
                            ? item.product._id 
                            : ''
                        )}
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
                {wishlist.items.length > 3 && (
                  <div className="text-sm text-gray-500">
                    +{wishlist.items.length - 3} more items
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Tags */}
          {wishlist.tags && wishlist.tags.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {wishlist.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Actions */}
          {showActions && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onView?.(wishlist._id)}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit?.(wishlist._id)}
                className="flex-1"
              >
                <Edit3 className="h-4 w-4 mr-2" />
                Edit
              </Button>

              {wishlist.isPublic && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShare}
                >
                  <Share2 className="h-4 w-4" />
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={handleDeleteWishlist}
                disabled={isLoading}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
