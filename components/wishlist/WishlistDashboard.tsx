"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Heart, 
  Plus, 
  TrendingUp, 
  Package, 
  Star,
  Calendar,
  ShoppingCart,
  Gift
} from "lucide-react";
import { motion } from "framer-motion";
import { WishlistCard } from "./WishlistCard";
import { 
  useGetUserWishlistsQuery,
  useGetWishlistSummaryQuery,
  useCreateWishlistMutation
} from "@/lib/redux/features/wishlist/wishlistApiSlice";
import { formatWishlistValue, formatWishlistDate } from "@/types/wishlist";
import { toast } from "sonner";

interface WishlistDashboardProps {
  userId: string;
}

export function WishlistDashboard({ userId }: WishlistDashboardProps) {
  const [createWishlist] = useCreateWishlistMutation();
  const [isCreating, setIsCreating] = useState(false);

  // Fetch user's wishlists and summary
  const { data: wishlists = [], isLoading: wishlistsLoading } = useGetUserWishlistsQuery(userId);
  const { data: summary, isLoading: summaryLoading } = useGetWishlistSummaryQuery(userId);

  const handleCreateWishlist = async () => {
    setIsCreating(true);
    try {
      await createWishlist({
        userId,
        name: `Wishlist ${wishlists.length + 1}`,
        description: 'My new wishlist',
        isPublic: false
      }).unwrap();
      toast.success('Wishlist created successfully');
    } catch (error) {
      console.error('Failed to create wishlist:', error);
      toast.error('Failed to create wishlist');
    } finally {
      setIsCreating(false);
    }
  };

  const handleViewWishlist = (wishlistId: string) => {
    // Navigate to wishlist detail page
    window.location.href = `/wishlist/${wishlistId}`;
  };

  const handleEditWishlist = (wishlistId: string) => {
    // Navigate to wishlist edit page
    window.location.href = `/wishlist/${wishlistId}/edit`;
  };

  if (wishlistsLoading || summaryLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Heart className="h-8 w-8 text-red-500" />
            My Wishlists
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your favorite products and shopping lists
          </p>
        </div>
        <Button onClick={handleCreateWishlist} disabled={isCreating}>
          <Plus className="h-4 w-4 mr-2" />
          {isCreating ? 'Creating...' : 'New Wishlist'}
        </Button>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Wishlists</p>
                    <p className="text-2xl font-bold text-purple-600">{summary.totalWishlists}</p>
                  </div>
                  <Heart className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Items</p>
                    <p className="text-2xl font-bold text-blue-600">{summary.totalItems}</p>
                  </div>
                  <Package className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Value</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatWishlistValue(summary.totalValue)}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">High Priority</p>
                    <p className="text-2xl font-bold text-red-600">{summary.topPriority.length}</p>
                  </div>
                  <Star className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Wishlists</TabsTrigger>
          <TabsTrigger value="recent">Recent Items</TabsTrigger>
          <TabsTrigger value="priority">High Priority</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {wishlists.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No wishlists yet
                </h3>
                <p className="text-gray-600 mb-6">
                  Create your first wishlist to start saving your favorite products
                </p>
                <Button onClick={handleCreateWishlist} disabled={isCreating}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Wishlist
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {wishlists.map((wishlist, index) => (
                <motion.div
                  key={wishlist._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <WishlistCard
                    wishlist={wishlist}
                    userId={userId}
                    onView={handleViewWishlist}
                    onEdit={handleEditWishlist}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="recent" className="space-y-6">
          {summary?.recentlyAdded && summary.recentlyAdded.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {summary.recentlyAdded.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                          <Package className="h-6 w-6 text-gray-500" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">
                            {typeof item.product === 'object' && 'name' in item.product 
                              ? item.product.name 
                              : 'Product'}
                          </h4>
                          <p className="text-sm text-gray-600">
                            Added {formatWishlistDate(item.addedAt)}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {item.priority}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No recent items
                </h3>
                <p className="text-gray-600">
                  Start adding products to your wishlists to see them here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="priority" className="space-y-6">
          {summary?.topPriority && summary.topPriority.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {summary.topPriority.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                          <Star className="h-6 w-6 text-red-500" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">
                            {typeof item.product === 'object' && 'name' in item.product 
                              ? item.product.name 
                              : 'Product'}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {typeof item.product === 'object' && 'price' in item.product
                              ? formatWishlistValue(item.product.price)
                              : 'Price not available'}
                          </p>
                        </div>
                        <Badge variant="destructive">
                          High Priority
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Star className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No high priority items
                </h3>
                <p className="text-gray-600">
                  Mark items as high priority to see them here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
