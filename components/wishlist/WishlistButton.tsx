"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Heart } from "lucide-react";
import { motion } from "framer-motion";
import { 
  useCheckProductInWishlistQuery,
  useAddToWishlistMutation,
  useRemoveFromWishlistMutation
} from "@/lib/redux/features/wishlist/wishlistApiSlice";
import { toast } from "sonner";

interface WishlistButtonProps {
  productId: string;
  userId: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  showText?: boolean;
  className?: string;
}

export function WishlistButton({ 
  productId, 
  userId, 
  size = 'md',
  variant = 'outline',
  showText = true,
  className = ""
}: WishlistButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Check if product is in wishlist
  const { data: wishlistStatus, isLoading: isChecking } = useCheckProductInWishlistQuery(
    { userId, productId },
    { skip: !userId || !productId }
  );

  const [addToWishlist] = useAddToWishlistMutation();
  const [removeFromWishlist] = useRemoveFromWishlistMutation();

  const isInWishlist = wishlistStatus?.isInWishlist || false;

  const handleToggleWishlist = async () => {
    if (!userId) {
      toast.error('Please log in to use wishlist');
      return;
    }

    setIsLoading(true);
    try {
      if (isInWishlist) {
        // Remove from wishlist - we'll need to get the user's default wishlist
        // For now, we'll create a simple approach by using a placeholder wishlistId
        // In a real implementation, you'd fetch the user's wishlists first
        await removeFromWishlist({
          wishlistId: 'default', // This will be handled in the backend
          userId,
          productId
        }).unwrap();
        toast.success('Removed from wishlist');
      } else {
        // Add to wishlist
        await addToWishlist({
          userId,
          productId,
          priority: 'medium'
        }).unwrap();
        toast.success('Added to wishlist');
      }
    } catch (error) {
      console.error('Wishlist operation failed:', error);
      toast.error(isInWishlist ? 'Failed to remove from wishlist' : 'Failed to add to wishlist');
    } finally {
      setIsLoading(false);
    }
  };

  const buttonSize = {
    sm: 'h-8 px-2',
    md: 'h-10 px-4',
    lg: 'h-12 px-6'
  }[size];

  const iconSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }[size];

  const textSize = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }[size];

  return (
    <Button
      variant={variant}
      size="sm"
      onClick={handleToggleWishlist}
      disabled={isLoading || isChecking}
      className={`${buttonSize} ${className} transition-all duration-200 ${
        isInWishlist 
          ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100' 
          : 'hover:bg-gray-50'
      }`}
    >
      <motion.div
        animate={{ 
          scale: isInWishlist ? [1, 1.2, 1] : 1,
          rotate: isInWishlist ? [0, 10, -10, 0] : 0
        }}
        transition={{ duration: 0.3 }}
        className="flex items-center gap-2"
      >
        <Heart 
          className={`${iconSize} transition-colors duration-200 ${
            isInWishlist 
              ? 'fill-red-500 text-red-500' 
              : 'text-gray-500'
          }`}
        />
        {showText && (
          <span className={textSize}>
            {isLoading 
              ? (isInWishlist ? 'Removing...' : 'Adding...') 
              : (isInWishlist ? 'In Wishlist' : 'Add to Wishlist')
            }
          </span>
        )}
      </motion.div>
    </Button>
  );
}
