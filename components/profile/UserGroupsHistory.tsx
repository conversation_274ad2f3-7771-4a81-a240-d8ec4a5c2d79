"use client";

import { useState } from "react";
import { useQuery} from "@tanstack/react-query";
import { 
  getUserGroupsWithUndeliveredItems, 
  type GroupWithUndeliveredItems 
} from "@/lib/frontendGroupMembershipUtilities";
import { 
  useTransferCartItems, 
  type TransferCartItemsInput 
} from "@/lib/frontendShoppingCartUtilities";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ShoppingBag, ArrowRight, AlertCircle } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

interface UserGroupsHistoryProps {
  userId: string;
}

export function UserGroupsHistory({ userId }: UserGroupsHistoryProps) {
  const [transferDialogOpen, setTransferDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<GroupWithUndeliveredItems | null>(null);
  const [currentGroup, setCurrentGroup] = useState<GroupWithUndeliveredItems | null>(null);

  // Fetch user groups with undelivered items
  const { 
    data: groups, 
    isLoading, 
    isError, 
    refetch 
  } = useQuery({
    queryKey: ["userGroupsHistory", userId],
    queryFn: () => getUserGroupsWithUndeliveredItems(userId),
    enabled: !!userId
  });

  // Transfer cart items mutation
  const { 
    mutate: transferItems, 
    isPending: isTransferring 
  } = useTransferCartItems();

  // Handle transfer button click
  const handleTransferClick = (group: GroupWithUndeliveredItems) => {
    // Find current group
    const current = groups?.find(g => g.isCurrentGroup);
    if (!current) {
      toast.error("Could not find your current group.");
      return;
    }

    setSelectedGroup(group);
    setCurrentGroup(current);
    setTransferDialogOpen(true);
  };

  // Handle transfer confirmation
  const handleTransferConfirm = () => {
    if (!selectedGroup || !currentGroup) {
      toast.error("Missing group information.");
      return;
    }

    const transferData: TransferCartItemsInput = {
      userId,
      sourceGroupId: selectedGroup._id,
      targetGroupId: currentGroup._id
    };

    transferItems(transferData, {
      onSuccess: () => {
        toast.success("Items transferred successfully!");
        setTransferDialogOpen(false);
        refetch();
      },
      onError: (error) => {
        toast.error(`Failed to transfer items: ${error.message}`);
      }
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700"></div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md flex items-center gap-2">
        <AlertCircle className="h-5 w-5" />
        <p>Failed to load your groups history.</p>
      </div>
    );
  }

  if (!groups || groups.length === 0) {
    return (
      <div className="p-4 bg-gray-50 text-gray-700 rounded-md">
        <p>You haven&apos;t joined any groups yet.</p>
      </div>
    );
  }

  // Separate current and previous groups
  const currentGroups = groups.filter(group => group.isCurrentGroup);
  const previousGroups = groups.filter(group => !group.isCurrentGroup);

  return (
    <div className="space-y-6">
      {/* Current Groups Section */}
      <div>
        <h3 className="text-lg font-medium mb-3">Current Group</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {currentGroups.map(group => (
            <Card key={group._id} className="border-purple-200 bg-purple-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-purple-800">{group.name}</CardTitle>
                <CardDescription>{group.geolocation}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  {group.description || "No description available."}
                </p>
              </CardContent>
              <CardFooter>
                <Link href={`/groups/${group._id}`} className="w-full">
                  <Button variant="default" className="w-full">
                    View Group
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      {/* Previous Groups Section */}
      {previousGroups.length > 0 && (
        <div>
          <h3 className="text-lg font-medium mb-3">Previous Groups</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {previousGroups.map(group => (
              <Card key={group._id} className={`border-gray-200 ${group.hasUndeliveredItems ? 'bg-amber-50' : 'bg-gray-50'}`}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-gray-800">{group.name}</CardTitle>
                    {group.hasUndeliveredItems && (
                      <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
                        <ShoppingBag className="h-3 w-3 mr-1" />
                        {group.undeliveredItemsCount} item{group.undeliveredItemsCount !== 1 ? 's' : ''}
                      </Badge>
                    )}
                  </div>
                  <CardDescription>{group.geolocation}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">
                    {group.description || "No description available."}
                  </p>
                  {group.hasUndeliveredItems && (
                    <div className="mt-2 p-2 bg-amber-100 rounded-md text-sm text-amber-800">
                      You have unfinished transactions in this group.
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between gap-2">
                  <Button 
                    variant="outline" 
                    className="flex-1 text-gray-500"
                    disabled={true}
                  >
                    View Group
                  </Button>
                  
                  {group.hasUndeliveredItems && (
                    <Button 
                      variant="default" 
                      className="flex-1 bg-amber-600 hover:bg-amber-700"
                      onClick={() => handleTransferClick(group)}
                    >
                      Transfer Items
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Transfer Dialog */}
      <Dialog open={transferDialogOpen} onOpenChange={setTransferDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Transfer Items</DialogTitle>
            <DialogDescription>
              Transfer your unfinished transactions from {selectedGroup?.name} to your current group.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex-1">
                <p className="font-medium">{selectedGroup?.name}</p>
                <p className="text-sm text-gray-500">{selectedGroup?.geolocation}</p>
                <Badge variant="outline" className="mt-1 bg-amber-100 text-amber-800 border-amber-300">
                  <ShoppingBag className="h-3 w-3 mr-1" />
                  {selectedGroup?.undeliveredItemsCount} item{selectedGroup?.undeliveredItemsCount !== 1 ? 's' : ''}
                </Badge>
              </div>
              
              <ArrowRight className="mx-4 text-gray-400" />
              
              <div className="flex-1">
                <p className="font-medium">{currentGroup?.name}</p>
                <p className="text-sm text-gray-500">{currentGroup?.geolocation}</p>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mt-4">
              This will move all your unfinished transactions from your previous group to your current group.
              This action cannot be undone.
            </p>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setTransferDialogOpen(false)}
              disabled={isTransferring}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleTransferConfirm}
              disabled={isTransferring}
            >
              {isTransferring ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Transferring...
                </>
              ) : (
                "Confirm Transfer"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
