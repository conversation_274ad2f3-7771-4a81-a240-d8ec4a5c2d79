"use client"

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Package, Clock } from 'lucide-react'

interface OrderDetailsDialogProps {
  isOpen: boolean
  onClose: () => void
  order: {
    id: string
    date: string
    items: string[]
    total: number
    status: string
    type: string
    deliveryDate: string
    trackingNumber: string
  }
}

export function OrderDetailsDialog({ isOpen, onClose, order }: OrderDetailsDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Order Details - {order.id}</DialogTitle>
          <DialogDescription>
            View detailed information about your order
          </DialogDescription>
        </DialogHeader>
        
        {/* Order Status Timeline */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium">Order Placed</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-gray-400" />
              <span className="text-sm text-gray-500">{order.date}</span>
            </div>
          </div>

          {/* Order Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Order Type</h4>
                <p className="mt-1">{order.type}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Status</h4>
                <p className="mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    order.status === 'Delivered' 
                      ? 'bg-green-100 text-green-800'
                      : order.status === 'In Transit'
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {order.status}
                  </span>
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Delivery Date</h4>
                <p className="mt-1">{order.deliveryDate}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Tracking Number</h4>
                <p className="mt-1">{order.trackingNumber}</p>
              </div>
            </div>

            {/* Order Items */}
            <div>
              <h4 className="text-sm font-medium text-gray-500 mb-2">Items</h4>
              <ul className="space-y-2">
                {order.items.map((item, index) => (
                  <li key={index} className="flex items-center justify-between py-2 border-b">
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Total */}
            <div className="flex items-center justify-between pt-4 border-t">
              <span className="font-medium">Total Amount</span>
              <span className="font-bold">R{order.total.toFixed(2)}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button>
              Track Order
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

