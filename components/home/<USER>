
// components/home/<USER>
"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { ShoppingCart, Heart, Search, Star, Eye, CheckCircle, Sparkles, TrendingUp } from "lucide-react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice"
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice"
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice"
import { useAuth } from "@/context/AuthContext"
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership"
import { SearchOverlay } from "@/components/search/SearchOverlay"
import type { Product } from "@/types/product"
import type { ProductCategory } from "@/types/productCategory"
import { RatingOverlay } from "../ratings/RatingOverlay"

// Define a runtime product type that matches the actual data structure
interface RuntimeProduct extends Omit<Product, 'category'> {
  category: string | ProductCategory;
}

const HomeProductCard: React.FC<{ product: Product }> = ({ product }) => {
  const [isFavorite, setIsFavorite] = useState(false)
  const [isRatingOpen, setIsRatingOpen] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  // Auth context for user ID
  const { user } = useAuth()
  const userId = user?._id || ''

  // Get user groups
  const { userGroups } = useGroupMembership(userId)

  // Get the current group ID from localStorage or use null for individual shopping
  const [groupId, setGroupId] = useState<string | null>(null)

  // Check if product is popular (high rating or high stock)
  const isPopular = (product.averageRating && product.averageRating >= 4.5) || product.stock > 50;

  // Check if product is trending (recently added or has discount)
  const isTrending = (product.originalPrice && product.originalPrice > product.price) ||
                    (new Date(product.createdAt).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000);

  // Format price function
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Get the current group ID from localStorage on the client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedGroupId = localStorage.getItem('currentGroupId');
      if (storedGroupId) {
        setGroupId(storedGroupId);
      }
    }
  }, [])

  // RTK Query hook
  const [addToCart] = useAddToCartMutation()

  const handleAddToCart = async () => {
    // If user is not logged in, open the join group modal
    if (!user) {
      // Redirect to login if user is not logged in
      router.push('/login?redirect=home');
      return;
    }

    // If user is logged in but not in any group, redirect to join group page
    if (userGroups && userGroups.length === 0) {  
      router.push('/groups');
      return;
    }

    try {
      // Set loading state
      setIsAdding(true);

      // Validate required fields before making the API call
      if (!userId || !product._id || !groupId) {
        console.error('Missing required fields:', {
          userId,
          productId: product._id,
          groupId
        });
        setIsAdding(false);
        return;
      }

      const result = await addToCart({
        userId,
        productId: product._id,
        quantity: 1,
        groupId
      }).unwrap();

      if (result) {
        // Show success state
        setSuccess(true);
        setTimeout(() => setSuccess(false), 2000);

        // Show notification
        window.dispatchEvent(new CustomEvent('cart:notification:show'));
      }
    } catch (error: unknown) {
      console.error('Error adding to cart:', error);

      // Extract error message
      // Type guard to safely access error properties
      const errorMessage =
        typeof error === 'object' && error !== null
          ? (
              // Check if error has data.error property
              'data' in error &&
              typeof error.data === 'object' &&
              error.data !== null &&
              'error' in error.data &&
              typeof error.data.error === 'string'
                ? error.data.error
                // Check if error has error.data.error property
                : 'error' in error &&
                  typeof error.error === 'object' &&
                  error.error !== null &&
                  'data' in error.error &&
                  typeof error.error.data === 'object' &&
                  error.error.data !== null &&
                  'error' in error.error.data &&
                  typeof error.error.data.error === 'string'
                  ? error.error.data.error
                  : 'Failed to add item to cart. Please try again.'
            )
          : 'Failed to add item to cart. Please try again.';

      // Show error message
      alert(errorMessage);
    } finally {
      setIsAdding(false);
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ y: -12, scale: 1.03 }}
      transition={{ duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
      className="group h-full"
    >
      <div className="h-full flex flex-col overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-700 rounded-3xl bg-gradient-to-br from-white via-white to-slate-50/50 relative backdrop-blur-sm">
        {/* Enhanced Gradient Border Effect */}
        <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-[#2A7C6C]/20 via-[#7FDBCA]/10 to-[#2A7C6C]/20 p-[2px] opacity-0 group-hover:opacity-100 transition-opacity duration-500">
          <div className="h-full w-full rounded-3xl bg-white" />
        </div>

        {/* Floating Glow Effect */}
        <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-[#2A7C6C]/5 to-[#7FDBCA]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Floating Glow Effect */}
        <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-blue-600/10 rounded-3xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Product Image Container */}
        <div className="relative h-64 w-full overflow-hidden bg-gradient-to-br from-gray-50/50 via-white to-purple-50/30 rounded-t-3xl">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.3),transparent_50%)]" />
          </div>

          <Image
            src={product.image && product.image.trim() !== '' ? `/api/images/${product.image}` : "/placeholder.svg"}
            alt={product.name || "Product image"}
            fill
            className="object-contain p-6 group-hover:scale-110 transition-all duration-500 ease-out relative z-10"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = "/placeholder.svg"
            }}
          />

          {/* Top Badges Row */}
          <div className="absolute top-4 left-4 right-4 flex justify-between items-start z-20">
            {/* Left Side Badges */}
            <div className="flex flex-col gap-2">
              {/* Stock Badge */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Badge
                  className="text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-md bg-emerald-500/90 text-white border-0 shadow-emerald-500/25"
                >
                  In Stock
                </Badge>
              </motion.div>

              {/* Popular Badge */}
              {isPopular && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-semibold px-3 py-1.5 text-xs rounded-full shadow-lg border-0 shadow-yellow-500/25">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Popular
                  </Badge>
                </motion.div>
              )}

              {/* Trending Badge */}
              {isTrending && !isPopular && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Badge className="bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold px-3 py-1.5 text-xs rounded-full shadow-lg border-0 shadow-pink-500/25">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Trending
                  </Badge>
                </motion.div>
              )}
            </div>

            {/* Right Side - Action Buttons */}
            <div className="flex flex-col gap-2 items-end">
              {/* Discount Badge */}
              {product.originalPrice && product.originalPrice > product.price && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Badge className="bg-gradient-to-r from-red-500 to-pink-600 text-white font-bold px-3 py-1.5 text-xs rounded-full shadow-lg border-0 shadow-red-500/25">
                    -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                  </Badge>
                </motion.div>
              )}

              {/* Enhanced Action Buttons */}
              <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {/* Enhanced Rating Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsRatingOpen(true)}
                  className="relative h-10 w-10 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 shadow-lg border-0 shadow-yellow-500/25 flex items-center justify-center text-white transition-all duration-300"
                  title={product.averageRating ? `Current rating: ${product.averageRating.toFixed(1)}/5` : "Rate this product"}
                >
                  <Star className="h-4 w-4 fill-current" />
                  {/* Rating indicator dot */}
                  {product.averageRating && product.averageRating > 0 && (
                    <div className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                      <div className="h-1 w-1 bg-white rounded-full" />
                    </div>
                  )}
                </motion.button>

                {/* Enhanced Wishlist Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsFavorite(!isFavorite)}
                  className={`h-10 w-10 rounded-full backdrop-blur-md shadow-lg border-0 flex items-center justify-center transition-all duration-300 ${
                    isFavorite
                      ? "bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-red-500/25"
                      : "bg-white/95 hover:bg-white text-gray-600 hover:text-red-500 shadow-black/10"
                  }`}
                  title={isFavorite ? "Remove from wishlist" : "Add to wishlist"}
                >
                  <Heart className={`h-4 w-4 transition-colors ${
                    isFavorite ? "fill-current" : ""
                  }`} />
                </motion.button>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Product Details */}
        <div className="flex-1 p-8 relative z-10">
          <div className="space-y-6 h-full flex flex-col">
            {/* Product Name */}
            <motion.h3
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="font-bold text-xl md:text-2xl text-gray-900 line-clamp-2 leading-tight min-h-[3.5rem] group-hover:text-[#2A7C6C] transition-colors duration-500"
              style={{
                fontFamily: "ClashDisplay-Variable, sans-serif",
                letterSpacing: "-0.02em",
                fontWeight: "700"
              }}
            >
              {product.name}
            </motion.h3>

            {/* Product Description/Category */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.12 }}
              className="flex items-center gap-2"
            >
              <div className="h-1 w-8 bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] rounded-full" />
              <span className="text-sm text-gray-500 font-medium" style={{ fontFamily: "Avenir, sans-serif" }}>
                Premium Quality
              </span>
            </motion.div>

            {/* Enhanced Price Section */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="space-y-4 flex-1"
            >
              <div className="flex items-baseline gap-3">
                <span
                  className="text-3xl md:text-4xl font-black bg-gradient-to-r from-[#2A7C6C] to-[#1e5b4f] bg-clip-text text-transparent"
                  style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                >
                  {formatPrice(product.price)}
                </span>
                {product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-lg text-gray-400 line-through font-medium">
                    {formatPrice(product.originalPrice)}
                  </span>
                )}
              </div>

              {/* Enhanced Savings Display */}
              {product.originalPrice && product.originalPrice > product.price && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-50 to-teal-50 px-4 py-2 rounded-2xl border border-emerald-200/50 shadow-sm"
                >
                  <div className="h-2 w-2 bg-emerald-500 rounded-full animate-pulse" />
                  <p className="text-sm text-emerald-700 font-bold">
                    Save {formatPrice(product.originalPrice - product.price)}
                  </p>
                  <Sparkles className="h-3 w-3 text-emerald-500" />
                </motion.div>
              )}

              {/* Stock Status */}
              <motion.div
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.25 }}
                className="flex items-center gap-2"
              >
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm text-gray-600 font-medium" style={{ fontFamily: "Avenir, sans-serif" }}>
                  {product.stock > 10 ? 'In Stock' : `Only ${product.stock} left`}
                </span>
              </motion.div>
            </motion.div>

            {/* Enhanced Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="space-y-4 pt-6"
            >
              {/* Favorite & Quick Actions Row */}
              <div className="flex items-center justify-between">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setIsFavorite(!isFavorite)}
                  className={`h-12 w-12 rounded-2xl shadow-lg border-0 flex items-center justify-center transition-all duration-300 ${
                    isFavorite
                      ? 'bg-gradient-to-r from-red-400 to-pink-500 text-white shadow-red-500/30'
                      : 'bg-gray-100 text-gray-600 hover:text-red-500 hover:bg-red-50'
                  }`}
                  title={isFavorite ? "Remove from favorites" : "Add to favorites"}
                >
                  <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />
                </motion.button>

                <div className="flex items-center gap-2">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setIsRatingOpen(true)}
                    className="h-12 w-12 rounded-2xl bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 shadow-lg border-0 shadow-yellow-500/30 flex items-center justify-center text-white transition-all duration-300"
                    title={product.averageRating ? `Current rating: ${product.averageRating.toFixed(1)}/5` : "Rate this product"}
                  >
                    <Star className="h-5 w-5 fill-current" />
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => router.push(`/products/${product._id}`)}
                    className="h-12 w-12 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-lg border-0 shadow-blue-500/30 flex items-center justify-center text-white transition-all duration-300"
                    title="View product details"
                  >
                    <Eye className="h-5 w-5" />
                  </motion.button>
                </div>
              </div>

              {/* Main Action Button */}
              <Button
                onClick={handleAddToCart}
                disabled={isAdding}
                className="w-full bg-gradient-to-r from-[#2A7C6C] to-[#1e5b4f] hover:from-[#236358] hover:to-[#164239] text-white font-bold py-4 rounded-2xl transition-all duration-300 disabled:opacity-50 shadow-xl hover:shadow-2xl hover:shadow-[#2A7C6C]/30 transform hover:scale-[1.02] active:scale-[0.98] group"
                size="lg"
              >
                <AnimatePresence mode="wait">
                  {isAdding ? (
                    <motion.div
                      key="loading"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center"
                    >
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="mr-2"
                      >
                        <ShoppingCart className="h-5 w-5" />
                      </motion.div>
                      Adding to Cart...
                    </motion.div>
                  ) : success ? (
                    <motion.div
                      key="success"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex items-center"
                    >
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Added Successfully!
                    </motion.div>
                  ) : (
                    <motion.div
                      key="default"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex items-center"
                    >
                      <ShoppingCart className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                      Add to Cart
                    </motion.div>
                  )}
                </AnimatePresence>
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Enhanced Rating Overlay */}
      <RatingOverlay
        isOpen={isRatingOpen}
        onClose={() => setIsRatingOpen(false)}
        productId={product._id}
        userId={userId}
        productName={product.name}
      />
    </motion.div>
  )
}



export function ProductListing() {
  const { data: products = [], isLoading } = useGetAllProductsQuery()
  const { data: categories = [] } = useGetCategoriesQuery()
  const [selectedCategory, setSelectedCategory] = useState("All Products")
  const [isSearchOverlayOpen, setIsSearchOverlayOpen] = useState(false)

  // Helper function to safely extract category name from a product
  const getCategoryName = (product: RuntimeProduct): string => {
    // Case 1: If category is a string ID
    if (typeof product.category === 'string') {
      const categoryObj = categories.find(cat => cat._id === product.category);
      return categoryObj?.name || '';
    }
    // Case 2: If category is an object with a name property
    else if (product.category && typeof product.category === 'object' && 'name' in product.category) {
      return String(product.category.name);
    }
    // Default case
    return '';
  };

  // Convert selectedCategory to string to ensure type safety
  const selectedCategoryName: string = selectedCategory;

  const filteredProducts =
    selectedCategoryName === "All Products"
      ? products
      : products.filter((product) => {
          // Cast the product to RuntimeProduct to handle the category property correctly
          const runtimeProduct = product as unknown as RuntimeProduct;
          const categoryName = getCategoryName(runtimeProduct);
          return categoryName === selectedCategoryName;
        })

  if (isLoading) {
    return (
      <section className="py-24 bg-gradient-to-b from-[#2A7C6C] to-[#1e5b4f]">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-white text-center">Loading products...</h2>
        </div>
      </section>
    )
  }

  // Get categories that have products with product counts
  const categoriesWithProducts = categories
    .map((category) => {
      const productCount = products.filter((product) => {
        const runtimeProduct = product as unknown as RuntimeProduct;
        const categoryName = getCategoryName(runtimeProduct);
        return categoryName === category.name;
      }).length;

      return {
        name: category.name,
        count: productCount
      };
    })
    .filter((category) => category.count > 0); // Only show categories with products

  const allCategories = [
    { name: "All Products", count: products.length },
    ...categoriesWithProducts
  ]

  return (
    <section className="relative py-32 bg-gradient-to-br from-slate-50 via-white to-slate-100 overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232A7C6C' fill-opacity='0.4'%3E%3Cpath d='M60 60L30 90L90 90z'/%3E%3Cpath d='M60 60L90 30L90 90z'/%3E%3Cpath d='M60 60L30 30L90 30z'/%3E%3Cpath d='M60 60L30 30L30 90z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-[#2A7C6C]/10 rounded-full blur-2xl" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-[#2A7C6C]/5 rounded-full blur-xl" />

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-block"
          >
            <h2
              className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent mb-6"
              style={{
                fontFamily: "ClashDisplay-Variable, sans-serif",
                letterSpacing: "-0.03em",
              }}
            >
              Discover Our
              <span className="block text-[#2A7C6C]">Top Picks</span>
            </h2>
          </motion.div>

          <div className="w-32 h-1 bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] mx-auto mb-8 rounded-full" />

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-gray-600 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed mb-12"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Curated selection of premium products loved by our community.
            Experience quality that brings neighbors together.
          </motion.p>
        </div>

        {/* Modern Search Field */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="relative w-full max-w-2xl mx-auto mb-16"
        >
          <div className="relative">
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
            <Input
              type="text"
              placeholder="Search for your favorite products..."
              className="w-full pl-16 pr-6 py-6 text-lg rounded-3xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/70 backdrop-blur-sm text-gray-800 shadow-lg hover:shadow-xl"
              onClick={() => setIsSearchOverlayOpen(true)}
              readOnly
              style={{ fontFamily: 'Avenir, sans-serif' }}
            />
            <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
              <div className="h-8 w-8 bg-[#2A7C6C] rounded-full flex items-center justify-center">
                <Search className="h-4 w-4 text-white" />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Modern Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex flex-wrap justify-center gap-3 mb-16"
        >
          {allCategories.map((category, index) => (
            <motion.button
              key={category.name}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSelectedCategory(category.name)}
              className={`px-8 py-3 rounded-full text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl relative ${
                selectedCategory === category.name
                  ? "bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white shadow-[#2A7C6C]/25"
                  : "bg-white/70 backdrop-blur-sm text-gray-700 hover:bg-white hover:text-[#2A7C6C] border border-gray-200/50"
              }`}
              style={{ fontFamily: "Avenir, sans-serif" }}
            >
              <span className="flex items-center gap-2">
                {category.name}
                <Badge
                  className={`text-xs font-bold ${
                    selectedCategory === category.name
                      ? "bg-white/20 text-white border-white/30"
                      : "bg-[#2A7C6C]/10 text-[#2A7C6C] border-[#2A7C6C]/20"
                  }`}
                >
                  {category.count}
                </Badge>
              </span>
            </motion.button>
          ))}
        </motion.div>

        {/* Products Grid */}
        {filteredProducts.length > 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-20"
          >
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product._id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <HomeProductCard product={product} />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-center py-20 mb-20"
          >
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
              <Search className="h-10 w-10 text-gray-400" />
            </div>
            <h3
              className="text-2xl font-bold text-gray-800 mb-4"
              style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
            >
              No products found in {selectedCategory}
            </h3>
            <p
              className="text-gray-600 max-w-md mx-auto mb-6"
              style={{ fontFamily: "Avenir, sans-serif" }}
            >
              We're constantly adding new products. Check back soon or explore other categories.
            </p>
            <Button
              onClick={() => setSelectedCategory("All Products")}
              className="bg-gradient-to-r from-[#2A7C6C] to-[#1e5b4f] hover:from-[#236358] hover:to-[#164239] text-white font-semibold px-8 py-3 rounded-xl"
            >
              View All Products
            </Button>
          </motion.div>
        )}

        {/* Enhanced View More Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-[#2A7C6C]/10 to-[#7FDBCA]/10 p-12 rounded-3xl border border-[#2A7C6C]/20 max-w-2xl mx-auto">
            <h3
              className="text-3xl md:text-4xl font-bold text-[#2F4858] mb-4"
              style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
            >
              Explore Our Full Collection
            </h3>
            <p
              className="text-gray-600 text-lg mb-8 leading-relaxed"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Discover thousands more products in our complete store.
              From everyday essentials to premium selections.
            </p>

            <Link href="/store">
              <Button
                className="group relative overflow-hidden bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#1E5A4F] hover:to-[#2A7C6C] text-white rounded-full px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                <span className="relative z-10 flex items-center">
                  Browse All Products
                  <motion.div
                    className="ml-2"
                    animate={{ x: [0, 4, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    →
                  </motion.div>
                </span>
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>

      <SearchOverlay isOpen={isSearchOverlayOpen} onClose={() => setIsSearchOverlayOpen(false)} />
    </section>
  )
}
