// components/home/<USER>

"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { JoinGroupModal } from "@/components/modals/JoinGroupModal"
import { StartSavingOverlay } from "../investments/StartSavingOverlay"
import { motion, AnimatePresence } from "framer-motion"

const slides = [
  {
    id: 1,
    title: "Revolutionizing\nCommunity Commerce",
    subtitle:
      "Join South Africa's most innovative Stokvel platform. Unite with neighbors, unlock savings, and transform how communities shop together.",
    image: "/headers/slider1.png",
    number: "01",
    gradient: "from-emerald-600/80 to-green-600/80",
    accent: "#059669"
  },
  {
    id: 2,
    title: "Collective Power\nUnlimited Savings",
    subtitle:
      "Harness the strength of group buying to access premium products at wholesale prices. Your community is your competitive advantage.",
    image: "/headers/slider2.png",
    number: "02",
    gradient: "from-emerald-600/80 to-teal-600/80",
    accent: "#059669"
  },
  {
    id: 3,
    title: "Ubuntu Meets\nInnovation",
    subtitle:
      "Where time-honored African values blend seamlessly with cutting-edge technology to create the future of community-driven commerce.",
    image: "/headers/greenslider.png",
    number: "03",
    gradient: "from-green-600/80 to-emerald-600/80",
    accent: "#16A34A"
  },
]

export function HeroSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isStartSavingOpen, setIsStartSavingOpen] = useState(false)
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [])

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
  }, [])

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }, [])

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }, [])

  const openJoinGroupModal = useCallback(() => {
    setIsJoinGroupOpen(true)
  }, [])

  return (
    <section className="relative min-h-[90vh] overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Global Pattern Overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M40 40L20 60L60 60z'/%3E%3Cpath d='M40 40L60 20L60 60z'/%3E%3Cpath d='M40 40L20 20L60 20z'/%3E%3Cpath d='M40 40L20 20L20 60z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative h-[90vh] w-full">
        <AnimatePresence initial={false}>
          {slides.map(
            (slide, index) =>
              index === currentSlide && (
                <motion.div
                  key={slide.id}
                  initial={{ opacity: 0, scale: 1.1 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  className="absolute inset-0"
                >
                  <Image
                    src={slide.image || "/placeholder.svg"}
                    alt={`Slide ${index + 1}`}
                    fill
                    priority={index === 0}
                    className="object-cover"
                  />

                  {/* Modern Gradient Overlay */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${slide.gradient}`} />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30" />

                  {/* Floating Elements */}
                  <div className="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-3xl" />
                  <div className="absolute bottom-20 right-20 w-40 h-40 bg-white/5 rounded-full blur-3xl" />

                  {/* Modern Number Display */}
                  <motion.div
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3, duration: 0.8 }}
                    className="absolute right-8 top-1/2 -translate-y-1/2 select-none"
                  >
                    <div className="text-right">
                      <div
                        className="text-[12rem] md:text-[16rem] lg:text-[20rem] font-bold text-white/10 leading-none"
                        style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                      >
                        {slide.number}
                      </div>
                      <div className="absolute bottom-0 right-0 w-24 h-1 bg-white/30 rounded-full" />
                    </div>
                  </motion.div>

                  <div className="container relative mx-auto h-full px-4 md:px-6 z-10">
                    <div className="flex h-full items-center">
                      <div className="max-w-4xl">
                        {/* Slide Indicator */}
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.2, duration: 0.6 }}
                          className="flex items-center mb-6"
                        >
                          <div className="w-12 h-1 bg-white/60 rounded-full mr-4" />
                          <span
                            className="text-white/80 text-sm font-medium tracking-wider uppercase"
                            style={{ fontFamily: "Avenir, sans-serif" }}
                          >
                            {slide.number} / 03
                          </span>
                        </motion.div>

                        <motion.h1
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.4, duration: 0.8 }}
                          className="mb-8 max-w-4xl text-5xl md:text-6xl lg:text-8xl font-bold text-white leading-tight"
                          style={{
                            fontFamily: "ClashDisplay-Variable, sans-serif",
                            letterSpacing: "-0.03em",
                            lineHeight: "0.9",
                          }}
                        >
                          {slide.title}
                        </motion.h1>

                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.5, duration: 0.6 }}
                          className="w-24 h-1 bg-white/60 rounded-full mb-8"
                        />

                        <motion.p
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.6, duration: 0.6 }}
                          className="text-white/90 mb-12 max-w-2xl text-xl md:text-2xl leading-relaxed"
                          style={{ fontFamily: "Avenir, sans-serif" }}
                        >
                          {slide.subtitle}
                        </motion.p>

                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.7, duration: 0.6 }}
                          className="flex flex-col sm:flex-row gap-6"
                        >
                          <Button
                            className="group relative overflow-hidden bg-white text-slate-900 hover:bg-white/95 rounded-full px-10 py-4 text-lg font-semibold shadow-2xl hover:shadow-3xl transform hover:-translate-y-1 transition-all duration-300"
                            onClick={openJoinGroupModal}
                            style={{ fontFamily: "Avenir, sans-serif" }}
                          >
                            <span className="relative z-10 flex items-center">
                              Join Your Community
                              <motion.div
                                className="ml-2"
                                animate={{ x: [0, 4, 0] }}
                                transition={{ duration: 1.5, repeat: Infinity }}
                              >
                                →
                              </motion.div>
                            </span>
                          </Button>

                          {/* <Button
                            variant="outline"
                            className="border-2 border-white/60 text-white bg-white/10 backdrop-blur-sm hover:bg-white hover:text-slate-900 hover:border-white rounded-full px-10 py-4 text-lg font-semibold transition-all duration-300"
                            onClick={() => setIsStartSavingOpen(true)}
                            style={{ fontFamily: "Avenir, sans-serif" }}
                          >
                            Start Saving Today
                          </Button> */}
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ),
          )}
        </AnimatePresence>
      </div>

      {/* Modern Pagination Dots */}
      <div className="absolute bottom-8 left-8 flex gap-3">
        {slides.map((slide, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`group relative transition-all duration-300 ${
              index === currentSlide ? "w-16" : "w-4"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          >
            <div className={`h-1 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? "bg-white shadow-lg"
                : "bg-white/40 hover:bg-white/60"
            }`} />
            {index === currentSlide && (
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 5, ease: "linear" }}
                className="absolute top-0 left-0 h-1 bg-white rounded-full"
              />
            )}
          </button>
        ))}
      </div>

      {/* Modern Navigation Arrows */}
      <div className="absolute bottom-8 right-8 flex gap-2">
        <button
          onClick={prevSlide}
          className="group h-12 w-12 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-white hover:bg-white hover:text-slate-900 transition-all duration-300 shadow-lg hover:shadow-xl"
          aria-label="Previous slide"
        >
          <ChevronLeft className="h-5 w-5 group-hover:-translate-x-0.5 transition-transform duration-300" />
        </button>
        <button
          onClick={nextSlide}
          className="group h-12 w-12 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-white hover:bg-white hover:text-slate-900 transition-all duration-300 shadow-lg hover:shadow-xl"
          aria-label="Next slide"
        >
          <ChevronRight className="h-5 w-5 group-hover:translate-x-0.5 transition-transform duration-300" />
        </button>
      </div>

      {/* Slide Progress Indicator */}
      <div className="absolute top-8 right-8 text-white/80">
        <div className="text-right">
          <div className="text-sm font-medium mb-2" style={{ fontFamily: "Avenir, sans-serif" }}>
            {String(currentSlide + 1).padStart(2, '0')} / {String(slides.length).padStart(2, '0')}
          </div>
          <div className="w-16 h-0.5 bg-white/20 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-white rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${((currentSlide + 1) / slides.length) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>
      </div>

      {/* Modals */}
      <JoinGroupModal isOpen={isJoinGroupOpen} onClose={() => setIsJoinGroupOpen(false)} />
      <StartSavingOverlay isOpen={isStartSavingOpen} onClose={() => setIsStartSavingOpen(false)} />
    </section>
  )
}

