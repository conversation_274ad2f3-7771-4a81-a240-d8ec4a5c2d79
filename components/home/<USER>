"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ead<PERSON> } from "@/components/ui/card"
import { SingleSavingOverlay } from "../singleinvestment/SingleSavingOverlay"
import { useState } from "react"

const plans = [
  {
    id: "basic",
    name: "Basic",
    price: 350,
    description: "Perfect for individuals starting their stockvel journey. Access to basic group buying features and community zones.",
  },
  {
    id: "standard",
    name: "Standard",
    price: 500,
    description: "Ideal for active stockvel members. Enhanced group buying power and priority delivery scheduling.",
  },
  {
    id: "premium",
    name: "Premium",
    price: 1000,
    description: "Our ultimate stockvel experience. Maximum discounts, exclusive zones, and premium delivery options.",
  },
]

export function PricingSection() {
  const [isOverlayOpen, setIsOverlayOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<string | undefined>(undefined)

  const handleStartSaving = (planId: string) => {
    setSelectedPlan(planId)
    setIsO<PERSON>layOpen(true)
  }

  return (
    <section className="py-24 px-4 md:px-6">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 
            className="text-[#2F4858] text-4xl md:text-5xl lg:text-6xl mb-6"
            style={{ 
              fontFamily: 'ClashDisplay-Variable, sans-serif',
              letterSpacing: '-0.02em' 
            }}
          >
            Choose the Perfect Plan for
          </h2>
          <h2 
            className="text-[#2F4858] text-4xl md:text-5xl lg:text-6xl"
            style={{ 
              fontFamily: 'ClashDisplay-Variable, sans-serif',
              letterSpacing: '-0.02em' 
            }}
          >
            Your Grocery Needs
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan) => (
            <Card 
              key={plan.id}
              className="border rounded-lg overflow-hidden"
            >
              <CardHeader className="text-center">
                <h3 className="text-2xl font-bold">{plan.name}</h3>
                <p className="text-4xl font-bold mt-4">
                  R{plan.price}
                  <span className="text-base font-normal text-gray-600">/month</span>
                </p>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600">{plan.description}</p>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button 
                  onClick={() => handleStartSaving(plan.id)}
                  className="w-full"
                >
                  Start Saving
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      <SingleSavingOverlay 
        isOpen={isOverlayOpen} 
        onClose={() => {
          setIsOverlayOpen(false)
          setSelectedPlan(undefined)
        }}
        initialPlan={selectedPlan}
      />
    </section>
  )
}
