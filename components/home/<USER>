"use client"

import { useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShoppingCart, Truck, Users, Heart, Sparkles } from 'lucide-react'
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { JoinGroupModal } from "@/components/modals/JoinGroupModal"

const steps = [
  {
    number: "01",
    icon: Users,
    title: "Join Your\nCommunity\nZone",
    description: "Connect with neighbors in your area and form a powerful shopping collective.",
    gradient: "from-blue-500 to-purple-600"
  },
  {
    number: "02",
    icon: ShoppingCart,
    title: "Shop Together\nSave Together",
    description: "Browse premium products and unlock exclusive bulk discounts through group buying.",
    gradient: "from-green-500 to-teal-600"
  },
  {
    number: "03",
    icon: Truck,
    title: "Enjoy Smart\nDelivery",
    description: "Receive your orders efficiently while building lasting community connections.",
    gradient: "from-orange-500 to-red-600"
  },
]

export function MembershipSection() {
  const router = useRouter()
  const [activeStep, setActiveStep] = useState(0)
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false)

  const openJoinGroupModal = useCallback(() => {
    setIsJoinGroupOpen(true)
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prevStep) => (prevStep + 1) % steps.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [])

  return (
    <section className="relative py-32 bg-gradient-to-br from-slate-50 via-white to-slate-100 overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232A7C6C' fill-opacity='0.4'%3E%3Cpath d='M60 60L30 90L90 90z'/%3E%3Cpath d='M60 60L90 30L90 90z'/%3E%3Cpath d='M60 60L30 30L90 30z'/%3E%3Cpath d='M60 60L30 30L30 90z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-[#2A7C6C]/10 rounded-full blur-2xl" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-[#2A7C6C]/5 rounded-full blur-xl" />

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-block"
          >
            <h2
              className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent mb-6"
              style={{
                fontFamily: 'ClashDisplay-Variable, sans-serif',
                letterSpacing: '-0.03em'
              }}
            >
              Your Journey to
              <span className="block text-[#2A7C6C]">Smart Shopping</span>
            </h2>
          </motion.div>

          <div className="w-32 h-1 bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] mx-auto mb-8 rounded-full" />

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-gray-600 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Transform how you shop with our revolutionary community-powered platform.
            Three simple steps to unlock incredible savings and meaningful connections.
          </motion.p>
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Modern Step Cards */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                className={`group relative bg-white/70 backdrop-blur-sm p-8 rounded-3xl border transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl ${
                  index <= activeStep
                    ? 'border-[#2A7C6C]/30 shadow-lg'
                    : 'border-gray-200/50 shadow-sm'
                }`}
              >
                {/* Gradient Background on Hover */}
                <div className={`absolute inset-0 bg-gradient-to-br ${step.gradient} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500`} />

                <div className="relative z-10">
                  {/* Step Number */}
                  <div className="flex items-center justify-between mb-6">
                    <span
                      className={`text-6xl font-bold transition-colors duration-500 ${
                        index <= activeStep ? 'text-[#2A7C6C]' : 'text-gray-300'
                      }`}
                      style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                    >
                      {step.number}
                    </span>
                    <div className={`h-16 w-16 rounded-2xl flex items-center justify-center transition-all duration-500 ${
                      index <= activeStep
                        ? `bg-gradient-to-br ${step.gradient} shadow-lg`
                        : 'bg-gray-100'
                    }`}>
                      <step.icon
                        size={32}
                        className={`transition-colors duration-500 ${
                          index <= activeStep ? 'text-white' : 'text-gray-400'
                        }`}
                      />
                    </div>
                  </div>

                  {/* Content */}
                  <h3
                    className={`text-2xl md:text-3xl font-bold mb-4 whitespace-pre-line transition-colors duration-500 ${
                      index <= activeStep ? 'text-[#2F4858]' : 'text-gray-500'
                    }`}
                    style={{
                      fontFamily: 'ClashDisplay-Variable, sans-serif',
                      letterSpacing: '-0.01em'
                    }}
                  >
                    {step.title}
                  </h3>

                  <p
                    className={`text-lg leading-relaxed transition-colors duration-500 ${
                      index <= activeStep ? 'text-gray-700' : 'text-gray-400'
                    }`}
                    style={{ fontFamily: 'Avenir, sans-serif' }}
                  >
                    {step.description}
                  </p>
                </div>

                {/* Active Step Indicator */}
                {index === activeStep && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-[#2A7C6C] rounded-full flex items-center justify-center"
                  >
                    <Sparkles className="h-3 w-3 text-white" />
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Progress Bar */}
          <div className="relative mb-16">
            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${((activeStep + 1) / steps.length) * 100}%` }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />
            </div>
          </div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center"
          >
            <div className="bg-gradient-to-r from-[#2A7C6C]/10 to-[#7FDBCA]/10 p-12 rounded-3xl border border-[#2A7C6C]/20 mb-8">
              <h3
                className="text-3xl md:text-4xl font-bold text-[#2F4858] mb-4"
                style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
              >
                Ready to Start Saving?
              </h3>
              <p
                className="text-gray-600 text-lg md:text-xl mb-8 max-w-2xl mx-auto"
                style={{ fontFamily: 'Avenir, sans-serif' }}
              >
                Join thousands of families who have already transformed their shopping experience.
                Your community is waiting for you.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  className="group relative overflow-hidden bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#1E5A4F] hover:to-[#2A7C6C] text-white rounded-full px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                  onClick={openJoinGroupModal}
                >
                  <span className="relative z-10 flex items-center">
                    <Heart className="h-5 w-5 mr-2" />
                    Join Your Community
                  </span>
                </Button>

                <Button
                  variant="outline"
                  className="border-2 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white rounded-full px-10 py-4 text-lg font-semibold transition-all duration-300"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                  onClick={() => router.push("/aboutus")}
                >
                  Learn More
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Modal */}
      <JoinGroupModal isOpen={isJoinGroupOpen} onClose={() => setIsJoinGroupOpen(false)} />
    </section>
  )
}

