
// components/chat/ChatWindow.tsx
"use client"

import React, { useState, useCallback, useRef } from "react"
import { ThumbsUp, ThumbsDown, Copy, Edit, Send, Volume2, ChevronDown, ChevronUp, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { v4 as uuidv4 } from 'uuid'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

interface FAQ {
  question: string
  answer: string
  isOpen: boolean
}


export const ChatWindow = ({ onClose }: { onClose: () => void }) => {
    const [messages, setMessages] = useState<Message[]>([])
    const [inputValue, setInputValue] = useState("")
    const [isTyping, setIsTyping] = useState(false)
    const [showSendButton, setShowSendButton] = useState(false)
    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const [sessionId] = useState(() => uuidv4())

  const [faqs, setFaqs] = useState<FAQ[]>([
    {
      question: "How do I join a Stockvel?",
      answer:
        "You can join a Stockvel by navigating to our Membership section and selecting a plan that suits your needs. Follow the registration process to become a member.",
      isOpen: false,
    },
    {
      question: "What are the investment options?",
      answer:
        "We offer various investment options including mutual funds, stocks, and fixed deposits. Each option is carefully selected to maximize returns while managing risk.",
      isOpen: false,
    },
    {
      question: "How secure are my investments?",
      answer:
        "Your investments are protected by state-of-the-art security measures and we're fully compliant with financial regulations. We also provide insurance coverage for added protection.",
      isOpen: false,
    },
  ])

  const toggleFAQ = (index: number) => {
    setFaqs(
      faqs.map((faq, i) => ({
        ...faq,
        isOpen: i === index ? !faq.isOpen : faq.isOpen,
      })),
    )
  }


  const debounce = <F extends (...args: Array<unknown>) => void>(
    func: F, 
    wait: number
  ) => {
    let timeout: NodeJS.Timeout | null = null
    return (...args: Parameters<F>) => {
      if (timeout) {
        clearTimeout(timeout)
      }
      timeout = setTimeout(() => {
        func(...args)
        timeout = null
      }, wait)
    }
  }

  
  const debouncedStopTyping = useCallback(
    debounce(() => {
      setIsTyping(false)
      if (inputValue.trim().length > 0) {
        setShowSendButton(true)
      }
    }, 1500),
    [inputValue]
  )

// Input change handler with dynamic textarea resizing
const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    setInputValue(value)
    setIsTyping(true)
    setShowSendButton(false)
    debouncedStopTyping()

    // Dynamic textarea resizing
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      const scrollHeight = textareaRef.current.scrollHeight
      textareaRef.current.style.height = `${Math.min(Math.max(scrollHeight, 48), 150)}px`
    }
  }


const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  if (!inputValue.trim()) return;

  const userMessage: Message = {
    id: Date.now().toString(),
    content: inputValue,
    isUser: true,
    timestamp: new Date(),
  };

  setMessages(prev => [...prev, userMessage]);
  setInputValue("");
  setShowSendButton(false);
  setIsTyping(false);

  try {
    const response = await fetch('/api/ai/ask', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: inputValue,
        sessionId 
      }),
    });

    if (!response.ok) throw new Error('Failed to get AI response');

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No reader available');

    const aiMessage: Message = {
      id: Date.now().toString(),
      content: '',
      isUser: false,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, aiMessage]);

    const decoder = new TextDecoder();
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        lastMessage.content += chunk;
        return newMessages;
      });
    }
  } catch (error) {
    console.error('Error:', error);
    // Handle error appropriately
  }
};  return (
    <div className="flex flex-col w-80 h-[32rem] bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="flex justify-between bg-[#2A7C6C] items-center p-4 border-b">
        <h3
          className="text-lg font-semibold text-white"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Chat Support
        </h3>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <div className="mb-4">
          <h3
            className="text-lg font-semibold mb-3 text-[#2F4858]"
            style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
          >
            Frequently Asked Questions
          </h3>
          {faqs.map((faq, index) => (
            <div key={index} className="mb-2">
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full text-left p-2 bg-gray-50 hover:bg-gray-100 rounded-md"
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium text-[#2F4858]">{faq.question}</span>
                  <span>{faq.isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}</span>
                </div>
              </button>
              {faq.isOpen && (
                <div
                  className="p-2 text-sm text-gray-600 bg-gray-50/50 rounded-md mt-1"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  {faq.answer}
                </div>
              )}
            </div>
          ))}
        </div>

        {messages.map((message) => (
          <div key={message.id} className={`flex gap-4 ${message.isUser ? "flex-row-reverse" : ""}`}>
            <Avatar className="h-8 w-8 bg-[#2A7C6C]">
              <span className="text-xs text-white">{message.isUser ? "U" : "AI"}</span>
            </Avatar>
            <div className={`flex-1 space-y-2 ${message.isUser ? "items-end" : "items-start"}`}>
              <div className={`relative p-4 rounded-lg ${message.isUser ? "bg-[#2A7C6C] text-white" : "bg-gray-50"}`}>
                <p style={{ fontFamily: "Avenir, sans-serif" }}>{message.content}</p>
                <div className={`flex gap-2 mt-2 text-sm ${message.isUser ? "justify-end" : "justify-start"}`}>
                  <button className="hover:text-[#2A7C6C] transition-colors">
                    <ThumbsUp className="h-4 w-4" />
                  </button>
                  <button className="hover:text-[#2A7C6C] transition-colors">
                    <ThumbsDown className="h-4 w-4" />
                  </button>
                  <button className="hover:text-[#2A7C6C] transition-colors">
                    <Copy className="h-4 w-4" />
                  </button>
                  <button className="hover:text-[#2A7C6C] transition-colors">
                    <Edit className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-[#F0F2F5] px-4 py-3">
        <form onSubmit={handleSubmit} className="flex items-center w-full gap-2">
          <div className="relative flex-1">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={handleInputChange}
              placeholder="Type a message"
              rows={1}
              className={cn(
                "w-full px-4 py-3 bg-white border-0 rounded-2xl focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 shadow-sm placeholder:text-gray-500 resize-none overflow-hidden transition-all duration-300 ease-in-out transform-gpu",
                isTyping && "py-5 shadow-md scale-[1.02] translate-y-[-2px]"
              )}
              style={{ 
                fontFamily: "Avenir, sans-serif", 
                minHeight: "48px", 
                maxHeight: "150px" 
              }}
            />
            <Button
              type="submit"
              size="sm"
              className={cn(
                "absolute right-2 top-1/2 -translate-y-1/2 bg-[#2A7C6C] hover:bg-[#236358] rounded-full w-8 h-8 p-0 flex items-center justify-center shadow-sm transition-all duration-300 ease-in-out transform-gpu",
                (!showSendButton || isTyping) && "opacity-0 translate-x-4",
                showSendButton && !isTyping && "opacity-100 translate-x-0"
              )}
              disabled={!showSendButton || isTyping}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          <Button 
            variant="ghost" 
            size="icon"
            className={cn(
              "rounded-full h-10 w-10 hover:bg-white/80 text-[#2A7C6C] flex-shrink-0 transition-all duration-300 ease-in-out transform-gpu",
              isTyping && "opacity-0 scale-90 translate-x-4"
            )}
          >
            <Volume2 className="h-5 w-5" />
          </Button>
        </form>
      </div>

    </div>
  )
}

