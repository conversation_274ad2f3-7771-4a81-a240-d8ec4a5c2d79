'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Heart, 
  ShoppingCart, 
  Star, 
  Users, 
  TrendingUp,
  Share2,
  Eye,
  Plus,
  Minus,
  Check
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useCart } from '@/context/CartContext';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';
import Image from 'next/image';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  inStock: boolean;
  stockCount: number;
  groupDiscount?: {
    threshold: number;
    discount: number;
    currentCount: number;
  };
  isWishlisted?: boolean;
  isTrending?: boolean;
  isNew?: boolean;
}

interface MobileProductCardProps {
  product: Product;
  variant?: 'grid' | 'list' | 'featured';
  showGroupInfo?: boolean;
  onWishlistToggle?: (productId: string, isWishlisted: boolean) => void;
  onShare?: (product: Product) => void;
  onQuickView?: (product: Product) => void;
}

export function MobileProductCard({
  product,
  variant = 'grid',
  showGroupInfo = true,
  onWishlistToggle,
  onShare,
  onQuickView
}: MobileProductCardProps) {
  const { addToCart, getItemQuantity } = useCart();
  const [isWishlisted, setIsWishlisted] = useState(product.isWishlisted || false);
  const [quantity, setQuantity] = useState(1);
  const [isAdding, setIsAdding] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const currentQuantity = getItemQuantity(product.id);
  const discountPercentage = product.originalPrice 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0;

  const handleAddToCart = async () => {
    if (!product.inStock) {
      toast.error('Product is out of stock');
      return;
    }

    setIsAdding(true);
    
    try {
      await addToCart({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
        quantity: quantity
      });
      
      toast.success(`Added ${quantity} ${product.name} to cart`);
    } catch (error) {
      toast.error('Failed to add to cart');
    } finally {
      setIsAdding(false);
    }
  };

  const handleWishlistToggle = () => {
    const newWishlistState = !isWishlisted;
    setIsWishlisted(newWishlistState);
    onWishlistToggle?.(product.id, newWishlistState);
    
    toast.success(
      newWishlistState 
        ? 'Added to wishlist' 
        : 'Removed from wishlist'
    );
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: `Check out this product: ${product.name}`,
        url: `/products/${product.id}`
      });
    } else {
      onShare?.(product);
    }
  };

  const renderGridCard = () => (
    <Card className="overflow-hidden bg-white shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      <div className="relative">
        {/* Product Image */}
        <Link href={`/products/${product.id}`}>
          <div className="relative aspect-square bg-gray-100">
            <Image
              src={product.image}
              alt={product.name}
              fill
              className={`object-cover transition-opacity duration-300 ${
                imageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={() => setImageLoaded(true)}
            />
            
            {!imageLoaded && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse" />
            )}
          </div>
        </Link>

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {product.isNew && (
            <Badge className="bg-green-600 text-white text-xs">NEW</Badge>
          )}
          {product.isTrending && (
            <Badge className="bg-orange-600 text-white text-xs">
              <TrendingUp className="h-3 w-3 mr-1" />
              TRENDING
            </Badge>
          )}
          {discountPercentage > 0 && (
            <Badge className="bg-red-600 text-white text-xs">
              -{discountPercentage}%
            </Badge>
          )}
        </div>

        {/* Action Buttons */}
        <div className="absolute top-2 right-2 flex flex-col gap-1">
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={handleWishlistToggle}
            className={`
              w-8 h-8 rounded-full flex items-center justify-center
              ${isWishlisted 
                ? 'bg-red-100 text-red-600' 
                : 'bg-white/80 text-gray-600 hover:bg-white'
              }
              backdrop-blur-sm transition-colors duration-200
            `}
          >
            <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current' : ''}`} />
          </motion.button>
          
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={handleShare}
            className="w-8 h-8 rounded-full bg-white/80 text-gray-600 hover:bg-white backdrop-blur-sm flex items-center justify-center transition-colors duration-200"
          >
            <Share2 className="h-4 w-4" />
          </motion.button>
          
          {onQuickView && (
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={() => onQuickView(product)}
              className="w-8 h-8 rounded-full bg-white/80 text-gray-600 hover:bg-white backdrop-blur-sm flex items-center justify-center transition-colors duration-200"
            >
              <Eye className="h-4 w-4" />
            </motion.button>
          )}
        </div>

        {/* Stock Status */}
        {!product.inStock && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <Badge variant="destructive" className="text-white">
              Out of Stock
            </Badge>
          </div>
        )}
      </div>

      <CardContent className="p-3">
        {/* Product Info */}
        <div className="space-y-2">
          <Link href={`/products/${product.id}`}>
            <h3 className="font-medium text-sm text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors">
              {product.name}
            </h3>
          </Link>
          
          <div className="flex items-center gap-1">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-3 w-3 ${
                    i < Math.floor(product.rating)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500">({product.reviewCount})</span>
          </div>

          {/* Price */}
          <div className="flex items-center gap-2">
            <span className="font-bold text-lg text-gray-900">
              {formatCurrency(product.price)}
            </span>
            {product.originalPrice && (
              <span className="text-sm text-gray-500 line-through">
                {formatCurrency(product.originalPrice)}
              </span>
            )}
          </div>

          {/* Group Discount Info */}
          {showGroupInfo && product.groupDiscount && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
              <div className="flex items-center gap-1 text-xs text-blue-700 mb-1">
                <Users className="h-3 w-3" />
                <span>Group Discount Available</span>
              </div>
              <div className="text-xs text-blue-600">
                {product.groupDiscount.currentCount}/{product.groupDiscount.threshold} people
                • {product.groupDiscount.discount}% off
              </div>
            </div>
          )}

          {/* Add to Cart Section */}
          <div className="flex items-center gap-2 pt-2">
            {currentQuantity > 0 ? (
              <div className="flex items-center gap-2 flex-1">
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-1 hover:bg-gray-100 rounded-l-lg"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-3 py-1 text-sm font-medium min-w-[40px] text-center">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="p-1 hover:bg-gray-100 rounded-r-lg"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <Badge variant="secondary" className="text-xs">
                  {currentQuantity} in cart
                </Badge>
              </div>
            ) : (
              <div className="flex-1" />
            )}
            
            <Button
              onClick={handleAddToCart}
              disabled={!product.inStock || isAdding}
              size="sm"
              className="flex-shrink-0"
            >
              {isAdding ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
              ) : currentQuantity > 0 ? (
                <Plus className="h-4 w-4" />
              ) : (
                <ShoppingCart className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderListCard = () => (
    <Card className="overflow-hidden bg-white shadow-sm border border-gray-200">
      <CardContent className="p-3">
        <div className="flex gap-3">
          {/* Product Image */}
          <Link href={`/products/${product.id}`} className="flex-shrink-0">
            <div className="relative w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
              <Image
                src={product.image}
                alt={product.name}
                fill
                className="object-cover"
              />
              
              {discountPercentage > 0 && (
                <Badge className="absolute top-1 left-1 bg-red-600 text-white text-xs px-1 py-0">
                  -{discountPercentage}%
                </Badge>
              )}
            </div>
          </Link>

          {/* Product Info */}
          <div className="flex-1 min-w-0">
            <Link href={`/products/${product.id}`}>
              <h3 className="font-medium text-sm text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors">
                {product.name}
              </h3>
            </Link>
            
            <div className="flex items-center gap-1 mt-1">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < Math.floor(product.rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-500">({product.reviewCount})</span>
            </div>

            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center gap-2">
                <span className="font-bold text-base text-gray-900">
                  {formatCurrency(product.price)}
                </span>
                {product.originalPrice && (
                  <span className="text-sm text-gray-500 line-through">
                    {formatCurrency(product.originalPrice)}
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleWishlistToggle}
                  className="p-1 h-8 w-8"
                >
                  <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current text-red-600' : 'text-gray-600'}`} />
                </Button>
                
                <Button
                  onClick={handleAddToCart}
                  disabled={!product.inStock || isAdding}
                  size="sm"
                  className="h-8"
                >
                  {isAdding ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                  ) : (
                    <ShoppingCart className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderFeaturedCard = () => (
    <Card className="overflow-hidden bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200">
      <div className="relative">
        <Link href={`/products/${product.id}`}>
          <div className="relative aspect-[4/3] bg-gray-100">
            <Image
              src={product.image}
              alt={product.name}
              fill
              className="object-cover"
            />
          </div>
        </Link>

        <div className="absolute top-3 left-3">
          <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            FEATURED
          </Badge>
        </div>

        <div className="absolute top-3 right-3">
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={handleWishlistToggle}
            className={`
              w-10 h-10 rounded-full flex items-center justify-center
              ${isWishlisted 
                ? 'bg-red-100 text-red-600' 
                : 'bg-white/90 text-gray-600'
              }
              backdrop-blur-sm
            `}
          >
            <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
          </motion.button>
        </div>
      </div>

      <CardContent className="p-4">
        <Link href={`/products/${product.id}`}>
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors">
            {product.name}
          </h3>
        </Link>
        
        <div className="flex items-center gap-2 mt-2">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < Math.floor(product.rating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm text-gray-500">({product.reviewCount} reviews)</span>
        </div>

        <div className="flex items-center gap-3 mt-3">
          <span className="font-bold text-xl text-gray-900">
            {formatCurrency(product.price)}
          </span>
          {product.originalPrice && (
            <span className="text-lg text-gray-500 line-through">
              {formatCurrency(product.originalPrice)}
            </span>
          )}
          {discountPercentage > 0 && (
            <Badge className="bg-red-600 text-white">
              -{discountPercentage}%
            </Badge>
          )}
        </div>

        <Button
          onClick={handleAddToCart}
          disabled={!product.inStock || isAdding}
          className="w-full mt-4"
          size="lg"
        >
          {isAdding ? (
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent" />
          ) : (
            <>
              <ShoppingCart className="h-5 w-5 mr-2" />
              Add to Cart
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );

  switch (variant) {
    case 'list':
      return renderListCard();
    case 'featured':
      return renderFeaturedCard();
    default:
      return renderGridCard();
  }
}
