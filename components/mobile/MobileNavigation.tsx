'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Home, 
  Search, 
  ShoppingCart, 
  Users, 
  User,
  Menu,
  X,
  Bell,
  Heart,
  Package,
  Settings,
  LogOut,
  TrendingUp,
  Gift
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/context/AuthContext';
import { useCart } from '@/context/CartContext';
import Link from 'next/link';

interface NavigationItem {
  id: string;
  label: string;
  icon: any;
  href: string;
  badge?: number;
  color?: string;
}

interface MobileNavigationProps {
  showLabels?: boolean;
  variant?: 'bottom' | 'sidebar';
  className?: string;
}

export function MobileNavigation({
  showLabels = true,
  variant = 'bottom',
  className = ''
}: MobileNavigationProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { totalItems } = useCart();
  
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [notifications, setNotifications] = useState(3); // Mock notification count

  // Main navigation items for bottom bar
  const mainNavItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      href: '/',
      color: 'text-blue-600'
    },
    {
      id: 'search',
      label: 'Search',
      icon: Search,
      href: '/search',
      color: 'text-green-600'
    },
    {
      id: 'cart',
      label: 'Cart',
      icon: ShoppingCart,
      href: '/cart',
      badge: totalItems,
      color: 'text-purple-600'
    },
    {
      id: 'groups',
      label: 'Groups',
      icon: Users,
      href: '/groups',
      color: 'text-orange-600'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      href: '/profile',
      color: 'text-pink-600'
    }
  ];

  // Sidebar navigation items
  const sidebarNavItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      href: '/'
    },
    {
      id: 'products',
      label: 'Products',
      icon: Package,
      href: '/products'
    },
    {
      id: 'groups',
      label: 'My Groups',
      icon: Users,
      href: '/groups'
    },
    {
      id: 'orders',
      label: 'Orders',
      icon: Package,
      href: '/orders'
    },
    {
      id: 'wishlist',
      label: 'Wishlist',
      icon: Heart,
      href: '/wishlist'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: TrendingUp,
      href: '/analytics'
    },
    {
      id: 'promotions',
      label: 'Promotions',
      icon: Gift,
      href: '/promotions'
    }
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  const handleNavigation = (href: string) => {
    router.push(href);
    if (variant === 'sidebar') {
      setIsSidebarOpen(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    setIsSidebarOpen(false);
    router.push('/');
  };

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isSidebarOpen && variant === 'sidebar') {
        const sidebar = document.getElementById('mobile-sidebar');
        if (sidebar && !sidebar.contains(event.target as Node)) {
          setIsSidebarOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isSidebarOpen, variant]);

  // Prevent body scroll when sidebar is open
  useEffect(() => {
    if (isSidebarOpen && variant === 'sidebar') {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isSidebarOpen, variant]);

  if (variant === 'bottom') {
    return (
      <motion.nav
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        className={`
          fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 
          safe-area-pb ${className}
        `}
      >
        <div className="flex items-center justify-around px-2 py-2">
          {mainNavItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            
            return (
              <motion.button
                key={item.id}
                onClick={() => handleNavigation(item.href)}
                className={`
                  relative flex flex-col items-center justify-center p-2 rounded-lg
                  min-w-[60px] transition-colors duration-200
                  ${active 
                    ? 'text-blue-600 bg-blue-50' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
                whileTap={{ scale: 0.95 }}
              >
                <div className="relative">
                  <Icon className={`h-6 w-6 ${active ? item.color : ''}`} />
                  
                  {item.badge && item.badge > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
                    >
                      {item.badge > 99 ? '99+' : item.badge}
                    </Badge>
                  )}
                </div>
                
                {showLabels && (
                  <span className={`
                    text-xs mt-1 font-medium
                    ${active ? 'text-blue-600' : 'text-gray-600'}
                  `}>
                    {item.label}
                  </span>
                )}
                
                {active && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 bg-blue-100 rounded-lg -z-10"
                    initial={false}
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
              </motion.button>
            );
          })}
        </div>
      </motion.nav>
    );
  }

  // Sidebar variant
  return (
    <>
      {/* Menu Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsSidebarOpen(true)}
        className="fixed top-4 left-4 z-50 md:hidden"
      >
        <Menu className="h-6 w-6" />
      </Button>

      {/* Overlay */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.div
            id="mobile-sidebar"
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ type: "spring", damping: 30, stiffness: 300 }}
            className="fixed top-0 left-0 bottom-0 w-80 bg-white shadow-xl z-50 overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">S</span>
                </div>
                <div>
                  <h2 className="font-semibold text-gray-900">StockvelMarket</h2>
                  {user && (
                    <p className="text-sm text-gray-600">{user.name}</p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="relative"
                >
                  <Bell className="h-5 w-5" />
                  {notifications > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs"
                    >
                      {notifications}
                    </Badge>
                  )}
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSidebarOpen(false)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="py-4">
              {sidebarNavItems.map((item) => {
                const Icon = item.icon;
                const active = isActive(item.href);
                
                return (
                  <motion.button
                    key={item.id}
                    onClick={() => handleNavigation(item.href)}
                    className={`
                      w-full flex items-center gap-3 px-4 py-3 text-left
                      transition-colors duration-200
                      ${active 
                        ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' 
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                      }
                    `}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="font-medium">{item.label}</span>
                    
                    {item.badge && item.badge > 0 && (
                      <Badge 
                        variant="secondary" 
                        className="ml-auto"
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </motion.button>
                );
              })}
            </div>

            <Separator />

            {/* Settings and Logout */}
            <div className="py-4">
              <motion.button
                onClick={() => handleNavigation('/settings')}
                className="w-full flex items-center gap-3 px-4 py-3 text-left text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200"
                whileTap={{ scale: 0.98 }}
              >
                <Settings className="h-5 w-5" />
                <span className="font-medium">Settings</span>
              </motion.button>
              
              {user && (
                <motion.button
                  onClick={handleLogout}
                  className="w-full flex items-center gap-3 px-4 py-3 text-left text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors duration-200"
                  whileTap={{ scale: 0.98 }}
                >
                  <LogOut className="h-5 w-5" />
                  <span className="font-medium">Logout</span>
                </motion.button>
              )}
            </div>

            {/* Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
              <p className="text-xs text-gray-500 text-center">
                StockvelMarket v1.0.0
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
