

"use client"

import { Bell, ChevronDown } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuth } from '@/context/AuthContext'
import { SafeLink } from '@/components/ui/safe-link'
import { ROUTES } from '@/lib/routes'

export function AdminTopNavigation() {
  const { user, logout } = useAuth()

  return (
    <header className="bg-white shadow-sm w-full">
      <div className="flex items-center justify-end p-4">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2">
                <span style={{ fontFamily: "Avenir, sans-serif" }}>{user?.name || 'Admin User'}</span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <SafeLink href={ROUTES.ADMIN_PROFILE}>Profile</SafeLink>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <SafeLink href={ROUTES.ADMIN_SETTINGS}>Settings</SafeLink>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

