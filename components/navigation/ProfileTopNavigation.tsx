

"use client"

import { <PERSON>, ChevronDown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/context/AuthContext"
import Link from "next/link"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { CartIconWithOverlay } from "@/components/cart/CartIconWithOverlay"

export function ProfileTopNavigation() {
  const { user, logout } = useAuth()
  const router = useRouter()

  const navigateToHome = () => {
    router.push("/")
  }

  return (
    <header className="bg-white shadow-sm w-full">
      <div className="flex items-center justify-between p-4">
        {/* Logo and Title */}
        <div className="flex items-center gap-3 cursor-pointer" onClick={navigateToHome}>
          <div className="relative h-8 w-8 overflow-hidden rounded-full">
            <Image src="/StokvelLogo.avif" alt="Stokvel Logo" fill className="object-cover" />
          </div>
          <h2
            className="text-lg font-semibold text-[#2A7C6C] hidden sm:block"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            Stokvel
          </h2>
        </div>

        <div className="flex items-center gap-2">
          {/* Notification Button */}
          <Button variant="ghost" size="icon" aria-label="Notifications">
            <Bell className="h-5 w-5" />
          </Button>

          {/* Cart Button */}
          <CartIconWithOverlay />

          {/* User Dropdown Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2">
                <Avatar>
                  <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
                </Avatar>
                <span className="hidden sm:inline" style={{ fontFamily: "Avenir, sans-serif" }}>
                  {user?.name || "User"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Link href="/profile">Profile</Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="/profile/settings">Settings</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

