"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  ChevronLeft,
  ChevronRight,
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Settings,
  HelpCircle,
  LogOut,
  Archive,
  TagIcon,
  ArchiveIcon,
  BarChart3,
  Activity,
  FileText,
  UserCheck,
  Brain,
  Zap,
  MapPin
} from 'lucide-react'
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { useAuth } from "@/context/AuthContext"
import { ADMIN_NAV_ITEMS, type StaticRoute } from "@/lib/routes"
import { SafeLink } from "@/components/ui/safe-link"

const mainNavItems = [
  { icon: LayoutDashboard, ...ADMIN_NAV_ITEMS[0] },
  { icon: Brain, label: "AI-Powered", href: "/admin/ai" },
  { icon: Zap, label: "Automation", href: "/admin/automation" },
  { icon: BarChart3, label: "Analytics", href: "/admin/analytics" },
  { icon: Activity, label: "Performance", href: "/admin/performance" },
  { icon: FileText, label: "Reports", href: "/admin/reports" },
  { icon: Package, ...ADMIN_NAV_ITEMS[1] },
  { icon: TagIcon, ...ADMIN_NAV_ITEMS[2] },
  { icon: ArchiveIcon, ...ADMIN_NAV_ITEMS[3] },
  { icon: Archive, ...ADMIN_NAV_ITEMS[4] },
  { icon: ShoppingCart, ...ADMIN_NAV_ITEMS[5] },
  { icon: Users, ...ADMIN_NAV_ITEMS[6] },
  { icon: MapPin, ...ADMIN_NAV_ITEMS[7] },
  { icon: UserCheck, label: "Enhanced Users", href: "/admin/users/enhanced" },
]

const secondaryNavItems = [
  { icon: Settings, label: "Settings", href: "/admin/settings" },
  { icon: HelpCircle, label: "Help", href: "/admin/help" },
]

export function DashboardSidebar() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const pathname = usePathname()
  const { logout } = useAuth()

  const isLinkActive = (href: string) => {
    if (href === "/admin") {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const renderNavItem = (item: { icon: React.ElementType, label: string, href: string }) => {
    const isActive = isLinkActive(item.href)
    return (
      <SafeLink key={item.label} href={item.href}>
        <Button
          variant={isActive ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start",
            !isSidebarOpen && "px-2",
            isActive && "bg-[#2A7C6C] text-white hover:bg-[#236358] hover:text-white"
          )}
        >
          <item.icon className="h-5 w-5 mr-2" />
          {isSidebarOpen && (
            <span style={{ fontFamily: "Avenir, sans-serif" }}>
              {item.label}
            </span>
          )}
        </Button>
      </SafeLink>
    )
  }

  return (
    <aside
      className={cn(
        "relative flex flex-col",
        "min-h-screen bg-white border-r transition-all duration-300 ease-in-out",
        "shadow-[1px_0_3px_rgba(0,0,0,0.1)]",
        isSidebarOpen ? "w-64" : "w-20"
      )}
    >
      {/* Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-4 top-6 z-50 h-8 w-8 rounded-full border bg-white shadow-md"
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
      >
        {isSidebarOpen ? (
          <ChevronLeft className="h-4 w-4" />
        ) : (
          <ChevronRight className="h-4 w-4" />
        )}
      </Button>

      {/* Logo and Profile Section */}
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="relative h-10 w-10 overflow-hidden rounded-full">
            <Image
              src="/StokvelLogo.avif"
              alt="Stokvel Logo"
              fill
              className="object-cover"
            />
          </div>
          {isSidebarOpen && (
            <div>
              <h2 
                className="text-lg font-semibold text-[#2A7C6C]"
                style={{ 
                  fontFamily: 'ClashDisplay-Variable, sans-serif',
                  letterSpacing: '-0.02em' 
                }}
              >
                StockVel
              </h2>
            </div>
          )}
        </div>
      </div>

      <Separator />

      {/* Main Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-2">
        {mainNavItems.map(renderNavItem)}
      </nav>

      <Separator />

      {/* Secondary Navigation */}
      <nav className="px-4 py-4 space-y-2">
        {secondaryNavItems.map(renderNavItem)}
      </nav>

      {/* Logout Section */}
      <div className="p-4 mt-auto border-t">
        <Button 
          variant="ghost" 
          className={cn("w-full justify-start", !isSidebarOpen && "px-2")} 
          onClick={logout}
        >
          <LogOut className="h-5 w-5 mr-2" />
          {isSidebarOpen && (
            <span style={{ fontFamily: "Avenir, sans-serif" }}>
              Logout
            </span>
          )}
        </Button>
      </div>
    </aside>
  )
}
