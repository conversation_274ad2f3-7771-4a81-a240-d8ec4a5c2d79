
"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { usePathname, useRouter } from "next/navigation"
import {
  User,
  Menu,
  LogOut,
  Home,
  Store,
  Info,
  HelpCircle,
  Mail,
  Users,
  LayoutDashboard,
  Search,
  ChevronDown,
  Sparkles,
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

import { Button } from "@/components/ui/button"
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
  NavigationMenuLink,
} from "@/components/ui/navigation-menu"
import { MobileMenu } from "@/components/navigation/mobile-menu"
import { cn } from "@/lib/utils"
import { useAuth } from "@/context/AuthContext"
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership"
import { SearchOverlay } from "@/components/search/SearchOverlay"
import { CartIconWithOverlay } from "@/components/cart/CartIconWithOverlay"
import { ROUTES, getRouteFromNavItem } from "@/lib/routes"
import { SafeLink } from "@/components/ui/safe-link"

const navItems = [
  { name: "Home", icon: Home },
  { name: "Store", icon: Store },
  // { name: "Offers", icon: Tag },
  { name: "Aboutus", icon: Info },
  { name: "FAQ", icon: HelpCircle },
  { name: "Contact", icon: Mail },
  // { name: "Loans", icon: CreditCard },
]

export function SiteHeader() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [isSearchOverlayOpen, setIsSearchOverlayOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const { user, isAuthenticated, logout } = useAuth()
  const { userGroups, error: groupsError } = useGroupMembership(user?._id)

  // Log any errors with groups
  useEffect(() => {
    if (groupsError) {
      console.error('SiteHeader - Error loading groups:', groupsError);
    }
  }, [groupsError])

  const isActive = (path: string) => {
    if (path === "Home" && pathname === "/") return true
    return pathname.startsWith(`/${path.toLowerCase()}`)
  }

  const handleLogout = async () => {
    await logout()
    setIsUserMenuOpen(false)
    router.push("/")
  }

  const renderUserIcons = () => {
    if (!isAuthenticated) {
      return (
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant="ghost"
            className="relative flex items-center h-12 px-6 rounded-2xl bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white hover:from-[#236358] hover:to-[#164239] transition-all duration-300 shadow-lg hover:shadow-xl font-semibold"
            aria-label="User account"
            onClick={() => router.push("/login")}
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            <User className="h-5 w-5 mr-2" />
            <span>Login</span>
          </Button>
        </motion.div>
      )
    }

    return (
      <div className="flex items-center space-x-2">
        {user?.role === "admin" && (
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant="ghost"
              className="relative flex items-center h-12 px-4 rounded-2xl bg-purple-100/50 hover:bg-purple-200/50 text-purple-700 hover:text-purple-800 transition-all duration-300 shadow-sm hover:shadow-md font-semibold"
              aria-label="Admin dashboard"
              onClick={() => router.push("/admin")}
              style={{ fontFamily: "Avenir, sans-serif" }}
            >
              <LayoutDashboard className="h-5 w-5 mr-2" />
              <span className="hidden xl:inline">Dashboard</span>
            </Button>
          </motion.div>
        )}

        {userGroups && userGroups.length > 0 && (
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <SafeLink href={ROUTES.GROUPS}>
              <Button
                variant="ghost"
                className="relative flex items-center h-12 px-4 rounded-2xl bg-blue-100/50 hover:bg-blue-200/50 text-blue-700 hover:text-blue-800 transition-all duration-300 shadow-sm hover:shadow-md font-semibold"
                aria-label="User groups"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                <Users className="h-5 w-5 mr-2" />
                <span className="hidden xl:inline">Groups</span>
              </Button>
            </SafeLink>
          </motion.div>
        )}

        {user?.role === "customer" && (
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <SafeLink href={ROUTES.PROFILE}>
              <Button
                variant="ghost"
                className="relative flex items-center h-12 px-4 rounded-2xl bg-green-100/50 hover:bg-green-200/50 text-green-700 hover:text-green-800 transition-all duration-300 shadow-sm hover:shadow-md font-semibold"
                aria-label="User profile"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                <User className="h-5 w-5 mr-2" />
                <span className="hidden xl:inline">Profile</span>
              </Button>
            </SafeLink>
          </motion.div>
        )}

        <div className="relative">
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant="ghost"
              className="text-sm font-semibold flex items-center h-12 px-4 rounded-2xl bg-gray-100/50 hover:bg-[#2A7C6C]/10 hover:text-[#2A7C6C] transition-all duration-300 shadow-sm hover:shadow-md"
              aria-label="User account"
              onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
              style={{ fontFamily: "Avenir, sans-serif" }}
            >
              <User className="h-5 w-5 mr-2" />
              <span className="hidden sm:inline">{user?.name}</span>
              <ChevronDown className="h-4 w-4 ml-1" />
            </Button>
          </motion.div>

          <AnimatePresence>
            {isUserMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="absolute right-0 mt-2 w-56 bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 py-2 z-10"
              >
                <div className="px-4 py-3 border-b border-gray-200/50">
                  <p className="text-sm font-semibold text-gray-900" style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}>
                    {user?.name}
                  </p>
                  <p className="text-xs text-gray-500" style={{ fontFamily: "Avenir, sans-serif" }}>
                    {user?.email}
                  </p>
                </div>
                <motion.button
                  whileHover={{ backgroundColor: "rgba(42, 124, 108, 0.1)" }}
                  onClick={handleLogout}
                  className="flex items-center px-4 py-3 text-sm text-gray-700 hover:text-[#2A7C6C] w-full text-left transition-colors duration-200 font-semibold"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  <LogOut className="h-4 w-4 mr-3" />
                  Logout
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    )
  }

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="sticky top-0 z-50 w-full bg-white/95 backdrop-blur-md border-b border-gray-200/50 shadow-lg shadow-gray-900/5"
    >
      {/* Gradient accent line */}
      <div className="h-1 bg-gradient-to-r from-[#2A7C6C] via-[#7FDBCA] to-[#2A7C6C]" />

      <div className="container flex h-20 items-center justify-between">
        {/* Logo Section */}
        <motion.div
          className="flex items-center space-x-6"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <SafeLink href="/" className="group flex items-center space-x-3">
            <div className="relative">
              <motion.div
                whileHover={{ scale: 1.05, rotate: 5 }}
                transition={{ duration: 0.3 }}
                className="relative"
              >
                <Image
                  src="/StokvelLogo.avif"
                  alt="Stokvel Logo"
                  width={48}
                  height={48}
                  className="rounded-xl shadow-lg"
                />
                <div className="absolute inset-0 bg-gradient-to-br from-[#2A7C6C]/20 to-[#7FDBCA]/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </motion.div>
            </div>
            <div className="hidden sm:block">
              <h1
                className="text-2xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent"
                style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
              >
                Stokvel
              </h1>
              <p
                className="text-xs text-gray-500 -mt-1"
                style={{ fontFamily: "Avenir, sans-serif" }}
              >
                Community Commerce
              </p>
            </div>
          </SafeLink>

          {/* Desktop Navigation */}
          <NavigationMenu className="hidden lg:flex">
            <NavigationMenuList className="gap-2">
              {navItems.map((item, index) => {
                const href = getRouteFromNavItem(item.name);
                return (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.1 + index * 0.1 }}
                >
                  <NavigationMenuItem>
                    <NavigationMenuLink asChild>
                      <SafeLink
                        href={href}
                        className={cn(
                          "group relative inline-flex h-12 items-center justify-center rounded-2xl px-6 py-2 text-sm font-semibold transition-all duration-300 hover:scale-105",
                          isActive(item.name)
                            ? "bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white shadow-lg shadow-[#2A7C6C]/25"
                            : "text-gray-700 hover:text-[#2A7C6C] hover:bg-[#2A7C6C]/5"
                        )}
                        style={{ fontFamily: "Avenir, sans-serif" }}
                      >
                        <item.icon className="mr-2 h-4 w-4" />
                        {item.name}
                        {isActive(item.name) && (
                          <motion.div
                            layoutId="activeTab"
                            className="absolute inset-0 bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] rounded-2xl"
                            style={{ zIndex: -1 }}
                            transition={{ duration: 0.3 }}
                          />
                        )}
                      </SafeLink>
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                </motion.div>
                );
              })}
            </NavigationMenuList>
          </NavigationMenu>
        </motion.div>

        {/* Right Side Actions */}
        <motion.div
          className="flex items-center space-x-3"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {renderUserIcons()}

          {/* Search Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant="ghost"
              size="icon"
              className="relative h-12 w-12 rounded-2xl bg-gray-100/50 hover:bg-[#2A7C6C]/10 hover:text-[#2A7C6C] transition-all duration-300 shadow-sm hover:shadow-md"
              aria-label="Search"
              onClick={() => setIsSearchOverlayOpen(true)}
            >
              <Search className="h-5 w-5" />
            </Button>
          </motion.div>

          {/* Cart Icon */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <CartIconWithOverlay />
          </motion.div>

          {/* Mobile Menu Button */}
          <motion.div
            className="lg:hidden"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              size="icon"
              className="h-12 w-12 rounded-2xl bg-gray-100/50 hover:bg-[#2A7C6C]/10 hover:text-[#2A7C6C] transition-all duration-300 shadow-sm hover:shadow-md"
              onClick={() => setIsMobileMenuOpen(true)}
              aria-label="Open mobile menu"
            >
              <Menu className="h-6 w-6" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navItems={navItems}
        isActive={isActive}
      />
      <SearchOverlay isOpen={isSearchOverlayOpen} onClose={() => setIsSearchOverlayOpen(false)} />
    </motion.header>
  )
}

