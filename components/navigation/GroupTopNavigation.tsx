// components/navigation/GroupTopNavigation.tsx

"use client";

import { ChevronDown, ShoppingBag, Truck, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/context/AuthContext";
import { CartIconWithOverlay } from "@/components/cart/CartIconWithOverlay";
import { buildGroupProductsRoute, buildGroupMembersRoute, ROUTES } from "@/lib/routes";
import { SafeLink } from "@/components/ui/safe-link";
// import { cn } from "@/lib/utils";

interface GroupTopNavigationProps {
  groupProgress: {
    totalAmount: number;
    targetAmount: number;
    nextDeliveryDate?: string;
    groupId: string;
  };
}

export function GroupTopNavigation({ groupProgress }: GroupTopNavigationProps) {
  const { user } = useAuth();
  const progressPercentage = (groupProgress.totalAmount / groupProgress.targetAmount) * 100;

  return (
    <header className="bg-white shadow-sm w-full">
      <div className="flex items-center justify-between p-4">
        {/* Progress Section */}
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5 text-[#2A7C6C]" />
            <div>
              <p className="text-sm text-gray-600">Group Progress</p>
              <p className="font-semibold">
                R{groupProgress.totalAmount.toLocaleString()} / R
                {groupProgress.targetAmount.toLocaleString()}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Truck className="h-5 w-5 text-[#2A7C6C]" />
            <div>
              <p className="text-sm text-gray-600">Next Delivery</p>
              <p className="font-semibold">
                {groupProgress.nextDeliveryDate || "To be scheduled"}
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-48 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-[#2A7C6C] transition-all duration-500"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* User Section with Group Order Overlay and Cart Icon */}
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            {/* Profile Button */}
            <Button
              variant="ghost"
              className="relative"
              asChild
            >
              <SafeLink href="/profile" className="flex items-center">
                <User className="h-5 w-5" />
                <span className="ml-2 hidden md:inline">Profile</span>
              </SafeLink>
            </Button>

            {/* Cart Button */}
            <CartIconWithOverlay />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2">
                <span style={{ fontFamily: "Avenir, sans-serif" }}>
                  {user?.name || "User"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Group Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <SafeLink href={buildGroupProductsRoute(groupProgress.groupId)}>View Products</SafeLink>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <SafeLink href={buildGroupMembersRoute(groupProgress.groupId)}>View Members</SafeLink>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <SafeLink href={ROUTES.PROFILE}>Back to Profile</SafeLink>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Cart overlay is now handled by CartIconWithOverlay */}
    </header>
  );
}
