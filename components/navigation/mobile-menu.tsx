// // import { useState } from "react"
// import Link from "next/link"
// import { usePathname } from "next/navigation"
// import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle } from "@/components/ui/sheet"

// interface MobileMenuProps {
//   isOpen: boolean
//   onClose: () => void
//   navItems: string[]
// }

// export function MobileMenu({ isOpen, onClose, navItems }: MobileMenuProps) {
//   const pathname = usePathname()

//   const isActive = (path: string) => {
//     if (path === "Home" && pathname === "/") return true
//     return pathname === `/${path.toLowerCase()}`
//   }

//   return (
//     <Sheet open={isOpen} onOpenChange={onClose}>
//       <SheetContent side="left">
//         <SheetHeader>
//           <SheetTitle>Menu</SheetTitle>
//         </SheetHeader>
//         <nav className="mt-8">
//           <ul className="space-y-4">
//             {navItems.map((item) => (
//               <li key={item}>
//                 <Link
//                   href={item === "Home" ? "/" : `/${item.toLowerCase()}`}
//                   className={`block py-2 text-lg ${
//                     isActive(item) ? 'text-[#2A7C6C] font-medium' : 'text-gray-600'
//                   }`}
//                   onClick={onClose}
//                 >
//                   {item}
//                 </Link>
//               </li>
//             ))}
//           </ul>
//         </nav>
//       </SheetContent>
//     </Sheet>
//   )
// }


// import { useState, useEffect } from 'react'
// import Link from 'next/link'
// import { cn } from "@/lib/utils"

// interface MobileMenuProps {
//   isOpen: boolean
//   onClose: () => void
//   navItems: string[]
//   isActive: (path: string) => boolean
// }

// export function MobileMenu({ isOpen, onClose, navItems, isActive }: MobileMenuProps) {
//   const [isClosing, setIsClosing] = useState(false)

//   useEffect(() => {
//     if (!isOpen) {
//       setIsClosing(false)
//     }
//   }, [isOpen])

//   const handleClose = () => {
//     setIsClosing(true)
//     setTimeout(onClose, 300) // Match this with the transition duration
//   }

//   if (!isOpen && !isClosing) {
//     return null
//   }

//   return (
//     <div
//       className={cn(
//         "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
//         isClosing ? "animate-out fade-out" : "animate-in fade-in",
//       )}
//       onClick={handleClose}
//     >
//       <div
//         className={cn(
//           "fixed right-0 top-0 h-full w-3/4 max-w-xs bg-background p-6 shadow-lg",
//           isClosing ? "animate-out slide-out-to-right" : "animate-in slide-in-from-right",
//         )}
//         onClick={(e) => e.stopPropagation()}
//       >
//         <nav className="space-y-4">
//           {navItems.map((item) => (
//             <Link
//               key={item}
//               href={item === "Home" ? "/" : `/${item.toLowerCase()}`}
//               className={cn(
//                 "block py-2 text-lg font-medium transition-colors hover:text-primary",
//                 isActive(item) ? "text-primary" : "text-foreground"
//               )}
//               onClick={handleClose}
//             >
//               {item}
//             </Link>
//           ))}
//         </nav>
//       </div>
//     </div>
//   )
// }




import { useState, useEffect } from 'react'
import Link from 'next/link'
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { X } from 'lucide-react'

interface NavItem {
  name: string
  icon: React.ElementType
}

interface MobileMenuProps {
  isOpen: boolean
  onClose: () => void
  navItems: NavItem[]
  isActive: (path: string) => boolean
}

export function MobileMenu({ isOpen, onClose, navItems, isActive }: MobileMenuProps) {
  const [isClosing, setIsClosing] = useState(false)

  useEffect(() => {
    if (!isOpen) {
      setIsClosing(false)
    }
  }, [isOpen])

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(onClose, 300) // Match this with the transition duration
  }

  if (!isOpen && !isClosing) {
    return null
  }

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
        isClosing ? "animate-out fade-out" : "animate-in fade-in",
      )}
      onClick={handleClose}
    >
      <div
        className={cn(
          "fixed left-0 top-0 h-full w-3/4 max-w-xs bg-background p-6 shadow-lg",
          isClosing ? "animate-out slide-out-to-left" : "animate-in slide-in-from-left",
        )}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-end mb-4">
          <Button variant="ghost" size="icon" onClick={handleClose}>
            <X className="h-6 w-6" />
          </Button>
        </div>
        <nav className="space-y-4">
          {navItems.map((item) => {
            const href = item.name === "Home" ? "/" : `/${item.name.toLowerCase()}`;
            return (
            <Link
              key={item.name}
              href={href as "/" | "/store" | "/aboutus" | "/faq" | "/contact"}
              className={cn(
                "flex items-center py-2 text-lg font-medium transition-colors hover:text-primary",
                isActive(item.name) ? "text-primary" : "text-foreground"
              )}
              onClick={handleClose}
            >
              <item.icon className="mr-2 h-5 w-5" />
              {item.name}
            </Link>
            );
          })}
        </nav>
      </div>
    </div>
  )
}

