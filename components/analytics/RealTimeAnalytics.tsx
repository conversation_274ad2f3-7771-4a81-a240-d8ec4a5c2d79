'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Users, 
  ShoppingCart, 
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap,
  Eye,
  Wifi,
  Server
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { formatCurrency, formatNumber } from '@/lib/utils';
// Removed unused import: useRealTimeUpdates

interface RealTimeMetric {
  id: string;
  name: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  unit: string;
}

interface LiveEvent {
  id: string;
  type: string;
  message: string;
  timestamp: Date;
  severity: 'info' | 'success' | 'warning' | 'error';
  data?: Record<string, unknown>;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  uptime: number;
}

export function RealTimeAnalytics() {
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([
    {
      id: 'active_users',
      name: 'Active Users',
      value: 0,
      change: 0,
      trend: 'stable',
      icon: Users,
      color: 'text-blue-600',
      unit: 'users'
    },
    {
      id: 'live_orders',
      name: 'Live Orders',
      value: 0,
      change: 0,
      trend: 'stable',
      icon: ShoppingCart,
      color: 'text-green-600',
      unit: 'orders'
    },
    {
      id: 'revenue_today',
      name: 'Revenue Today',
      value: 0,
      change: 0,
      trend: 'stable',
      icon: DollarSign,
      color: 'text-purple-600',
      unit: 'ZAR'
    },
    {
      id: 'conversion_rate',
      name: 'Conversion Rate',
      value: 0,
      change: 0,
      trend: 'stable',
      icon: TrendingUp,
      color: 'text-orange-600',
      unit: '%'
    }
  ]);

  const [liveEvents, setLiveEvents] = useState<LiveEvent[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    status: 'healthy',
    cpu: 45,
    memory: 62,
    disk: 38,
    network: 25,
    uptime: 99.9
  });

  const [isConnected, _setIsConnected] = useState(true);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update metrics with random changes
      setMetrics(prev => prev.map(metric => {
        const change = (Math.random() - 0.5) * 10;
        const newValue = Math.max(0, metric.value + change);
        
        return {
          ...metric,
          value: newValue,
          change: change,
          trend: change > 2 ? 'up' : change < -2 ? 'down' : 'stable'
        };
      }));

      // Add random events
      if (Math.random() > 0.7) {
        const eventTypes = [
          { type: 'order_placed', message: 'New order placed', severity: 'success' as const },
          { type: 'user_joined', message: 'New user registered', severity: 'info' as const },
          { type: 'payment_completed', message: 'Payment processed successfully', severity: 'success' as const },
          { type: 'group_milestone', message: 'Group reached discount milestone', severity: 'success' as const },
          { type: 'system_alert', message: 'High server load detected', severity: 'warning' as const }
        ];

        const randomEvent = eventTypes[Math.floor(Math.random() * eventTypes.length)];
        const newEvent: LiveEvent = {
          id: Date.now().toString(),
          ...randomEvent,
          timestamp: new Date()
        };

        setLiveEvents(prev => [newEvent, ...prev.slice(0, 19)]); // Keep last 20 events
      }

      // Update system health
      setSystemHealth(prev => ({
        ...prev,
        cpu: Math.max(0, Math.min(100, prev.cpu + (Math.random() - 0.5) * 10)),
        memory: Math.max(0, Math.min(100, prev.memory + (Math.random() - 0.5) * 5)),
        network: Math.max(0, Math.min(100, prev.network + (Math.random() - 0.5) * 15))
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Initialize with some sample data
  useEffect(() => {
    setMetrics(prev => prev.map(metric => ({
      ...metric,
      value: Math.floor(Math.random() * 100) + 50
    })));
  }, []);

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'order_placed':
        return <ShoppingCart className="h-4 w-4" />;
      case 'user_joined':
        return <Users className="h-4 w-4" />;
      case 'payment_completed':
        return <DollarSign className="h-4 w-4" />;
      case 'group_milestone':
        return <TrendingUp className="h-4 w-4" />;
      case 'system_alert':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getEventColor = (severity: string) => {
    switch (severity) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getHealthColor = (value: number, isInverted = false) => {
    if (isInverted) {
      if (value < 30) return 'text-green-600';
      if (value < 70) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (value > 70) return 'text-green-600';
      if (value > 40) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Real-time Analytics</h2>
          <p className="text-gray-600">Live metrics and system monitoring</p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <Wifi className="h-4 w-4 text-red-600" />
            )}
            <span className={`text-sm ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <Activity className="h-3 w-3 mr-1" />
            Live
          </Badge>
        </div>
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{metric.name}</p>
                    <motion.p 
                      className="text-3xl font-bold"
                      key={metric.value}
                      initial={{ scale: 1.1 }}
                      animate={{ scale: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      {metric.unit === 'ZAR' 
                        ? formatCurrency(metric.value)
                        : metric.unit === '%'
                        ? `${metric.value.toFixed(1)}%`
                        : formatNumber(metric.value)
                      }
                    </motion.p>
                    <div className="flex items-center mt-2">
                      {metric.trend === 'up' && (
                        <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                      )}
                      {metric.trend === 'down' && (
                        <TrendingUp className="h-4 w-4 text-red-600 mr-1 rotate-180" />
                      )}
                      <span className={`text-sm ${
                        metric.trend === 'up' ? 'text-green-600' : 
                        metric.trend === 'down' ? 'text-red-600' : 'text-gray-500'
                      }`}>
                        {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}
                      </span>
                    </div>
                  </div>
                  <metric.icon className={`h-8 w-8 ${metric.color}`} />
                </div>
                
                {/* Pulse animation for active metrics */}
                {metric.trend !== 'stable' && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"
                    initial={{ x: '-100%' }}
                    animate={{ x: '100%' }}
                    transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 2 }}
                  />
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Live Events Feed */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Live Events
            </CardTitle>
            <CardDescription>
              Real-time system events and user activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-80">
              <AnimatePresence>
                {liveEvents.map((event, index) => (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ delay: index * 0.05 }}
                    className={`flex items-start gap-3 p-3 rounded-lg border mb-3 ${getEventColor(event.severity)}`}
                  >
                    <div className="flex-shrink-0 mt-0.5">
                      {getEventIcon(event.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{event.message}</p>
                      <p className="text-xs opacity-75">
                        {event.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {liveEvents.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>Waiting for events...</p>
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              System Health
            </CardTitle>
            <CardDescription>
              Real-time system performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Overall Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">System Status</span>
              <Badge 
                variant="outline" 
                className={
                  systemHealth.status === 'healthy' ? 'bg-green-50 text-green-700 border-green-200' :
                  systemHealth.status === 'warning' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                  'bg-red-50 text-red-700 border-red-200'
                }
              >
                {systemHealth.status === 'healthy' && <CheckCircle className="h-3 w-3 mr-1" />}
                {systemHealth.status === 'warning' && <AlertCircle className="h-3 w-3 mr-1" />}
                {systemHealth.status === 'critical' && <AlertCircle className="h-3 w-3 mr-1" />}
                {systemHealth.status.charAt(0).toUpperCase() + systemHealth.status.slice(1)}
              </Badge>
            </div>

            <Separator />

            {/* Performance Metrics */}
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>CPU Usage</span>
                  <span className={getHealthColor(systemHealth.cpu, true)}>
                    {systemHealth.cpu.toFixed(1)}%
                  </span>
                </div>
                <Progress value={systemHealth.cpu} className="h-2" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Memory Usage</span>
                  <span className={getHealthColor(systemHealth.memory, true)}>
                    {systemHealth.memory.toFixed(1)}%
                  </span>
                </div>
                <Progress value={systemHealth.memory} className="h-2" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Disk Usage</span>
                  <span className={getHealthColor(systemHealth.disk, true)}>
                    {systemHealth.disk.toFixed(1)}%
                  </span>
                </div>
                <Progress value={systemHealth.disk} className="h-2" />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Network I/O</span>
                  <span className="text-blue-600">
                    {systemHealth.network.toFixed(1)} MB/s
                  </span>
                </div>
                <Progress value={systemHealth.network} className="h-2" />
              </div>
            </div>

            <Separator />

            {/* Uptime */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Uptime</span>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-green-600" />
                <span className="text-sm font-bold text-green-600">
                  {systemHealth.uptime}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
