'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  Download,
  RefreshCw,
  <PERSON><PERSON><PERSON>,
  LineChart
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { Progress } from '@/components/ui/progress';
import { formatCurrency, formatNumber, formatPercentage } from '@/lib/utils';
import { DateRange } from 'react-day-picker';
import { addDays } from 'date-fns';

// Removed unused interface AnalyticsMetric

interface AnalyticsData {
  revenue: {
    totalRevenue: number;
    revenueGrowth: number;
    averageOrderValue: number;
    revenueByCategory: Array<{ category: string; revenue: number; percentage: number }>;
    topProducts: Array<{ productName: string; revenue: number; quantity: number }>;
  };
  users: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    userGrowthRate: number;
    userRetentionRate: number;
  };
  orders: {
    totalOrders: number;
    completedOrders: number;
    averageOrderValue: number;
    orderCompletionRate: number;
    groupOrderMetrics: {
      totalGroupOrders: number;
      averageGroupSize: number;
      groupDiscountSavings: number;
    };
  };
}

interface AnalyticsDashboardProps {
  initialData?: AnalyticsData;
}

export function AnalyticsDashboard({ initialData }: AnalyticsDashboardProps) {
  const [data, setData] = useState<AnalyticsData | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(!initialData);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date()
  });
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch analytics data
  const fetchAnalytics = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        period: selectedPeriod,
        type: 'overview'
      });

      if (dateRange?.from && dateRange?.to) {
        params.append('startDate', dateRange.from.toISOString());
        params.append('endDate', dateRange.to.toISOString());
      }

      const response = await fetch(`/api/analytics/overview?${params}`);
      if (!response.ok) throw new Error('Failed to fetch analytics');

      const result = await response.json();
      setData(result.summary);
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedPeriod, dateRange]);

  useEffect(() => {
    if (!initialData) {
      fetchAnalytics();
    }
  }, [selectedPeriod, dateRange, fetchAnalytics, initialData]);

  const handleRefresh = () => {
    fetchAnalytics();
  };

  const handleExport = () => {
    // Implementation for exporting analytics data
    console.log('Exporting analytics data...');
  };

  if (isLoading && !data) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading analytics...</span>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No analytics data available</p>
        <Button onClick={handleRefresh} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  // Calculate key metrics
  const keyMetrics = [
    {
      id: 'revenue',
      name: 'Total Revenue',
      value: data.revenue.totalRevenue,
      change: data.revenue.revenueGrowth,
      trend: data.revenue.revenueGrowth >= 0 ? 'up' : 'down',
      format: 'currency',
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      id: 'orders',
      name: 'Total Orders',
      value: data.orders.totalOrders,
      change: 0, // Would calculate from previous period
      trend: 'up',
      format: 'number',
      icon: ShoppingCart,
      color: 'text-blue-600'
    },
    {
      id: 'users',
      name: 'Active Users',
      value: data.users.activeUsers,
      change: data.users.userGrowthRate,
      trend: data.users.userGrowthRate >= 0 ? 'up' : 'down',
      format: 'number',
      icon: Users,
      color: 'text-purple-600'
    },
    {
      id: 'aov',
      name: 'Avg Order Value',
      value: data.orders.averageOrderValue,
      change: 0,
      trend: 'stable',
      format: 'currency',
      icon: TrendingUp,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-gray-600">Comprehensive business insights and metrics</p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Date Range Picker */}
          <DatePickerWithRange
            date={dateRange}
            onDateChange={setDateRange}
          />
          
          {/* Period Selector */}
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Daily</SelectItem>
              <SelectItem value="week">Weekly</SelectItem>
              <SelectItem value="month">Monthly</SelectItem>
              <SelectItem value="quarter">Quarterly</SelectItem>
              <SelectItem value="year">Yearly</SelectItem>
            </SelectContent>
          </Select>

          {/* Action Buttons */}
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {keyMetrics.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{metric.name}</p>
                    <p className="text-3xl font-bold">
                      {metric.format === 'currency' 
                        ? formatCurrency(metric.value)
                        : formatNumber(metric.value)
                      }
                    </p>
                    {metric.change !== 0 && (
                      <div className="flex items-center mt-2">
                        {metric.trend === 'up' ? (
                          <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                        )}
                        <span className={`text-sm ${
                          metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatPercentage(Math.abs(metric.change))}
                        </span>
                      </div>
                    )}
                  </div>
                  <metric.icon className={`h-8 w-8 ${metric.color}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue by Category */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Revenue by Category
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.revenue.revenueByCategory?.slice(0, 5).map((category, index) => (
                    <div key={category.category} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: `hsl(${index * 60}, 70%, 50%)` }}
                        />
                        <span className="text-sm font-medium">{category.category}</span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold">{formatCurrency(category.revenue)}</p>
                        <p className="text-xs text-gray-500">{formatPercentage(category.percentage)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Products */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Top Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.revenue.topProducts?.slice(0, 5).map((product, index) => (
                    <div key={product.productName} className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">{product.productName}</p>
                        <p className="text-xs text-gray-500">{product.quantity} sold</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold">{formatCurrency(product.revenue)}</p>
                        <Badge variant="outline" className="text-xs">
                          #{index + 1}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Group Order Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Group Order Performance</CardTitle>
              <CardDescription>
                Insights into group buying behavior and savings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {formatNumber(data.orders.groupOrderMetrics.totalGroupOrders)}
                  </p>
                  <p className="text-sm text-gray-600">Group Orders</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {formatNumber(data.orders.groupOrderMetrics.averageGroupSize)}
                  </p>
                  <p className="text-sm text-gray-600">Avg Group Size</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">
                    {formatCurrency(data.orders.groupOrderMetrics.groupDiscountSavings)}
                  </p>
                  <p className="text-sm text-gray-600">Total Savings</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Total Revenue</span>
                  <span className="font-bold">{formatCurrency(data.revenue.totalRevenue)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Revenue Growth</span>
                  <span className={`font-bold ${data.revenue.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(data.revenue.revenueGrowth)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Average Order Value</span>
                  <span className="font-bold">{formatCurrency(data.revenue.averageOrderValue)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <LineChart className="h-16 w-16 opacity-50" />
                  <span className="ml-4">Chart visualization would go here</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>User Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Total Users</span>
                  <span className="font-bold">{formatNumber(data.users.totalUsers)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Active Users</span>
                  <span className="font-bold">{formatNumber(data.users.activeUsers)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>New Users</span>
                  <span className="font-bold">{formatNumber(data.users.newUsers)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>User Growth Rate</span>
                  <span className={`font-bold ${data.users.userGrowthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatPercentage(data.users.userGrowthRate)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Retention Rate</span>
                  <span className="font-bold">{formatPercentage(data.users.userRetentionRate)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Active Users</span>
                      <span>{formatPercentage((data.users.activeUsers / data.users.totalUsers) * 100)}</span>
                    </div>
                    <Progress value={(data.users.activeUsers / data.users.totalUsers) * 100} />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Retention Rate</span>
                      <span>{formatPercentage(data.users.userRetentionRate)}</span>
                    </div>
                    <Progress value={data.users.userRetentionRate} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="orders" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Total Orders</span>
                  <span className="font-bold">{formatNumber(data.orders.totalOrders)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Completed Orders</span>
                  <span className="font-bold">{formatNumber(data.orders.completedOrders)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Completion Rate</span>
                  <span className="font-bold text-green-600">
                    {formatPercentage(data.orders.orderCompletionRate)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Average Order Value</span>
                  <span className="font-bold">{formatCurrency(data.orders.averageOrderValue)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Order Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Completion Rate</span>
                      <span>{formatPercentage(data.orders.orderCompletionRate)}</span>
                    </div>
                    <Progress value={data.orders.orderCompletionRate} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
