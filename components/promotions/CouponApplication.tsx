'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Tag, 
  Check, 
  X, 
  AlertCircle, 
  Gift, 
  Percent,
  DollarSign,
  Package,
  Users,
  Loader2,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface CartItem {
  productId: string;
  quantity: number;
  price: number;
  category: string;
  name: string;
}

interface AppliedCoupon {
  code: string;
  name: string;
  discountAmount: number;
  type: string;
}

interface AvailableCoupon {
  code: string;
  name: string;
  description: string;
  type: string;
  discountValue: number;
  minimumOrderValue?: number;
  validUntil: Date;
  groupOrdersOnly?: boolean;
}

interface CouponApplicationProps {
  cartItems: CartItem[];
  orderTotal: number;
  isGroupOrder?: boolean;
  groupSize?: number;
  userId: string;
  appliedCoupons: AppliedCoupon[];
  onCouponApplied: (coupon: AppliedCoupon) => void;
  onCouponRemoved: (couponCode: string) => void;
}

export function CouponApplication({
  cartItems,
  orderTotal,
  isGroupOrder = false,
  groupSize = 1,
  userId,
  appliedCoupons,
  onCouponApplied,
  onCouponRemoved
}: CouponApplicationProps) {
  const [couponCode, setCouponCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [isApplying, setIsApplying] = useState(false);
  const [availableCoupons, setAvailableCoupons] = useState<AvailableCoupon[]>([]);
  const [showAvailableCoupons, setShowAvailableCoupons] = useState(false);
  const [isLoadingAvailable, setIsLoadingAvailable] = useState(false);

  // Fetch available coupons for user
  const fetchAvailableCoupons = async () => {
    setIsLoadingAvailable(true);
    try {
      const response = await fetch(`/api/coupons?type=available&public=true`, {
        headers: {
          'x-user-id': userId
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAvailableCoupons(data.coupons || []);
      }
    } catch (error) {
      console.error('Error fetching available coupons:', error);
    } finally {
      setIsLoadingAvailable(false);
    }
  };

  useEffect(() => {
    if (showAvailableCoupons && availableCoupons.length === 0) {
      fetchAvailableCoupons();
    }
  }, [showAvailableCoupons]);

  const validateCoupon = async (code: string) => {
    setIsValidating(true);
    
    try {
      const response = await fetch('/api/coupons/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          userId,
          cartItems,
          orderTotal,
          isGroupOrder,
          groupSize
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error validating coupon:', error);
      return { valid: false, error: 'Failed to validate coupon' };
    } finally {
      setIsValidating(false);
    }
  };

  const applyCoupon = async (code: string) => {
    // Check if coupon is already applied
    if (appliedCoupons.some(c => c.code === code)) {
      toast.error('Coupon is already applied');
      return;
    }

    setIsApplying(true);

    try {
      const response = await fetch('/api/coupons/validate?action=apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          userId,
          cartItems,
          orderTotal,
          isGroupOrder,
          groupSize
        })
      });

      const result = await response.json();

      if (result.success) {
        // Get coupon details for display
        const couponResponse = await fetch(`/api/coupons/validate?code=${code}`);
        const couponData = await couponResponse.json();

        const appliedCoupon: AppliedCoupon = {
          code,
          name: couponData.coupon?.name || code,
          discountAmount: result.discountAmount || 0,
          type: couponData.coupon?.type || 'unknown'
        };

        onCouponApplied(appliedCoupon);
        setCouponCode('');
        toast.success(`Coupon applied! You saved ${formatCurrency(result.discountAmount || 0)}`);
      } else {
        toast.error(result.error || 'Failed to apply coupon');
      }
    } catch (error) {
      console.error('Error applying coupon:', error);
      toast.error('Failed to apply coupon');
    } finally {
      setIsApplying(false);
    }
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error('Please enter a coupon code');
      return;
    }

    await applyCoupon(couponCode.trim().toUpperCase());
  };

  const handleRemoveCoupon = (couponCode: string) => {
    onCouponRemoved(couponCode);
    toast.success('Coupon removed');
  };

  const handleQuickApply = async (code: string) => {
    await applyCoupon(code);
  };

  const getCouponIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return <Percent className="h-4 w-4" />;
      case 'fixed_amount':
        return <DollarSign className="h-4 w-4" />;
      case 'free_shipping':
        return <Package className="h-4 w-4" />;
      case 'bulk_discount':
        return <Users className="h-4 w-4" />;
      default:
        return <Gift className="h-4 w-4" />;
    }
  };

  const getDiscountText = (coupon: AvailableCoupon) => {
    switch (coupon.type) {
      case 'percentage':
        return `${coupon.discountValue}% off`;
      case 'fixed_amount':
        return `${formatCurrency(coupon.discountValue)} off`;
      case 'free_shipping':
        return 'Free shipping';
      case 'bulk_discount':
        return `Bulk discount`;
      default:
        return 'Discount';
    }
  };

  const isEligibleForCoupon = (coupon: AvailableCoupon) => {
    if (coupon.groupOrdersOnly && !isGroupOrder) {
      return false;
    }
    
    if (coupon.minimumOrderValue && orderTotal < coupon.minimumOrderValue) {
      return false;
    }

    return true;
  };

  return (
    <div className="space-y-4">
      {/* Applied Coupons */}
      <AnimatePresence>
        {appliedCoupons.map((coupon) => (
          <motion.div
            key={coupon.code}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-green-50 border border-green-200 rounded-lg p-3"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  {getCouponIcon(coupon.type)}
                </div>
                <div>
                  <p className="font-medium text-green-800">{coupon.name}</p>
                  <p className="text-sm text-green-600">
                    Code: {coupon.code} • Saved {formatCurrency(coupon.discountAmount)}
                  </p>
                </div>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveCoupon(coupon.code)}
                className="text-green-700 hover:text-green-800 hover:bg-green-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Coupon Input */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Tag className="h-5 w-5" />
            Apply Coupon Code
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Enter coupon code"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
              onKeyPress={(e) => e.key === 'Enter' && handleApplyCoupon()}
              className="flex-1"
            />
            <Button 
              onClick={handleApplyCoupon}
              disabled={isValidating || isApplying || !couponCode.trim()}
            >
              {isValidating || isApplying ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Apply'
              )}
            </Button>
          </div>

          {/* Available Coupons */}
          <Collapsible open={showAvailableCoupons} onOpenChange={setShowAvailableCoupons}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <span className="text-sm text-blue-600">View available coupons</span>
                {showAvailableCoupons ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="space-y-2 mt-3">
              {isLoadingAvailable ? (
                <div className="text-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  <p className="text-sm text-gray-600 mt-2">Loading available coupons...</p>
                </div>
              ) : availableCoupons.length === 0 ? (
                <p className="text-sm text-gray-600 text-center py-4">
                  No available coupons at the moment
                </p>
              ) : (
                availableCoupons.map((coupon) => {
                  const isEligible = isEligibleForCoupon(coupon);
                  const isAlreadyApplied = appliedCoupons.some(c => c.code === coupon.code);
                  
                  return (
                    <div
                      key={coupon.code}
                      className={`
                        border rounded-lg p-3 transition-colors
                        ${isEligible && !isAlreadyApplied
                          ? 'border-blue-200 bg-blue-50 hover:bg-blue-100' 
                          : 'border-gray-200 bg-gray-50'
                        }
                      `}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`
                            w-8 h-8 rounded-full flex items-center justify-center
                            ${isEligible && !isAlreadyApplied
                              ? 'bg-blue-100 text-blue-600' 
                              : 'bg-gray-100 text-gray-400'
                            }
                          `}>
                            {getCouponIcon(coupon.type)}
                          </div>
                          <div>
                            <p className="font-medium text-sm">{coupon.name}</p>
                            <p className="text-xs text-gray-600">{getDiscountText(coupon)}</p>
                            {coupon.minimumOrderValue && (
                              <p className="text-xs text-gray-500">
                                Min order: {formatCurrency(coupon.minimumOrderValue)}
                              </p>
                            )}
                          </div>
                        </div>
                        
                        {isAlreadyApplied ? (
                          <Badge variant="secondary" className="text-xs">
                            <Check className="h-3 w-3 mr-1" />
                            Applied
                          </Badge>
                        ) : isEligible ? (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuickApply(coupon.code)}
                            disabled={isApplying}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        ) : (
                          <Badge variant="outline" className="text-xs text-gray-500">
                            Not eligible
                          </Badge>
                        )}
                      </div>
                      
                      {!isEligible && (
                        <div className="mt-2">
                          <Alert className="py-2">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription className="text-xs">
                              {coupon.groupOrdersOnly && !isGroupOrder && 'Group orders only'}
                              {coupon.minimumOrderValue && orderTotal < coupon.minimumOrderValue && 
                                `Minimum order value: ${formatCurrency(coupon.minimumOrderValue)}`
                              }
                            </AlertDescription>
                          </Alert>
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Group Order Benefits */}
      {isGroupOrder && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-5 w-5 text-orange-600" />
              <h4 className="font-medium text-orange-800">Group Order Benefits</h4>
            </div>
            <p className="text-sm text-orange-700">
              You're shopping as a group of {groupSize} people! 
              Look for group-exclusive coupons and bulk discounts.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
