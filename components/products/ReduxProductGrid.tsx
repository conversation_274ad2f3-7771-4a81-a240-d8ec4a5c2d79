"use client";

import { ReduxProductCard } from "./ReduxProductCard";
import { ShoppingBag } from "lucide-react";
import { Button } from "@/components/ui/button";
import type { Product } from "@/types/product";

interface ProductGridProps {
  products: Product[];
  groupId: string;
  isLoading?: boolean;
}

export function ReduxProductGrid({ products, groupId, isLoading = false }: ProductGridProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="bg-gray-100 rounded-lg p-4 h-80 animate-pulse">
            <div className="bg-gray-200 h-40 w-full rounded-md mb-4"></div>
            <div className="bg-gray-200 h-4 w-3/4 rounded mb-2"></div>
            <div className="bg-gray-200 h-4 w-1/2 rounded mb-4"></div>
            <div className="bg-gray-200 h-10 w-full rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <ShoppingBag className="h-16 w-16 text-purple-300 mb-4" />
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">No products found</h2>
        <p className="text-gray-500 mb-6">We couldn't find any products matching your criteria.</p>
        <Button className="bg-purple-600 hover:bg-purple-700">
          View All Products
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {products.map((product) => (
        <ReduxProductCard key={product._id} product={product} groupId={groupId} />
      ))}
    </div>
  );
}
