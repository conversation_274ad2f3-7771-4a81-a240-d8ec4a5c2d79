// components/products/ProductGrid.tsx

import { ReduxProductCard } from "./ReduxProductCard"
import type { Product } from "@/types/product"

interface ProductGridProps {
  products: Product[]
  groupId: string
}

export function ProductGrid({ products, groupId }: ProductGridProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {products.map((product) => (
        <ReduxProductCard key={product._id} product={product} groupId={groupId} />
      ))}
    </div>
  )
}

