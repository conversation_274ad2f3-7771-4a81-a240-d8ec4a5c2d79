"use client";

import { useState } from "react";
import Image from "next/image";
import { ShoppingCart, CheckCircle } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useAuth } from "@/context/AuthContext";
import type { Product } from "@/types/product";
import { ConfirmationModal } from "@/components/ConfirmationModal";
import { useRouter } from "next/navigation";

interface ProductCardProps {
  product: Product;
  groupId: string;
}

export function ReduxProductCard({ product, groupId }: ProductCardProps) {
  const [addToCart, { isLoading: isAdding }] = useAddToCartMutation();
  const { user } = useAuth();
  const [success, setSuccess] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();

  const handleAddToCart = async () => {
    if (!user?._id) {
      // Redirect to login if user is not authenticated
      console.log('Redirecting to login page');
      router.push('/login');
      return;
    }

    // Store the current group ID in localStorage
    localStorage.setItem('currentGroupId', groupId);
    console.log('ReduxProductCard - Set currentGroupId in localStorage:', groupId);

    try {
      const cartItem = {
        userId: user._id,
        productId: product._id,
        quantity: 1, // Always add exactly 1 item when clicked
        groupId
      };

      await addToCart(cartItem).unwrap();
      setSuccess(true);
      setIsModalOpen(true);

      // Reset success state after 2 seconds
      setTimeout(() => setSuccess(false), 2000);
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      // Optionally show an error message to the user
    }
  };

  return (
    <>
      <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
        <div className="relative h-48 w-full bg-gray-50">
          <Image
            src={`/api/images/${product.image}`}
            alt={product.name}
            width={300}
            height={300}
            className="object-contain h-full w-full p-2"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/placeholder.svg";
            }}
          />
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg mb-2 line-clamp-2 h-14">{product.name}</h3>
          <p className="text-purple-600 font-bold">R{product.price.toFixed(2)}</p>
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <Button
            className="w-full bg-purple-600 hover:bg-purple-700"
            onClick={() => router.push(`/products/${product._id}`)}
          >
            View Details
          </Button>
          <Button
            className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800"
            onClick={handleAddToCart}
            disabled={isAdding || !user} // Disable if no user or adding in progress
          >
            {isAdding ? (
              <span>Adding...</span>
            ) : success ? (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Added
              </>
            ) : (
              <>
                <ShoppingCart className="mr-2 h-4 w-4" /> Add to Cart
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        productName={product.name}
      />
    </>
  );
}
