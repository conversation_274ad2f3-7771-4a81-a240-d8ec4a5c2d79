// components/products/ProductSearch.tsx
import { useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice"

interface ProductSearchProps {
  onSearch: (query: string, category: string) => void
}

export function ProductSearch({ onSearch }: ProductSearchProps) {
  const [query, setQuery] = useState("")
  const [category, setCategory] = useState("all")
  const { data: categories = [] } = useGetCategoriesQuery()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(query, category)
  }

  return (
    <form onSubmit={handleSearch} className="flex gap-2 mb-6">
      <Input
        type="text"
        placeholder="Search products..."
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        className="flex-grow"
      />
      <Select value={category} onValueChange={setCategory}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Category" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Categories</SelectItem>
          {categories.map((cat) => (
            <SelectItem key={cat._id} value={cat._id}>
              {cat.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Button type="submit" className="bg-[#2A7C6C] hover:bg-[#236358]">
        <Search className="mr-2 h-4 w-4" /> Search
      </Button>
    </form>
  )
}

