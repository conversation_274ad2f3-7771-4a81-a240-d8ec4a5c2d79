// components/products/ProductCategoryTabs.tsx
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import type { ProductCategory } from "@/types/productCategory"

interface ProductCategoryTabsProps {
  categories: ProductCategory[]
  selectedCategory: string
  onCategoryChange: (category: string) => void
}

export function ProductCategoryTabs({ categories, selectedCategory, onCategoryChange }: ProductCategoryTabsProps) {
  return (
    <Tabs value={selectedCategory} onValueChange={onCategoryChange} className="mb-6">
      <TabsList className="w-full justify-start overflow-x-auto">
        <TabsTrigger value="all" className="px-4 py-2">
          All
        </TabsTrigger>
        {categories.map((category) => (
          <TabsTrigger key={category._id} value={category._id} className="px-4 py-2">
            {category.name}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  )
}

