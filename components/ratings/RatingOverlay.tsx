"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ProductRating } from "./ProductRating"
import { RatingsProvider } from "@/context/RatingsProvider"

interface RatingOverlayProps {
  isOpen: boolean
  onClose: () => void
  productId: string
  userId: string
  productName: string
}

export function RatingOverlay({
  isOpen,
  onClose,
  productId,
  userId,
  productName,
}: RatingOverlayProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Rate {productName}</DialogTitle>
        </DialogHeader>
        <RatingsProvider productId={productId} userId={userId}>
          <ProductRating
            productId={productId}
            onRatingSubmit={onClose}
          />
        </RatingsProvider>
      </DialogContent>
    </Dialog>
  )
}