"use client"

import { useState } from "react"
import { Star } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useRatings } from "@/context/RatingsProvider"

interface ProductRatingProps {
  productId: string
  onRatingSubmit?: () => void
}

export function ProductRating({ productId, onRatingSubmit }: ProductRatingProps) {
  const { userRating, createRating, updateRating } = useRatings()
  const [rating, setRating] = useState(userRating?.rating || 0)
  const [comment, setComment] = useState(userRating?.comment || "")
  const [hoveredStar, setHoveredStar] = useState(0)

  const handleSubmit = async () => {
    try {
      if (userRating) {
        await updateRating(userRating._id, rating, comment)
      } else {
        await createRating(productId, rating, comment)
      }
      onRatingSubmit?.()
    } catch (error) {
      console.error("Error submitting rating:", error)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => setRating(star)}
            onMouseEnter={() => setHoveredStar(star)}
            onMouseLeave={() => setHoveredStar(0)}
            className="focus:outline-none"
          >
            <Star
              className={`h-6 w-6 ${
                star <= (hoveredStar || rating)
                  ? "fill-yellow-400 stroke-yellow-400"
                  : "stroke-gray-300"
              }`}
            />
          </button>
        ))}
      </div>
      <Textarea
        placeholder="Share your thoughts about this product..."
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        className="min-h-[100px]"
      />
      <Button 
        onClick={handleSubmit}
        disabled={rating === 0}
        className="w-full bg-[#2A7C6C] hover:bg-[#236358] text-white"
      >
        {userRating ? "Update Rating" : "Submit Rating"}
      </Button>
    </div>
  )
}