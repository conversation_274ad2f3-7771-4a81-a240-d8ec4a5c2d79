'use client'

import { But<PERSON> } from "@/components/ui/button"
import { useQuery } from "@tanstack/react-query"
import type { Product } from "@/types/product"
import { useAppSelector } from "@/lib/redux/hooks"
import { selectCartItems, selectSubtotal } from "@/lib/redux/features/cart/cartSlice"

interface OrderSummaryProps {
  customerInfo: {
    name: string
    email: string
    address: string
    city: string
    country: string
    postalCode: string
  }
  paymentMethod: string
  onConfirm: () => void
}

export function OrderSummary({ customerInfo, paymentMethod, onConfirm }: OrderSummaryProps) {
  // Get cart data from Redux
  const cartItems = useAppSelector(selectCartItems)
  const subtotal = useAppSelector(selectSubtotal)

  // Fetch product details for each item in the cart
  const { data: products } = useQuery<Product[]>({
    queryKey: ['products', cartItems.map(item => item.productId)],
    queryFn: async () => {
      const productIds = cartItems.map(item => item.productId)
      const response = await fetch(`/api/products?ids=${productIds.join(',')}`)
      if (!response.ok) throw new Error('Failed to fetch products')
      return response.json()
    },
    enabled: cartItems.length > 0
  })

  return (
    <div className="max-w-2xl mx-auto">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Order Summary</h2>
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">Customer Information</h3>
          <p>{customerInfo.name}</p>
          <p>{customerInfo.email}</p>
          <p>{customerInfo.address}</p>
          <p>{customerInfo.city}, {customerInfo.country} {customerInfo.postalCode}</p>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Payment Method</h3>
          <p>{paymentMethod}</p>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Order Items</h3>
          {cartItems.map((item) => {
            const product = products?.find((p: Product) => p._id === item.productId)
            return (
              <div key={item.productId} className="flex justify-between py-2">
                <span>{product?.name || 'Loading...'} x {item.quantity}</span>
                <span>R{((product?.price || 0) * item.quantity).toFixed(2)}</span>
              </div>
            )
          })}
          <div className="border-t mt-4 pt-4">
            <div className="flex justify-between font-semibold">
              <span>Total</span>
              <span>R{subtotal.toFixed(2)}</span>
            </div>
          </div>
        </div>

        <Button
          onClick={onConfirm}
          className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white"
        >
          Confirm Order
        </Button>
      </div>
    </div>
  )
}
