'use client'

import { But<PERSON> } from "@/components/ui/button"
import { useAppSelector } from "@/lib/redux/hooks"
import { selectDisplayItems, selectSubtotal } from "@/lib/redux/features/cart/cartSlice"

interface OrderReduxSummaryProps {
  customerInfo: {
    name: string
    email: string
    address: string
    city: string
    country: string
    postalCode: string
  }
  paymentMethod: string
  onConfirm: () => void
}

export function OrderReduxSummary({ customerInfo, paymentMethod, onConfirm }: OrderReduxSummaryProps) {
  const cartItems = useAppSelector(selectDisplayItems)
  const subtotal = useAppSelector(selectSubtotal)

  return (
    <div className="max-w-2xl mx-auto">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Order Summary</h2>
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">Customer Information</h3>
          <p>{customerInfo.name}</p>
          <p>{customerInfo.email}</p>
          <p>{customerInfo.address}</p>
          <p>{customerInfo.city}, {customerInfo.country} {customerInfo.postalCode}</p>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Payment Method</h3>
          <p>{paymentMethod}</p>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Order Items</h3>
          {cartItems.map((item) => (
            <div key={item.productId || item._id} className="flex justify-between py-2">
              <span>{item.name} x {item.quantity}</span>
              <span>R{(item.price * item.quantity).toFixed(2)}</span>
            </div>
          ))}
          <div className="border-t mt-4 pt-4">
            <div className="flex justify-between font-semibold">
              <span>Total</span>
              <span>R{subtotal.toFixed(2)}</span>
            </div>
          </div>
        </div>

        <Button 
          onClick={onConfirm}
          className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white"
        >
          Confirm Order
        </Button>
      </div>
    </div>
  )
}
