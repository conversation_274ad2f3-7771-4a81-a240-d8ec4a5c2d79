'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { CreditCard, ShoppingCartIcon as Paypal, Landmark, Truck } from 'lucide-react'

const paymentMethods = [
  { id: 'credit-card', name: 'Credit Card', icon: CreditCard },
  { id: 'paypal', name: 'PayPal', icon: Paypal },
  { id: 'debit-card', name: 'Debit Card', icon: Landmark },
  { id: 'pay-on-delivery', name: 'Pay on Delivery', icon: Truck },
]

interface PaymentMethodsProps {
  onSelect: (method: string) => void
}

export function PaymentMethods({ onSelect }: PaymentMethodsProps) {
  const [selectedMethod, setSelectedMethod] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedMethod) {
      onSelect(selectedMethod)
    }
  }

  return (
    <div className="max-w-md mx-auto">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Select Payment Method</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <RadioGroup value={selectedMethod} onValueChange={setSelectedMethod}>
          {paymentMethods.map((method) => (
            <div key={method.id} className="flex items-center space-x-3 border p-4 rounded-lg">
              <RadioGroupItem value={method.id} id={method.id} />
              <Label htmlFor={method.id} className="flex items-center cursor-pointer">
                <method.icon className="h-5 w-5 mr-2 text-[#2A7C6C]" />
                {method.name}
              </Label>
            </div>
          ))}
        </RadioGroup>
        <Button 
          type="submit" 
          className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white"
          disabled={!selectedMethod}
        >
          Continue to Order Summary
        </Button>
      </form>
    </div>
  )
}

