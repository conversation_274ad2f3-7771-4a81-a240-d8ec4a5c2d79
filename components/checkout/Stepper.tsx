import { CheckCircle } from 'lucide-react'

interface StepperProps {
  steps: string[]
  currentStep: number
}

export function Stepper({ steps, currentStep }: StepperProps) {
  return (
    <div className="flex justify-between items-center mb-8">
      {steps.map((step, index) => (
        <div key={step} className="flex items-center">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
            index < currentStep
              ? 'bg-[#2A7C6C] border-[#2A7C6C] text-white'
              : index === currentStep
              ? 'border-[#2A7C6C] text-[#2A7C6C]'
              : 'border-gray-300 text-gray-300'
          }`}>
            {index < currentStep ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <span>{index + 1}</span>
            )}
          </div>
          <div className={`ml-2 text-sm ${
            index <= currentStep ? 'text-[#2A7C6C] font-medium' : 'text-gray-400'
          }`}>
            {step}
          </div>
          {index < steps.length - 1 && (
            <div className={`w-full h-0.5 mx-2 ${
              index < currentStep ? 'bg-[#2A7C6C]' : 'bg-gray-300'
            }`} />
          )}
        </div>
      ))}
    </div>
  )
}

