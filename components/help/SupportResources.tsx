import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Video, Book, HeadphonesIcon } from 'lucide-react'

const resources = [
  {
    title: "User Guide",
    description: "Comprehensive guide on how to use our platform",
    icon: FileText,
    link: "#"
  },
  {
    title: "Video Tutorials",
    description: "Step-by-step video guides for common tasks",
    icon: Video,
    link: "#"
  },
  {
    title: "Stokvel Handbook",
    description: "Learn about Stokvel best practices and management",
    icon: Book,
    link: "#"
  },
  {
    title: "Webinars",
    description: "Join our live webinars for in-depth learning",
    icon: HeadphonesIcon,
    link: "#"
  }
]

export function SupportResources() {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      {resources.map((resource, index) => (
        <Card key={index}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <resource.icon className="h-6 w-6 mr-2" />
              {resource.title}
            </CardTitle>
            <CardDescription>{resource.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <a href={resource.link}>Access Resource</a>
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

