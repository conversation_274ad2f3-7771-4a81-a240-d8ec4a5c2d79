"use client"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const faqs = [
  {
    question: "What is a Stokvel?",
    answer: "A Stokvel is a type of savings or investment society to which members regularly contribute an agreed amount and from which they receive a lump sum payment."
  },
  {
    question: "How do I join a Stokvel group?",
    answer: "To join a Stokvel group, you can either create your own group or request to join an existing one. Navigate to the 'My Groups' section and click on 'Join a Group' to see available options."
  },
  {
    question: "How are payments processed?",
    answer: "Payments are processed securely through our platform. You can add multiple payment methods in the 'Payments' section of your profile. We support various payment options including bank transfers and credit cards."
  },
  {
    question: "What happens if a member doesn't contribute?",
    answer: "Each Stokvel group has its own rules regarding missed contributions. Generally, members who consistently miss payments may face penalties or be removed from the group. Check your group's specific guidelines for more information."
  },
  {
    question: "How is the money distributed?",
    answer: "The distribution of funds depends on the type of Stokvel and the agreement among members. Some groups rotate payouts, while others save towards a common goal. The distribution method will be clearly outlined in your group's settings."
  }
]

export function FAQSection() {
  return (
    <Accordion type="single" collapsible className="w-full">
      {faqs.map((faq, index) => (
        <AccordionItem value={`item-${index}`} key={index}>
          <AccordionTrigger>{faq.question}</AccordionTrigger>
          <AccordionContent>{faq.answer}</AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  )
}

