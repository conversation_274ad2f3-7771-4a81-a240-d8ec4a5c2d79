// components/AuthWrapper.tsx
"use client"

import { useAuth } from "@/context/AuthContext"
import { CartProvider } from "@/components/cart/CartProvider"
import { ClientLayout } from "@/app/ClientLayout"
import type React from "react"

export function AuthWrapper({ children }: { children: React.ReactNode }) {
  // Keep the auth context for future use
  const { user: _user } = useAuth()
  // Use null for non-group pages instead of invalid ObjectId
  const groupId = null; // No group ID for non-group pages

  return (
    <CartProvider groupId={groupId}>
      <ClientLayout>{children}</ClientLayout>
    </CartProvider>
  );
}
