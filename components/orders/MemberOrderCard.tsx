"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Package, 
  Calendar, 
  CreditCard, 
  MapPin, 
  Eye, 
  X,
  RefreshCw,
  Truck
} from "lucide-react";
import { motion } from "framer-motion";
import { IMemberOrder } from "@/models/MemberOrder";
import { 
  getOrderStatusConfig, 
  isOrderCancellable,
  ORDER_STATUS_CONFIG 
} from "@/types/memberOrder";
import { 
  useCancelMemberOrderMutation,
  useUpdateMemberOrderStatusMutation 
} from "@/lib/redux/features/memberOrders/memberOrdersApiSlice";

interface MemberOrderCardProps {
  order: IMemberOrder;
  onViewDetails?: (orderId: string) => void;
  showActions?: boolean;
}

export function MemberOrderCard({ 
  order, 
  onViewDetails, 
  showActions = true 
}: MemberOrderCardProps) {
  const [cancelOrder] = useCancelMemberOrderMutation();
  const [updateStatus] = useUpdateMemberOrderStatusMutation();
  const [isLoading, setIsLoading] = useState(false);

  const statusConfig = getOrderStatusConfig(order.status);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleCancelOrder = async () => {
    if (!isOrderCancellable(order.status)) return;
    
    const confirmed = window.confirm('Are you sure you want to cancel this order?');
    if (!confirmed) return;

    setIsLoading(true);
    try {
      await cancelOrder({
        orderId: order._id,
        reason: 'Cancelled by customer'
      }).unwrap();
    } catch (error) {
      console.error('Failed to cancel order:', error);
      alert('Failed to cancel order. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = () => {
    switch (order.status) {
      case 'pending':
        return <RefreshCw className="h-4 w-4" />;
      case 'confirmed':
      case 'processing':
        return <Package className="h-4 w-4" />;
      case 'shipped':
        return <Truck className="h-4 w-4" />;
      case 'delivered':
        return <Package className="h-4 w-4" />;
      case 'cancelled':
        return <X className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="hover:shadow-lg transition-shadow duration-300">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">
              Order #{order.orderNumber}
            </CardTitle>
            <Badge 
              variant="secondary"
              className={`bg-${statusConfig.color}-100 text-${statusConfig.color}-800 border-${statusConfig.color}-200`}
            >
              <div className="flex items-center gap-1">
                {getStatusIcon()}
                {statusConfig.label}
              </div>
            </Badge>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {formatDate(order.createdAt)}
            </div>
            <div className="flex items-center gap-1">
              <CreditCard className="h-4 w-4" />
              {order.paymentInfo.method}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Order Items Summary */}
          <div>
            <h4 className="font-medium mb-2">Items ({order.items.length})</h4>
            <div className="space-y-2">
              {order.items.slice(0, 2).map((item, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    {typeof item.product === 'object' && 'name' in item.product 
                      ? item.product.name 
                      : 'Product'} × {item.quantity}
                  </span>
                  <span className="font-medium">
                    {formatCurrency(item.subtotal)}
                  </span>
                </div>
              ))}
              {order.items.length > 2 && (
                <div className="text-sm text-gray-500">
                  +{order.items.length - 2} more items
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Total Amount */}
          <div className="flex justify-between items-center">
            <span className="font-medium">Total Amount</span>
            <span className="text-xl font-bold text-purple-600">
              {formatCurrency(order.totalAmount)}
            </span>
          </div>

          {/* Customer Info */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <MapPin className="h-4 w-4" />
            <span>{order.customerInfo.city}, {order.customerInfo.country}</span>
          </div>

          {/* Payment Status */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Payment Status</span>
            <Badge 
              variant={order.paymentInfo.status === 'paid' ? 'default' : 'secondary'}
              className={
                order.paymentInfo.status === 'paid' 
                  ? 'bg-green-100 text-green-800 border-green-200'
                  : order.paymentInfo.status === 'failed'
                  ? 'bg-red-100 text-red-800 border-red-200'
                  : 'bg-yellow-100 text-yellow-800 border-yellow-200'
              }
            >
              {order.paymentInfo.status.charAt(0).toUpperCase() + order.paymentInfo.status.slice(1)}
            </Badge>
          </div>

          {/* Shipping Info */}
          {order.shippingInfo?.trackingNumber && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center gap-2 text-sm">
                <Truck className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Tracking: {order.shippingInfo.trackingNumber}</span>
              </div>
              {order.shippingInfo.carrier && (
                <div className="text-xs text-gray-600 mt-1">
                  Carrier: {order.shippingInfo.carrier}
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          {showActions && (
            <div className="flex gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails?.(order._id)}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
              
              {isOrderCancellable(order.status) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelOrder}
                  disabled={isLoading}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-2" />
                  {isLoading ? 'Cancelling...' : 'Cancel'}
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
