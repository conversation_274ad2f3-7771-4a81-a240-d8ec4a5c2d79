"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Package, 
  User, 
  Calendar, 
  DollarSign,
  RefreshCw,
  Eye,
  ShoppingCart
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { formatCurrency } from "@/lib/utils";

interface OrdersDebugViewProps {
  groupId: string;
}

export function OrdersDebugView({ groupId }: OrdersDebugViewProps) {
  const { user } = useAuth();
  const [memberOrders, setMemberOrders] = useState<any[]>([]);
  const [groupOrders, setGroupOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchOrders = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Fetch member orders
      const memberResponse = await fetch(`/api/member-orders?userId=${user._id}`);
      if (memberResponse.ok) {
        const memberData = await memberResponse.json();
        setMemberOrders(Array.isArray(memberData) ? memberData : memberData.orders || []);
      }

      // Fetch group orders
      const groupResponse = await fetch(`/api/groups/${groupId}/orders`);
      if (groupResponse.ok) {
        const groupData = await groupResponse.json();
        setGroupOrders(Array.isArray(groupData) ? groupData : []);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [user, groupId]);

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-purple-100 text-purple-800';
      case 'shipped': return 'bg-indigo-100 text-indigo-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Orders Debug View</h2>
        <Button onClick={fetchOrders} disabled={loading} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Member Orders */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Your Member Orders
            <Badge variant="secondary">{memberOrders.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {memberOrders.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Package className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No member orders found</p>
              <p className="text-sm">Orders will appear here after checkout</p>
            </div>
          ) : (
            <div className="space-y-4">
              {memberOrders.map((order) => (
                <Card key={order._id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold">Order #{order.orderNumber}</h4>
                        <p className="text-sm text-gray-600">
                          <Calendar className="h-3 w-3 inline mr-1" />
                          {formatDate(order.createdAt)}
                        </p>
                      </div>
                      <Badge className={getStatusColor(order.status)}>
                        {order.status}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <p className="text-sm text-gray-600">Total Amount</p>
                        <p className="font-semibold text-lg">
                          {formatCurrency(order.totalAmount)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Items</p>
                        <p className="font-semibold">{order.items?.length || 0} items</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Payment</p>
                        <p className="font-semibold">{order.paymentInfo?.status || 'N/A'}</p>
                      </div>
                    </div>

                    {order.items && order.items.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2">Items:</p>
                        <div className="space-y-1">
                          {order.items.map((item: any, index: number) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span>
                                {typeof item.product === 'object' ? item.product.name : 'Product'} 
                                × {item.quantity}
                              </span>
                              <span>{formatCurrency(item.subtotal)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Group Orders */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Group Orders
            <Badge variant="secondary">{groupOrders.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {groupOrders.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ShoppingCart className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No group orders found</p>
              <p className="text-sm">Group orders will appear here after members checkout</p>
            </div>
          ) : (
            <div className="space-y-4">
              {groupOrders.map((order) => (
                <Card key={order._id} className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold">Group Order #{order._id?.slice(-8)}</h4>
                        <p className="text-sm text-gray-600">
                          <Calendar className="h-3 w-3 inline mr-1" />
                          {formatDate(order.createdAt)}
                        </p>
                      </div>
                      <Badge className={getStatusColor(order.status)}>
                        {order.status}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <p className="text-sm text-gray-600">Total Value</p>
                        <p className="font-semibold text-lg">
                          {formatCurrency(order.totalOrderValue || 0)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Items</p>
                        <p className="font-semibold">{order.orderItems?.length || 0} items</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Payment Status</p>
                        <p className="font-semibold">{order.paymentStatus || 'N/A'}</p>
                      </div>
                    </div>

                    {order.orderItems && order.orderItems.length > 0 && (
                      <div>
                        <p className="text-sm font-medium mb-2">Items:</p>
                        <div className="space-y-1">
                          {order.orderItems.map((item: any, index: number) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span>
                                {typeof item.product === 'object' ? item.product.name : 'Product'} 
                                × {item.quantity}
                              </span>
                              <span>{formatCurrency(item.subtotal)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
