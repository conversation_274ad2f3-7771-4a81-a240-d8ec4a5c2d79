'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Package, 
  CheckCircle2, 
  Clock, 
  Truck, 
  MapPin,
  AlertCircle,
  ExternalLink,
  RefreshCw,
  Calendar,
  User,
  MessageSquare
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { FulfillmentStatus } from '@/types/orderFulfillment';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface OrderTrackingInfo {
  orderId: string;
  status: FulfillmentStatus;
  priority: string;
  estimatedFulfillmentDate?: Date;
  actualFulfillmentDate?: Date;
  progressPercentage: number;
  timeline: Array<{
    id: string;
    name: string;
    status: FulfillmentStatus;
    description: string;
    completedAt?: Date;
    estimatedCompletion?: Date;
    notes?: string;
    assignedTo?: string;
    isCompleted: boolean;
    isCurrent: boolean;
  }>;
  shipping?: {
    provider: string;
    trackingNumber?: string;
    trackingUrl?: string;
    estimatedDelivery?: Date;
    actualDelivery?: Date;
    shippingCost: number;
    shippingAddress: any;
  };
  warehouse?: string;
  notes: string[];
  nextMilestone?: {
    name: string;
    estimatedCompletion: Date;
    description: string;
  };
  lastUpdated: Date;
}

interface OrderStatusTrackerProps {
  orderId: string;
  onStatusUpdate?: (status: FulfillmentStatus) => void;
  showAdminControls?: boolean;
}

const getStatusIcon = (status: FulfillmentStatus, isCompleted: boolean, isCurrent: boolean) => {
  if (isCompleted) {
    return <CheckCircle2 className="h-5 w-5 text-green-600" />;
  }
  
  if (isCurrent) {
    return <Clock className="h-5 w-5 text-blue-600 animate-pulse" />;
  }
  
  switch (status) {
    case 'shipped':
    case 'out_for_delivery':
      return <Truck className="h-5 w-5 text-gray-400" />;
    case 'delivered':
      return <MapPin className="h-5 w-5 text-gray-400" />;
    default:
      return <Package className="h-5 w-5 text-gray-400" />;
  }
};

const getStatusColor = (status: FulfillmentStatus) => {
  switch (status) {
    case 'delivered':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'shipped':
    case 'out_for_delivery':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'processing':
    case 'packed':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'cancelled':
    case 'failed':
    case 'returned':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'urgent':
      return 'bg-red-100 text-red-800';
    case 'high':
      return 'bg-orange-100 text-orange-800';
    case 'normal':
      return 'bg-blue-100 text-blue-800';
    case 'low':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export function OrderStatusTracker({ 
  orderId, 
  onStatusUpdate, 
  showAdminControls = false 
}: OrderStatusTrackerProps) {
  const [trackingInfo, setTrackingInfo] = useState<OrderTrackingInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTrackingInfo = async () => {
    try {
      const response = await fetch(`/api/orders/track/${orderId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch tracking information');
      }
      
      const data = await response.json();
      setTrackingInfo(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching tracking info:', err);
      setError('Failed to load tracking information');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchTrackingInfo();
  }, [orderId]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchTrackingInfo();
    toast.success('Tracking information updated');
  };

  const handleTrackingClick = () => {
    if (trackingInfo?.shipping?.trackingUrl) {
      window.open(trackingInfo.shipping.trackingUrl, '_blank');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading tracking information...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !trackingInfo) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || 'Unable to load tracking information'}
            </AlertDescription>
          </Alert>
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            className="mt-4"
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Order Status Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Order #{orderId.slice(-8)}
              </CardTitle>
              <CardDescription>
                Last updated: {new Date(trackingInfo.lastUpdated).toLocaleString()}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getPriorityColor(trackingInfo.priority)}>
                {trackingInfo.priority.charAt(0).toUpperCase() + trackingInfo.priority.slice(1)} Priority
              </Badge>
              <Badge variant="outline" className={getStatusColor(trackingInfo.status)}>
                {trackingInfo.status.replace('_', ' ').toUpperCase()}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Order Progress</span>
              <span>{trackingInfo.progressPercentage}% Complete</span>
            </div>
            <Progress value={trackingInfo.progressPercentage} className="h-2" />
          </div>

          {/* Next Milestone */}
          {trackingInfo.nextMilestone && (
            <Alert>
              <Calendar className="h-4 w-4" />
              <AlertTitle>Next Milestone</AlertTitle>
              <AlertDescription>
                <div className="mt-2">
                  <p className="font-medium">{trackingInfo.nextMilestone.name}</p>
                  <p className="text-sm text-gray-600">{trackingInfo.nextMilestone.description}</p>
                  <p className="text-sm text-gray-500 mt-1">
                    Expected: {new Date(trackingInfo.nextMilestone.estimatedCompletion).toLocaleString()}
                  </p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Shipping Information */}
          {trackingInfo.shipping && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-3 flex items-center">
                <Truck className="h-4 w-4 mr-2" />
                Shipping Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700 font-medium">Provider:</span>
                  <span className="ml-2">{trackingInfo.shipping.provider.replace('_', ' ').toUpperCase()}</span>
                </div>
                {trackingInfo.shipping.trackingNumber && (
                  <div>
                    <span className="text-blue-700 font-medium">Tracking:</span>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={handleTrackingClick}
                      className="ml-1 p-0 h-auto text-blue-600"
                    >
                      {trackingInfo.shipping.trackingNumber}
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </Button>
                  </div>
                )}
                {trackingInfo.shipping.estimatedDelivery && (
                  <div>
                    <span className="text-blue-700 font-medium">Est. Delivery:</span>
                    <span className="ml-2">
                      {new Date(trackingInfo.shipping.estimatedDelivery).toLocaleDateString()}
                    </span>
                  </div>
                )}
                <div>
                  <span className="text-blue-700 font-medium">Shipping Cost:</span>
                  <span className="ml-2">
                    {trackingInfo.shipping.shippingCost === 0 
                      ? 'Free' 
                      : formatCurrency(trackingInfo.shipping.shippingCost)
                    }
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Order Timeline</CardTitle>
          <CardDescription>
            Track your order progress through each fulfillment stage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {trackingInfo.timeline.map((step, index) => (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-start space-x-4"
              >
                <div className="flex-shrink-0 mt-1">
                  {getStatusIcon(step.status, step.isCompleted, step.isCurrent)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className={`font-medium ${step.isCurrent ? 'text-blue-600' : step.isCompleted ? 'text-green-600' : 'text-gray-500'}`}>
                      {step.name}
                    </h4>
                    {step.completedAt && (
                      <span className="text-sm text-gray-500">
                        {new Date(step.completedAt).toLocaleString()}
                      </span>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                  
                  {step.estimatedCompletion && !step.completedAt && (
                    <p className="text-xs text-gray-500 mt-1">
                      Expected: {new Date(step.estimatedCompletion).toLocaleString()}
                    </p>
                  )}
                  
                  {step.assignedTo && (
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <User className="h-3 w-3 mr-1" />
                      Assigned to: {step.assignedTo}
                    </div>
                  )}
                  
                  {step.notes && (
                    <div className="flex items-start mt-2 text-xs text-gray-600">
                      <MessageSquare className="h-3 w-3 mr-1 mt-0.5" />
                      {step.notes}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Order Notes */}
      {trackingInfo.notes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Order Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {trackingInfo.notes.map((note, index) => (
                <div key={index} className="text-sm p-3 bg-gray-50 rounded border-l-4 border-gray-300">
                  {note}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
