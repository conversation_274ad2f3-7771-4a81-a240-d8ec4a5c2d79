'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Download, 
  X, 
  Smartphone, 
  Monitor, 
  Tablet,
  Share,
  Plus,
  ChevronRight,
  Wifi,
  WifiOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { usePWA } from '@/hooks/usePWA';
import { toast } from 'sonner';

interface PWAInstallBannerProps {
  showOnMobile?: boolean;
  showOnDesktop?: boolean;
  autoShow?: boolean;
  position?: 'top' | 'bottom' | 'floating';
  variant?: 'banner' | 'card' | 'modal';
}

export function PWAInstallBanner({
  showOnMobile = true,
  showOnDesktop = true,
  autoShow = true,
  position = 'bottom',
  variant = 'banner'
}: PWAInstallBannerProps) {
  const {
    isInstallable,
    isInstalled,
    isStandalone,
    platform,
    canInstall,
    showInstallPrompt,
    isOnline,
    installationInstructions,
    install,
    dismissInstallPrompt
  } = usePWA();

  const [isVisible, setIsVisible] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  // Control banner visibility
  useEffect(() => {
    if (!autoShow) return;

    const shouldShow = 
      showInstallPrompt &&
      canInstall &&
      !isInstalled &&
      !isStandalone &&
      ((platform === 'ios' || platform === 'android') && showOnMobile) ||
      (platform === 'desktop' && showOnDesktop);

    setIsVisible(shouldShow);
  }, [
    autoShow,
    showInstallPrompt,
    canInstall,
    isInstalled,
    isStandalone,
    platform,
    showOnMobile,
    showOnDesktop
  ]);

  const handleInstall = async () => {
    setIsInstalling(true);
    
    try {
      if (installationInstructions.canShowPrompt) {
        const success = await install();
        if (success) {
          toast.success('App installed successfully!');
          setIsVisible(false);
        } else {
          toast.error('Installation cancelled');
        }
      } else {
        setShowInstructions(true);
      }
    } catch (error) {
      console.error('Installation error:', error);
      toast.error('Installation failed');
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    dismissInstallPrompt();
    
    // Remember dismissal for this session
    sessionStorage.setItem('pwa-install-dismissed', 'true');
  };

  const getPlatformIcon = () => {
    switch (platform) {
      case 'ios':
        return <Smartphone className="h-5 w-5" />;
      case 'android':
        return <Smartphone className="h-5 w-5" />;
      case 'desktop':
        return <Monitor className="h-5 w-5" />;
      default:
        return <Tablet className="h-5 w-5" />;
    }
  };

  const getBannerContent = () => (
    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
      <div className="flex items-center gap-3">
        {getPlatformIcon()}
        <div>
          <h3 className="font-semibold">Install StockvelMarket</h3>
          <p className="text-sm opacity-90">
            Get the full app experience with offline access
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        {!isOnline && (
          <Badge variant="secondary" className="bg-yellow-500 text-yellow-900">
            <WifiOff className="h-3 w-3 mr-1" />
            Offline
          </Badge>
        )}
        
        <Button
          variant="secondary"
          size="sm"
          onClick={handleInstall}
          disabled={isInstalling}
          className="bg-white text-blue-600 hover:bg-gray-100"
        >
          {isInstalling ? (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent" />
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Install
            </>
          )}
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="text-white hover:bg-white/20"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const getCardContent = () => (
    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-purple-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getPlatformIcon()}
          Install StockvelMarket App
        </CardTitle>
        <CardDescription>
          Get faster access, offline browsing, and push notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <Wifi className="h-6 w-6 mx-auto mb-2 text-blue-600" />
            <p className="text-xs text-gray-600">Offline Access</p>
          </div>
          <div>
            <Download className="h-6 w-6 mx-auto mb-2 text-green-600" />
            <p className="text-xs text-gray-600">Faster Loading</p>
          </div>
          <div>
            <Smartphone className="h-6 w-6 mx-auto mb-2 text-purple-600" />
            <p className="text-xs text-gray-600">Native Feel</p>
          </div>
        </div>
        
        <Separator />
        
        <div className="flex gap-2">
          <Button
            onClick={handleInstall}
            disabled={isInstalling}
            className="flex-1"
          >
            {isInstalling ? (
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Install App
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleDismiss}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const getInstructionsModal = () => (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
      onClick={() => setShowInstructions(false)}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-lg p-6 max-w-md w-full"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Install Instructions</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowInstructions(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            {getPlatformIcon()}
            <span>{installationInstructions.platform}</span>
          </div>
          
          <div className="space-y-2">
            {installationInstructions.instructions.map((instruction, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </div>
                <p className="text-sm text-gray-700">{instruction}</p>
              </div>
            ))}
          </div>
          
          {platform === 'ios' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-blue-700 text-sm font-medium mb-1">
                <Share className="h-4 w-4" />
                Look for the Share button
              </div>
              <p className="text-xs text-blue-600">
                The Share button looks like a square with an arrow pointing up
              </p>
            </div>
          )}
          
          <Button
            onClick={() => setShowInstructions(false)}
            className="w-full"
          >
            Got it!
          </Button>
        </div>
      </motion.div>
    </motion.div>
  );

  if (!isVisible && !showInstructions) {
    return null;
  }

  return (
    <>
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ 
              opacity: 0, 
              y: position === 'top' ? -100 : position === 'bottom' ? 100 : 0,
              scale: position === 'floating' ? 0.9 : 1
            }}
            animate={{ 
              opacity: 1, 
              y: 0,
              scale: 1
            }}
            exit={{ 
              opacity: 0, 
              y: position === 'top' ? -100 : position === 'bottom' ? 100 : 0,
              scale: position === 'floating' ? 0.9 : 1
            }}
            className={`
              ${position === 'top' ? 'fixed top-0 left-0 right-0 z-40' : ''}
              ${position === 'bottom' ? 'fixed bottom-0 left-0 right-0 z-40' : ''}
              ${position === 'floating' ? 'fixed bottom-4 left-4 right-4 z-40 max-w-sm mx-auto' : ''}
            `}
          >
            {variant === 'banner' && getBannerContent()}
            {variant === 'card' && getCardContent()}
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showInstructions && getInstructionsModal()}
      </AnimatePresence>
    </>
  );
}
