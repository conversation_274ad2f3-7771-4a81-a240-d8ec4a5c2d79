'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  WifiOff, 
  Wifi, 
  Refresh<PERSON>w, 
  <PERSON>ertTriangle,
  CheckCircle,
  Clock,
  Database
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { usePWA } from '@/hooks/usePWA';
import { toast } from 'sonner';

interface OfflineIndicatorProps {
  showWhenOnline?: boolean;
  position?: 'top' | 'bottom' | 'floating';
  variant?: 'minimal' | 'detailed' | 'banner';
}

export function OfflineIndicator({
  showWhenOnline = false,
  position = 'top',
  variant = 'minimal'
}: OfflineIndicatorProps) {
  const { isOnline, cacheSize, refreshCacheSize } = usePWA();
  const [showIndicator, setShowIndicator] = useState(false);
  const [wasOffline, setWasOffline] = useState(false);
  const [offlineStartTime, setOfflineStartTime] = useState<Date | null>(null);
  const [offlineDuration, setOfflineDuration] = useState(0);
  const [pendingActions, setPendingActions] = useState<string[]>([]);
  const [syncProgress, setSyncProgress] = useState(0);
  const [isSyncing, setIsSyncing] = useState(false);

  // Track online/offline state changes
  useEffect(() => {
    if (!isOnline && !wasOffline) {
      // Just went offline
      setWasOffline(true);
      setOfflineStartTime(new Date());
      setShowIndicator(true);
      toast.warning('You are now offline. Some features may be limited.');
    } else if (isOnline && wasOffline) {
      // Just came back online
      setWasOffline(false);
      setOfflineStartTime(null);
      setOfflineDuration(0);
      
      if (showWhenOnline) {
        setShowIndicator(true);
        toast.success('You are back online!');
        
        // Auto-hide after 3 seconds when back online
        setTimeout(() => {
          setShowIndicator(false);
        }, 3000);
      } else {
        setShowIndicator(false);
      }
      
      // Trigger sync if there are pending actions
      if (pendingActions.length > 0) {
        handleSync();
      }
    } else if (!isOnline) {
      setShowIndicator(true);
    } else if (isOnline && !showWhenOnline) {
      setShowIndicator(false);
    }
  }, [isOnline, wasOffline, showWhenOnline, pendingActions.length]);

  // Update offline duration
  useEffect(() => {
    if (!isOnline && offlineStartTime) {
      const interval = setInterval(() => {
        const now = new Date();
        const duration = Math.floor((now.getTime() - offlineStartTime.getTime()) / 1000);
        setOfflineDuration(duration);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isOnline, offlineStartTime]);

  // Simulate pending actions (in a real app, this would come from IndexedDB or local storage)
  useEffect(() => {
    if (!isOnline) {
      // Simulate adding pending actions while offline
      const actions = [
        'Cart update pending',
        'Product view pending',
        'User preference change pending'
      ];
      setPendingActions(actions);
    } else {
      setPendingActions([]);
    }
  }, [isOnline]);

  const handleSync = async () => {
    if (!isOnline) {
      toast.error('Cannot sync while offline');
      return;
    }

    setIsSyncing(true);
    setSyncProgress(0);

    try {
      // Simulate sync progress
      for (let i = 0; i <= 100; i += 10) {
        setSyncProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Clear pending actions
      setPendingActions([]);
      toast.success('All data synced successfully');
    } catch (error) {
      toast.error('Sync failed. Will retry automatically.');
    } finally {
      setIsSyncing(false);
      setSyncProgress(0);
    }
  };

  const handleRefreshCache = async () => {
    await refreshCacheSize();
    toast.success('Cache information updated');
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  };

  const formatCacheSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getMinimalIndicator = () => (
    <div className={`
      flex items-center gap-2 px-3 py-2 rounded-full text-sm font-medium
      ${isOnline 
        ? 'bg-green-100 text-green-800 border border-green-200' 
        : 'bg-red-100 text-red-800 border border-red-200'
      }
    `}>
      {isOnline ? (
        <>
          <Wifi className="h-4 w-4" />
          <span>Online</span>
        </>
      ) : (
        <>
          <WifiOff className="h-4 w-4" />
          <span>Offline</span>
          {offlineDuration > 0 && (
            <span className="text-xs opacity-75">
              {formatDuration(offlineDuration)}
            </span>
          )}
        </>
      )}
    </div>
  );

  const getBannerIndicator = () => (
    <div className={`
      w-full px-4 py-3 text-center text-sm font-medium
      ${isOnline 
        ? 'bg-green-600 text-white' 
        : 'bg-red-600 text-white'
      }
    `}>
      <div className="flex items-center justify-center gap-2">
        {isOnline ? (
          <>
            <CheckCircle className="h-4 w-4" />
            <span>Back online - All features available</span>
          </>
        ) : (
          <>
            <WifiOff className="h-4 w-4" />
            <span>
              You're offline - Limited functionality available
              {offlineDuration > 0 && ` (${formatDuration(offlineDuration)})`}
            </span>
          </>
        )}
      </div>
    </div>
  );

  const getDetailedIndicator = () => (
    <Card className={`
      ${isOnline 
        ? 'border-green-200 bg-green-50' 
        : 'border-red-200 bg-red-50'
      }
    `}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          {isOnline ? (
            <>
              <Wifi className="h-5 w-5 text-green-600" />
              <span className="text-green-800">Online</span>
            </>
          ) : (
            <>
              <WifiOff className="h-5 w-5 text-red-600" />
              <span className="text-red-800">Offline Mode</span>
            </>
          )}
          
          <Badge variant="outline" className="ml-auto">
            {isOnline ? 'Connected' : 'Disconnected'}
          </Badge>
        </CardTitle>
        
        {!isOnline && (
          <CardDescription className="text-red-700">
            Some features are limited while offline. Changes will sync when you're back online.
          </CardDescription>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Offline Duration */}
        {!isOnline && offlineDuration > 0 && (
          <div className="flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4 text-red-600" />
            <span>Offline for: {formatDuration(offlineDuration)}</span>
          </div>
        )}

        {/* Pending Actions */}
        {pendingActions.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span>Pending Actions ({pendingActions.length})</span>
            </div>
            <div className="space-y-1">
              {pendingActions.slice(0, 3).map((action, index) => (
                <div key={index} className="text-xs text-gray-600 pl-6">
                  • {action}
                </div>
              ))}
              {pendingActions.length > 3 && (
                <div className="text-xs text-gray-500 pl-6">
                  ... and {pendingActions.length - 3} more
                </div>
              )}
            </div>
          </div>
        )}

        {/* Sync Progress */}
        {isSyncing && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
              <span>Syncing data...</span>
            </div>
            <Progress value={syncProgress} className="h-2" />
          </div>
        )}

        {/* Cache Information */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4 text-gray-600" />
            <span>Cached data: {formatCacheSize(cacheSize)}</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefreshCache}
            className="h-6 px-2 text-xs"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          {isOnline && pendingActions.length > 0 && (
            <Button
              onClick={handleSync}
              disabled={isSyncing}
              size="sm"
              className="flex-1"
            >
              {isSyncing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Sync Now
                </>
              )}
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowIndicator(false)}
          >
            Dismiss
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  if (!showIndicator) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ 
          opacity: 0, 
          y: position === 'top' ? -50 : position === 'bottom' ? 50 : 0,
          scale: position === 'floating' ? 0.9 : 1
        }}
        animate={{ 
          opacity: 1, 
          y: 0,
          scale: 1
        }}
        exit={{ 
          opacity: 0, 
          y: position === 'top' ? -50 : position === 'bottom' ? 50 : 0,
          scale: position === 'floating' ? 0.9 : 1
        }}
        className={`
          ${position === 'top' ? 'fixed top-0 left-0 right-0 z-50' : ''}
          ${position === 'bottom' ? 'fixed bottom-0 left-0 right-0 z-50' : ''}
          ${position === 'floating' ? 'fixed top-4 right-4 z-50 max-w-sm' : ''}
        `}
      >
        {variant === 'minimal' && getMinimalIndicator()}
        {variant === 'banner' && getBannerIndicator()}
        {variant === 'detailed' && getDetailedIndicator()}
      </motion.div>
    </AnimatePresence>
  );
}
