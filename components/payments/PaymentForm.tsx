'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CreditCard, 
  Lock, 
  Eye, 
  EyeOff,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PaymentMethodType, PaymentFormData } from '@/types/payment';

// Validation schemas for different payment methods
const creditCardSchema = z.object({
  cardNumber: z.string()
    .min(1, 'Card number is required')
    .transform((val) => val.replace(/\s/g, '')) // Remove spaces before validation
    .refine((val) => /^\d+$/.test(val), 'Card number must contain only digits')
    .refine((val) => val.length >= 13, 'Card number must be at least 13 digits')
    .refine((val) => val.length <= 19, 'Card number must be at most 19 digits'),
  expiryMonth: z.string().min(1, 'Expiry month is required'),
  expiryYear: z.string().min(1, 'Expiry year is required'),
  cvv: z.string()
    .min(3, 'CVV must be at least 3 digits')
    .max(4, 'CVV must be at most 4 digits')
    .regex(/^\d+$/, 'CVV must contain only digits'),
  cardholderName: z.string().min(2, 'Cardholder name is required'),
});

const bankTransferSchema = z.object({
  bankName: z.string().min(2, 'Bank name is required'),
  accountNumber: z.string()
    .min(8, 'Account number must be at least 8 digits')
    .regex(/^\d+$/, 'Account number must contain only digits'),
  accountHolderName: z.string().min(2, 'Account holder name is required'),
});

const eftSchema = z.object({
  bankName: z.string().min(2, 'Bank name is required'),
  accountNumber: z.string()
    .min(8, 'Account number must be at least 8 digits')
    .regex(/^\d+$/, 'Account number must contain only digits'),
  branchCode: z.string()
    .min(6, 'Branch code must be 6 digits')
    .max(6, 'Branch code must be 6 digits')
    .regex(/^\d+$/, 'Branch code must contain only digits'),
  accountHolderName: z.string().min(2, 'Account holder name is required'),
});

interface PaymentFormProps {
  paymentMethod: PaymentMethodType;
  onSubmit: (data: PaymentFormData) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

// Format card number with spaces
const formatCardNumber = (value: string) => {
  const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
  const matches = v.match(/\d{4,16}/g);
  const match = matches && matches[0] || '';
  const parts = [];
  for (let i = 0, len = match.length; i < len; i += 4) {
    parts.push(match.substring(i, i + 4));
  }
  if (parts.length) {
    return parts.join(' ');
  } else {
    return v;
  }
};

// Get card type from number
const getCardType = (cardNumber: string) => {
  const number = cardNumber.replace(/\s/g, '');
  if (/^4/.test(number)) return 'visa';
  if (/^5[1-5]/.test(number)) return 'mastercard';
  if (/^3[47]/.test(number)) return 'amex';
  return 'unknown';
};

export function PaymentForm({
  paymentMethod,
  onSubmit,
  isLoading = false,
  disabled = false
}: PaymentFormProps) {
  const [showCvv, setShowCvv] = useState(false);
  const [savePaymentInfo, setSavePaymentInfo] = useState(false);

  // Create separate form instances for each payment method
  const creditCardForm = useForm({
    resolver: zodResolver(creditCardSchema),
    defaultValues: {
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: '',
    }
  });

  const bankTransferForm = useForm({
    resolver: zodResolver(bankTransferSchema),
    defaultValues: {
      bankName: '',
      accountNumber: '',
      accountHolderName: '',
    }
  });

  const eftForm = useForm({
    resolver: zodResolver(eftSchema),
    defaultValues: {
      bankName: '',
      accountNumber: '',
      accountHolderName: '',
      branchCode: '',
    }
  });

  // Get the appropriate form based on payment method
  const getCurrentForm = () => {
    switch (paymentMethod) {
      case 'credit_card':
        return creditCardForm;
      case 'bank_transfer':
        return bankTransferForm;
      case 'eft':
        return eftForm;
      default:
        return creditCardForm;
    }
  };

  const form = getCurrentForm();

  // Reset form when payment method changes
  useEffect(() => {
    form.reset();
  }, [paymentMethod, form]);

  const handleSubmit = (data: any) => {
    const formData: PaymentFormData = {
      paymentMethod,
      savePaymentInfo,
      ...data
    };
    onSubmit(formData);
  };

  const renderCreditCardForm = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="cardNumber">Card Number</Label>
        <div className="relative">
          <Input
            id="cardNumber"
            placeholder="1234 5678 9012 3456"
            {...form.register('cardNumber')}
            onChange={(e) => {
              const formatted = formatCardNumber(e.target.value);
              form.setValue('cardNumber', formatted);
            }}
            className="pl-10"
            disabled={disabled}
          />
          <CreditCard className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          {form.watch('cardNumber') && (
            <div className="absolute right-3 top-3">
              <div className={`text-xs px-2 py-1 rounded ${
                getCardType(form.watch('cardNumber')) !== 'unknown' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {getCardType(form.watch('cardNumber')).toUpperCase()}
              </div>
            </div>
          )}
        </div>
        {form.formState.errors.cardNumber && (
          <p className="text-sm text-red-500 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {form.formState.errors.cardNumber.message}
          </p>
        )}
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="expiryMonth">Month</Label>
          <Select 
            onValueChange={(value) => form.setValue('expiryMonth', value)}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="MM" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                <SelectItem key={month} value={month.toString().padStart(2, '0')}>
                  {month.toString().padStart(2, '0')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {form.formState.errors.expiryMonth && (
            <p className="text-xs text-red-500">{form.formState.errors.expiryMonth.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="expiryYear">Year</Label>
          <Select 
            onValueChange={(value) => form.setValue('expiryYear', value)}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="YYYY" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i).map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {form.formState.errors.expiryYear && (
            <p className="text-xs text-red-500">{form.formState.errors.expiryYear.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="cvv">CVV</Label>
          <div className="relative">
            <Input
              id="cvv"
              type={showCvv ? 'text' : 'password'}
              placeholder="123"
              maxLength={4}
              {...form.register('cvv')}
              onChange={(e) => {
                // Only allow digits
                const value = e.target.value.replace(/\D/g, '');
                form.setValue('cvv', value);
              }}
              disabled={disabled}
            />
            <button
              type="button"
              onClick={() => setShowCvv(!showCvv)}
              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
              disabled={disabled}
            >
              {showCvv ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
          {form.formState.errors.cvv && (
            <p className="text-xs text-red-500">{form.formState.errors.cvv.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="cardholderName">Cardholder Name</Label>
        <Input
          id="cardholderName"
          placeholder="John Doe"
          {...form.register('cardholderName')}
          disabled={disabled}
        />
        {form.formState.errors.cardholderName && (
          <p className="text-sm text-red-500 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {form.formState.errors.cardholderName.message}
          </p>
        )}
      </div>
    </div>
  );

  const renderBankTransferForm = () => (
    <div className="space-y-4">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-2" />
          <div>
            <h4 className="text-sm font-medium text-blue-800">Bank Transfer Instructions</h4>
            <p className="text-sm text-blue-700 mt-1">
              You will receive bank details after confirming your order. 
              Please use the provided reference number when making the transfer.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="bankName">Your Bank Name</Label>
        <Input
          id="bankName"
          placeholder="e.g., Standard Bank"
          {...form.register('bankName')}
          disabled={disabled}
        />
        {form.formState.errors.bankName && (
          <p className="text-sm text-red-500">{form.formState.errors.bankName.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="accountNumber">Your Account Number</Label>
        <Input
          id="accountNumber"
          placeholder="**********"
          {...form.register('accountNumber')}
          onChange={(e) => {
            // Only allow digits
            const value = e.target.value.replace(/\D/g, '');
            form.setValue('accountNumber', value);
          }}
          disabled={disabled}
        />
        {form.formState.errors.accountNumber && (
          <p className="text-sm text-red-500">{form.formState.errors.accountNumber.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="accountHolderName">Account Holder Name</Label>
        <Input
          id="accountHolderName"
          placeholder="John Doe"
          {...form.register('accountHolderName')}
          disabled={disabled}
        />
        {form.formState.errors.accountHolderName && (
          <p className="text-sm text-red-500">{form.formState.errors.accountHolderName.message}</p>
        )}
      </div>
    </div>
  );

  const renderEFTForm = () => (
    <div className="space-y-4">
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start">
          <CheckCircle2 className="h-5 w-5 text-green-600 mt-0.5 mr-2" />
          <div>
            <h4 className="text-sm font-medium text-green-800">Fast Electronic Transfer</h4>
            <p className="text-sm text-green-700 mt-1">
              EFT payments are processed within 24 hours. You'll receive confirmation once processed.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="bankName">Bank Name</Label>
          <Input
            id="bankName"
            placeholder="e.g., FNB"
            {...form.register('bankName')}
            disabled={disabled}
          />
          {form.formState.errors.bankName && (
            <p className="text-sm text-red-500">{form.formState.errors.bankName.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="branchCode">Branch Code</Label>
          <Input
            id="branchCode"
            placeholder="123456"
            maxLength={6}
            {...form.register('branchCode')}
            onChange={(e) => {
              // Only allow digits and limit to 6 characters
              const value = e.target.value.replace(/\D/g, '').slice(0, 6);
              form.setValue('branchCode', value);
            }}
            disabled={disabled}
          />
          {form.formState.errors.branchCode && (
            <p className="text-sm text-red-500">{form.formState.errors.branchCode.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="accountNumber">Account Number</Label>
        <Input
          id="accountNumber"
          placeholder="**********"
          {...form.register('accountNumber')}
          onChange={(e) => {
            // Only allow digits
            const value = e.target.value.replace(/\D/g, '');
            form.setValue('accountNumber', value);
          }}
          disabled={disabled}
        />
        {form.formState.errors.accountNumber && (
          <p className="text-sm text-red-500">{form.formState.errors.accountNumber.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="accountHolderName">Account Holder Name</Label>
        <Input
          id="accountHolderName"
          placeholder="John Doe"
          {...form.register('accountHolderName')}
          disabled={disabled}
        />
        {form.formState.errors.accountHolderName && (
          <p className="text-sm text-red-500">{form.formState.errors.accountHolderName.message}</p>
        )}
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Lock className="h-5 w-5 mr-2" />
          Payment Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form key={paymentMethod} onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={paymentMethod}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {paymentMethod === 'credit_card' && renderCreditCardForm()}
              {paymentMethod === 'bank_transfer' && renderBankTransferForm()}
              {paymentMethod === 'eft' && renderEFTForm()}
            </motion.div>
          </AnimatePresence>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="savePaymentInfo"
              checked={savePaymentInfo}
              onCheckedChange={(checked) => setSavePaymentInfo(checked as boolean)}
              disabled={disabled}
            />
            <Label htmlFor="savePaymentInfo" className="text-sm">
              Save payment information for future orders
            </Label>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={disabled || isLoading}
          >
            {isLoading ? 'Processing...' : 'Continue to Review'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
