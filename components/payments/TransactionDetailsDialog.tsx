"use client"

import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ArrowDownUp, Calendar, DollarSign, FileText } from 'lucide-react'

interface TransactionDetailsDialogProps {
  isOpen: boolean
  onClose: () => void
  transaction: {
    id: string
    date: string
    description: string
    amount: number
    type: string
  }
}

export function TransactionDetailsDialog({ isOpen, onClose, transaction }: TransactionDetailsDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Transaction Details</DialogTitle>
          <DialogDescription>
            View detailed information about this transaction
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-blue-100 rounded-full">
              <ArrowDownUp className="h-4 w-4 text-blue-500" />
            </div>
            <div>
              <p className="text-sm font-medium">Transaction ID</p>
              <p className="text-xs text-gray-500">{transaction.id}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-green-100 rounded-full">
              <Calendar className="h-4 w-4 text-green-500" />
            </div>
            <div>
              <p className="text-sm font-medium">Date</p>
              <p className="text-xs text-gray-500">{transaction.date}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-purple-100 rounded-full">
              <FileText className="h-4 w-4 text-purple-500" />
            </div>
            <div>
              <p className="text-sm font-medium">Description</p>
              <p className="text-xs text-gray-500">{transaction.description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-yellow-100 rounded-full">
              <DollarSign className="h-4 w-4 text-yellow-500" />
            </div>
            <div>
              <p className="text-sm font-medium">Amount</p>
              <p className="text-xs text-gray-500">R{transaction.amount.toFixed(2)}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className={`p-2 rounded-full ${
              transaction.type === 'Credit' ? 'bg-green-100' : 'bg-red-100'
            }`}>
              <ArrowDownUp className={`h-4 w-4 ${
                transaction.type === 'Credit' ? 'text-green-500' : 'text-red-500'
              }`} />
            </div>
            <div>
              <p className="text-sm font-medium">Type</p>
              <p className="text-xs text-gray-500">{transaction.type}</p>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

