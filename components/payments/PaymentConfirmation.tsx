'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle2, 
  XCircle, 
  Clock, 
  AlertTriangle,
  CreditCard,
  BanknoteIcon,
  Landmark,
  Copy,
  ExternalLink,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { PaymentResult, PaymentStatus, PaymentMethodType } from '@/types/payment';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface PaymentConfirmationProps {
  paymentResult: PaymentResult;
  onRetry?: () => void;
  onContinue?: () => void;
  onViewOrder?: (orderId: string) => void;
  showActions?: boolean;
}

const getStatusIcon = (status: PaymentStatus) => {
  switch (status) {
    case 'completed':
      return <CheckCircle2 className="h-8 w-8 text-green-600" />;
    case 'pending':
    case 'processing':
      return <Clock className="h-8 w-8 text-yellow-600" />;
    case 'failed':
    case 'cancelled':
      return <XCircle className="h-8 w-8 text-red-600" />;
    default:
      return <AlertTriangle className="h-8 w-8 text-gray-600" />;
  }
};

const getStatusColor = (status: PaymentStatus) => {
  switch (status) {
    case 'completed':
      return 'bg-green-50 border-green-200 text-green-800';
    case 'pending':
    case 'processing':
      return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    case 'failed':
    case 'cancelled':
      return 'bg-red-50 border-red-200 text-red-800';
    default:
      return 'bg-gray-50 border-gray-200 text-gray-800';
  }
};

const getPaymentMethodIcon = (method: PaymentMethodType) => {
  switch (method) {
    case 'credit_card':
      return <CreditCard className="h-4 w-4" />;
    case 'bank_transfer':
      return <BanknoteIcon className="h-4 w-4" />;
    case 'eft':
      return <Landmark className="h-4 w-4" />;
    default:
      return <CreditCard className="h-4 w-4" />;
  }
};

const getStatusMessage = (status: PaymentStatus, method: PaymentMethodType) => {
  switch (status) {
    case 'completed':
      return {
        title: 'Payment Successful!',
        description: 'Your payment has been processed successfully and your order is confirmed.'
      };
    case 'pending':
      if (method === 'bank_transfer') {
        return {
          title: 'Payment Pending',
          description: 'Please complete the bank transfer using the details provided below. Your order will be processed once payment is received.'
        };
      }
      return {
        title: 'Payment Pending',
        description: 'Your payment is being processed. You will receive confirmation shortly.'
      };
    case 'processing':
      return {
        title: 'Processing Payment',
        description: 'Your payment is currently being processed. Please wait for confirmation.'
      };
    case 'failed':
      return {
        title: 'Payment Failed',
        description: 'Your payment could not be processed. Please try again or use a different payment method.'
      };
    case 'cancelled':
      return {
        title: 'Payment Cancelled',
        description: 'The payment was cancelled. You can try again or use a different payment method.'
      };
    default:
      return {
        title: 'Payment Status Unknown',
        description: 'We are unable to determine the payment status. Please contact support.'
      };
  }
};

const copyToClipboard = (text: string, label: string) => {
  navigator.clipboard.writeText(text);
  toast.success(`${label} copied to clipboard`);
};

export function PaymentConfirmation({
  paymentResult,
  onRetry,
  onContinue,
  onViewOrder,
  showActions = true
}: PaymentConfirmationProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const statusMessage = getStatusMessage(paymentResult.status, 'credit_card'); // Default to credit_card for now
  
  const handleRefreshStatus = async () => {
    setIsRefreshing(true);
    // In a real implementation, you would fetch the latest payment status
    setTimeout(() => {
      setIsRefreshing(false);
      toast.info('Payment status refreshed');
    }, 2000);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="max-w-2xl mx-auto"
    >
      <Card>
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            {getStatusIcon(paymentResult.status)}
          </div>
          <CardTitle className="text-2xl">{statusMessage.title}</CardTitle>
          <CardDescription className="text-base">
            {statusMessage.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Payment Status Badge */}
          <div className="flex justify-center">
            <Badge 
              variant="outline" 
              className={`px-4 py-2 text-sm font-medium ${getStatusColor(paymentResult.status)}`}
            >
              {paymentResult.status.charAt(0).toUpperCase() + paymentResult.status.slice(1)}
            </Badge>
          </div>

          {/* Payment Details */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <h4 className="font-medium text-gray-900">Payment Details</h4>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium ml-2">
                  {formatCurrency(paymentResult.amount)} {paymentResult.currency}
                </span>
              </div>
              
              <div>
                <span className="text-gray-600">Payment ID:</span>
                <span className="font-mono text-xs ml-2">{paymentResult.paymentId}</span>
              </div>
              
              {paymentResult.transactionId && (
                <div className="col-span-2">
                  <span className="text-gray-600">Transaction ID:</span>
                  <div className="flex items-center mt-1">
                    <span className="font-mono text-xs bg-white px-2 py-1 rounded border">
                      {paymentResult.transactionId}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(paymentResult.transactionId!, 'Transaction ID')}
                      className="ml-2 h-8 w-8 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}
              
              {paymentResult.processingFee && (
                <div>
                  <span className="text-gray-600">Processing Fee:</span>
                  <span className="ml-2">{formatCurrency(paymentResult.processingFee)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Special Instructions for Bank Transfer */}
          {paymentResult.status === 'pending' && paymentResult.actionType === 'redirect' && paymentResult.actionData && (
            <Alert>
              <BanknoteIcon className="h-4 w-4" />
              <AlertTitle>Bank Transfer Instructions</AlertTitle>
              <AlertDescription className="mt-2">
                <div className="space-y-2">
                  <p>{paymentResult.actionData.instructions}</p>
                  
                  {paymentResult.actionData.bankDetails && (
                    <div className="bg-white p-3 rounded border mt-3">
                      <h5 className="font-medium mb-2">Bank Details:</h5>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Bank:</span>
                          <span className="font-medium">{paymentResult.actionData.bankDetails.bankName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Account:</span>
                          <div className="flex items-center">
                            <span className="font-mono">{paymentResult.actionData.bankDetails.accountNumber}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(paymentResult.actionData.bankDetails.accountNumber, 'Account Number')}
                              className="ml-2 h-6 w-6 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span>Branch Code:</span>
                          <span className="font-mono">{paymentResult.actionData.bankDetails.branchCode}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Reference:</span>
                          <div className="flex items-center">
                            <span className="font-mono font-bold text-red-600">
                              {paymentResult.actionData.bankDetails.reference}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(paymentResult.actionData.bankDetails.reference, 'Reference Number')}
                              className="ml-2 h-6 w-6 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Error Message */}
          {paymentResult.error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertTitle>Payment Error</AlertTitle>
              <AlertDescription>{paymentResult.error}</AlertDescription>
            </Alert>
          )}

          {/* Estimated Settlement */}
          {paymentResult.estimatedSettlement && (
            <div className="text-center text-sm text-gray-600">
              <Clock className="h-4 w-4 inline mr-1" />
              Estimated settlement: {new Date(paymentResult.estimatedSettlement).toLocaleDateString()}
            </div>
          )}

          {/* Actions */}
          {showActions && (
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              {paymentResult.status === 'completed' && onContinue && (
                <Button onClick={onContinue} className="flex-1">
                  Continue Shopping
                </Button>
              )}
              
              {paymentResult.status === 'completed' && onViewOrder && (
                <Button 
                  variant="outline" 
                  onClick={() => onViewOrder(paymentResult.paymentId)}
                  className="flex-1"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Order
                </Button>
              )}
              
              {(paymentResult.status === 'failed' || paymentResult.status === 'cancelled') && onRetry && (
                <Button onClick={onRetry} className="flex-1">
                  Try Again
                </Button>
              )}
              
              {(paymentResult.status === 'pending' || paymentResult.status === 'processing') && (
                <Button 
                  variant="outline" 
                  onClick={handleRefreshStatus}
                  disabled={isRefreshing}
                  className="flex-1"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                  Refresh Status
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
