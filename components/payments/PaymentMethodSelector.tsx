'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  BanknoteIcon, 
  Landmark, 
  Smartphone,
  CheckCircle2,
  Clock,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { PaymentMethodType, PaymentMethod } from '@/types/payment';
import { formatCurrency } from '@/lib/utils';

interface PaymentMethodSelectorProps {
  selectedMethod: PaymentMethodType;
  onMethodChange: (method: PaymentMethodType) => void;
  amount: number;
  currency?: string;
  disabled?: boolean;
}

// Available payment methods with their details
const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'credit_card',
    type: 'credit_card',
    provider: 'stripe',
    name: 'Credit Card',
    description: 'Pay securely with your credit or debit card',
    icon: 'credit-card',
    isActive: true,
    processingFee: 2.9,
    processingFeeType: 'percentage',
    minAmount: 10,
    maxAmount: 50000,
    supportedCurrencies: ['ZAR', 'USD', 'EUR'],
    estimatedProcessingTime: 'Instant'
  },
  {
    id: 'bank_transfer',
    type: 'bank_transfer',
    provider: 'payfast',
    name: 'Direct Bank Transfer',
    description: 'Transfer directly from your bank account',
    icon: 'banknote',
    isActive: true,
    processingFee: 0,
    processingFeeType: 'fixed',
    minAmount: 50,
    maxAmount: 100000,
    supportedCurrencies: ['ZAR'],
    estimatedProcessingTime: '1-3 business days'
  },
  {
    id: 'eft',
    type: 'eft',
    provider: 'ozow',
    name: 'Electronic Fund Transfer (EFT)',
    description: 'Fast and secure electronic transfer',
    icon: 'landmark',
    isActive: true,
    processingFee: 1.5,
    processingFeeType: 'percentage',
    minAmount: 20,
    maxAmount: 75000,
    supportedCurrencies: ['ZAR'],
    estimatedProcessingTime: 'Within 24 hours'
  }
];

const getIcon = (iconName: string) => {
  switch (iconName) {
    case 'credit-card':
      return CreditCard;
    case 'banknote':
      return BanknoteIcon;
    case 'landmark':
      return Landmark;
    case 'smartphone':
      return Smartphone;
    default:
      return CreditCard;
  }
};

const getProcessingFeeDisplay = (method: PaymentMethod, amount: number) => {
  if (method.processingFee === 0) return 'Free';
  
  if (method.processingFeeType === 'percentage') {
    const fee = (amount * method.processingFee!) / 100;
    return `${method.processingFee}% (${formatCurrency(fee)})`;
  } else {
    return formatCurrency(method.processingFee!);
  }
};

const isMethodAvailable = (method: PaymentMethod, amount: number, currency: string = 'ZAR') => {
  if (!method.isActive) return false;
  if (method.minAmount && amount < method.minAmount) return false;
  if (method.maxAmount && amount > method.maxAmount) return false;
  if (!method.supportedCurrencies.includes(currency)) return false;
  return true;
};

export function PaymentMethodSelector({
  selectedMethod,
  onMethodChange,
  amount,
  currency = 'ZAR',
  disabled = false
}: PaymentMethodSelectorProps) {
  const [hoveredMethod, setHoveredMethod] = useState<string | null>(null);

  const availableMethods = PAYMENT_METHODS.filter(method => 
    isMethodAvailable(method, amount, currency)
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Choose Payment Method</h3>
        <Badge variant="outline" className="text-sm">
          Amount: {formatCurrency(amount)}
        </Badge>
      </div>

      <RadioGroup
        value={selectedMethod}
        onValueChange={(value) => onMethodChange(value as PaymentMethodType)}
        disabled={disabled}
        className="space-y-3"
      >
        {availableMethods.map((method) => {
          const IconComponent = getIcon(method.icon || 'credit-card');
          const isSelected = selectedMethod === method.type;
          const isHovered = hoveredMethod === method.id;

          return (
            <motion.div
              key={method.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              onHoverStart={() => setHoveredMethod(method.id)}
              onHoverEnd={() => setHoveredMethod(null)}
            >
              <Card 
                className={`cursor-pointer transition-all duration-200 ${
                  isSelected 
                    ? 'ring-2 ring-purple-500 border-purple-500 bg-purple-50' 
                    : isHovered 
                    ? 'border-gray-300 shadow-md' 
                    : 'border-gray-200'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem 
                      value={method.type} 
                      id={method.id}
                      className="mt-1"
                      disabled={disabled}
                    />
                    
                    <div className="flex-1">
                      <Label 
                        htmlFor={method.id} 
                        className="flex items-center cursor-pointer"
                      >
                        <div className={`p-2 rounded-lg mr-3 ${
                          isSelected ? 'bg-purple-100' : 'bg-gray-100'
                        }`}>
                          <IconComponent className={`h-5 w-5 ${
                            isSelected ? 'text-purple-600' : 'text-gray-600'
                          }`} />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900">
                              {method.name}
                            </h4>
                            {isSelected && (
                              <CheckCircle2 className="h-5 w-5 text-purple-600" />
                            )}
                          </div>
                          
                          <p className="text-sm text-gray-600 mt-1">
                            {method.description}
                          </p>
                          
                          <div className="flex items-center justify-between mt-2">
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {method.estimatedProcessingTime}
                              </div>
                              
                              <div className="flex items-center">
                                <span className="font-medium">Fee:</span>
                                <span className="ml-1">
                                  {getProcessingFeeDisplay(method, amount)}
                                </span>
                              </div>
                            </div>
                            
                            {method.minAmount && amount < method.minAmount && (
                              <div className="flex items-center text-xs text-red-500">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Min: {formatCurrency(method.minAmount)}
                              </div>
                            )}
                          </div>
                        </div>
                      </Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </RadioGroup>

      {availableMethods.length === 0 && (
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <h3 className="font-medium text-gray-900 mb-1">
              No payment methods available
            </h3>
            <p className="text-sm text-gray-600">
              The order amount of {formatCurrency(amount)} doesn't meet the requirements 
              for any available payment methods.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Payment security notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <CheckCircle2 className="h-5 w-5 text-blue-600" />
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">
              Secure Payment Processing
            </h4>
            <p className="text-sm text-blue-700 mt-1">
              All payments are processed securely using industry-standard encryption. 
              Your payment information is never stored on our servers.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
