"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Award,
  TrendingUp,
  Calendar,
  Gift,
  Star,
  RefreshCw,
  DollarSign,
  Truck,
  ShoppingBag
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";

interface LoyaltyPointsDisplayProps {
  userId: string;
}

interface LoyaltyData {
  totalPoints: number;
  availablePoints: number;
  currentTier: string;
  tierProgress: {
    currentPoints: number;
    nextTierPoints: number;
    progressPercentage: number;
  };
  nextTier: string | null;
  tierBenefits: string[];
  pointsHistory: Array<{
    action: string;
    points: number;
    description: string;
    createdAt: string;
  }>;
  statistics: {
    totalEarned: number;
    totalRedeemed: number;
    totalCashValue: number;
    lifetimeValue: number;
  };
}

export function LoyaltyPointsDisplay({ userId }: LoyaltyPointsDisplayProps) {
  const [loyaltyData, setLoyaltyData] = useState<LoyaltyData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchLoyaltyData();
  }, [userId]);

  const fetchLoyaltyData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/loyalty/user/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setLoyaltyData(data.data);
        }
      } else {
        toast.error('Failed to load loyalty data');
      }
    } catch (error) {
      console.error('Error fetching loyalty data:', error);
      toast.error('Failed to load loyalty data');
    } finally {
      setIsLoading(false);
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'text-amber-600 bg-amber-100';
      case 'Silver': return 'text-gray-600 bg-gray-100';
      case 'Gold': return 'text-yellow-600 bg-yellow-100';
      case 'Platinum': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'purchase': return <ShoppingBag className="h-4 w-4 text-green-600" />;
      case 'referral': return <Gift className="h-4 w-4 text-purple-600" />;
      case 'signup': return <Star className="h-4 w-4 text-blue-600" />;
      case 'review': return <Star className="h-4 w-4 text-orange-600" />;
      case 'social_share': return <TrendingUp className="h-4 w-4 text-pink-600" />;
      default: return <Award className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!loyaltyData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            No loyalty data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Points Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Available Points</p>
                  <p className="text-3xl font-bold text-primary">
                    {loyaltyData.availablePoints.toLocaleString()}
                  </p>
                </div>
                <Award className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Cash Value</p>
                  <p className="text-3xl font-bold text-green-600">
                    R{loyaltyData.statistics.totalCashValue}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Tier</p>
                  <Badge className={getTierColor(loyaltyData.currentTier)}>
                    {loyaltyData.currentTier}
                  </Badge>
                </div>
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Tier Progress */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Tier Progress
          </CardTitle>
          <Button variant="outline" size="sm" onClick={fetchLoyaltyData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          {loyaltyData.nextTier ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">
                  Progress to {loyaltyData.nextTier}
                </span>
                <span className="text-sm text-gray-600">
                  {loyaltyData.tierProgress.currentPoints} / {loyaltyData.tierProgress.nextTierPoints} points
                </span>
              </div>
              <Progress 
                value={loyaltyData.tierProgress.progressPercentage} 
                className="h-3"
              />
              <p className="text-sm text-gray-600">
                {loyaltyData.tierProgress.nextTierPoints - loyaltyData.tierProgress.currentPoints} more points to reach {loyaltyData.nextTier}
              </p>
            </div>
          ) : (
            <div className="text-center py-4">
              <Star className="h-12 w-12 mx-auto mb-2 text-yellow-600" />
              <p className="font-medium text-gray-900">You've reached the highest tier!</p>
              <p className="text-sm text-gray-600">Congratulations on achieving Platinum status</p>
            </div>
          )}

          <div className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Current Tier Benefits</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {loyaltyData.tierBenefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  {benefit}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Points Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Points Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {loyaltyData.statistics.totalEarned.toLocaleString()}
              </div>
              <div className="text-sm text-blue-600">Total Earned</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {loyaltyData.statistics.totalRedeemed.toLocaleString()}
              </div>
              <div className="text-sm text-red-600">Total Redeemed</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                R{loyaltyData.statistics.lifetimeValue}
              </div>
              <div className="text-sm text-green-600">Lifetime Value</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {loyaltyData.totalPoints.toLocaleString()}
              </div>
              <div className="text-sm text-purple-600">Total Points</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loyaltyData.pointsHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Award className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">No activity yet</p>
              <p className="text-sm">Start shopping and referring friends to earn points!</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Activity</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Points</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loyaltyData.pointsHistory.map((activity, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getActionIcon(activity.action)}
                          <span className="capitalize">{activity.action.replace('_', ' ')}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {activity.description}
                      </TableCell>
                      <TableCell>
                        <span className="font-medium text-green-600">
                          +{activity.points}
                        </span>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {formatDate(activity.createdAt)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
