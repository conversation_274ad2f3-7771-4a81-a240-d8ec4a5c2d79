"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { 
  MessageCircle,
  Facebook,
  Twitter,
  Instagram,
  Mail,
  Copy,
  Share2,
  Smartphone,
  Users,
  Gift,
  Calendar,
  Clock,
  Zap,
  Target,
  Globe,
  QrCode,
  Link,
  Video,
  Image as ImageIcon,
  Sparkles
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";

interface PremiumSocialShareProps {
  referralCode: string;
  userName: string;
  tier: string;
}

const premiumTemplates = [
  {
    id: 'personal',
    name: 'Personal Story',
    template: `🌟 I've been saving so much with Stok<PERSON>! Last month alone I saved R{savings} on groceries. Join me and get {discount}% off your first order! Use my code: {code}`,
    category: 'Personal',
    performance: '+45% CTR'
  },
  {
    id: 'urgency',
    name: 'Limited Time',
    template: `⏰ URGENT: My exclusive {discount}% discount expires in 48 hours! Don't miss out on amazing savings at Stokvel. Use code: {code}`,
    category: 'Urgency',
    performance: '+32% conversions'
  },
  {
    id: 'social_proof',
    name: 'Social Proof',
    template: `🎉 Over {referrals} friends have joined Stokvel through my link! They're all loving the savings. Get {discount}% off your first order: {code}`,
    category: 'Social Proof',
    performance: '+28% engagement'
  },
  {
    id: 'value_focused',
    name: 'Value Proposition',
    template: `💰 Why pay full price? Stokvel offers premium groceries at wholesale prices. Get {discount}% off your first order with my code: {code}`,
    category: 'Value',
    performance: '+38% conversions'
  }
];

const advancedPlatforms = [
  {
    name: 'WhatsApp',
    icon: MessageCircle,
    color: 'bg-green-500 hover:bg-green-600',
    description: 'Best for personal networks',
    features: ['Auto-scheduling', 'Contact groups', 'Message tracking']
  },
  {
    name: 'Facebook',
    icon: Facebook,
    color: 'bg-blue-600 hover:bg-blue-700',
    description: 'Great for community sharing',
    features: ['Story integration', 'Group posting', 'Event creation']
  },
  {
    name: 'Instagram',
    icon: Instagram,
    color: 'bg-pink-500 hover:bg-pink-600',
    description: 'Visual content sharing',
    features: ['Story templates', 'Reel creation', 'IGTV integration']
  },
  {
    name: 'Twitter',
    icon: Twitter,
    color: 'bg-sky-500 hover:bg-sky-600',
    description: 'Public engagement',
    features: ['Thread creation', 'Hashtag optimization', 'Trending topics']
  },
  {
    name: 'LinkedIn',
    icon: Users,
    color: 'bg-blue-700 hover:bg-blue-800',
    description: 'Professional network',
    features: ['Article publishing', 'Network targeting', 'Industry insights']
  },
  {
    name: 'TikTok',
    icon: Video,
    color: 'bg-black hover:bg-gray-800',
    description: 'Video content',
    features: ['Video templates', 'Trend integration', 'Music sync']
  }
];

export function PremiumSocialShare({ referralCode, userName, tier }: PremiumSocialShareProps) {
  const [selectedTemplate, setSelectedTemplate] = useState(premiumTemplates[0]);
  const [customMessage, setCustomMessage] = useState('');
  const [autoSchedule, setAutoSchedule] = useState(false);
  const [trackClicks, setTrackClicks] = useState(true);
  const [generateQR, setGenerateQR] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  const referralLink = `${baseUrl}/signup?ref=${referralCode}&utm_source=referral&utm_medium=social&utm_campaign=${tier.toLowerCase()}`;

  const processTemplate = (template: string) => {
    return template
      .replace('{code}', referralCode)
      .replace('{discount}', '10')
      .replace('{savings}', '450')
      .replace('{referrals}', '23');
  };

  const handleAdvancedShare = async (platform: string) => {
    setIsSharing(true);
    
    try {
      const message = customMessage || processTemplate(selectedTemplate.template);
      
      // Advanced sharing logic with tracking
      const shareData = {
        platform,
        message,
        referralCode,
        template: selectedTemplate.id,
        trackClicks,
        autoSchedule,
        tier
      };

      // Simulate API call for advanced sharing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate platform-specific share URL
      let shareUrl = '';
      const encodedMessage = encodeURIComponent(message);
      const encodedUrl = encodeURIComponent(referralLink);

      switch (platform) {
        case 'WhatsApp':
          shareUrl = `https://wa.me/?text=${encodedMessage}%20${encodedUrl}`;
          break;
        case 'Facebook':
          shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedMessage}`;
          break;
        case 'Twitter':
          shareUrl = `https://twitter.com/intent/tweet?text=${encodedMessage}&url=${encodedUrl}`;
          break;
        case 'LinkedIn':
          shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
          break;
        case 'Instagram':
          // Instagram doesn't support direct URL sharing, so copy to clipboard
          await navigator.clipboard.writeText(`${message} ${referralLink}`);
          toast.success('Content copied! Paste it in your Instagram story or post.');
          return;
        default:
          shareUrl = referralLink;
      }

      if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
        toast.success(`${platform} share opened! Your clicks will be tracked.`);
      }
    } catch (error) {
      console.error('Error sharing:', error);
      toast.error('Failed to share');
    } finally {
      setIsSharing(false);
    }
  };

  const generateQRCode = () => {
    // Simulate QR code generation
    toast.success('QR code generated! Check your downloads folder.');
  };

  const copyAdvancedLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      toast.success('Advanced tracking link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  return (
    <div className="space-y-6">
      {/* Premium Features Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Sparkles className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">Premium Social Sharing</h3>
              <p className="text-sm text-gray-600 font-normal">
                AI-optimized templates with advanced tracking & automation
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
              <Target className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium">Click Tracking</p>
                <p className="text-xs text-gray-500">Real-time analytics</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Auto-Schedule</p>
                <p className="text-xs text-gray-500">Optimal timing</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
              <QrCode className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">QR Codes</p>
                <p className="text-xs text-gray-500">Offline sharing</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-white rounded-lg">
              <Zap className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">AI Templates</p>
                <p className="text-xs text-gray-500">Optimized content</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            AI-Optimized Templates
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {premiumTemplates.map((template) => (
              <motion.div
                key={template.id}
                whileHover={{ scale: 1.02 }}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  selectedTemplate.id === template.id
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-purple-300'
                }`}
                onClick={() => setSelectedTemplate(template)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">{template.name}</h4>
                  <Badge className="bg-green-100 text-green-800 text-xs">
                    {template.performance}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  {processTemplate(template.template)}
                </p>
                <Badge variant="outline" className="text-xs">
                  {template.category}
                </Badge>
              </motion.div>
            ))}
          </div>

          <div className="space-y-4">
            <Label htmlFor="custom-message">Custom Message (Optional)</Label>
            <Textarea
              id="custom-message"
              placeholder="Override the template with your own message..."
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Advanced Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="track-clicks">Click Tracking</Label>
                  <p className="text-xs text-gray-500">Monitor link performance</p>
                </div>
                <Switch
                  id="track-clicks"
                  checked={trackClicks}
                  onCheckedChange={setTrackClicks}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-schedule">Auto-Schedule</Label>
                  <p className="text-xs text-gray-500">Post at optimal times</p>
                </div>
                <Switch
                  id="auto-schedule"
                  checked={autoSchedule}
                  onCheckedChange={setAutoSchedule}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="generate-qr">Generate QR Code</Label>
                  <p className="text-xs text-gray-500">For offline sharing</p>
                </div>
                <Switch
                  id="generate-qr"
                  checked={generateQR}
                  onCheckedChange={setGenerateQR}
                />
              </div>

              {generateQR && (
                <Button onClick={generateQRCode} variant="outline" className="w-full">
                  <QrCode className="h-4 w-4 mr-2" />
                  Generate QR Code
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Sharing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Premium Platform Sharing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {advancedPlatforms.map((platform, index) => (
              <motion.div
                key={platform.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="border rounded-lg p-4 hover:shadow-lg transition-all"
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className={`p-2 rounded-lg ${platform.color}`}>
                    <platform.icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{platform.name}</h4>
                    <p className="text-xs text-gray-500">{platform.description}</p>
                  </div>
                </div>
                
                <div className="space-y-2 mb-4">
                  {platform.features.map((feature) => (
                    <div key={feature} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                      <span className="text-xs text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>

                <Button
                  onClick={() => handleAdvancedShare(platform.name)}
                  disabled={isSharing}
                  className={`w-full ${platform.color} text-white`}
                >
                  {isSharing ? (
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <platform.icon className="h-4 w-4 mr-2" />
                  )}
                  Share on {platform.name}
                </Button>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button onClick={copyAdvancedLink} variant="outline">
              <Copy className="h-4 w-4 mr-2" />
              Copy Tracking Link
            </Button>
            <Button variant="outline">
              <ImageIcon className="h-4 w-4 mr-2" />
              Create Visual Content
            </Button>
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Posts
            </Button>
            <Button variant="outline">
              <Globe className="h-4 w-4 mr-2" />
              View Analytics
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
