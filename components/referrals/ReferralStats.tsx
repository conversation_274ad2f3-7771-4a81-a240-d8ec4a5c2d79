"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  RefreshCw,
  User,
  Calendar,
  DollarSign,
  Award,
  CheckCircle,
  Clock,
  TrendingUp
} from "lucide-react";
import { motion } from "framer-motion";

interface ReferralStatsProps {
  stats: {
    totalReferrals: number;
    successfulReferrals: number;
    pendingReferrals: number;
    totalEarnings: number;
    totalPoints: number;
    conversionRate: number;
    referralHistory: Array<{
      refereeId: string;
      refereeName: string;
      status: string;
      joinedAt: string;
      firstPurchaseAt?: string;
      earnedPoints: number;
      earnedAmount: number;
    }>;
  } | null;
  onRefresh: () => void;
}

export function ReferralStats({ stats, onRefresh }: ReferralStatsProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">
          <CheckCircle className="h-3 w-3 mr-1" />
          Completed
        </Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
          <Clock className="h-3 w-3 mr-1" />
          Pending
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            No referral data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Overview
          </CardTitle>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalReferrals}</div>
              <div className="text-sm text-blue-600">Total Invites Sent</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.successfulReferrals}</div>
              <div className="text-sm text-green-600">Friends Joined</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">R{stats.totalEarnings.toFixed(2)}</div>
              <div className="text-sm text-purple-600">Total Earnings</div>
            </div>
          </div>

          {stats.totalReferrals > 0 && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">Conversion Rate</span>
                <span className="text-lg font-bold text-gray-900">{stats.conversionRate.toFixed(1)}%</span>
              </div>
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(100, stats.conversionRate)}%` }}
                ></div>
              </div>
              <div className="mt-1 text-xs text-gray-500">
                {stats.successfulReferrals} out of {stats.totalReferrals} friends made their first purchase
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Referral History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Referral History
          </CardTitle>
        </CardHeader>
        <CardContent>
          {stats.referralHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <User className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">No referrals yet</p>
              <p className="text-sm">Start inviting friends to see your referral history here</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Friend</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Joined Date</TableHead>
                    <TableHead>First Purchase</TableHead>
                    <TableHead>Points Earned</TableHead>
                    <TableHead>Cash Earned</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {stats.referralHistory.map((referral, index) => (
                    <motion.tr
                      key={referral.refereeId}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="hover:bg-gray-50"
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <User className="h-4 w-4 text-primary" />
                          </div>
                          {referral.refereeName}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(referral.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <Calendar className="h-3 w-3" />
                          {formatDate(referral.joinedAt)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {referral.firstPurchaseAt ? (
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Calendar className="h-3 w-3" />
                            {formatDate(referral.firstPurchaseAt)}
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">Not yet</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Award className="h-3 w-3 text-purple-600" />
                          <span className="font-medium text-purple-600">
                            {referral.earnedPoints}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3 text-green-600" />
                          <span className="font-medium text-green-600">
                            R{referral.earnedAmount.toFixed(2)}
                          </span>
                        </div>
                      </TableCell>
                    </motion.tr>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tips for Better Results */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Tips for Better Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Share Personally</h4>
              <p className="text-sm text-blue-700">
                Personal messages work better than generic posts. Tell friends why you love Stokvel!
              </p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2">Timing Matters</h4>
              <p className="text-sm text-green-700">
                Share when friends are likely to be thinking about shopping - weekends work great!
              </p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-900 mb-2">Follow Up</h4>
              <p className="text-sm text-purple-700">
                Check in with friends who joined to help them get started and make their first purchase.
              </p>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-900 mb-2">Use Multiple Channels</h4>
              <p className="text-sm text-orange-700">
                Try WhatsApp, social media, and in-person conversations for the best reach.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
