"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { 
  Rocket, 
  Zap, 
  Calendar, 
  Target, 
  Mail,
  MessageSquare,
  Bell,
  Gift,
  TrendingUp,
  Users,
  Clock,
  Settings,
  Play,
  Pause,
  Edit,
  Plus,
  BarChart3
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

interface AutomatedCampaignsProps {
  userId: string;
}

interface Campaign {
  id: string;
  name: string;
  description: string;
  type: 'milestone' | 'seasonal' | 'behavioral' | 'tier_based';
  status: 'active' | 'paused' | 'draft';
  trigger: string;
  action: string;
  participants: number;
  conversions: number;
  revenue: number;
  isActive: boolean;
  performance: number;
}

const mockCampaigns: Campaign[] = [
  {
    id: '1',
    name: 'Welcome Series',
    description: 'Automated welcome sequence for new referrals',
    type: 'milestone',
    status: 'active',
    trigger: 'New referral signs up',
    action: 'Send welcome email + bonus points',
    participants: 156,
    conversions: 89,
    revenue: 4320.50,
    isActive: true,
    performance: 92
  },
  {
    id: '2',
    name: 'First Purchase Boost',
    description: 'Encourage first purchase with time-limited bonus',
    type: 'behavioral',
    status: 'active',
    trigger: 'Referral inactive for 3 days',
    action: 'Send reminder + 15% bonus discount',
    participants: 78,
    conversions: 45,
    revenue: 2890.25,
    isActive: true,
    performance: 87
  },
  {
    id: '3',
    name: 'Tier Upgrade Celebration',
    description: 'Celebrate and reward tier upgrades',
    type: 'tier_based',
    status: 'active',
    trigger: 'User reaches new tier',
    action: 'Send congratulations + exclusive rewards',
    participants: 23,
    conversions: 21,
    revenue: 1560.80,
    isActive: true,
    performance: 95
  },
  {
    id: '4',
    name: 'Holiday Special',
    description: 'Seasonal campaign for holiday periods',
    type: 'seasonal',
    status: 'paused',
    trigger: 'Holiday period starts',
    action: 'Send holiday-themed content + bonus',
    participants: 234,
    conversions: 123,
    revenue: 6780.40,
    isActive: false,
    performance: 78
  }
];

export function AutomatedCampaigns({ userId }: AutomatedCampaignsProps) {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setCampaigns(mockCampaigns);
      setIsLoading(false);
    }, 1000);
  }, [userId]);

  const toggleCampaign = (campaignId: string) => {
    setCampaigns(prev => prev.map(campaign => 
      campaign.id === campaignId 
        ? { ...campaign, isActive: !campaign.isActive, status: campaign.isActive ? 'paused' : 'active' }
        : campaign
    ));
    toast.success('Campaign status updated successfully!');
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'milestone': return Target;
      case 'seasonal': return Calendar;
      case 'behavioral': return Users;
      case 'tier_based': return TrendingUp;
      default: return Rocket;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'milestone': return 'bg-blue-100 text-blue-800';
      case 'seasonal': return 'bg-green-100 text-green-800';
      case 'behavioral': return 'bg-purple-100 text-purple-800';
      case 'tier_based': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading automated campaigns...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-600 rounded-lg">
                <Rocket className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">Automated Campaigns</CardTitle>
                <p className="text-sm text-gray-600">Set up intelligent automation workflows</p>
              </div>
            </div>
            
            <Button className="bg-purple-600 hover:bg-purple-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Campaign
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Rocket className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">{campaigns.filter(c => c.isActive).length}</p>
              <p className="text-sm text-gray-600">Active Campaigns</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">
                {campaigns.reduce((sum, c) => sum + c.participants, 0)}
              </p>
              <p className="text-sm text-gray-600">Total Participants</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">
                {Math.round(campaigns.reduce((sum, c) => sum + c.performance, 0) / campaigns.length)}%
              </p>
              <p className="text-sm text-gray-600">Avg Performance</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Gift className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">
                R{campaigns.reduce((sum, c) => sum + c.revenue, 0).toLocaleString()}
              </p>
              <p className="text-sm text-gray-600">Total Revenue</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Campaign Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Quick Setup Templates
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                name: 'Welcome Series',
                description: 'Onboard new referrals with automated sequence',
                icon: Mail,
                color: 'bg-blue-500'
              },
              {
                name: 'Re-engagement',
                description: 'Win back inactive referrals',
                icon: Bell,
                color: 'bg-orange-500'
              },
              {
                name: 'Milestone Rewards',
                description: 'Celebrate referral achievements',
                icon: Gift,
                color: 'bg-green-500'
              },
              {
                name: 'Seasonal Campaigns',
                description: 'Holiday and special event automation',
                icon: Calendar,
                color: 'bg-purple-500'
              },
              {
                name: 'Tier Upgrades',
                description: 'Reward loyalty tier progressions',
                icon: TrendingUp,
                color: 'bg-pink-500'
              },
              {
                name: 'Social Sharing',
                description: 'Encourage social media sharing',
                icon: MessageSquare,
                color: 'bg-cyan-500'
              }
            ].map((template, index) => (
              <motion.div
                key={template.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 border rounded-lg hover:shadow-md transition-all cursor-pointer"
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className={`p-2 rounded-lg ${template.color}`}>
                    <template.icon className="h-5 w-5 text-white" />
                  </div>
                  <h4 className="font-medium text-gray-900">{template.name}</h4>
                </div>
                <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                <Button variant="outline" size="sm" className="w-full">
                  Use Template
                </Button>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Active Campaigns */}
      <Card>
        <CardHeader>
          <CardTitle>Your Campaigns</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <AnimatePresence>
              {campaigns.map((campaign, index) => {
                const TypeIcon = getTypeIcon(campaign.type);
                
                return (
                  <motion.div
                    key={campaign.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1 }}
                    className="border rounded-lg p-6 hover:shadow-md transition-all"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-4">
                        <div className="p-3 bg-purple-100 rounded-lg">
                          <TypeIcon className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">
                            {campaign.name}
                          </h3>
                          <p className="text-sm text-gray-600 mb-2">{campaign.description}</p>
                          <div className="flex gap-2">
                            <Badge className={getTypeColor(campaign.type)}>
                              {campaign.type.replace('_', ' ')}
                            </Badge>
                            <Badge className={getStatusColor(campaign.status)}>
                              {campaign.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={campaign.isActive}
                          onCheckedChange={() => toggleCampaign(campaign.id)}
                        />
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm font-medium text-gray-600 mb-1">Trigger</p>
                        <p className="text-sm text-gray-900">{campaign.trigger}</p>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm font-medium text-gray-600 mb-1">Action</p>
                        <p className="text-sm text-gray-900">{campaign.action}</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">{campaign.participants}</p>
                        <p className="text-xs text-gray-600">Participants</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{campaign.conversions}</p>
                        <p className="text-xs text-gray-600">Conversions</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600">R{campaign.revenue.toLocaleString()}</p>
                        <p className="text-xs text-gray-600">Revenue</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{campaign.performance}%</p>
                        <p className="text-xs text-gray-600">Performance</p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Campaign Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Top Performing Campaigns</h4>
              {campaigns
                .sort((a, b) => b.performance - a.performance)
                .slice(0, 3)
                .map((campaign) => (
                  <div key={campaign.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">{campaign.name}</span>
                    <Badge className="bg-green-100 text-green-800">
                      {campaign.performance}% success
                    </Badge>
                  </div>
                ))}
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Optimization Tips</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                  <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">Timing Optimization</p>
                    <p className="text-xs text-blue-700">Send campaigns during peak engagement hours</p>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                  <Target className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-green-900">Personalization</p>
                    <p className="text-xs text-green-700">Use dynamic content based on user behavior</p>
                  </div>
                </div>
                <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-purple-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-purple-900">A/B Testing</p>
                    <p className="text-xs text-purple-700">Test different messages and timing</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
