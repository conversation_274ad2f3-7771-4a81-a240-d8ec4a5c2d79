"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Trophy, 
  Crown, 
  Medal, 
  Star, 
  TrendingUp,
  Users,
  DollarSign,
  Award,
  Zap,
  Target,
  Gift,
  Sparkles
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface ReferralLeaderboardProps {
  userId: string;
}

interface LeaderboardEntry {
  rank: number;
  userId: string;
  userName: string;
  avatar?: string;
  referrals: number;
  earnings: number;
  tier: string;
  badge?: string;
  isCurrentUser?: boolean;
  growth: number;
  streak: number;
}

const mockLeaderboardData: LeaderboardEntry[] = [
  {
    rank: 1,
    userId: '1',
    userName: '<PERSON>',
    avatar: '/avatars/sarah.jpg',
    referrals: 247,
    earnings: 15420.50,
    tier: 'Diamond',
    badge: 'Top Performer',
    growth: 23.5,
    streak: 12
  },
  {
    rank: 2,
    userId: '2',
    userName: 'Michael Chen',
    avatar: '/avatars/michael.jpg',
    referrals: 189,
    earnings: 12340.80,
    tier: 'Platinum',
    badge: 'Social Media Master',
    growth: 18.2,
    streak: 8
  },
  {
    rank: 3,
    userId: '3',
    userName: 'Emma <PERSON>',
    avatar: '/avatars/emma.jpg',
    referrals: 156,
    earnings: 9870.25,
    tier: 'Gold',
    badge: 'Conversion King',
    growth: 15.7,
    streak: 6
  },
  {
    rank: 4,
    userId: '4',
    userName: 'David Rodriguez',
    referrals: 134,
    earnings: 8450.60,
    tier: 'Gold',
    growth: 12.3,
    streak: 4
  },
  {
    rank: 5,
    userId: '5',
    userName: 'Lisa Thompson',
    referrals: 98,
    earnings: 6230.40,
    tier: 'Silver',
    growth: 9.8,
    streak: 3
  }
];

export function ReferralLeaderboard({ userId }: ReferralLeaderboardProps) {
  const [period, setPeriod] = useState('monthly');
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [userRank, setUserRank] = useState<LeaderboardEntry | null>(null);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setLeaderboard(mockLeaderboardData);
      // Simulate current user rank
      setUserRank({
        rank: 12,
        userId: userId,
        userName: 'You',
        referrals: 45,
        earnings: 2340.50,
        tier: 'Silver',
        isCurrentUser: true,
        growth: 8.5,
        streak: 2
      });
    }, 1000);
  }, [period, userId]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Crown className="h-6 w-6 text-yellow-500" />;
      case 2: return <Medal className="h-6 w-6 text-gray-400" />;
      case 3: return <Award className="h-6 w-6 text-amber-600" />;
      default: return <span className="text-lg font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Diamond': return 'from-purple-500 to-pink-500';
      case 'Platinum': return 'from-gray-400 to-gray-600';
      case 'Gold': return 'from-yellow-400 to-yellow-600';
      case 'Silver': return 'from-gray-300 to-gray-500';
      default: return 'from-amber-400 to-amber-600';
    }
  };

  const getRankBackground = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200';
      case 2: return 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200';
      case 3: return 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200';
      default: return 'bg-white border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-600 rounded-lg">
                <Trophy className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">Referral Leaderboard</CardTitle>
                <p className="text-sm text-gray-600">Compete with top performers</p>
              </div>
            </div>
            
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">This Week</SelectItem>
                <SelectItem value="monthly">This Month</SelectItem>
                <SelectItem value="quarterly">This Quarter</SelectItem>
                <SelectItem value="yearly">This Year</SelectItem>
                <SelectItem value="all-time">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Trophy className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">{userRank?.rank || '--'}</p>
              <p className="text-sm text-gray-600">Your Rank</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">+{userRank?.growth || 0}%</p>
              <p className="text-sm text-gray-600">Growth Rate</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Zap className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">{userRank?.streak || 0}</p>
              <p className="text-sm text-gray-600">Week Streak</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top 3 Podium */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Top Performers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {leaderboard.slice(0, 3).map((entry, index) => (
              <motion.div
                key={entry.userId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
                className={`relative p-6 rounded-xl border-2 ${getRankBackground(entry.rank)}`}
              >
                <div className="text-center">
                  <div className="relative mb-4">
                    <Avatar className="h-16 w-16 mx-auto border-4 border-white shadow-lg">
                      <AvatarImage src={entry.avatar} />
                      <AvatarFallback className={`bg-gradient-to-r ${getTierColor(entry.tier)} text-white font-bold`}>
                        {entry.userName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute -top-2 -right-2">
                      {getRankIcon(entry.rank)}
                    </div>
                  </div>
                  
                  <h3 className="font-bold text-gray-900 mb-1">{entry.userName}</h3>
                  <Badge className={`bg-gradient-to-r ${getTierColor(entry.tier)} text-white mb-3`}>
                    {entry.tier}
                  </Badge>
                  
                  {entry.badge && (
                    <Badge variant="outline" className="mb-3 text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      {entry.badge}
                    </Badge>
                  )}
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Referrals:</span>
                      <span className="font-medium">{entry.referrals}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Earnings:</span>
                      <span className="font-medium">R{entry.earnings.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Growth:</span>
                      <span className="font-medium text-green-600">+{entry.growth}%</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Full Leaderboard */}
      <Card>
        <CardHeader>
          <CardTitle>Complete Rankings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <AnimatePresence>
              {leaderboard.map((entry, index) => (
                <motion.div
                  key={entry.userId}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.05 }}
                  className={`flex items-center gap-4 p-4 rounded-lg border ${
                    entry.isCurrentUser ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
                  } hover:shadow-md transition-all`}
                >
                  <div className="flex items-center justify-center w-12 h-12">
                    {getRankIcon(entry.rank)}
                  </div>
                  
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={entry.avatar} />
                    <AvatarFallback className={`bg-gradient-to-r ${getTierColor(entry.tier)} text-white`}>
                      {entry.userName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-gray-900">{entry.userName}</h4>
                      {entry.isCurrentUser && (
                        <Badge className="bg-blue-100 text-blue-800 text-xs">You</Badge>
                      )}
                      <Badge className={`bg-gradient-to-r ${getTierColor(entry.tier)} text-white text-xs`}>
                        {entry.tier}
                      </Badge>
                    </div>
                    {entry.badge && (
                      <Badge variant="outline" className="text-xs">
                        <Star className="h-3 w-3 mr-1" />
                        {entry.badge}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-900">{entry.referrals}</p>
                        <p className="text-gray-500">Referrals</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">R{entry.earnings.toLocaleString()}</p>
                        <p className="text-gray-500">Earnings</p>
                      </div>
                      <div>
                        <p className="font-medium text-green-600">+{entry.growth}%</p>
                        <p className="text-gray-500">Growth</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Your Position */}
      {userRank && userRank.rank > 10 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-full font-bold">
                  #{userRank.rank}
                </div>
                <div>
                  <p className="font-medium text-blue-900">Your Current Position</p>
                  <p className="text-sm text-blue-700">
                    {userRank.referrals} referrals • R{userRank.earnings.toLocaleString()} earned
                  </p>
                </div>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Target className="h-4 w-4 mr-2" />
                Improve Rank
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
