"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Gift,
  DollarSign,
  Truck,
  Percent,
  CreditCard,
  Banknote,
  Smartphone,
  MapPin,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";

interface PointsRedemptionProps {
  userId: string;
}

interface RedemptionOption {
  type: string;
  name: string;
  pointsCost: number;
  value: number;
  description: string;
  available: boolean;
  icon: any;
  color: string;
}

interface WithdrawalForm {
  amount: number;
  method: 'bank_transfer' | 'mobile_money' | 'cash_pickup';
  bankDetails?: {
    accountName: string;
    accountNumber: string;
    bankName: string;
  };
  mobileMoneyDetails?: {
    phoneNumber: string;
    provider: 'vodacom' | 'mtn' | 'cell_c' | 'telkom';
  };
  pickupLocation?: string;
}

export function PointsRedemption({ userId }: PointsRedemptionProps) {
  const [availablePoints, setAvailablePoints] = useState(0);
  const [redemptionOptions, setRedemptionOptions] = useState<RedemptionOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [showWithdrawalDialog, setShowWithdrawalDialog] = useState(false);
  const [withdrawalForm, setWithdrawalForm] = useState<WithdrawalForm>({
    amount: 0,
    method: 'bank_transfer'
  });

  useEffect(() => {
    fetchRedemptionData();
  }, [userId]);

  const fetchRedemptionData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/loyalty/user/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAvailablePoints(data.data.availablePoints);
          
          // Transform redemption options with icons and colors
          const options = data.data.redemptionOptions.map((option: any) => ({
            ...option,
            icon: getRedemptionIcon(option.type),
            color: getRedemptionColor(option.type)
          }));
          
          setRedemptionOptions(options);
        }
      }
    } catch (error) {
      console.error('Error fetching redemption data:', error);
      toast.error('Failed to load redemption options');
    } finally {
      setIsLoading(false);
    }
  };

  const getRedemptionIcon = (type: string) => {
    switch (type) {
      case 'discount': return Percent;
      case 'free_shipping': return Truck;
      case 'delivery_payment': return CreditCard;
      case 'cash_withdrawal': return Banknote;
      default: return Gift;
    }
  };

  const getRedemptionColor = (type: string) => {
    switch (type) {
      case 'discount': return 'bg-blue-500';
      case 'free_shipping': return 'bg-green-500';
      case 'delivery_payment': return 'bg-purple-500';
      case 'cash_withdrawal': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const handleRedeem = async (option: RedemptionOption) => {
    if (option.type === 'cash_withdrawal') {
      setShowWithdrawalDialog(true);
      return;
    }

    setIsRedeeming(true);
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/loyalty?action=redeem', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          rewardType: option.type,
          pointsCost: option.pointsCost
        })
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(`Successfully redeemed ${option.name}!`);
        if (data.couponCode) {
          toast.success(`Your coupon code: ${data.couponCode}`);
        }
        fetchRedemptionData(); // Refresh data
      } else {
        toast.error(data.error || 'Failed to redeem reward');
      }
    } catch (error) {
      console.error('Error redeeming reward:', error);
      toast.error('Failed to redeem reward');
    } finally {
      setIsRedeeming(false);
    }
  };

  const handleWithdrawal = async () => {
    if (withdrawalForm.amount < 10) {
      toast.error('Minimum withdrawal amount is R10');
      return;
    }

    const requiredPoints = withdrawalForm.amount * 10;
    if (availablePoints < requiredPoints) {
      toast.error(`Insufficient points. You need ${requiredPoints} points for R${withdrawalForm.amount}`);
      return;
    }

    setIsRedeeming(true);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/loyalty?action=withdraw', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          amount: withdrawalForm.amount,
          pointsUsed: requiredPoints,
          method: withdrawalForm.method,
          bankDetails: withdrawalForm.bankDetails,
          mobileMoneyDetails: withdrawalForm.mobileMoneyDetails,
          pickupLocation: withdrawalForm.pickupLocation
        })
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(`Withdrawal request submitted! Reference: ${data.withdrawalId}`);
        setShowWithdrawalDialog(false);
        setWithdrawalForm({ amount: 0, method: 'bank_transfer' });
        fetchRedemptionData(); // Refresh data
      } else {
        toast.error(data.error || 'Failed to process withdrawal');
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      toast.error('Failed to process withdrawal');
    } finally {
      setIsRedeeming(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Points Balance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Available Points
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <div className="text-4xl font-bold text-primary mb-2">
              {availablePoints.toLocaleString()}
            </div>
            <div className="text-lg text-gray-600">
              Cash Value: R{Math.floor(availablePoints / 10)}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              10 points = R1
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Redemption Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {redemptionOptions.map((option, index) => {
          const IconComponent = option.icon;
          
          return (
            <motion.div
              key={option.type + option.pointsCost}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`${option.available ? 'hover:shadow-lg transition-shadow' : 'opacity-60'}`}>
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={`w-12 h-12 ${option.color} rounded-lg flex items-center justify-center`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{option.name}</h3>
                      <p className="text-sm text-gray-600 mb-3">{option.description}</p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">
                            {option.pointsCost.toLocaleString()} points
                          </Badge>
                          {option.value > 0 && (
                            <Badge variant="secondary">
                              R{option.value} value
                            </Badge>
                          )}
                        </div>
                        
                        <Button
                          onClick={() => handleRedeem(option)}
                          disabled={!option.available || isRedeeming}
                          size="sm"
                        >
                          {!option.available ? 'Insufficient Points' : 'Redeem'}
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Withdrawal Dialog */}
      <Dialog open={showWithdrawalDialog} onOpenChange={setShowWithdrawalDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Cash Withdrawal</DialogTitle>
            <DialogDescription>
              Withdraw your points as cash. Minimum withdrawal is R10 (100 points).
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="amount">Withdrawal Amount (R)</Label>
              <Input
                id="amount"
                type="number"
                min="10"
                max={Math.floor(availablePoints / 10)}
                value={withdrawalForm.amount || ''}
                onChange={(e) => setWithdrawalForm(prev => ({
                  ...prev,
                  amount: parseInt(e.target.value) || 0
                }))}
                placeholder="Enter amount"
              />
              <div className="text-xs text-gray-500 mt-1">
                Points required: {(withdrawalForm.amount * 10).toLocaleString()}
              </div>
            </div>

            <div>
              <Label htmlFor="method">Withdrawal Method</Label>
              <Select
                value={withdrawalForm.method}
                onValueChange={(value: any) => setWithdrawalForm(prev => ({
                  ...prev,
                  method: value
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="mobile_money">Mobile Money</SelectItem>
                  <SelectItem value="cash_pickup">Cash Pickup</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {withdrawalForm.method === 'bank_transfer' && (
              <div className="space-y-3">
                <Input
                  placeholder="Account Name"
                  value={withdrawalForm.bankDetails?.accountName || ''}
                  onChange={(e) => setWithdrawalForm(prev => ({
                    ...prev,
                    bankDetails: {
                      ...prev.bankDetails,
                      accountName: e.target.value,
                      accountNumber: prev.bankDetails?.accountNumber || '',
                      bankName: prev.bankDetails?.bankName || ''
                    }
                  }))}
                />
                <Input
                  placeholder="Account Number"
                  value={withdrawalForm.bankDetails?.accountNumber || ''}
                  onChange={(e) => setWithdrawalForm(prev => ({
                    ...prev,
                    bankDetails: {
                      ...prev.bankDetails,
                      accountName: prev.bankDetails?.accountName || '',
                      accountNumber: e.target.value,
                      bankName: prev.bankDetails?.bankName || ''
                    }
                  }))}
                />
                <Input
                  placeholder="Bank Name"
                  value={withdrawalForm.bankDetails?.bankName || ''}
                  onChange={(e) => setWithdrawalForm(prev => ({
                    ...prev,
                    bankDetails: {
                      ...prev.bankDetails,
                      accountName: prev.bankDetails?.accountName || '',
                      accountNumber: prev.bankDetails?.accountNumber || '',
                      bankName: e.target.value
                    }
                  }))}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowWithdrawalDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleWithdrawal} disabled={isRedeeming}>
              {isRedeeming ? 'Processing...' : 'Submit Withdrawal'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
