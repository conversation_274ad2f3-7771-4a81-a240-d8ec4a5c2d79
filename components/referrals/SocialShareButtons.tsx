"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  MessageCircle,
  Facebook,
  Twitter,
  Instagram,
  Mail,
  Copy,
  Share2,
  Smartphone,
  Users,
  Gift
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";

interface SocialShareButtonsProps {
  referralCode: string;
  userName: string;
}

export function SocialShareButtons({ referralCode, userName }: SocialShareButtonsProps) {
  const [customMessage, setCustomMessage] = useState('');
  const [isSharing, setIsSharing] = useState(false);

  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  const referralLink = `${baseUrl}/signup?ref=${referralCode}`;
  
  const defaultMessage = `🎉 Hey! I've been using <PERSON><PERSON><PERSON> for my grocery shopping and it's amazing! Join me and get 10% off your first order. Use my code: ${referralCode}`;

  const shareOptions = [
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'bg-green-500 hover:bg-green-600',
      platform: 'whatsapp' as const,
      description: 'Share with friends and family'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'bg-blue-600 hover:bg-blue-700',
      platform: 'facebook' as const,
      description: 'Post to your timeline'
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'bg-sky-500 hover:bg-sky-600',
      platform: 'twitter' as const,
      description: 'Tweet to your followers'
    },
    {
      name: 'Email',
      icon: Mail,
      color: 'bg-gray-600 hover:bg-gray-700',
      platform: 'email' as const,
      description: 'Send via email'
    }
  ];

  const handleShare = async (platform: string) => {
    setIsSharing(true);
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/referrals?action=share', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform,
          referralCode,
          customMessage: customMessage || defaultMessage
        })
      });

      const data = await response.json();
      
      if (data.success && data.shareUrl) {
        // Open the share URL
        window.open(data.shareUrl, '_blank', 'width=600,height=400');
        toast.success(`${platform.charAt(0).toUpperCase() + platform.slice(1)} share opened!`);
      } else {
        toast.error('Failed to generate share link');
      }
    } catch (error) {
      console.error('Error sharing:', error);
      toast.error('Failed to share');
    } finally {
      setIsSharing(false);
    }
  };

  const copyReferralLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      toast.success('Referral link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  const copyReferralCode = async () => {
    try {
      await navigator.clipboard.writeText(referralCode);
      toast.success('Referral code copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy code');
    }
  };

  return (
    <div className="space-y-6">
      {/* Share Message Customization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Customize Your Message
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Your Personal Message
            </label>
            <Textarea
              placeholder={defaultMessage}
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              className="min-h-[100px]"
            />
            <p className="text-xs text-gray-500 mt-1">
              Leave empty to use the default message
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Social Media Sharing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share on Social Media
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {shareOptions.map((option, index) => (
              <motion.div
                key={option.platform}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Button
                  onClick={() => handleShare(option.platform)}
                  disabled={isSharing}
                  className={`w-full h-auto p-4 ${option.color} text-white`}
                  variant="default"
                >
                  <div className="flex items-center gap-3">
                    <option.icon className="h-6 w-6" />
                    <div className="text-left">
                      <div className="font-medium">{option.name}</div>
                      <div className="text-xs opacity-90">{option.description}</div>
                    </div>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Copy Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Copy className="h-5 w-5" />
            Quick Copy
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Referral Code
              </label>
              <div className="flex gap-2">
                <Input
                  value={referralCode}
                  readOnly
                  className="font-mono font-bold"
                />
                <Button onClick={copyReferralCode} variant="outline">
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Referral Link
              </label>
              <div className="flex gap-2">
                <Input
                  value={referralLink}
                  readOnly
                  className="text-sm"
                />
                <Button onClick={copyReferralLink} variant="outline">
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* WhatsApp Quick Share */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <MessageCircle className="h-5 w-5" />
            WhatsApp Quick Share
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-green-700 mb-4">
            WhatsApp is the most effective way to share with friends and family in South Africa!
          </p>
          <Button
            onClick={() => handleShare('whatsapp')}
            disabled={isSharing}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            Share on WhatsApp
          </Button>
        </CardContent>
      </Card>

      {/* Sharing Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Sharing Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-bold text-blue-600">1</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Personal Touch</h4>
                <p className="text-sm text-gray-600">
                  Add a personal note about why you love Stokvel - it makes a big difference!
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-bold text-green-600">2</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Best Times to Share</h4>
                <p className="text-sm text-gray-600">
                  Weekends and evenings work best when people are planning their shopping.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-bold text-purple-600">3</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Follow Up</h4>
                <p className="text-sm text-gray-600">
                  Check in with friends who join to help them get started and earn your rewards!
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
