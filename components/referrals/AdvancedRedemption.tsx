"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { 
  Gift,
  DollarSign,
  Truck,
  Percent,
  CreditCard,
  Banknote,
  Smartphone,
  MapPin,
  AlertCircle,
  CheckCircle,
  Crown,
  Zap,
  Star,
  Sparkles,
  Calendar,
  Globe,
  Bitcoin,
  Wallet,
  Clock
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

interface AdvancedRedemptionProps {
  userId: string;
}

interface PremiumReward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  cashValue: number;
  category: 'discount' | 'cashback' | 'premium' | 'exclusive' | 'crypto' | 'experience';
  icon: any;
  color: string;
  available: boolean;
  popularity: number;
  timeLimit?: string;
  exclusiveToTier?: string;
  estimatedDelivery?: string;
}

const premiumRewards: PremiumReward[] = [
  {
    id: '1',
    name: 'Instant Cash Withdrawal',
    description: 'Withdraw cash instantly to your bank account',
    pointsCost: 1000,
    cashValue: 100,
    category: 'cashback',
    icon: Banknote,
    color: 'bg-green-500',
    available: true,
    popularity: 95,
    estimatedDelivery: 'Instant'
  },
  {
    id: '2',
    name: 'Premium Delivery Package',
    description: 'Free premium delivery for 3 months',
    pointsCost: 1500,
    cashValue: 150,
    category: 'premium',
    icon: Truck,
    color: 'bg-blue-500',
    available: true,
    popularity: 87,
    estimatedDelivery: 'Immediate activation'
  },
  {
    id: '3',
    name: 'Cryptocurrency Reward',
    description: 'Receive Bitcoin equivalent of your points',
    pointsCost: 2000,
    cashValue: 200,
    category: 'crypto',
    icon: Bitcoin,
    color: 'bg-orange-500',
    available: true,
    popularity: 72,
    estimatedDelivery: '24 hours'
  },
  {
    id: '4',
    name: 'VIP Shopping Experience',
    description: 'Personal shopper service + exclusive deals',
    pointsCost: 5000,
    cashValue: 500,
    category: 'experience',
    icon: Crown,
    color: 'bg-purple-500',
    available: true,
    popularity: 89,
    exclusiveToTier: 'Diamond',
    estimatedDelivery: '48 hours'
  },
  {
    id: '5',
    name: 'Smart Home Bundle',
    description: 'Premium smart home devices package',
    pointsCost: 8000,
    cashValue: 800,
    category: 'exclusive',
    icon: Smartphone,
    color: 'bg-indigo-500',
    available: true,
    popularity: 78,
    exclusiveToTier: 'Platinum',
    timeLimit: '7 days left'
  },
  {
    id: '6',
    name: 'Travel Voucher',
    description: 'R1000 travel voucher for any destination',
    pointsCost: 10000,
    cashValue: 1000,
    category: 'experience',
    icon: Globe,
    color: 'bg-teal-500',
    available: true,
    popularity: 91,
    exclusiveToTier: 'Diamond',
    estimatedDelivery: '1-2 business days'
  }
];

export function AdvancedRedemption({ userId }: AdvancedRedemptionProps) {
  const [availablePoints, setAvailablePoints] = useState(12450);
  const [selectedReward, setSelectedReward] = useState<PremiumReward | null>(null);
  const [showRedemptionDialog, setShowRedemptionDialog] = useState(false);
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [filter, setFilter] = useState('all');
  const [userTier, setUserTier] = useState('Diamond');

  const filteredRewards = premiumRewards.filter(reward => {
    if (filter === 'all') return true;
    if (filter === 'available') return reward.available && availablePoints >= reward.pointsCost;
    if (filter === 'exclusive') return reward.exclusiveToTier;
    return reward.category === filter;
  });

  const handleRedeem = async (reward: PremiumReward) => {
    if (availablePoints < reward.pointsCost) {
      toast.error('Insufficient points for this reward');
      return;
    }

    setSelectedReward(reward);
    setShowRedemptionDialog(true);
  };

  const confirmRedemption = async () => {
    if (!selectedReward) return;

    setIsRedeeming(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setAvailablePoints(prev => prev - selectedReward.pointsCost);
      toast.success(`Successfully redeemed ${selectedReward.name}!`);
      setShowRedemptionDialog(false);
      setSelectedReward(null);
    } catch (error) {
      toast.error('Failed to redeem reward');
    } finally {
      setIsRedeeming(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'discount': return Percent;
      case 'cashback': return DollarSign;
      case 'premium': return Star;
      case 'exclusive': return Crown;
      case 'crypto': return Bitcoin;
      case 'experience': return Sparkles;
      default: return Gift;
    }
  };

  return (
    <div className="space-y-6">
      {/* Points Balance & Quick Stats */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Wallet className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">Premium Rewards</h3>
              <p className="text-sm text-gray-600 font-normal">
                Exclusive rewards for premium members
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-purple-600 mb-1">
                {availablePoints.toLocaleString()}
              </div>
              <p className="text-sm text-gray-600">Available Points</p>
              <p className="text-xs text-gray-500">≈ R{Math.floor(availablePoints / 10)}</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Crown className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-lg font-bold text-gray-900">{userTier}</p>
              <p className="text-sm text-gray-600">Current Tier</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Gift className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-lg font-bold text-gray-900">{filteredRewards.length}</p>
              <p className="text-sm text-gray-600">Available Rewards</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Zap className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <p className="text-lg font-bold text-gray-900">3</p>
              <p className="text-sm text-gray-600">Exclusive Offers</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-3">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              All Rewards
            </Button>
            <Button
              variant={filter === 'available' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('available')}
            >
              Available Now
            </Button>
            <Button
              variant={filter === 'exclusive' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('exclusive')}
            >
              Exclusive
            </Button>
            <Button
              variant={filter === 'cashback' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('cashback')}
            >
              Cash & Crypto
            </Button>
            <Button
              variant={filter === 'experience' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('experience')}
            >
              Experiences
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Rewards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredRewards.map((reward, index) => {
            const IconComponent = reward.icon;
            const CategoryIcon = getCategoryIcon(reward.category);
            const canAfford = availablePoints >= reward.pointsCost;
            const isExclusive = reward.exclusiveToTier && reward.exclusiveToTier !== userTier;
            
            return (
              <motion.div
                key={reward.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
                className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 ${
                  canAfford && !isExclusive
                    ? 'border-purple-200 hover:border-purple-400 hover:shadow-lg'
                    : 'border-gray-200 opacity-75'
                }`}
              >
                <Card className="h-full border-0">
                  <CardContent className="p-6">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className={`p-3 rounded-xl ${reward.color}`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex flex-col gap-1">
                        {reward.timeLimit && (
                          <Badge className="bg-red-100 text-red-800 text-xs">
                            <Clock className="h-3 w-3 mr-1" />
                            {reward.timeLimit}
                          </Badge>
                        )}
                        {reward.exclusiveToTier && (
                          <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                            <Crown className="h-3 w-3 mr-1" />
                            {reward.exclusiveToTier} Only
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Content */}
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {reward.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3">
                        {reward.description}
                      </p>
                      
                      {reward.estimatedDelivery && (
                        <div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
                          <Clock className="h-3 w-3" />
                          {reward.estimatedDelivery}
                        </div>
                      )}
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-2xl font-bold text-gray-900">
                          {reward.pointsCost.toLocaleString()}
                        </span>
                        <span className="text-sm text-gray-500">
                          ≈ R{reward.cashValue}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">points required</div>
                    </div>

                    {/* Popularity */}
                    <div className="mb-4">
                      <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                        <span>Popularity</span>
                        <span>{reward.popularity}%</span>
                      </div>
                      <Progress value={reward.popularity} className="h-1" />
                    </div>

                    {/* Action Button */}
                    <Button
                      onClick={() => handleRedeem(reward)}
                      disabled={!canAfford || isExclusive}
                      className={`w-full ${
                        canAfford && !isExclusive
                          ? `${reward.color} hover:opacity-90 text-white`
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      {isExclusive ? (
                        <>
                          <Crown className="h-4 w-4 mr-2" />
                          Tier Locked
                        </>
                      ) : !canAfford ? (
                        <>
                          <AlertCircle className="h-4 w-4 mr-2" />
                          Insufficient Points
                        </>
                      ) : (
                        <>
                          <Gift className="h-4 w-4 mr-2" />
                          Redeem Now
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                {/* Exclusive Overlay */}
                {isExclusive && (
                  <div className="absolute inset-0 bg-black/10 flex items-center justify-center">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 text-center">
                      <Crown className="h-6 w-6 text-yellow-600 mx-auto mb-1" />
                      <p className="text-xs font-medium text-gray-900">
                        {reward.exclusiveToTier} Tier Required
                      </p>
                    </div>
                  </div>
                )}
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Redemption Confirmation Dialog */}
      <Dialog open={showRedemptionDialog} onOpenChange={setShowRedemptionDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5" />
              Confirm Redemption
            </DialogTitle>
            <DialogDescription>
              You're about to redeem the following reward:
            </DialogDescription>
          </DialogHeader>
          
          {selectedReward && (
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                <div className={`p-2 rounded-lg ${selectedReward.color}`}>
                  <selectedReward.icon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{selectedReward.name}</h4>
                  <p className="text-sm text-gray-600">{selectedReward.description}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Points Required:</span>
                  <span className="font-medium">{selectedReward.pointsCost.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Cash Value:</span>
                  <span className="font-medium">R{selectedReward.cashValue}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Remaining Points:</span>
                  <span className="font-medium">
                    {(availablePoints - selectedReward.pointsCost).toLocaleString()}
                  </span>
                </div>
                {selectedReward.estimatedDelivery && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Delivery:</span>
                    <span className="font-medium">{selectedReward.estimatedDelivery}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRedemptionDialog(false)}>
              Cancel
            </Button>
            <Button onClick={confirmRedemption} disabled={isRedeeming}>
              {isRedeeming ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Confirm Redemption
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
