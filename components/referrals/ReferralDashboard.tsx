"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  Users, 
  Gift, 
  TrendingUp, 
  Share2,
  Copy,
  DollarSign,
  Award,
  Target,
  Clock
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { ReferralStats } from "./ReferralStats";
import { SocialShareButtons } from "./SocialShareButtons";
import { LoyaltyPointsDisplay } from "./LoyaltyPointsDisplay";
import { PointsRedemption } from "./PointsRedemption";

interface ReferralDashboardProps {
  userId: string;
}

interface UserData {
  referralCode: string;
  name: string;
}

interface ReferralStatsData {
  totalReferrals: number;
  successfulReferrals: number;
  pendingReferrals: number;
  totalEarnings: number;
  totalPoints: number;
  conversionRate: number;
  referralHistory: Array<{
    refereeId: string;
    refereeName: string;
    status: string;
    joinedAt: string;
    firstPurchaseAt?: string;
    earnedPoints: number;
    earnedAmount: number;
  }>;
}

export function ReferralDashboard({ userId }: ReferralDashboardProps) {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [referralStats, setReferralStats] = useState<ReferralStatsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  console.log('ReferralDashboard rendered with userId:', userId);

  useEffect(() => {
    if (userId) {
      fetchUserData();
      fetchReferralStats();
    }
  }, [userId]);

  const fetchUserData = async () => {
    try {
      console.log('Fetching user data...');
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('No token found in localStorage');
        toast.error('Please log in to access referral dashboard');
        return;
      }

      console.log('Making request to /api/auth/me with token:', token.substring(0, 20) + '...');
      const response = await fetch(`/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('User data received:', data);
        let referralCode = data.user?.referralCode;

        // If user doesn't have a referral code, generate one
        if (!referralCode) {
          console.log('No referral code found, generating one...');
          referralCode = await generateReferralCodeForUser(token);
        }

        setUserData({
          referralCode: referralCode || 'GENERATING...',
          name: data.user?.name || 'User'
        });
        console.log('User data set:', { referralCode, name: data.user?.name });
      } else {
        const errorData = await response.json();
        console.error('Failed to fetch user data:', response.status, errorData);
        toast.error(`Failed to load user data: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.error(`Failed to load user data: ${error instanceof Error ? error.message : 'Network error'}`);
    }
  };

  const generateReferralCodeForUser = async (token: string): Promise<string | null> => {
    try {
      const response = await fetch('/api/referrals?action=generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          programId: '000000000000000000000001' // Default program ID
        })
      });

      if (response.ok) {
        const data = await response.json();
        return data.referralCode;
      }
    } catch (error) {
      console.error('Error generating referral code:', error);
    }
    return null;
  };

  const fetchReferralStats = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.warn('No token available for fetching referral stats');
        return;
      }

      const response = await fetch(`/api/referrals/user/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.stats) {
          setReferralStats(data.stats);
        } else {
          // Initialize with empty stats if no data
          setReferralStats({
            totalReferrals: 0,
            successfulReferrals: 0,
            pendingReferrals: 0,
            totalEarnings: 0,
            totalPoints: 0,
            conversionRate: 0,
            referralHistory: []
          });
        }
      } else {
        const errorData = await response.json();
        console.error('Failed to fetch referral stats:', errorData);
        // Initialize with empty stats on error
        setReferralStats({
          totalReferrals: 0,
          successfulReferrals: 0,
          pendingReferrals: 0,
          totalEarnings: 0,
          totalPoints: 0,
          conversionRate: 0,
          referralHistory: []
        });
      }
    } catch (error) {
      console.error('Error fetching referral stats:', error);
      // Initialize with empty stats on error
      setReferralStats({
        totalReferrals: 0,
        successfulReferrals: 0,
        pendingReferrals: 0,
        totalEarnings: 0,
        totalPoints: 0,
        conversionRate: 0,
        referralHistory: []
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generateReferralLink = () => {
    if (!userData?.referralCode) return '';
    const baseUrl = window.location.origin;
    return `${baseUrl}/signup?ref=${userData.referralCode}`;
  };

  const copyReferralLink = async () => {
    const link = generateReferralLink();
    try {
      await navigator.clipboard.writeText(link);
      toast.success('Referral link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  const copyReferralCode = async () => {
    if (!userData?.referralCode) return;
    try {
      await navigator.clipboard.writeText(userData.referralCode);
      toast.success('Referral code copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy code');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-3 text-gray-600">Loading referral dashboard...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Gift className="h-8 w-8 text-blue-600" />
            Referral Program
          </h1>
          <p className="text-gray-600 mt-1">
            Invite friends and earn rewards together
          </p>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Referrals</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {referralStats?.totalReferrals || 0}
                  </p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Successful</p>
                  <p className="text-2xl font-bold text-green-600">
                    {referralStats?.successfulReferrals || 0}
                  </p>
                </div>
                <Target className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Points</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {referralStats?.totalPoints || 0}
                  </p>
                </div>
                <Award className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {referralStats?.conversionRate?.toFixed(1) || 0}%
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Referral Code Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Your Referral Code
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                <code className="text-lg font-mono font-bold text-blue-600">
                  {userData?.referralCode || 'Loading...'}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyReferralCode}
                  className="ml-auto"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={copyReferralLink} variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="share">Share & Invite</TabsTrigger>
          <TabsTrigger value="points">Loyalty Points</TabsTrigger>
          <TabsTrigger value="redeem">Redeem Rewards</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <ReferralStats 
            stats={referralStats} 
            onRefresh={fetchReferralStats}
          />
        </TabsContent>

        <TabsContent value="share" className="space-y-4">
          <SocialShareButtons 
            referralCode={userData?.referralCode || ''}
            userName={userData?.name || ''}
          />
        </TabsContent>

        <TabsContent value="points" className="space-y-4">
          <LoyaltyPointsDisplay userId={userId} />
        </TabsContent>

        <TabsContent value="redeem" className="space-y-4">
          <PointsRedemption userId={userId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
