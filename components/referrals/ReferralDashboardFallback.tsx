"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  Gift, 
  TrendingUp, 
  Share2,
  Copy,
  DollarSign,
  Award,
  Target,
  Clock,
  Crown,
  Zap,
  BarChart3,
  Globe,
  Sparkles,
  Trophy,
  Rocket,
  Brain,
  Calendar,
  Star
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "sonner";

interface ReferralDashboardFallbackProps {
  userId: string;
}

export function ReferralDashboardFallback({ userId }: ReferralDashboardFallbackProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState({
    referralCode: 'PREMIUM123',
    name: 'Premium User',
    tier: 'Diamond',
    tierProgress: 75,
    lifetimeEarnings: 15420.50,
    currentStreak: 12
  });

  const [stats, setStats] = useState({
    totalReferrals: 247,
    activeReferrals: 189,
    conversionRate: 23.5,
    totalEarnings: 15420.50,
    monthlyGrowth: 18.2
  });

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setIsLoading(false);
      toast.success('Referral dashboard loaded successfully!');
    }, 1500);
  }, [userId]);

  const copyReferralCode = async () => {
    try {
      await navigator.clipboard.writeText(userData.referralCode);
      toast.success('Referral code copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy code');
    }
  };

  const generateReferralLink = () => {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    return `${baseUrl}/signup?ref=${userData.referralCode}`;
  };

  const copyReferralLink = async () => {
    try {
      const link = generateReferralLink();
      await navigator.clipboard.writeText(link);
      toast.success('Referral link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="relative">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
          <Sparkles className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-6 w-6 text-purple-600" />
        </div>
        <p className="ml-4 text-gray-600 font-medium">Loading Premium Dashboard...</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Premium Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 p-8 text-white"
      >
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="p-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500">
                  <Crown className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Premium Referral Hub</h1>
                  <p className="text-purple-100">Advanced analytics & AI-powered insights</p>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 mb-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  {userData.tier} Tier
                </Badge>
                <Badge className="bg-yellow-500/20 text-yellow-200 border-yellow-400/30">
                  <Crown className="h-3 w-3 mr-1" />
                  Premium
                </Badge>
              </div>
              <p className="text-2xl font-bold">R{userData.lifetimeEarnings.toLocaleString()}</p>
              <p className="text-purple-200 text-sm">Lifetime Earnings</p>
            </div>
          </div>

          {/* Tier Progress */}
          <div className="mt-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Progress to Next Tier</span>
              <span className="text-sm">{userData.tierProgress}% Complete</span>
            </div>
            <Progress 
              value={userData.tierProgress} 
              className="h-2 bg-white/20"
            />
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: "Total Referrals",
            value: stats.totalReferrals,
            change: "+12%",
            icon: Users,
            color: "text-blue-600",
            bgColor: "bg-blue-50"
          },
          {
            title: "Active Referrals",
            value: stats.activeReferrals,
            change: "+8%",
            icon: Target,
            color: "text-green-600",
            bgColor: "bg-green-50"
          },
          {
            title: "Conversion Rate",
            value: `${stats.conversionRate}%`,
            change: "+5.2%",
            icon: TrendingUp,
            color: "text-purple-600",
            bgColor: "bg-purple-50"
          },
          {
            title: "Monthly Growth",
            value: `${stats.monthlyGrowth}%`,
            change: "+15.3%",
            icon: Rocket,
            color: "text-orange-600",
            bgColor: "bg-orange-50"
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-md">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs text-green-600 font-medium">{stat.change} vs last month</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Referral Code Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Your Premium Referral Code
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                <code className="text-xl font-mono font-bold text-purple-600">
                  {userData.referralCode}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyReferralCode}
                  className="ml-auto"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={copyReferralLink} className="bg-purple-600 hover:bg-purple-700">
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
              <Button variant="outline">
                <Share2 className="h-4 w-4 mr-2" />
                Share Now
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-all cursor-pointer">
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">Analytics</h3>
            <p className="text-sm text-gray-600">View detailed performance</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all cursor-pointer">
          <CardContent className="p-6 text-center">
            <Brain className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">AI Insights</h3>
            <p className="text-sm text-gray-600">Smart recommendations</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all cursor-pointer">
          <CardContent className="p-6 text-center">
            <Trophy className="h-8 w-8 text-yellow-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">Leaderboard</h3>
            <p className="text-sm text-gray-600">See your ranking</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all cursor-pointer">
          <CardContent className="p-6 text-center">
            <Gift className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">Rewards</h3>
            <p className="text-sm text-gray-600">Redeem your points</p>
          </CardContent>
        </Card>
      </div>

      {/* Feature Notice */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <Sparkles className="h-6 w-6 text-purple-600" />
            <div>
              <h3 className="font-semibold text-gray-900 mb-1">Premium Features Loading</h3>
              <p className="text-sm text-gray-600">
                Advanced analytics, AI insights, and automation features are being loaded. 
                This lightweight version ensures you can access your referral code immediately.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
