"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  Lightbulb, 
  Target, 
  Clock, 
  Users, 
  MessageSquare,
  TrendingUp,
  Zap,
  CheckCircle,
  AlertTriangle,
  Info,
  Sparkles,
  Calendar,
  Globe,
  Share2
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

interface SmartRecommendationsProps {
  userId: string;
}

interface Recommendation {
  id: string;
  type: 'audience' | 'timing' | 'content' | 'platform' | 'strategy';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number;
  actionable: boolean;
  estimatedIncrease: string;
  timeToImplement: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
}

const mockRecommendations: Recommendation[] = [
  {
    id: '1',
    type: 'timing',
    title: 'Optimize Sharing Times',
    description: 'Your referrals perform 34% better when shared between 7-9 PM on weekdays. Consider scheduling your shares during these peak engagement hours.',
    impact: 'high',
    confidence: 92,
    actionable: true,
    estimatedIncrease: '+34% conversions',
    timeToImplement: '5 minutes',
    difficulty: 'easy',
    category: 'Timing Optimization'
  },
  {
    id: '2',
    type: 'platform',
    title: 'Focus on WhatsApp',
    description: 'WhatsApp generates 3x higher conversion rates than other platforms for your audience. Increase your WhatsApp sharing by 40%.',
    impact: 'high',
    confidence: 88,
    actionable: true,
    estimatedIncrease: '+28% revenue',
    timeToImplement: '10 minutes',
    difficulty: 'easy',
    category: 'Platform Strategy'
  },
  {
    id: '3',
    type: 'content',
    title: 'Personalize Your Message',
    description: 'Adding personal stories increases click-through rates by 45%. Try mentioning specific products you love or savings you\'ve achieved.',
    impact: 'medium',
    confidence: 85,
    actionable: true,
    estimatedIncrease: '+45% CTR',
    timeToImplement: '15 minutes',
    difficulty: 'medium',
    category: 'Content Enhancement'
  },
  {
    id: '4',
    type: 'audience',
    title: 'Target Young Professionals',
    description: 'Users aged 25-35 in urban areas show 60% higher lifetime value. Consider targeting this demographic more actively.',
    impact: 'high',
    confidence: 90,
    actionable: true,
    estimatedIncrease: '+60% LTV',
    timeToImplement: '20 minutes',
    difficulty: 'medium',
    category: 'Audience Targeting'
  },
  {
    id: '5',
    type: 'strategy',
    title: 'Follow-up Strategy',
    description: 'Sending a follow-up message 3 days after initial share increases conversion by 25%. Implement automated follow-ups.',
    impact: 'medium',
    confidence: 82,
    actionable: true,
    estimatedIncrease: '+25% conversions',
    timeToImplement: '30 minutes',
    difficulty: 'hard',
    category: 'Engagement Strategy'
  }
];

export function SmartRecommendations({ userId }: SmartRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [implementedIds, setImplementedIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate AI analysis
    setTimeout(() => {
      setRecommendations(mockRecommendations);
      setIsLoading(false);
    }, 2000);
  }, [userId]);

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'hard': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'timing': return Clock;
      case 'platform': return Share2;
      case 'content': return MessageSquare;
      case 'audience': return Users;
      case 'strategy': return Target;
      default: return Lightbulb;
    }
  };

  const implementRecommendation = (id: string) => {
    setImplementedIds(prev => [...prev, id]);
    toast.success('Recommendation implemented! Track your results in analytics.');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="relative mb-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <Brain className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-6 w-6 text-purple-600" />
          </div>
          <p className="text-gray-600 font-medium">AI is analyzing your referral patterns...</p>
          <p className="text-sm text-gray-500">This may take a few moments</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* AI Insights Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">AI-Powered Insights</h3>
              <p className="text-sm text-gray-600 font-normal">
                Personalized recommendations based on your performance data
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Sparkles className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">{recommendations.length}</p>
              <p className="text-sm text-gray-600">Active Recommendations</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">+47%</p>
              <p className="text-sm text-gray-600">Potential Revenue Increase</p>
            </div>
            <div className="text-center p-4 bg-white rounded-lg shadow-sm">
              <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-gray-900">87%</p>
              <p className="text-sm text-gray-600">Average Confidence</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations List */}
      <div className="space-y-4">
        <AnimatePresence>
          {recommendations.map((rec, index) => {
            const TypeIcon = getTypeIcon(rec.type);
            const isImplemented = implementedIds.includes(rec.id);
            
            return (
              <motion.div
                key={rec.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className={`hover:shadow-lg transition-all duration-300 ${
                  isImplemented ? 'bg-green-50 border-green-200' : 'hover:border-purple-200'
                }`}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className={`p-3 rounded-lg ${
                        isImplemented ? 'bg-green-100' : 'bg-purple-100'
                      }`}>
                        {isImplemented ? (
                          <CheckCircle className="h-6 w-6 text-green-600" />
                        ) : (
                          <TypeIcon className="h-6 w-6 text-purple-600" />
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-1">
                              {rec.title}
                            </h3>
                            <Badge variant="outline" className="text-xs">
                              {rec.category}
                            </Badge>
                          </div>
                          
                          <div className="flex gap-2">
                            <Badge className={getImpactColor(rec.impact)}>
                              {rec.impact.toUpperCase()} IMPACT
                            </Badge>
                            <Badge className={getDifficultyColor(rec.difficulty)}>
                              {rec.difficulty.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                        
                        <p className="text-gray-600 mb-4">{rec.description}</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="flex items-center gap-2">
                            <TrendingUp className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium text-green-600">
                              {rec.estimatedIncrease}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-blue-600" />
                            <span className="text-sm text-gray-600">
                              {rec.timeToImplement}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4 text-purple-600" />
                            <span className="text-sm text-gray-600">
                              {rec.confidence}% confidence
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">Confidence:</span>
                            <Progress value={rec.confidence} className="w-24 h-2" />
                            <span className="text-sm font-medium">{rec.confidence}%</span>
                          </div>
                          
                          {!isImplemented ? (
                            <Button 
                              onClick={() => implementRecommendation(rec.id)}
                              className="bg-purple-600 hover:bg-purple-700"
                            >
                              <Zap className="h-4 w-4 mr-2" />
                              Implement
                            </Button>
                          ) : (
                            <Badge className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Implemented
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* AI Learning Notice */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <Info className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-blue-900">
                AI Continuous Learning
              </p>
              <p className="text-xs text-blue-700">
                Our AI analyzes your performance daily and updates recommendations based on new data and market trends.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
