"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Gift, Users, Award, Copy } from "lucide-react";
import { toast } from "sonner";

interface ReferralDashboardSimpleProps {
  userId: string;
}

export function ReferralDashboardSimple({ userId }: ReferralDashboardSimpleProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState<{ referralCode: string; name: string } | null>(null);

  useEffect(() => {
    console.log('ReferralDashboardSimple mounted with userId:', userId);
    
    // Simulate loading
    setTimeout(() => {
      setUserData({
        referralCode: 'ABC123XYZ',
        name: 'Test User'
      });
      setIsLoading(false);
      toast.success('Referral dashboard loaded successfully!');
    }, 2000);
  }, [userId]);

  const copyReferralCode = async () => {
    if (!userData?.referralCode) return;
    try {
      await navigator.clipboard.writeText(userData.referralCode);
      toast.success('Referral code copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy code');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-3 text-gray-600">Loading referral dashboard...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Gift className="h-8 w-8 text-blue-600" />
            Referral Program (Test)
          </h1>
          <p className="text-gray-600 mt-1">
            Invite friends and earn rewards together
          </p>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Referrals</p>
                <p className="text-2xl font-bold text-gray-900">0</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Successful</p>
                <p className="text-2xl font-bold text-green-600">0</p>
              </div>
              <Award className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Points</p>
                <p className="text-2xl font-bold text-purple-600">0</p>
              </div>
              <Award className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Referral Code Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Your Referral Code
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                <code className="text-lg font-mono font-bold text-blue-600">
                  {userData?.referralCode || 'Loading...'}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyReferralCode}
                  className="ml-auto"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>User ID:</strong> {userId}</p>
            <p><strong>User Name:</strong> {userData?.name}</p>
            <p><strong>Referral Code:</strong> {userData?.referralCode}</p>
            <p><strong>Status:</strong> Dashboard loaded successfully</p>
          </div>
        </CardContent>
      </Card>

      {/* Test Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button onClick={() => toast.success('Success toast test!')}>
              Test Success Toast
            </Button>
            <Button onClick={() => toast.error('Error toast test!')} variant="destructive">
              Test Error Toast
            </Button>
            <Button onClick={() => toast.info('Info toast test!')} variant="outline">
              Test Info Toast
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
