'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>Axis, 
  <PERSON>Axis, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useGetGroupOrdersQuery } from '@/lib/redux/features/cart/cartApiSlice';
import { Skeleton } from '@/components/ui/skeleton';

interface GroupOrderAnalyticsProps {
  groupId: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export function GroupOrderReduxAnalytics({ groupId }: GroupOrderAnalyticsProps) {
  const { data: orders, isLoading, error } = useGetGroupOrdersQuery(groupId);
  const [activeTab, setActiveTab] = useState('overview');
  
  const [analytics, setAnalytics] = useState({
    totalOrders: 0,
    totalValue: 0,
    averageOrderValue: 0,
    monthlyTrends: [] as Array<{
      month: string;
      orders: number;
      value: number;
    }>,
    topContributors: [] as Array<{
      userId: string;
      userName: string;
      totalSpent: number;
      percentage: number;
    }>,
    statusDistribution: [] as Array<{
      status: string;
      count: number;
    }>
  });
  
  useEffect(() => {
    if (orders && orders.length > 0) {
      // Calculate basic metrics
      const totalOrders = orders.length;
      const totalValue = orders.reduce((sum, order) => sum + order.totalOrderValue, 0);
      const averageOrderValue = totalValue / totalOrders;
      
      // Calculate monthly trends
      const monthlyData = orders.reduce((acc, order) => {
        const date = new Date(order.orderPlacedAt);
        const monthYear = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
        
        if (!acc[monthYear]) {
          acc[monthYear] = { orders: 0, value: 0 };
        }
        
        acc[monthYear].orders += 1;
        acc[monthYear].value += order.totalOrderValue;
        
        return acc;
      }, {} as Record<string, { orders: number; value: number }>);
      
      const monthlyTrends = Object.entries(monthlyData).map(([month, data]) => ({
        month,
        orders: data.orders,
        value: data.value
      }));
      
      // Calculate top contributors
      const contributorsMap = new Map<string, { userName: string; totalSpent: number }>();
      
      orders.forEach(order => {
        order.userContributions.forEach(contribution => {
          const existing = contributorsMap.get(contribution.userId);
          
          if (existing) {
            existing.totalSpent += contribution.totalSpent;
          } else {
            contributorsMap.set(contribution.userId, {
              userName: contribution.userName,
              totalSpent: contribution.totalSpent
            });
          }
        });
      });
      
      const topContributors = Array.from(contributorsMap.entries())
        .map(([userId, data]) => ({
          userId,
          userName: data.userName,
          totalSpent: data.totalSpent,
          percentage: (data.totalSpent / totalValue) * 100
        }))
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 5);
      
      // Calculate status distribution
      const statusCounts = orders.reduce((acc, order) => {
        const status = order.status;
        
        if (!acc[status]) {
          acc[status] = 0;
        }
        
        acc[status] += 1;
        
        return acc;
      }, {} as Record<string, number>);
      
      const statusDistribution = Object.entries(statusCounts).map(([status, count]) => ({
        status,
        count
      }));
      
      setAnalytics({
        totalOrders,
        totalValue,
        averageOrderValue,
        monthlyTrends,
        topContributors,
        statusDistribution
      });
    }
  }, [orders]);
  
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-1/3" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }
  
  if (error) {
    return <div className="text-red-500">Error loading analytics data</div>;
  }
  
  if (!orders || orders.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium">No order data available</h3>
        <p className="text-muted-foreground">Place some orders to see analytics</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
          <TabsTrigger value="contributors">Top Contributors</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalOrders}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">R {analytics.totalValue.toFixed(2)}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Average Order</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">R {analytics.averageOrderValue.toFixed(2)}</div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Order Status Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={analytics.statusDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="status"
                    >
                      {analytics.statusDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="trends">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Order Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={analytics.monthlyTrends}>
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="orders" name="Number of Orders" fill="#8884d8" />
                    <Bar yAxisId="right" dataKey="value" name="Order Value (R)" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="contributors">
          <Card>
            <CardHeader>
              <CardTitle>Top Contributors</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Total Spent</TableHead>
                    <TableHead>Percentage</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics.topContributors.map((contributor) => (
                    <TableRow key={contributor.userId}>
                      <TableCell>{contributor.userName}</TableCell>
                      <TableCell>R {contributor.totalSpent.toFixed(2)}</TableCell>
                      <TableCell>{contributor.percentage.toFixed(2)}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
