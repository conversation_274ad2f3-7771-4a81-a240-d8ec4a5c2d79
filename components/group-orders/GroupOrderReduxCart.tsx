'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Minus, Plus, X, ShoppingBag, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useGetShoppingCartQuery, useUpdateCartItemMutation, useRemoveFromCartMutation, useClearCartMutation } from '@/lib/redux/features/cart/cartApiSlice'
import { useAppSelector } from '@/lib/redux/hooks'
import { selectCartItems, selectTotalItems, selectSubtotal } from '@/lib/redux/features/cart/cartSlice'
import { useAuth } from '@/context/AuthContext'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'

interface GroupOrderReduxCartProps {
  groupId: string
}

export function GroupOrderReduxCart({ groupId }: GroupOrderReduxCartProps) {
  const { user } = useAuth()
  const userId = user?._id || ''
  
  // Local state
  const [isConfirmingClear, setIsConfirmingClear] = useState(false)
  const [isItemLoading, setIsItemLoading] = useState(false)
  
  // Redux selectors
  const cartItems = useAppSelector(selectCartItems)
  const totalItems = useAppSelector(selectTotalItems)
  const subtotal = useAppSelector(selectSubtotal)
  
  // RTK Query hooks - don't create new queries, rely on CartProvider
  const { refetch, isLoading } = useGetShoppingCartQuery({
    userId,
    groupId
  }, {
    skip: !userId,
    // Don't refetch automatically, rely on CartProvider for data fetching
    refetchOnMountOrArgChange: false,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  })
  
  const [updateCartItem] = useUpdateCartItemMutation()
  const [removeFromCart] = useRemoveFromCartMutation()
  const [clearCart] = useClearCartMutation()
  
  // Handle updating item quantity
  const handleUpdateQuantity = async (productId: string, currentQuantity: number, increment: boolean) => {
    if (!userId) return
    
    setIsItemLoading(true)
    try {
      const newQuantity = increment ? currentQuantity + 1 : currentQuantity - 1
      
      if (newQuantity <= 0) {
        await handleRemoveItem(productId)
        return
      }
      
      await updateCartItem({
        userId,
        productId,
        quantity: newQuantity,
        groupId
      }).unwrap()
      
      await refetch()
    } catch (error) {
      console.error('Failed to update item quantity:', error)
    } finally {
      setIsItemLoading(false)
    }
  }
  
  // Handle removing an item
  const handleRemoveItem = async (productId: string) => {
    if (!userId) return
    
    setIsItemLoading(true)
    try {
      await removeFromCart({
        userId,
        productId,
        groupId
      }).unwrap()
      
      await refetch()
    } catch (error) {
      console.error('Failed to remove item:', error)
    } finally {
      setIsItemLoading(false)
    }
  }
  
  // Handle clearing the cart
  const handleClearCart = async () => {
    if (!userId) return
    
    try {
      await clearCart({
        userId,
        groupId
      }).unwrap()
      
      setIsConfirmingClear(false)
      await refetch()
    } catch (error) {
      console.error('Failed to clear cart:', error)
    }
  }
  
  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2A7C6C]"></div>
      </div>
    )
  }
  
  // Empty cart state
  if (totalItems === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="bg-purple-50 p-4 rounded-full mb-4">
          <ShoppingBag className="h-12 w-12 text-purple-500" />
        </div>
        <h3 className="text-xl font-medium mb-2">Your cart is empty</h3>
        <p className="text-gray-500 mb-6">Add items to your cart to start a group order</p>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Your Group Cart</h2>
      
      <div className="space-y-4">
        {cartItems.map((item) => (
          <div key={item._id} className="flex items-center gap-4 border-b pb-4">
            <div className="h-20 w-20 relative rounded-md overflow-hidden bg-gray-100">
              <Image
                src={item.image ? `/api/images/${item.image}` : '/placeholder.svg'}
                alt={item.name || 'Product'}
                width={80}
                height={80}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to placeholder if image fails to load
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
            </div>

            <div className="flex-grow">
              <h3 className="font-medium text-gray-900">{item.name || 'Unknown Product'}</h3>
              <p className="text-sm text-gray-500">
                R {typeof item.price === 'number' ? item.price.toFixed(2) : '0.00'}
              </p>
            </div>

            <div className="flex items-center gap-3">
              <Button
                size="icon"
                variant="outline"
                className="h-8 w-8 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white"
                onClick={() => handleUpdateQuantity(item.productId, item.quantity, false)}
                disabled={isItemLoading || item.quantity <= 1}
              >
                <Minus className="h-4 w-4" />
              </Button>

              <span className="w-8 text-center font-medium">{item.quantity}</span>

              <Button
                size="icon"
                variant="outline"
                className="h-8 w-8 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white"
                onClick={() => handleUpdateQuantity(item.productId, item.quantity, true)}
                disabled={isItemLoading}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="text-right w-24">
              <p className="font-medium">
                R {item.subtotal.toFixed(2)}
              </p>
            </div>

            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8 text-gray-400 hover:bg-red-50 hover:text-red-500"
              onClick={() => handleRemoveItem(item.productId)}
              disabled={isItemLoading}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      <div className="border-t pt-4">
        <div className="flex justify-between text-lg font-semibold">
          <span>Subtotal</span>
          <span>R {subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between items-center mt-4">
          <p className="text-sm text-gray-500">Shipping and taxes calculated at checkout</p>
          <Button
            variant="outline"
            size="sm"
            className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
            onClick={() => setIsConfirmingClear(true)}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Cart
          </Button>
        </div>
      </div>

      {/* Clear Cart Confirmation Dialog */}
      <Dialog open={isConfirmingClear} onOpenChange={setIsConfirmingClear}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clear Shopping Cart</DialogTitle>
          </DialogHeader>
          <p>
            Are you sure you want to clear your shopping cart? This action cannot be undone.
            All items in your cart will be removed.
          </p>
          <DialogFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setIsConfirmingClear(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleClearCart}
            >
              Clear Cart
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
