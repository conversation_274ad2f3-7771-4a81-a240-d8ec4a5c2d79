'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { GroupCart } from '../cart/GroupCart';
import { GroupOrderStepper } from './GroupOrderStepper';
import { GroupOrderSuccessMessage } from './GroupOrderSuccessMessage';
import { GroupOrderConfirmation } from './GroupOrderConfirmation';
import { useAppSelector } from '@/lib/redux/hooks';
import { selectTotalItems } from '@/lib/redux/features/cart/cartSlice';
import { useAuth } from '@/context/AuthContext';
import { GroupOrderReduxCheckout } from './GroupOrderReduxCheckout';

type CheckoutStep = 'cart' | 'checkout';

const STEPS = ['Cart', 'Checkout'];

interface GroupOrderReduxOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  groupId: string;
}

export function GroupOrderReduxOverlay({
  isOpen,
  onClose,
  groupId,
}: GroupOrderReduxOverlayProps) {
  // State management
  const [step, setStep] = useState<CheckoutStep>('cart');
  const [orderConfirmed, setOrderConfirmed] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Redux state
  const totalItems = useAppSelector(selectTotalItems);

  // Context hooks
  useAuth(); // Keep the hook for potential future use

  // Reset state when overlay is opened
  useEffect(() => {
    if (isOpen) {
      setStep('cart');
      setOrderConfirmed(false);
      setShowSuccess(false);
      setError(null);
    }
  }, [isOpen]);

  // This function will be used in the future when we implement the checkout flow
  // const showSuccessMessage = (duration = 3000) => {
  //   setShowSuccess(true);
  //   setTimeout(() => setShowSuccess(false), duration);
  // };

  const handleProceedToCheckout = () => {
    if (totalItems === 0) {
      setError('Please add items to your cart before proceeding');
      return;
    }
    setStep('checkout');
    setError(null);
  };

  // This function will be used in the future when we implement the checkout flow
  // const handleOrderComplete = () => {
  //   setOrderConfirmed(true);
  //   onCheckoutComplete();
  //   showSuccessMessage(5000);
  // };

  const getCurrentStepIndex = (): number => {
    return step === 'cart' ? 0 : 1;
  };

  const handleClose = () => {
    // Confirm before closing if there are items in cart and order is not confirmed
    if (totalItems > 0 && !orderConfirmed && step !== 'cart') {
      if (window.confirm('Are you sure you want to leave? Your progress will be lost.')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center overflow-auto py-4">
      <div className="bg-white rounded-lg p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
        {/* Close button */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-4 right-4 hover:bg-gray-100"
          onClick={handleClose}
        >
          <X className="h-6 w-6" />
        </Button>

        {/* Progress stepper */}
        <GroupOrderStepper
          steps={STEPS}
          currentStep={getCurrentStepIndex()}
        />

        {/* Success message */}
        {showSuccess && (
          <GroupOrderSuccessMessage
            title={orderConfirmed ? 'Order Placed Successfully' : 'Step Completed'}
            message={
              orderConfirmed
                ? 'Your order has been placed successfully. Thank you for your purchase!'
                : `You've successfully completed the ${STEPS[getCurrentStepIndex() - 1]} step.`
            }
          />
        )}

        {/* Error message */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 text-red-600 rounded-md">
            {error}
          </div>
        )}

        {/* Main content */}
        {orderConfirmed ? (
          <GroupOrderConfirmation onClose={onClose} />
        ) : (
          <>
            {step === 'cart' && (
              <div className="space-y-6">
                <GroupCart
                  groupId={groupId}
                  onCheckout={handleProceedToCheckout}
                />
              </div>
            )}

            {step === 'checkout' && (
              <GroupOrderReduxCheckout
                groupId={groupId}
              />
            )}
          </>
        )}

        {/* Back button (except for cart step) */}
        {step !== 'cart' && !orderConfirmed && (
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => setStep('cart')}
          >
            Back to Cart
          </Button>
        )}
      </div>
    </div>
  );
}
