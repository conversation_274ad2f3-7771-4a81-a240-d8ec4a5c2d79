import { CheckCircle } from "lucide-react"

interface SuccessMessageProps {
  title: string
  message: string
}

export function GroupOrderSuccessMessage({ title, message }: SuccessMessageProps) {
  return (
    <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <CheckCircle className="h-5 w-5 text-green-400" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-green-800">{title}</h3>
          <div className="mt-2 text-sm text-green-700">
            <p>{message}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

