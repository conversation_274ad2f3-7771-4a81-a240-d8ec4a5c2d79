"use client"

import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card"
import { 
  Progress 
} from "@/components/ui/progress"
import { 
  ShoppingBag, 
  TrendingUp, 
  Users, 
  Award 
} from "lucide-react"
import { IGroupOrder } from "@/models/GroupOrder"
import { useState, useEffect } from "react"

interface GroupOrderProgressProps {
  groupOrder: IGroupOrder
}

export function GroupOrderProgress({ groupOrder }: GroupOrderProgressProps) {
  const [progressPercentage, setProgressPercentage] = useState(0)
  const [discountTier, setDiscountTier] = useState<number | null>(null)

  useEffect(() => {
    // Calculate overall progress
    const totalTargetAmount = groupOrder.milestones[groupOrder.milestones.length - 1].targetAmount
    const currentProgress = Math.min(
      (groupOrder.totalOrderValue / totalTargetAmount) * 100, 
      100
    )
    setProgressPercentage(currentProgress)

    // Determine current discount tier
    const applicableTier = groupOrder.bulkDiscountTiers
      .filter(tier => groupOrder.totalOrderValue >= tier.threshold)
      .sort((a, b) => b.threshold - a.threshold)[0]

    setDiscountTier(applicableTier?.discountPercentage || null)
  }, [groupOrder])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Group Order Progress</span>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <ShoppingBag className="h-4 w-4" />
            Total: R{groupOrder.totalOrderValue.toLocaleString()}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm text-muted-foreground">
              {progressPercentage.toFixed(2)}%
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Milestones */}
        <div className="space-y-2">
          <h4 className="text-sm font-semibold flex items-center">
            <TrendingUp className="h-4 w-4 mr-2" /> Milestones
          </h4>
          {groupOrder.milestones.map((milestone, index) => (
            <div 
              key={index} 
              className={`flex justify-between items-center p-2 rounded-md ${
                milestone.isReached ? 'bg-green-50' : 'bg-gray-50'
              }`}
            >
              <span>{milestone.name}</span>
              <span className={milestone.isReached ? 'text-green-600' : 'text-gray-500'}>
                R{milestone.targetAmount.toLocaleString()}
              </span>
            </div>
          ))}
        </div>

        {/* User Contributions */}
        <div>
          <h4 className="text-sm font-semibold flex items-center mb-2">
            <Users className="h-4 w-4 mr-2" /> User Contributions
          </h4>
          {groupOrder.userContributions.map((contrib) => (
            <div 
              key={contrib.userId.toString()} 
              className="flex justify-between items-center p-2 bg-blue-50 rounded-md mb-1"
            >
              <span>{contrib.userName}</span>
              <span className="font-semibold">
                R{contrib.totalSpent.toLocaleString()} 
                ({contrib.contributionPercentage.toFixed(2)}%)
              </span>
            </div>
          ))}
        </div>

        {/* Discount Tier */}
        <div>
          <h4 className="text-sm font-semibold flex items-center">
            <Award className="h-4 w-4 mr-2" /> Bulk Discount
          </h4>
          <div className="flex justify-between items-center p-2 bg-purple-50 rounded-md">
            <span>Current Discount</span>
            <span className="font-semibold text-purple-600">
              {discountTier ? `${discountTier}%` : 'No Discount'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
