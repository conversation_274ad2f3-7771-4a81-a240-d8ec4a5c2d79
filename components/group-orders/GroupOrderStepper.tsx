interface StepperProps {
  steps: string[];
  currentStep: number;
}

export function GroupOrderStepper({ steps, currentStep }: StepperProps) {
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center">
        {steps.map((step, index) => (
          <div key={step} className="flex items-center">
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${
                index <= currentStep
                  ? "bg-[#2A7C6C] text-white"
                  : "border-gray-300 text-gray-300"
              }`}
            >
              <span>{index + 1}</span>
            </div>
            <div className="ml-2 text-sm">{step}</div>
            {index < steps.length - 1 && (
              <div
                className={`h-0.5 w-12 mx-2 ${
                  index < currentStep ? "bg-[#2A7C6C]" : "bg-gray-200"
                }`}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

