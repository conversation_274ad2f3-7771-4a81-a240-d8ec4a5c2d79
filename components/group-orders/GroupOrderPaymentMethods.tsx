// "use client"

// import { useState } from "react"
// import { Button } from "@/components/ui/button"
// import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
// import { Label } from "@/components/ui/label"
// import { CreditCard, ShoppingCartIcon as Paypal, Landmark, Truck } from "lucide-react"



// interface PaymentMethodsProps {
//   onSelect: (method: string) => void
// }

// export function GroupOrderPaymentMethods({ onSelect }: PaymentMethodsProps) {
//   const [selectedMethod, setSelectedMethod] = useState("")

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault()
//     if (selectedMethod) {
//       onSelect(selectedMethod)
//     }
//   }

//   return (
//     <div>
//       <h2 className="text-2xl font-semibold text-gray-900 mb-6">Select Payment Method</h2>
//       <form onSubmit={handleSubmit} className="space-y-6">
//         <RadioGroup value={selectedMethod} onValueChange={setSelectedMethod}>
//           {paymentMethods.map((method) => (
//             <div key={method.id} className="flex items-center space-x-3 border p-4 rounded-lg">
//               <RadioGroupItem value={method.id} id={method.id} />
//               <Label htmlFor={method.id} className="flex items-center cursor-pointer">
//                 <method.icon className="h-5 w-5 mr-2 text-[#2A7C6C]" />
//                 {method.name}
//               </Label>
//             </div>
//           ))}
//         </RadioGroup>
//         <Button type="submit" className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white" disabled={!selectedMethod}>
//           Continue to Order Summary
//         </Button>
//       </form>
//     </div>
//   )
// }



// interface PaymentMethodsProps {
//   onSelect: (method: string) => void;
//   selectedMethod: string; // Added this prop to the interface
// }

// export function GroupOrderPaymentMethods({ onSelect, selectedMethod }: PaymentMethodsProps) {
//   // const paymentMethods = [
//   //   { id: "credit-card", name: "Credit Card" },
//   //   { id: "debit-card", name: "Debit Card" },
//   //   { id: "bank-transfer", name: "Bank Transfer" },
//   // ];

//   const paymentMethods = [
//     { id: "credit-card", name: "Credit Card", icon: CreditCard },
//     { id: "paypal", name: "PayPal", icon: Paypal },
//     { id: "debit-card", name: "Debit Card", icon: Landmark },
//     { id: "pay-on-delivery", name: "Pay on Delivery", icon: Truck },
//   ]

//   return (
//     <div className="space-y-6">
//       <h2 className="text-2xl font-semibold">Select Payment Method</h2>
//       <div className="space-y-4">
//         {paymentMethods.map((method) => (
//           <div
//             key={method.id}
//             className={`p-4 border rounded-lg cursor-pointer transition-colors ${
//               selectedMethod === method.id
//                 ? "border-[#2A7C6C] bg-[#2A7C6C]/5"
//                 : "border-gray-200 hover:border-[#2A7C6C]"
//             }`}
//             onClick={() => onSelect(method.id)}
//           >
//             <div className="flex items-center space-x-3">
//               <div
//                 className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
//                   selectedMethod === method.id
//                     ? "border-[#2A7C6C]"
//                     : "border-gray-300"
//                 }`}
//               >
//                 {selectedMethod === method.id && (
//                   <div className="w-3 h-3 rounded-full bg-[#2A7C6C]" />
//                 )}
//               </div>
//               <span className="font-medium">{method.name}</span>
//             </div>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// }



"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { CreditCard, ShoppingCartIcon as Paypal, Landmark, Truck } from 'lucide-react'

const paymentMethods = [
  {
    id: "credit-card",
    name: "Credit Card",
    icon: CreditCard,
  },
  {
    id: "paypal",
    name: "PayPal",
    icon: Paypal,
  },
  {
    id: "bank-transfer",
    name: "Bank Transfer",
    icon: Landmark,
  },
  {
    id: "cash-on-delivery",
    name: "Cash on Delivery",
    icon: Truck,
  },
] as const;

interface PaymentMethodsProps {
  onSelect: (method: string) => void;
  selectedMethod: string;
}

export function GroupOrderPaymentMethods({ onSelect, selectedMethod: initialMethod }: PaymentMethodsProps) {
  const [selectedMethod, setSelectedMethod] = useState(initialMethod)

  // Update local state when prop changes
  useEffect(() => {
    setSelectedMethod(initialMethod)
  }, [initialMethod])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedMethod) {
      onSelect(selectedMethod)
    }
  }

  return (
    <div>
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Select Payment Method</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <RadioGroup value={selectedMethod} onValueChange={setSelectedMethod}>
          {paymentMethods.map((method) => (
            <div 
              key={method.id} 
              className={`flex items-center space-x-3 border p-4 rounded-lg transition-colors ${
                selectedMethod === method.id 
                  ? 'border-[#2A7C6C] bg-[#2A7C6C]/5' 
                  : 'border-gray-200 hover:border-[#2A7C6C]'
              }`}
            >
              <RadioGroupItem value={method.id} id={method.id} />
              <Label 
                htmlFor={method.id} 
                className="flex items-center cursor-pointer flex-1"
              >
                <method.icon className="h-5 w-5 mr-2 text-[#2A7C6C]" />
                {method.name}
              </Label>
            </div>
          ))}
        </RadioGroup>
        <Button 
          type="submit" 
          className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white" 
          disabled={!selectedMethod}
        >
          Continue to Order Summary
        </Button>
      </form>
    </div>
  )
}