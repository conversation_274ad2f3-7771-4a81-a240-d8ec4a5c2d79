"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Truck, Users, ShoppingBag, ChevronRight } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { formatCurrency } from "@/lib/utils"
import { GroupOrderStatus } from "@/types/shoppingCartConstants"
import Link from 'next/link'

interface GroupOrderCardProps {
  order: {
    _id: string
    groupId: string
    totalOrderValue: number
    status: string
    orderPlacedAt: Date
    userContributions: {
      userId: string
      userName: string
      totalSpent: number
    }[]
    milestones: {
      targetAmount: number
      isReached: boolean
    }[]
  }
  onViewDetails?: (orderId: string) => void
}

export function GroupOrderCard({ order, onViewDetails }: GroupOrderCardProps) {
  // Calculate progress towards the highest milestone
  const highestMilestone = order.milestones.reduce(
    (highest, current) => (current.targetAmount > highest ? current.targetAmount : highest),
    0
  )
  
  const progressPercentage = Math.min(
    Math.round((order.totalOrderValue / highestMilestone) * 100),
    100
  )

  // Get badge color based on status
  const getBadgeColor = (status: string) => {
    switch (status) {
      case GroupOrderStatus.DRAFT:
        return "bg-gray-200 text-gray-700"
      case GroupOrderStatus.PENDING:
        return "bg-blue-100 text-blue-700"
      case GroupOrderStatus.PROCESSING:
        return "bg-orange-100 text-orange-700"
      case GroupOrderStatus.READY_FOR_DELIVERY:
        return "bg-purple-100 text-purple-700"
      case GroupOrderStatus.SHIPPED:
        return "bg-indigo-100 text-indigo-700"
      case GroupOrderStatus.COMPLETED:
        return "bg-green-100 text-green-700"
      case GroupOrderStatus.CANCELLED:
        return "bg-red-100 text-red-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  // Format date
  const formattedDate = new Date(order.orderPlacedAt).toLocaleDateString('en-ZA', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-semibold">
            Group Order #{order._id.substring(0, 6)}
          </CardTitle>
          <Badge className={getBadgeColor(order.status)}>
            {order.status}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">Created on {formattedDate}</p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center gap-1.5">
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">
              {formatCurrency(order.totalOrderValue)}
            </span>
          </div>
          
          <div className="flex items-center gap-1.5">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">
              {order.userContributions.length} members
            </span>
          </div>
          
          <div className="flex items-center gap-1.5">
            <Truck className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">
              {order.status === GroupOrderStatus.SHIPPED || order.status === GroupOrderStatus.COMPLETED 
                ? 'Shipped' 
                : 'Not shipped'}
            </span>
          </div>
        </div>
        
        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span>Progress to discount</span>
            <span className="font-medium">{progressPercentage}%</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          {order.milestones.some(m => m.isReached) ? (
            <p className="text-xs text-green-600">
              Discount milestone reached!
            </p>
          ) : (
            <p className="text-xs text-muted-foreground">
              {formatCurrency(order.totalOrderValue)} of {formatCurrency(highestMilestone)} for max discount
            </p>
          )}
        </div>
      </CardContent>
      
      <CardFooter>
        <Button 
          variant="outline" 
          className="w-full" 
          onClick={() => onViewDetails?.(order._id)}
          asChild
        >
          <Link href={`/groups/${order.groupId}/orders/${order._id}`}>
            <span className="flex items-center justify-center">
              View Details
              <ChevronRight className="ml-2 h-4 w-4" />
            </span>
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
