import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"

interface GroupOrderConfirmationProps {
  onClose: () => void
}

export function GroupOrderConfirmation({ onClose }: GroupOrderConfirmationProps) {
  return (
    <div className="text-center py-10">
      <CheckCircle className="mx-auto h-16 w-16 text-[#2A7C6C] mb-4" />
      <h2 className="text-2xl font-semibold text-gray-900 mb-4">Group Order Confirmed!</h2>
      <p className="text-gray-600 mb-8">
        Thank you for your group order. We&apos;ve received your payment and will process your order shortly. You will
        receive an email confirmation with the order details.
      </p>
      <Button onClick={onClose} className="bg-[#2A7C6C] hover:bg-[#236657] text-white">
        Close
      </Button>
    </div>
  )
}

