"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'
import { formatCurrency } from '@/lib/utils'

interface GroupOrderAnalyticsProps {
  groupId: string
  generateAnalytics: (
    groupId?: string,
    startDate?: Date,
    endDate?: Date
  ) => Promise<{
    totalOrders: number;
    averageOrderValue: number;
    totalRevenue: number;
    popularProducts: {
      productId: string;
      name: string;
      quantity: number;
    }[];
    ordersByStatus: {
      [key: string]: number;
    };
    monthlySales?: {
      month: string;
      revenue: number;
    }[];
    memberContributions?: {
      userName: string;
      totalSpent: number;
    }[];
  }>
}

export function GroupOrderAnalytics({ 
  groupId,
  generateAnalytics
}: GroupOrderAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<'30days' | '90days' | 'year' | 'all'>('30days')
  const [analytics, setAnalytics] = useState<{
    totalOrders: number;
    averageOrderValue: number;
    totalRevenue: number;
    popularProducts: Array<{
      productId: string;
      name: string;
      quantity: number;
    }>;
    ordersByStatus: Record<string, number>;
    monthlySales?: Array<{
      month: string;
      revenue: number;
    }>;
    memberContributions?: Array<{
      userName: string;
      totalSpent: number;
    }>;
  } | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    const loadAnalytics = async () => {
      setLoading(true)
      try {
        let startDate: Date | undefined
        const endDate = new Date()
        
        // Calculate start date based on selected time range
        if (timeRange === '30days') {
          startDate = new Date()
          startDate.setDate(startDate.getDate() - 30)
        } else if (timeRange === '90days') {
          startDate = new Date()
          startDate.setDate(startDate.getDate() - 90)
        } else if (timeRange === 'year') {
          startDate = new Date()
          startDate.setFullYear(startDate.getFullYear() - 1)
        }
        // For 'all', we don't set a startDate
        
        const data = await generateAnalytics(groupId, startDate, endDate)
        setAnalytics(data)
      } catch (error) {
        console.error('Error loading analytics:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadAnalytics()
  }, [timeRange, groupId, generateAnalytics])
  
  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A569BD', '#FFC0CB']
  
  // Format order status for display
  const formatStatus = (status: string) => {
    return status
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }
  
  // Format percentage function removed as it's not currently used
  
  // Prepare data for the order status pie chart
  const prepareStatusData = () => {
    if (!analytics || !analytics.ordersByStatus) return []
    
    return Object.entries(analytics.ordersByStatus).map(([status, count]) => ({
      name: formatStatus(status),
      value: count,
    }))
  }
  
  // Prepare data for the popular products bar chart
  const prepareProductsData = () => {
    if (!analytics || !analytics.popularProducts) return []
    
    return analytics.popularProducts
      .slice(0, 5) // Top 5 products
      .map((product: { name: string; quantity: number }) => ({
        name: product.name.length > 15 ? product.name.slice(0, 15) + '...' : product.name,
        quantity: product.quantity,
      }))
  }
  
  // Prepare data for member contributions
  const prepareMemberData = () => {
    if (!analytics || !analytics.memberContributions) return []
    
    return analytics.memberContributions
      .slice(0, 5) // Top 5 contributors
      .map((member: { userName: string; totalSpent: number }) => ({
        name: member.userName,
        value: member.totalSpent,
      }))
  }

  if (loading) {
    return (
      <div className="w-full min-h-[400px] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  if (!analytics) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">Analytics data not available</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <h2 className="text-2xl font-bold tracking-tight">Group Order Analytics</h2>
        <Tabs value={timeRange} onValueChange={(value) => setTimeRange(value as '30days' | '90days' | 'year' | 'all')}>
          <TabsList>
            <TabsTrigger value="30days">30 Days</TabsTrigger>
            <TabsTrigger value="90days">90 Days</TabsTrigger>
            <TabsTrigger value="year">Year</TabsTrigger>
            <TabsTrigger value="all">All Time</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Key metrics cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalOrders}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Average Order Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics.averageOrderValue)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics.totalRevenue)}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Charts section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly revenue trend */}
        {analytics.monthlySales && analytics.monthlySales.length > 0 && (
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Revenue Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={analytics.monthlySales}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis 
                      tickFormatter={(value) => `R${value / 1000}k`}
                    />
                    <Tooltip 
                      formatter={(value) => [formatCurrency(value as number), 'Revenue']}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      stroke="#2A7C6C"
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        )}
        
        {/* Order status distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Order Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={prepareStatusData()}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {prepareStatusData().map((entry, i) => (
                      <Cell 
                        key={`cell-${i}`} 
                        fill={COLORS[i % COLORS.length]} 
                      />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} orders`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        {/* Popular products */}
        <Card>
          <CardHeader>
            <CardTitle>Popular Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={prepareProductsData()}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis 
                    dataKey="name" 
                    type="category" 
                    width={100}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip />
                  <Legend />
                  <Bar 
                    dataKey="quantity" 
                    fill="#2A7C6C" 
                    name="Units Ordered"
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        {/* Top contributors */}
        {analytics.memberContributions && analytics.memberContributions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Top Contributors</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={prepareMemberData()}
                    layout="vertical"
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      type="number"
                      tickFormatter={(value) => `R${value / 1000}k`}
                    />
                    <YAxis 
                      dataKey="name" 
                      type="category" 
                      width={100}
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip 
                      formatter={(value) => [formatCurrency(value as number), 'Contribution']}
                    />
                    <Legend />
                    <Bar 
                      dataKey="value" 
                      fill="#0088FE" 
                      name="Amount Spent"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
