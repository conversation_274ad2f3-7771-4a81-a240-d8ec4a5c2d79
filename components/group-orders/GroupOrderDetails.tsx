"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  Truck, Users, ShoppingBag, Calendar, 
  CheckCircle2, Package, AlertCircle, ArrowLeft 
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { GroupOrderStatus } from "@/types/shoppingCartConstants"
import { formatCurrency } from "@/lib/utils"
// Link import not used but might be needed in the future
import { useRouter } from 'next/navigation'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface GroupOrderDetailsProps {
  order: {
    _id: string
    groupId: string
    totalOrderValue: number
    status: string
    orderPlacedAt: Date
    lastUpdatedAt: Date
    paymentStatus: string
    userContributions: {
      userId: string
      userName: string
      totalSpent: number
      contributionPercentage: number
    }[]
    orderItems: {
      product: {
        _id: string
        name: string
        price: number
        image?: string
      }
      quantity: number
      userId: string
      unitPrice: number
      subtotal: number
    }[]
    milestones: {
      name: string
      targetAmount: number
      isReached: boolean
      currentAmount: number
    }[]
    statusHistory?: {
      status: string
      timestamp: Date
    }[]
    bulkDiscountTiers: {
      threshold: number
      discountPercentage: number
    }[]
    appliedDiscountTier?: {
      threshold: number
      discountPercentage: number
    }
  }
  currentUserId: string
  isAdmin: boolean
  onUpdateStatus: (newStatus: string) => Promise<void>
  onCancel: () => Promise<void>
}

export function GroupOrderDetails({ 
  order, 
  currentUserId,
  isAdmin,
  onUpdateStatus,
  onCancel
}: GroupOrderDetailsProps) {
  const router = useRouter()
  const [isUpdating, setIsUpdating] = useState(false)
  const [showConfirmCancel, setShowConfirmCancel] = useState(false)
  
  // Find the highest milestone and calculate progress
  const highestMilestone = order.milestones.reduce(
    (highest, current) => (current.targetAmount > highest ? current.targetAmount : highest),
    0
  )
  
  const progressPercentage = Math.min(
    Math.round((order.totalOrderValue / highestMilestone) * 100),
    100
  )
  
  // Get badge color based on status
  const getBadgeColor = (status: string) => {
    switch (status) {
      case GroupOrderStatus.DRAFT:
        return "bg-gray-200 text-gray-700"
      case GroupOrderStatus.PENDING:
        return "bg-blue-100 text-blue-700"
      case GroupOrderStatus.PROCESSING:
        return "bg-orange-100 text-orange-700"
      case GroupOrderStatus.READY_FOR_DELIVERY:
        return "bg-purple-100 text-purple-700"
      case GroupOrderStatus.SHIPPED:
        return "bg-indigo-100 text-indigo-700"
      case GroupOrderStatus.COMPLETED:
        return "bg-green-100 text-green-700"
      case GroupOrderStatus.CANCELLED:
        return "bg-red-100 text-red-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }
  
  // Format dates
  const formattedCreatedDate = new Date(order.orderPlacedAt).toLocaleDateString('en-ZA', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
  
  const formattedUpdatedDate = new Date(order.lastUpdatedAt).toLocaleDateString('en-ZA', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
  
  // Get possible next status options based on current status
  const getNextStatusOptions = (currentStatus: string): string[] => {
    switch (currentStatus) {
      case GroupOrderStatus.DRAFT:
        return [GroupOrderStatus.PENDING, GroupOrderStatus.CANCELLED]
      case GroupOrderStatus.PENDING:
        return [GroupOrderStatus.PROCESSING, GroupOrderStatus.CANCELLED]
      case GroupOrderStatus.PROCESSING:
        return [GroupOrderStatus.READY_FOR_DELIVERY, GroupOrderStatus.CANCELLED]
      case GroupOrderStatus.READY_FOR_DELIVERY:
        return [GroupOrderStatus.SHIPPED, GroupOrderStatus.CANCELLED]
      case GroupOrderStatus.SHIPPED:
        return [GroupOrderStatus.COMPLETED]
      default:
        return []
    }
  }
  
  const nextStatusOptions = getNextStatusOptions(order.status)
  
  // Note: We directly check for current user ID in the UI render below
  // rather than pre-computing it here
  
  // Calculate applied discount
  const appliedDiscount = order.appliedDiscountTier 
    ? order.totalOrderValue * (order.appliedDiscountTier.discountPercentage / 100)
    : 0
    
  const finalPrice = order.totalOrderValue - appliedDiscount
  
  // Handle status update
  const handleUpdateStatus = async (newStatus: string) => {
    try {
      setIsUpdating(true)
      await onUpdateStatus(newStatus)
      setIsUpdating(false)
    } catch (error) {
      console.error('Failed to update status:', error)
      setIsUpdating(false)
    }
  }
  
  // Handle order cancellation
  const handleCancelOrder = async () => {
    try {
      setIsUpdating(true)
      await onCancel()
      setShowConfirmCancel(false)
      setIsUpdating(false)
    } catch (error) {
      console.error('Failed to cancel order:', error)
      setIsUpdating(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
          className="pl-0 -ml-2"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Orders
        </Button>
        
        {isAdmin && nextStatusOptions.length > 0 && order.status !== GroupOrderStatus.CANCELLED && (
          <div className="flex flex-wrap gap-2">
            {nextStatusOptions.map(status => (
              <Button
                key={status}
                variant={status === GroupOrderStatus.CANCELLED ? "destructive" : "default"}
                disabled={isUpdating}
                onClick={() => {
                  if (status === GroupOrderStatus.CANCELLED) {
                    setShowConfirmCancel(true)
                  } else {
                    handleUpdateStatus(status)
                  }
                }}
              >
                {status === GroupOrderStatus.CANCELLED ? (
                  <>
                    <AlertCircle className="mr-2 h-4 w-4" />
                    Cancel Order
                  </>
                ) : (
                  <>
                    Mark as {status.replace(/_/g, ' ')}
                  </>
                )}
              </Button>
            ))}
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main order details section */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                <div>
                  <CardTitle className="text-xl font-bold">
                    Order #{order._id.substring(0, 8)}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Created on {formattedCreatedDate}
                  </p>
                </div>
                <Badge className={getBadgeColor(order.status)}>
                  {order.status.replace(/_/g, ' ')}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex flex-col gap-1">
                  <span className="text-sm text-muted-foreground">Order value</span>
                  <div className="flex items-center">
                    <ShoppingBag className="h-4 w-4 mr-1.5 text-muted-foreground" />
                    <span className="font-medium">{formatCurrency(order.totalOrderValue)}</span>
                  </div>
                </div>
                
                <div className="flex flex-col gap-1">
                  <span className="text-sm text-muted-foreground">Contributors</span>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1.5 text-muted-foreground" />
                    <span className="font-medium">{order.userContributions.length}</span>
                  </div>
                </div>
                
                <div className="flex flex-col gap-1">
                  <span className="text-sm text-muted-foreground">Last updated</span>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1.5 text-muted-foreground" />
                    <span className="font-medium">{formattedUpdatedDate}</span>
                  </div>
                </div>
                
                <div className="flex flex-col gap-1">
                  <span className="text-sm text-muted-foreground">Shipping</span>
                  <div className="flex items-center">
                    <Truck className="h-4 w-4 mr-1.5 text-muted-foreground" />
                    <span className="font-medium">
                      {order.status === GroupOrderStatus.SHIPPED || order.status === GroupOrderStatus.COMPLETED
                        ? 'Shipped'
                        : 'Not shipped'}
                    </span>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="font-semibold mb-2">Progress towards discount</h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Current progress</span>
                    <span className="font-medium">{progressPercentage}%</span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-4">
                    {order.milestones.map((milestone, index) => (
                      <Card key={index} className={`border ${milestone.isReached ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <p className="text-sm font-medium">{milestone.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {formatCurrency(milestone.targetAmount)}
                              </p>
                            </div>
                            {milestone.isReached && (
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
              
              {order.appliedDiscountTier && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex items-center gap-2 text-green-700 font-medium mb-1">
                    <CheckCircle2 className="h-5 w-5" />
                    <span>Discount Applied!</span>
                  </div>
                  <p className="text-sm text-green-700">
                    Your group order qualifies for a {order.appliedDiscountTier.discountPercentage}% discount.
                    You&apos;ve saved {formatCurrency(appliedDiscount)}.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Order Items</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead className="text-right">Price</TableHead>
                    <TableHead className="text-right">Quantity</TableHead>
                    <TableHead className="text-right">Subtotal</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {order.orderItems.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        {typeof item.product === 'object' ? item.product.name : 'Product'}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.unitPrice)}
                      </TableCell>
                      <TableCell className="text-right">{item.quantity}</TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(item.subtotal)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              <div className="mt-4 space-y-2 border-t pt-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>{formatCurrency(order.totalOrderValue)}</span>
                </div>
                
                {order.appliedDiscountTier && (
                  <div className="flex justify-between text-green-700">
                    <span>Discount ({order.appliedDiscountTier.discountPercentage}%)</span>
                    <span>-{formatCurrency(appliedDiscount)}</span>
                  </div>
                )}
                
                <div className="flex justify-between font-bold text-lg pt-2 border-t">
                  <span>Total</span>
                  <span>{formatCurrency(finalPrice)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Sidebar with user contributions */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Group Contributions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {order.userContributions.map((contribution, index) => (
                <div 
                  key={index} 
                  className={`p-3 rounded-md ${
                    contribution.userId === currentUserId 
                      ? 'bg-blue-50 border border-blue-200' 
                      : 'border'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium flex items-center">
                      {contribution.userId === currentUserId && (
                        <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded mr-2">
                          You
                        </span>
                      )}
                      {contribution.userName}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {contribution.contributionPercentage.toFixed(0)}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Contribution</span>
                    <span className="font-medium">
                      {formatCurrency(contribution.totalSpent)}
                    </span>
                  </div>
                  <Progress 
                    value={contribution.contributionPercentage} 
                    className="h-1.5 mt-2" 
                  />
                </div>
              ))}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {order.statusHistory 
                  ? order.statusHistory.map((entry, index) => (
                    <div key={index} className="flex gap-3">
                      <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center mt-0.5">
                        <Package className="h-3 w-3 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">
                          {entry.status.replace(/_/g, ' ')}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(entry.timestamp).toLocaleString('en-ZA')}
                        </p>
                      </div>
                    </div>
                  ))
                  : (
                    <p className="text-sm text-muted-foreground">
                      No status history available
                    </p>
                  )
                }
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Cancellation confirmation dialog */}
      <Dialog open={showConfirmCancel} onOpenChange={setShowConfirmCancel}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Group Order</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this group order? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowConfirmCancel(false)}
              disabled={isUpdating}
            >
              Keep Order
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleCancelOrder}
              disabled={isUpdating}
            >
              {isUpdating ? 'Cancelling...' : 'Cancel Order'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
