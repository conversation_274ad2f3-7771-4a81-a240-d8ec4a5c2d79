'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  ShoppingCart, 
  TrendingUp, 
  Clock, 
  Wifi,
  WifiOff,
  Bell,
  Activity,
  Target,
  Gift,
  ChevronRight,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useGroupOrderUpdates } from '@/hooks/useGroupOrderUpdates';
import { formatCurrency } from '@/lib/utils';
import { DiscountMilestoneEvent } from '@/types/realTimeUpdates';

interface GroupOrderDashboardProps {
  groupId: string;
  groupName: string;
  initialData?: {
    memberCount: number;
    totalValue: number;
    currentDiscount: number;
  };
}

export function GroupOrderDashboard({ 
  groupId, 
  groupName, 
  initialData 
}: GroupOrderDashboardProps) {
  const [showActivity, setShowActivity] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  const {
    connectionStatus,
    lastUpdate,
    recentActivity,
    notifications,
    memberCount,
    totalValue,
    currentDiscount,
    nextMilestone,
    isConnected,
    markNotificationAsRead,
    clearActivity,
    getConnectionStatusColor
  } = useGroupOrderUpdates({
    groupId,
    enableNotifications: true,
    enableActivityTracking: true,
    maxActivityItems: 20,
    onMemberJoined: (member) => {
      console.log('Member joined:', member);
    },
    onMemberLeft: (member) => {
      console.log('Member left:', member);
    },
    onDiscountMilestone: (milestone: DiscountMilestoneEvent) => {
      console.log('Discount milestone reached:', milestone);
    },
    onOrderUpdate: (update) => {
      console.log('Order updated:', update);
    }
  });

  // Use initial data if real-time data not available
  const displayMemberCount = memberCount || initialData?.memberCount || 0;
  const displayTotalValue = totalValue || initialData?.totalValue || 0;
  const displayCurrentDiscount = currentDiscount || initialData?.currentDiscount || 0;

  // Calculate progress to next milestone
  const progressToNextMilestone = nextMilestone 
    ? Math.max(0, Math.min(100, ((displayTotalValue) / nextMilestone.threshold) * 100))
    : 100;

  return (
    <div className="space-y-6">
      {/* Header with Connection Status */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{groupName} Dashboard</h2>
          <p className="text-gray-600">Real-time group order tracking</p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className={`h-4 w-4 ${getConnectionStatusColor()}`} />
            ) : (
              <WifiOff className={`h-4 w-4 ${getConnectionStatusColor()}`} />
            )}
            <span className={`text-sm ${getConnectionStatusColor()}`}>
              {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
            </span>
          </div>

          {/* Notifications */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowNotifications(!showNotifications)}
            className="relative"
          >
            <Bell className="h-4 w-4" />
            {notifications.length > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs"
              >
                {notifications.length}
              </Badge>
            )}
          </Button>

          {/* Activity Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowActivity(!showActivity)}
          >
            <Activity className="h-4 w-4 mr-2" />
            Activity
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Members */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Members</p>
                <p className="text-3xl font-bold">{displayMemberCount}</p>
                {lastUpdate?.updateType === 'member_added' && (
                  <motion.p 
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-sm text-green-600"
                  >
                    +1 new member
                  </motion.p>
                )}
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        {/* Total Value */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Order Value</p>
                <p className="text-3xl font-bold">{formatCurrency(displayTotalValue)}</p>
                {displayCurrentDiscount > 0 && (
                  <p className="text-sm text-green-600">
                    {displayCurrentDiscount}% discount applied
                  </p>
                )}
              </div>
              <ShoppingCart className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        {/* Savings */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Savings</p>
                <p className="text-3xl font-bold">
                  {formatCurrency(displayTotalValue * (displayCurrentDiscount / 100))}
                </p>
                <p className="text-sm text-gray-600">
                  {displayCurrentDiscount}% off
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discount Progress */}
      {nextMilestone && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Next Discount Milestone
            </CardTitle>
            <CardDescription>
              Reach {formatCurrency(nextMilestone.threshold)} to unlock {nextMilestone.discount}% discount
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{Math.round(progressToNextMilestone)}%</span>
              </div>
              <Progress value={progressToNextMilestone} className="h-3" />
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                {formatCurrency(nextMilestone.remaining)} remaining
              </span>
              <Badge variant="outline" className="bg-purple-50 text-purple-700">
                <Gift className="h-3 w-3 mr-1" />
                {nextMilestone.discount}% off
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activity Feed and Notifications */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <AnimatePresence>
          {showActivity && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Recent Activity
                    </CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearActivity}
                    >
                      Clear
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    {recentActivity.length > 0 ? (
                      <div className="space-y-3">
                        {recentActivity.map((activity, index) => (
                          <motion.div
                            key={`${activity.userId}-${activity.timestamp}-${index}`}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg"
                          >
                            <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2" />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm">
                                <span className="font-medium">{activity.userName}</span>
                                {' '}
                                {getActivityDescription(activity)}
                              </p>
                              <p className="text-xs text-gray-500">
                                {new Date(activity.timestamp).toLocaleTimeString()}
                              </p>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No recent activity</p>
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Notifications */}
        <AnimatePresence>
          {showNotifications && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Notifications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    {notifications.length > 0 ? (
                      <div className="space-y-3">
                        {notifications.map((notification) => (
                          <motion.div
                            key={notification.id}
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="p-3 bg-blue-50 border border-blue-200 rounded-lg"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-medium text-blue-900">
                                  {notification.title}
                                </h4>
                                <p className="text-sm text-blue-700 mt-1">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-blue-600 mt-2">
                                  {new Date(notification.timestamp).toLocaleString()}
                                </p>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markNotificationAsRead(notification.id)}
                              >
                                ×
                              </Button>
                            </div>
                            {notification.actionUrl && (
                              <Button
                                variant="link"
                                size="sm"
                                className="p-0 h-auto mt-2"
                                onClick={() => window.open(notification.actionUrl, '_blank')}
                              >
                                {notification.actionText || 'View'}
                                <ChevronRight className="h-3 w-3 ml-1" />
                              </Button>
                            )}
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No notifications</p>
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Last Update Info */}
      {lastUpdate && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-800">
                Last updated: {new Date(lastUpdate.timestamp).toLocaleString()}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Helper function to format activity descriptions
function getActivityDescription(activity: any): string {
  switch (activity.activityType) {
    case 'joined_group':
      return 'joined the group';
    case 'left_group':
      return 'left the group';
    case 'added_to_cart':
      return `added ${activity.details?.productName || 'an item'} to cart`;
    case 'placed_order':
      return 'placed an order';
    case 'viewed_product':
      return `viewed ${activity.details?.productName || 'a product'}`;
    case 'shared_product':
      return `shared ${activity.details?.productName || 'a product'}`;
    default:
      return 'performed an action';
  }
}
