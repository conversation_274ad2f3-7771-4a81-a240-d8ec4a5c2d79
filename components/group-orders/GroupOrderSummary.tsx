// "use client";

// // import { CheckCircle } from "lucide-react";
// import { Button } from "@/components/ui/button";
// import { useCombinedCart } from "@/context/CombinedCartContext";

// interface GroupOrderSummaryProps {
//   customerInfo: {
//     name: string;
//     email: string;
//     address: string;
//     city: string;
//     country: string;
//     postalCode: string;
//   };
//   paymentMethod: string;
//   onConfirm: () => void;
//   isSubmitting: boolean;
// }

// export function GroupOrderSummary({
//   customerInfo,
//   paymentMethod,
//   onConfirm,
//   isSubmitting,
// }: GroupOrderSummaryProps) {
//   const { cartItems, subtotal } = useCombinedCart();

//   return (
//     <div>
//       <h2 className="text-2xl font-semibold text-gray-900 mb-6">Order Summary</h2>
//       <div className="space-y-6">
//         <div>
//           <h3 className="text-lg font-semibold mb-2">Customer Information</h3>
//           <p>{customerInfo.name}</p>
//           <p>{customerInfo.email}</p>
//           <p>{customerInfo.address}</p>
//           <p>
//             {customerInfo.city}, {customerInfo.country} {customerInfo.postalCode}
//           </p>
//         </div>
//         <div>
//           <h3 className="text-lg font-semibold mb-2">Payment Method</h3>
//           <p>{paymentMethod}</p>
//         </div>
//         <div>
//           <h3 className="text-lg font-semibold mb-2">Order Items</h3>
//           {cartItems.map((item) => (
//             <div key={item._id} className="flex justify-between text-sm py-1">
//               <span>
//                 {item.name} × {item.quantity}
//               </span>
//               <span>R {(item.price * item.quantity).toFixed(2)}</span>
//             </div>
//           ))}
//           <div className="flex justify-between font-semibold text-lg mt-4 pt-4 border-t">
//             <span>Total</span>
//             <span>R {subtotal.toFixed(2)}</span>
//           </div>
//         </div>
//         <Button
//           onClick={onConfirm}
//           className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white"
//           disabled={isSubmitting}
//         >
//           {isSubmitting ? "Confirming Order..." : "Confirm Order"}
//         </Button>
//       </div>
//     </div>
//   );
// }



// "use client";

// import { Button } from "@/components/ui/button";
// import { useCombinedCart } from "@/context/CombinedCartContext";

// interface GroupOrderSummaryProps {
//   customerInfo: {
//     name: string;
//     email: string;
//     address: string;
//     city: string;
//     country: string;
//     postalCode: string;
//   };
//   paymentMethod: string;
//   onConfirm: () => void;
//   isSubmitting: boolean;
// }

// export function GroupOrderSummary({
//   customerInfo,
//   paymentMethod,
//   onConfirm,
//   isSubmitting,
// }: GroupOrderSummaryProps) {
//   const { cartItems, subtotal } = useCombinedCart();

//   return (
//     <div>
//       <h2 className="text-2xl font-semibold text-gray-900 mb-6">Order Summary</h2>
//       <div className="space-y-6">
//         <div>
//           <h3 className="text-lg font-semibold mb-2">Customer Information</h3>
//           <p>{customerInfo.name}</p>
//           <p>{customerInfo.email}</p>
//           <p>{customerInfo.address}</p>
//           <p>
//             {customerInfo.city}, {customerInfo.country} {customerInfo.postalCode}
//           </p>
//         </div>
//         <div>
//           <h3 className="text-lg font-semibold mb-2">Payment Method</h3>
//           <p>{paymentMethod}</p>
//         </div>
//         <div>
//           <h3 className="text-lg font-semibold mb-2">Order Items</h3>
//           {cartItems.map((item) => (
//             <div key={item._id} className="flex justify-between text-sm py-1">
//               <span>
//                 {item.product.name} × {item.quantity}
//               </span>
//               <span>R {(item.product.price * item.quantity).toFixed(2)}</span>
//             </div>
//           ))}
//           <div className="flex justify-between font-semibold text-lg mt-4 pt-4 border-t">
//             <span>Total</span>
//             <span>R {subtotal.toFixed(2)}</span>
//           </div>
//         </div>
//         <Button
//           onClick={onConfirm}
//           className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white"
//           disabled={isSubmitting}
//         >
//           {isSubmitting ? "Confirming Order..." : "Confirm Order"}
//         </Button>
//       </div>
//     </div>
//   );
// }



// components/group-orders/GroupOrderSummary.tsx
"use client";

import { Button } from "@/components/ui/button";
import { useAppSelector } from "@/lib/redux/hooks";
import { selectCartItems, selectSubtotal } from "@/lib/redux/features/cart/cartSlice";

interface GroupOrderSummaryProps {
  customerInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    country: string;
    postalCode: string;
  };
  paymentMethod: string;
  onConfirm: () => void;
  isSubmitting: boolean;
}

export function GroupOrderSummary({
  customerInfo,
  paymentMethod,
  onConfirm,
  isSubmitting,
}: GroupOrderSummaryProps) {
  const cartItems = useAppSelector(selectCartItems);
  const subtotal = useAppSelector(selectSubtotal);

  return (
    <div>
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Order Summary</h2>
      <div className="space-y-6">
        {/* Customer Info section */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Customer Information</h3>
          <p>{customerInfo.name}</p>
          <p>{customerInfo.email}</p>
          <p>{customerInfo.address}</p>
          <p>
            {customerInfo.city}, {customerInfo.country} {customerInfo.postalCode}
          </p>
        </div>

        {/* Payment Method section */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Payment Method</h3>
          <p>{paymentMethod}</p>
        </div>

        {/* Order Items section */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Order Items</h3>
          {cartItems.map((item) => (
            <div key={item.productId} className="flex justify-between text-sm py-1">
              <span>
                {item.name} × {item.quantity}
              </span>
              <span>
                R {(Number(item.price) * Number(item.quantity)).toFixed(2)}
              </span>
            </div>
          ))}
          <div className="flex justify-between font-semibold text-lg mt-4 pt-4 border-t">
            <span>Total</span>
            <span>R {subtotal.toFixed(2)}</span>
          </div>
        </div>

        {/* Confirm Order Button */}
        <Button
          onClick={onConfirm}
          className="w-full bg-[#2A7C6C] hover:bg-[#236657] text-white"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Confirming Order..." : "Confirm Order"}
        </Button>
      </div>
    </div>
  );
}