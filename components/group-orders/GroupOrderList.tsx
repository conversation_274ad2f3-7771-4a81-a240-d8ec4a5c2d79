"use client"

import React from 'react'
import { GroupOrderCard } from './GroupOrderCard'
import { useRouter } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Search, Filter } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { GroupOrderStatus } from '@/types/shoppingCartConstants'

interface GroupOrderListProps {
  orders: Array<{
    _id: string
    groupId: string
    totalOrderValue: number
    status: string
    orderPlacedAt: Date
    userContributions: {
      userId: string
      userName: string
      totalSpent: number
    }[]
    milestones: {
      targetAmount: number
      isReached: boolean
    }[]
  }>
  groupId: string
}

export function GroupOrderList({ orders, groupId }: GroupOrderListProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = React.useState('')
  const [statusFilter, setStatusFilter] = React.useState('all')

  const filteredOrders = React.useMemo(() => {
    return orders.filter(order => {
      // Apply status filter if selected
      if (statusFilter !== 'all' && order.status !== statusFilter) {
        return false
      }

      // Apply search filter if present (search by order ID)
      if (searchQuery && !order._id.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }

      return true
    })
  }, [orders, searchQuery, statusFilter])

  const handleViewDetails = (orderId: string) => {
    router.push(`/groups/${groupId}/orders/${orderId}`)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Group Orders</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/groups/${groupId}/new-order`)}
          >
            Create New Order
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by order ID"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Select
          value={statusFilter}
          onValueChange={setStatusFilter}
        >
          <SelectTrigger className="w-full md:w-[180px]">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <SelectValue placeholder="Filter by status" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All statuses</SelectItem>
            {Object.values(GroupOrderStatus).map(status => (
              <SelectItem key={status} value={status}>
                {status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ')}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {filteredOrders.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          <p>No orders found matching your criteria.</p>
          {(searchQuery || statusFilter) && (
            <Button
              variant="link"
              className="mt-2"
              onClick={() => {
                setSearchQuery('')
                setStatusFilter('')
              }}
            >
              Clear filters
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredOrders.map(order => (
            <GroupOrderCard
              key={order._id}
              order={order}
              onViewDetails={handleViewDetails}
            />
          ))}
        </div>
      )}
    </div>
  )
}
