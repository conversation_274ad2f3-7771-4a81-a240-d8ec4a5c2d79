import { ChevronDown } from 'lucide-react'
import { Button } from "@/components/ui/button"

interface Category {
  name: string
  count: number
}

interface CategoryFilterProps {
  categories: Category[]
  selectedCategory?: string
  onSelectCategory: (category: string) => void
}

export function CategoryFilter({ categories, selectedCategory, onSelectCategory }: CategoryFilterProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Categories</h3>
        <Button variant="ghost" size="icon">
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
      <div className="space-y-2">
        {categories.map((category) => (
          <button
            key={category.name}
            onClick={() => onSelectCategory(category.name)}
            className={`flex w-full items-center justify-between py-2 text-sm hover:text-gray-900 ${
              selectedCategory === category.name ? 'text-gray-900 font-medium' : 'text-gray-500'
            }`}
          >
            <span>{category.name}</span>
            <span className="text-gray-400">{category.count}</span>
          </button>
        ))}
      </div>
    </div>
  )
}

