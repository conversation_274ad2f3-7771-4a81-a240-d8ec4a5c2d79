import { ChevronDown } from 'lucide-react'
import { Button } from "@/components/ui/button"

interface Color {
  name: string
  value: string
}

interface ColorFilterProps {
  colors: Color[]
  selectedColors: string[]
  onSelectColor: (color: string) => void
}

export function ColorFilter({ colors, selectedColors, onSelectColor }: ColorFilterProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Colors</h3>
        <Button variant="ghost" size="icon">
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
      <div className="grid grid-cols-6 gap-2">
        {colors.map((color) => (
          <button
            key={color.name}
            onClick={() => onSelectColor(color.name)}
            className={`h-8 w-8 rounded-full border-2 ${
              selectedColors.includes(color.name) ? 'border-gray-900' : 'border-transparent'
            }`}
            style={{ backgroundColor: color.value }}
            title={color.name}
          >
            <span className="sr-only">{color.name}</span>
          </button>
        ))}
      </div>
    </div>
  )
}

