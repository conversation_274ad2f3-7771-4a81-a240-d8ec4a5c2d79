import { ChevronDown } from 'lucide-react'
import { Button } from "@/components/ui/button"

interface SizeFilterProps {
  sizes: string[]
  selectedSizes: string[]
  onSelectSize: (size: string) => void
}

export function SizeFilter({ sizes, selectedSizes, onSelectSize }: SizeFilterProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Size</h3>
        <Button variant="ghost" size="icon">
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
      <div className="grid grid-cols-5 gap-2">
        {sizes.map((size) => (
          <button
            key={size}
            onClick={() => onSelectSize(size)}
            className={`rounded border px-3 py-2 text-sm ${
              selectedSizes.includes(size)
                ? 'border-gray-900 bg-gray-900 text-white'
                : 'border-gray-200 hover:border-gray-900'
            }`}
          >
            {size}
          </button>
        ))}
      </div>
    </div>
  )
}

