import { ChevronDown } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"

interface PriceRange {
  label: string
  value: string
}

interface PriceFilterProps {
  priceRanges: PriceRange[]
  selectedPrice?: string
  onSelectPrice: (price: string) => void
}

export function PriceFilter({ priceRanges, selectedPrice, onSelectPrice }: PriceFilterProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Price</h3>
        <Button variant="ghost" size="icon">
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
      <RadioGroup value={selectedPrice} onValueChange={onSelectPrice}>
        {priceRanges.map((range) => (
          <div key={range.value} className="flex items-center space-x-2">
            <RadioGroupItem value={range.value} id={range.value} />
            <Label htmlFor={range.value} className="text-sm text-gray-500">
              {range.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  )
}

