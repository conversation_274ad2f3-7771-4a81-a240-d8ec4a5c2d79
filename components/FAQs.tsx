"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
  } from "@/components/ui/accordion"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Users,
  ShoppingCart,
  Truck,
  Shield,
  CreditCard,
  MapPin,
  Clock,
  Star,
  HelpCircle,
  MessageCircle,
  Phone,
  Mail,
  Sparkles
} from "lucide-react"

const faqCategories = [
  { id: "all", label: "All Questions", icon: HelpCircle, count: 24 },
  { id: "getting-started", label: "Getting Started", icon: Users, count: 6 },
  { id: "shopping", label: "Shopping & Orders", icon: ShoppingCart, count: 8 },
  { id: "delivery", label: "Delivery & Zones", icon: Truck, count: 5 },
  { id: "payments", label: "Payments & Savings", icon: CreditCard, count: 5 }
]

const faqs = [
  // Getting Started
  {
    category: "getting-started",
    question: "What is <PERSON>okvel and how does it work?",
    answer: "Stokvel is a revolutionary community-based grocery shopping platform that brings the traditional African concept of stokvels into the digital age. Users join shopping zones in their area and shop together to unlock bulk discounts. When your zone's collective order meets delivery requirements, groceries are delivered in bulk, creating significant savings for everyone involved.",
    tags: ["basics", "community", "savings"]
  },
  {
    category: "getting-started",
    question: "How do I join a shopping zone?",
    answer: "Joining is simple! Sign up on our platform, verify your location, and we'll show you available zones in your area. Browse zone profiles, check their shopping schedules, and select one that matches your lifestyle. Once approved by the zone coordinator, you can start shopping and saving immediately.",
    tags: ["registration", "zones", "community"]
  },
  {
    category: "getting-started",
    question: "Is Stokvel free to use?",
    answer: "Yes! Creating an account and joining shopping zones is completely free. We only earn when you save - through small service fees on successful group purchases. There are no monthly subscriptions, hidden charges, or membership fees.",
    tags: ["pricing", "free", "fees"]
  },
  {
    category: "getting-started",
    question: "What areas does Stokvel serve?",
    answer: "We currently serve all major areas across Gauteng, including Johannesburg, Pretoria, Ekurhuleni, and surrounding townships. We're rapidly expanding to other provinces. Check our coverage map during registration to see if your area is available.",
    tags: ["coverage", "gauteng", "expansion"]
  },
  {
    category: "getting-started",
    question: "Can I create my own shopping zone?",
    answer: "Absolutely! If there's no active zone in your area, you can create one. As a zone coordinator, you'll help organize group orders, coordinate with neighbors, and ensure smooth deliveries. We provide full support and training for new coordinators.",
    tags: ["coordinator", "leadership", "community"]
  },
  {
    category: "getting-started",
    question: "What if I'm new to online shopping?",
    answer: "No worries! Our platform is designed to be user-friendly for everyone. We offer video tutorials, step-by-step guides, and dedicated customer support. Many of our members were first-time online shoppers who now love the convenience and savings.",
    tags: ["beginner", "support", "tutorials"]
  },

  // Shopping & Orders
  {
    category: "shopping",
    question: "How much can I save by shopping with Stokvel?",
    answer: "Our members save an average of 25-40% on their grocery bills compared to traditional retail stores. Savings vary based on product categories, order sizes, and seasonal promotions. Fresh produce and bulk items typically offer the highest savings.",
    tags: ["savings", "discounts", "bulk"]
  },
  {
    category: "shopping",
    question: "What products are available?",
    answer: "We offer a comprehensive range including fresh produce, dairy, meat, pantry staples, household items, personal care products, and baby essentials. Our catalog features over 5,000 products from trusted local and national suppliers, with new items added weekly.",
    tags: ["products", "catalog", "variety"]
  },
  {
    category: "shopping",
    question: "Can I modify or cancel my order?",
    answer: "Yes, you can modify or cancel orders until your zone's order is finalized (usually 24-48 hours before delivery). After finalization, changes aren't possible as products are already being prepared for bulk delivery.",
    tags: ["modifications", "cancellation", "deadlines"]
  },
  {
    category: "shopping",
    question: "How do I know what my neighbors are buying?",
    answer: "You can see anonymized zone activity - popular items, trending products, and total zone savings - but individual member purchases remain private. This helps you discover new products while maintaining privacy.",
    tags: ["privacy", "community", "recommendations"]
  },
  {
    category: "shopping",
    question: "Are there minimum order requirements?",
    answer: "Individual orders have no minimum, but zones need to reach collective minimums for delivery (typically R2,000-R3,000 depending on area). Don't worry - active zones usually exceed this easily, and you'll be notified if targets aren't met.",
    tags: ["minimums", "requirements", "zones"]
  },
  {
    category: "shopping",
    question: "Can I shop for special dietary needs?",
    answer: "Yes! We have dedicated sections for halal, kosher, vegan, gluten-free, and diabetic-friendly products. Use our advanced filters to find products that meet your specific dietary requirements and preferences.",
    tags: ["dietary", "special-needs", "filters"]
  },
  {
    category: "shopping",
    question: "How do I track my order?",
    answer: "Track everything through your dashboard: zone progress, order status, preparation updates, and delivery tracking. You'll receive SMS and email notifications at each stage, plus real-time updates on delivery day.",
    tags: ["tracking", "notifications", "status"]
  },
  {
    category: "shopping",
    question: "What if an item is out of stock?",
    answer: "We'll notify you immediately and offer suitable alternatives or refund the item cost. Our smart inventory system minimizes stockouts, but when they occur, we ensure you're not left disappointed.",
    tags: ["stockouts", "alternatives", "refunds"]
  },

  // Delivery & Zones
  {
    category: "delivery",
    question: "How often are deliveries made?",
    answer: "Most active zones receive deliveries 1-2 times per week, depending on community demand and order volumes. Popular zones in high-density areas may have daily delivery options, while smaller zones typically have weekly schedules.",
    tags: ["frequency", "schedule", "zones"]
  },
  {
    category: "delivery",
    question: "What are delivery time slots?",
    answer: "Choose from morning (8AM-12PM), afternoon (12PM-5PM), or evening (5PM-8PM) slots. Premium zones may have 2-hour windows. You'll receive a 30-minute heads-up call before delivery arrives.",
    tags: ["time-slots", "scheduling", "notifications"]
  },
  {
    category: "delivery",
    question: "What if I'm not home during delivery?",
    answer: "We offer several options: delivery to trusted neighbors (with your permission), secure drop-off points, or rescheduling to the next available slot. Some zones have community collection points for added convenience.",
    tags: ["missed-delivery", "alternatives", "flexibility"]
  },
  {
    category: "delivery",
    question: "Are there delivery fees?",
    answer: "Delivery costs are shared among zone members, typically R15-R30 per household depending on order size and distance. This is significantly cheaper than individual delivery services, and the cost decreases as more neighbors participate.",
    tags: ["delivery-fees", "shared-costs", "savings"]
  },
  {
    category: "delivery",
    question: "How do you ensure food safety during delivery?",
    answer: "We use temperature-controlled vehicles, insulated packaging, and follow strict hygiene protocols. Fresh and frozen items are delivered in separate compartments, and all delivery staff are trained in food safety standards.",
    tags: ["food-safety", "temperature", "hygiene"]
  },

  // Payments & Savings
  {
    category: "payments",
    question: "What payment methods do you accept?",
    answer: "We accept all major credit/debit cards, EFT, SnapScan, Zapper, and mobile money. You can also set up automatic payments for convenience. All transactions are secured with bank-level encryption.",
    tags: ["payment-methods", "security", "convenience"]
  },
  {
    category: "payments",
    question: "When am I charged for my order?",
    answer: "Payment is processed when your zone's order is finalized and confirmed for delivery. This ensures you only pay for items that are actually available and being delivered to your zone.",
    tags: ["payment-timing", "confirmation", "security"]
  },
  {
    category: "payments",
    question: "How do bulk discounts work?",
    answer: "Discounts increase with zone order volume. Larger collective orders unlock better wholesale prices, which are passed directly to members. You'll see potential savings before confirming your order.",
    tags: ["bulk-discounts", "volume", "wholesale"]
  },
  {
    category: "payments",
    question: "Can I get a refund if I'm not satisfied?",
    answer: "Absolutely! We offer full refunds for damaged, expired, or unsatisfactory products. Report issues within 24 hours of delivery through our app, and refunds are processed within 2-3 business days.",
    tags: ["refunds", "satisfaction", "quality"]
  },
  {
    category: "payments",
    question: "Do you offer loyalty rewards?",
    answer: "Yes! Earn Stokvel Points with every purchase, refer friends, and participate in community activities. Points can be redeemed for discounts, exclusive products, or donated to community projects.",
    tags: ["loyalty", "rewards", "points"]
  }
]
  
export function FAQs() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [searchResults, setSearchResults] = useState(faqs)
  const [isSearchFocused, setIsSearchFocused] = useState(false)

  // Enhanced search function with scoring
  const searchFAQs = (term: string, category: string) => {
    if (!term.trim() && category === "all") {
      return faqs
    }

    const searchWords = term.toLowerCase().split(' ').filter(word => word.length > 0)

    const scoredFAQs = faqs.map(faq => {
      let score = 0
      const questionLower = faq.question.toLowerCase()
      const answerLower = faq.answer.toLowerCase()
      const tagsLower = faq.tags.map(tag => tag.toLowerCase())

      // Category filter
      if (category !== "all" && faq.category !== category) {
        return { ...faq, score: -1 } // Exclude from results
      }

      if (searchWords.length === 0) {
        return { ...faq, score: 1 } // Include all if no search term
      }

      searchWords.forEach(word => {
        // Exact question match (highest score)
        if (questionLower.includes(word)) {
          score += questionLower === word ? 100 : 50
        }

        // Answer match
        if (answerLower.includes(word)) {
          score += 20
        }

        // Tag match
        tagsLower.forEach(tag => {
          if (tag.includes(word)) {
            score += tag === word ? 40 : 30
          }
        })

        // Partial word matches
        if (questionLower.includes(word) || answerLower.includes(word)) {
          score += 10
        }
      })

      return { ...faq, score }
    })

    // Filter out non-matches and sort by score
    return scoredFAQs
      .filter(faq => faq.score > 0)
      .sort((a, b) => b.score - a.score)
  }

  // Update search results when search term or category changes
  useEffect(() => {
    const results = searchFAQs(searchTerm, selectedCategory)
    setSearchResults(results)
  }, [searchTerm, selectedCategory])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Focus search on Ctrl/Cmd + K
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
        }
      }

      // Clear search on Escape
      if (e.key === 'Escape' && searchTerm) {
        setSearchTerm("")
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [searchTerm])

  // Highlight search terms in text
  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm.trim()) return text

    const searchWords = searchTerm.toLowerCase().split(' ').filter(word => word.length > 0)
    let highlightedText = text

    searchWords.forEach(word => {
      const regex = new RegExp(`(${word})`, 'gi')
      highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">$1</mark>')
    })

    return highlightedText
  }

  // Update category counts based on current search
  const getCategoryCount = (categoryId: string) => {
    if (categoryId === "all") {
      return searchResults.length
    }
    return searchResults.filter(faq => faq.category === categoryId).length
  }

  const filteredFAQs = searchResults

  return (
    <section className="relative py-32 bg-gradient-to-br from-slate-50 via-white to-slate-100 overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232A7C6C' fill-opacity='0.4'%3E%3Cpath d='M50 50L25 75L75 75z'/%3E%3Cpath d='M50 50L75 25L75 75z'/%3E%3Cpath d='M50 50L25 25L75 25z'/%3E%3Cpath d='M50 50L25 25L25 75z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-[#2A7C6C]/10 rounded-full blur-2xl" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-[#2A7C6C]/5 rounded-full blur-xl" />

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        {/* Header Section */}
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-block"
          >
            <div className="flex items-center justify-center mb-6">
              <div className="h-16 w-16 bg-gradient-to-br from-[#2A7C6C] to-[#1E5A4F] rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <HelpCircle className="h-8 w-8 text-white" />
              </div>
              <h1
                className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent"
                style={{
                  fontFamily: 'ClashDisplay-Variable, sans-serif',
                  letterSpacing: '-0.03em'
                }}
              >
                Help Center
              </h1>
            </div>
          </motion.div>

          <div className="w-32 h-1 bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] mx-auto mb-8 rounded-full" />

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-gray-600 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Avenir, sans-serif' }}
          >
            Find answers to your questions about community shopping, savings, and how Stokvel works.
            Can't find what you're looking for? We're here to help!
          </motion.p>
        </div>

        {/* Enhanced Search Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="max-w-2xl mx-auto mb-16"
        >
          <div className="relative">
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
            <Input
              type="text"
              placeholder="Search questions, answers, or topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              className="w-full pl-16 pr-16 py-6 text-lg rounded-3xl border-2 border-gray-200/50 focus:border-[#2A7C6C] focus:ring-4 focus:ring-[#2A7C6C]/20 transition-all duration-300 bg-white/70 backdrop-blur-sm text-gray-800 shadow-lg hover:shadow-xl"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            />

            {/* Keyboard shortcut hint */}
            {!isSearchFocused && !searchTerm && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="absolute right-20 top-1/2 transform -translate-y-1/2 hidden md:flex items-center space-x-1 text-xs text-gray-400"
              >
                <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-600 font-mono">⌘</kbd>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-600 font-mono">K</kbd>
              </motion.div>
            )}
            {searchTerm && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                onClick={() => setSearchTerm("")}
                className="absolute right-16 top-1/2 transform -translate-y-1/2 h-6 w-6 bg-gray-300 hover:bg-gray-400 rounded-full flex items-center justify-center text-white text-sm transition-colors duration-200"
                title="Clear search"
              >
                ×
              </motion.button>
            )}
            <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
              <div className="h-8 w-8 bg-[#2A7C6C] rounded-full flex items-center justify-center">
                <Search className="h-4 w-4 text-white" />
              </div>
            </div>
          </div>

          {/* Search Results Count */}
          {searchTerm && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 text-center"
            >
              <p className="text-gray-600" style={{ fontFamily: 'Avenir, sans-serif' }}>
                Found <span className="font-semibold text-[#2A7C6C]">{filteredFAQs.length}</span> result{filteredFAQs.length !== 1 ? 's' : ''}
                {searchTerm && ` for "${searchTerm}"`}
              </p>
            </motion.div>
          )}
        </motion.div>

        {/* Category Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex flex-wrap justify-center gap-3 mb-16"
        >
          {faqCategories.map((category, index) => (
            <motion.button
              key={category.id}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl ${
                selectedCategory === category.id
                  ? "bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] text-white shadow-[#2A7C6C]/25"
                  : "bg-white/70 backdrop-blur-sm text-gray-700 hover:bg-white hover:text-[#2A7C6C] border border-gray-200/50"
              }`}
              style={{ fontFamily: "Avenir, sans-serif" }}
            >
              <category.icon className="h-4 w-4 mr-2" />
              {category.label}
              <Badge
                variant="secondary"
                className={`ml-2 ${
                  selectedCategory === category.id
                    ? "bg-white/20 text-white"
                    : "bg-[#2A7C6C]/10 text-[#2A7C6C]"
                }`}
              >
                {getCategoryCount(category.id)}
              </Badge>
            </motion.button>
          ))}
        </motion.div>

        {/* FAQ Content */}
        <div className="max-w-5xl mx-auto">
          <AnimatePresence>
            {filteredFAQs.length > 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Accordion type="single" collapsible className="w-full space-y-4">
                  {filteredFAQs.map((faq, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.05 }}
                    >
                      <AccordionItem
                        value={`item-${index}`}
                        className="bg-white/70 backdrop-blur-sm rounded-3xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                      >
                        <AccordionTrigger
                          className="text-left text-lg md:text-xl font-bold text-[#2F4858] hover:text-[#2A7C6C] px-8 py-6 hover:no-underline transition-colors duration-300"
                          style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                        >
                          <div className="flex items-start space-x-4">
                            <div className="h-8 w-8 bg-gradient-to-br from-[#2A7C6C] to-[#1E5A4F] rounded-xl flex items-center justify-center flex-shrink-0 mt-1">
                              <HelpCircle className="h-4 w-4 text-white" />
                            </div>
                            <span
                              className="text-left"
                              dangerouslySetInnerHTML={{
                                __html: highlightText(faq.question, searchTerm)
                              }}
                            />
                          </div>
                        </AccordionTrigger>
                        <AccordionContent
                          className="text-gray-700 text-base md:text-lg leading-relaxed px-8 pb-8"
                          style={{ fontFamily: 'Avenir, sans-serif' }}
                        >
                          <div className="pl-12">
                            <div
                              className="mb-4"
                              dangerouslySetInnerHTML={{
                                __html: highlightText(faq.answer, searchTerm)
                              }}
                            />
                            <div className="flex flex-wrap gap-2">
                              {faq.tags.map((tag, tagIndex) => {
                                const isHighlighted = searchTerm && tag.toLowerCase().includes(searchTerm.toLowerCase())
                                return (
                                  <Badge
                                    key={tagIndex}
                                    variant="outline"
                                    className={`text-xs transition-colors duration-200 ${
                                      isHighlighted
                                        ? "bg-yellow-100 text-yellow-800 border-yellow-300"
                                        : "bg-[#2A7C6C]/5 text-[#2A7C6C] border-[#2A7C6C]/20"
                                    }`}
                                  >
                                    {tag}
                                  </Badge>
                                )
                              })}
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </motion.div>
                  ))}
                </Accordion>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-16"
              >
                <div className="h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="h-12 w-12 text-gray-400" />
                </div>
                <h3
                  className="text-2xl font-bold text-gray-600 mb-4"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  No results found
                </h3>
                <p
                  className="text-gray-500 mb-6"
                  style={{ fontFamily: 'Avenir, sans-serif' }}
                >
                  Try adjusting your search terms or browse different categories
                </p>
                <Button
                  onClick={() => {
                    setSearchTerm("")
                    setSelectedCategory("all")
                  }}
                  className="bg-[#2A7C6C] hover:bg-[#236358] text-white rounded-full px-6 py-3"
                >
                  Clear Search
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Contact Support Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mt-20 text-center"
        >
          <div className="bg-gradient-to-r from-[#2A7C6C]/10 to-[#7FDBCA]/10 p-12 rounded-3xl border border-[#2A7C6C]/20 max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <Sparkles className="h-8 w-8 text-[#2A7C6C] mr-3" />
              <h3
                className="text-3xl md:text-4xl font-bold text-[#2F4858]"
                style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
              >
                Still Need Help?
              </h3>
            </div>

            <p
              className="text-gray-600 text-lg md:text-xl mb-8 leading-relaxed max-w-2xl mx-auto"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Our friendly support team is here to help you get the most out of your Stokvel experience.
              Reach out anytime!
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  className="group relative overflow-hidden bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] hover:from-[#236358] hover:to-[#164239] text-white rounded-full px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  <MessageCircle className="h-5 w-5 mr-2" />
                  Live Chat Support
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  className="border-2 border-[#2A7C6C] text-[#2A7C6C] hover:bg-[#2A7C6C] hover:text-white rounded-full px-8 py-4 text-lg font-semibold transition-all duration-300"
                  style={{ fontFamily: "Avenir, sans-serif" }}
                >
                  <Mail className="h-5 w-5 mr-2" />
                  Email Support
                </Button>
              </motion.div>
            </div>

            <div className="mt-8 flex items-center justify-center text-sm text-gray-500">
              <Clock className="h-4 w-4 mr-2" />
              <span style={{ fontFamily: 'Avenir, sans-serif' }}>
                Available Monday - Friday, 8AM - 6PM
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
  
  