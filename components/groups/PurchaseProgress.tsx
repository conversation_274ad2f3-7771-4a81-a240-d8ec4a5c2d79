// components/groups/PurchaseProgress.tsx
"use client"

import React from "react"
import { Progress } from "@/components/ui/progress"

interface PurchaseProgressProps {
  current: number
  goal: number
}

export default function PurchaseProgress({ current, goal }: PurchaseProgressProps) {
  const percentage = Math.min((current / goal) * 100, 100)

  return (
    <div className="mb-4">
      <h3 className="text-lg font-semibold mb-2">Purchase Progress</h3>
      <Progress value={percentage} />
      <p className="text-sm text-gray-600 mt-1">
        R{current} raised of R{goal} needed for delivery
      </p>
    </div>
  )
}
