"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  MessageSquare, 
  Activity, 
  List, 
  ShoppingCart, 
  Target, 
  TrendingUp,
  DollarSign,
  Clock,
  Star,
  Trophy,
  Zap,
  Bell,
  Settings,
  Share2,
  MoreHorizontal
} from 'lucide-react';
import { GroupChat } from './chat/GroupChat';
import { GroupActivityFeed } from './activity/GroupActivityFeed';
import { CollaborativeShoppingLists } from './collaboration/CollaborativeShoppingLists';
import { GroupChallenges } from './gamification/GroupChallenges';
import { BadgesAndAchievements } from './gamification/BadgesAndAchievements';
import { SocialProofAndLeaderboards } from './social/SocialProofAndLeaderboards';
import { useAuth } from '@/context/AuthContext';

// Types
interface GroupStats {
  totalMembers: number;
  onlineMembers: number;
  totalMessages: number;
  totalActivities: number;
  totalLists: number;
  totalSavings: number;
  currentDiscountTier: string;
  nextDiscountTarget: number;
  progressToNextTier: number;
}

interface EnhancedGroupDashboardProps {
  groupId: string;
  groupName: string;
  groupDescription?: string;
}

export function EnhancedGroupDashboard({ 
  groupId, 
  groupName, 
  groupDescription 
}: EnhancedGroupDashboardProps) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [groupStats, setGroupStats] = useState<GroupStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [notifications, setNotifications] = useState(0);

  // Fetch group statistics
  const fetchGroupStats = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/groups/${groupId}/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setGroupStats(result.data);
      }
    } catch (error) {
      console.error('Error fetching group stats:', error);
      // Mock data for development
      setGroupStats({
        totalMembers: 12,
        onlineMembers: 5,
        totalMessages: 247,
        totalActivities: 89,
        totalLists: 3,
        totalSavings: 2450,
        currentDiscountTier: '10% Discount',
        nextDiscountTarget: 10000,
        progressToNextTier: 65
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update online status
  const updateOnlineStatus = async (status: 'online' | 'offline') => {
    try {
      const token = localStorage.getItem('token');
      
      await fetch(`/api/groups/${groupId}/members/online`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      });
    } catch (error) {
      console.error('Error updating online status:', error);
    }
  };

  // Set user as online when component mounts
  useEffect(() => {
    updateOnlineStatus('online');
    fetchGroupStats();

    // Set user as offline when component unmounts
    return () => {
      updateOnlineStatus('offline');
    };
  }, [groupId]);

  // Refresh stats periodically
  useEffect(() => {
    const interval = setInterval(fetchGroupStats, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [groupId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Group Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{groupName}</h1>
          {groupDescription && (
            <p className="text-muted-foreground mt-1">{groupDescription}</p>
          )}
          <div className="flex items-center space-x-4 mt-2">
            <Badge variant="secondary" className="flex items-center">
              <Users className="h-3 w-3 mr-1" />
              {groupStats?.totalMembers} members
            </Badge>
            <Badge variant="outline" className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              {groupStats?.onlineMembers} online
            </Badge>
            <Badge variant="outline">
              {groupStats?.currentDiscountTier}
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
            {notifications > 0 && (
              <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                {notifications}
              </Badge>
            )}
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Invite
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R{groupStats?.totalSavings.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Group Activity</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{groupStats?.totalActivities}</div>
            <p className="text-xs text-muted-foreground">
              {groupStats?.totalMessages} messages today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shopping Lists</CardTitle>
            <List className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{groupStats?.totalLists}</div>
            <p className="text-xs text-muted-foreground">
              2 active lists
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Discount</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{groupStats?.progressToNextTier}%</div>
            <Progress value={groupStats?.progressToNextTier} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              R{((groupStats?.nextDiscountTarget || 0) * (groupStats?.progressToNextTier || 0) / 100).toLocaleString()} / R{groupStats?.nextDiscountTarget.toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview" className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center">
            <MessageSquare className="h-4 w-4 mr-2" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center">
            <Activity className="h-4 w-4 mr-2" />
            Activity
          </TabsTrigger>
          <TabsTrigger value="lists" className="flex items-center">
            <List className="h-4 w-4 mr-2" />
            Lists
          </TabsTrigger>
          <TabsTrigger value="challenges" className="flex items-center">
            <Trophy className="h-4 w-4 mr-2" />
            Challenges
          </TabsTrigger>
          <TabsTrigger value="badges" className="flex items-center">
            <Star className="h-4 w-4 mr-2" />
            Badges
          </TabsTrigger>
          <TabsTrigger value="social" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Social
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <GroupActivityFeed 
                  groupId={groupId} 
                  showFilters={false}
                  maxHeight="300px"
                  limit={5}
                />
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="h-5 w-5 mr-2" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add Products to Cart
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <List className="h-4 w-4 mr-2" />
                  Create Shopping List
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Invite Members
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Target className="h-4 w-4 mr-2" />
                  View Discount Progress
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Group Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Group Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">R{groupStats?.totalSavings.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">Total Savings</div>
                  <div className="text-xs text-green-600 mt-1">+12% this month</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{groupStats?.totalMembers}</div>
                  <div className="text-sm text-muted-foreground">Active Members</div>
                  <div className="text-xs text-blue-600 mt-1">+3 this week</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">{groupStats?.progressToNextTier}%</div>
                  <div className="text-sm text-muted-foreground">Discount Progress</div>
                  <div className="text-xs text-purple-600 mt-1">15% tier next</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Chat Tab */}
        <TabsContent value="chat">
          <GroupChat groupId={groupId} groupName={groupName} />
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity">
          <GroupActivityFeed groupId={groupId} />
        </TabsContent>

        {/* Lists Tab */}
        <TabsContent value="lists">
          <CollaborativeShoppingLists groupId={groupId} />
        </TabsContent>

        {/* Challenges Tab */}
        <TabsContent value="challenges">
          <GroupChallenges groupId={groupId} />
        </TabsContent>

        {/* Badges Tab */}
        <TabsContent value="badges">
          <BadgesAndAchievements userId={user?._id} groupId={groupId} showUserProfile={true} />
        </TabsContent>

        {/* Social Tab */}
        <TabsContent value="social">
          <SocialProofAndLeaderboards groupId={groupId} showGlobalLeaderboards={true} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
