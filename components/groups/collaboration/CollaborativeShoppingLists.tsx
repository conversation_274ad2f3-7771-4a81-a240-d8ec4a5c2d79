"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Plus,
  List,
  Users,
  ThumbsUp,
  ThumbsDown,
  ShoppingCart,
  Star,
  Clock,
  CheckCircle,
  XCircle,
  MoreVertical,
  Edit,
  Trash2,
  Share,
  Eye,
  MessageSquare,
  Target,
  Calendar
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/context/AuthContext';

// Types
interface CollaborativeList {
  id: string;
  groupId: string;
  name: string;
  description: string;
  createdBy: string;
  createdByName: string;
  items: CollaborativeListItem[];
  collaborators: string[];
  isPublic: boolean;
  tags: string[];
  status: 'active' | 'completed' | 'archived';
  targetDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface CollaborativeListItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  productPrice: number;
  quantity: number;
  priority: 'low' | 'medium' | 'high';
  addedBy: string;
  addedByName: string;
  votes: ProductVote[];
  notes: string;
  status: 'pending' | 'approved' | 'rejected' | 'purchased';
  addedAt: Date;
}

interface ProductVote {
  userId: string;
  userName: string;
  vote: 'up' | 'down';
  reason?: string;
  createdAt: Date;
}

interface CollaborativeShoppingListsProps {
  groupId: string;
}

export function CollaborativeShoppingLists({ groupId }: CollaborativeShoppingListsProps) {
  const { user } = useAuth();
  const [lists, setLists] = useState<CollaborativeList[]>([]);
  const [selectedList, setSelectedList] = useState<CollaborativeList | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newListName, setNewListName] = useState('');
  const [newListDescription, setNewListDescription] = useState('');

  // Fetch collaborative lists
  const fetchLists = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/groups/${groupId}/lists`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setLists(result.data || []);
        if (result.data && result.data.length > 0 && !selectedList) {
          setSelectedList(result.data[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching lists:', error);
      // Mock data for development
      const mockLists = generateMockLists();
      setLists(mockLists);
      if (mockLists.length > 0) {
        setSelectedList(mockLists[0]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Create new list
  const createList = async () => {
    if (!newListName.trim() || !user) return;

    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/groups/${groupId}/lists`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: newListName,
          description: newListDescription,
          isPublic: true
        })
      });

      if (response.ok) {
        const result = await response.json();
        setLists(prev => [result.data, ...prev]);
        setSelectedList(result.data);
        setNewListName('');
        setNewListDescription('');
        setShowCreateDialog(false);
      }
    } catch (error) {
      console.error('Error creating list:', error);
      // Mock creation for demo
      const mockList: CollaborativeList = {
        id: `list_${Date.now()}`,
        groupId,
        name: newListName,
        description: newListDescription,
        createdBy: user._id,
        createdByName: user.name,
        items: [],
        collaborators: [user._id],
        isPublic: true,
        tags: [],
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setLists(prev => [mockList, ...prev]);
      setSelectedList(mockList);
      setNewListName('');
      setNewListDescription('');
      setShowCreateDialog(false);
    }
  };

  // Vote on item
  const voteOnItem = async (listId: string, itemId: string, vote: 'up' | 'down') => {
    if (!user) return;

    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/groups/${groupId}/lists/${listId}/items/${itemId}/vote`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ vote })
      });

      if (response.ok) {
        // Update local state
        setLists(prev => prev.map(list => {
          if (list.id === listId) {
            return {
              ...list,
              items: list.items.map(item => {
                if (item.id === itemId) {
                  const existingVoteIndex = item.votes.findIndex(v => v.userId === user._id);
                  const newVotes = [...item.votes];
                  
                  if (existingVoteIndex >= 0) {
                    newVotes[existingVoteIndex] = {
                      userId: user._id,
                      userName: user.name,
                      vote,
                      createdAt: new Date()
                    };
                  } else {
                    newVotes.push({
                      userId: user._id,
                      userName: user.name,
                      vote,
                      createdAt: new Date()
                    });
                  }
                  
                  return { ...item, votes: newVotes };
                }
                return item;
              })
            };
          }
          return list;
        }));

        // Update selected list if it's the current one
        if (selectedList && selectedList.id === listId) {
          setSelectedList(prev => {
            if (!prev) return null;
            return {
              ...prev,
              items: prev.items.map(item => {
                if (item.id === itemId) {
                  const existingVoteIndex = item.votes.findIndex(v => v.userId === user._id);
                  const newVotes = [...item.votes];
                  
                  if (existingVoteIndex >= 0) {
                    newVotes[existingVoteIndex] = {
                      userId: user._id,
                      userName: user.name,
                      vote,
                      createdAt: new Date()
                    };
                  } else {
                    newVotes.push({
                      userId: user._id,
                      userName: user.name,
                      vote,
                      createdAt: new Date()
                    });
                  }
                  
                  return { ...item, votes: newVotes };
                }
                return item;
              })
            };
          });
        }
      }
    } catch (error) {
      console.error('Error voting on item:', error);
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'purchased': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate vote score
  const getVoteScore = (votes: ProductVote[]) => {
    return votes.reduce((score, vote) => score + (vote.vote === 'up' ? 1 : -1), 0);
  };

  // Get user initials
  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  useEffect(() => {
    fetchLists();
  }, [groupId]);

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
      {/* Lists Sidebar */}
      <Card className="lg:col-span-1">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <List className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Shopping Lists</CardTitle>
            </div>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New List
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Shopping List</DialogTitle>
                  <DialogDescription>
                    Create a collaborative shopping list for your group
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="listName">List Name</Label>
                    <Input
                      id="listName"
                      value={newListName}
                      onChange={(e) => setNewListName(e.target.value)}
                      placeholder="Enter list name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="listDescription">Description</Label>
                    <Textarea
                      id="listDescription"
                      value={newListDescription}
                      onChange={(e) => setNewListDescription(e.target.value)}
                      placeholder="Describe the purpose of this list"
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={createList} disabled={!newListName.trim()}>
                      Create List
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-96">
            <div className="p-4 space-y-2">
              {lists.map((list) => (
                <div
                  key={list.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedList?.id === list.id 
                      ? 'bg-primary/10 border-primary' 
                      : 'hover:bg-muted border-border'
                  }`}
                  onClick={() => setSelectedList(list)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{list.name}</h4>
                      <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                        {list.description}
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">
                          {list.items.length} items
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          <Users className="h-3 w-3 mr-1" />
                          {list.collaborators.length}
                        </Badge>
                        <Badge className={`text-xs ${getStatusColor(list.status)}`}>
                          {list.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* List Details */}
      <Card className="lg:col-span-2">
        {selectedList ? (
          <>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{selectedList.name}</CardTitle>
                  <p className="text-muted-foreground mt-1">{selectedList.description}</p>
                  <div className="flex items-center space-x-2 mt-2">
                    <span className="text-sm text-muted-foreground">
                      Created by {selectedList.createdByName}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(selectedList.createdAt), { addSuffix: true })}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Share className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                  <Button variant="outline" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {selectedList.items.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
                      <ShoppingCart className="h-8 w-8 mb-2 opacity-50" />
                      <p className="text-sm">No items in this list yet</p>
                      <Button variant="outline" size="sm" className="mt-2">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Item
                      </Button>
                    </div>
                  ) : (
                    selectedList.items.map((item) => {
                      const voteScore = getVoteScore(item.votes);
                      const userVote = item.votes.find(v => v.userId === user?._id);
                      
                      return (
                        <div key={item.id} className="border rounded-lg p-4">
                          <div className="flex items-start space-x-4">
                            {/* Product Image */}
                            <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
                              <ShoppingCart className="h-6 w-6 text-muted-foreground" />
                            </div>

                            {/* Product Details */}
                            <div className="flex-1">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h4 className="font-medium">{item.productName}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    R{item.productPrice.toFixed(2)} × {item.quantity}
                                  </p>
                                  <div className="flex items-center space-x-2 mt-2">
                                    <Badge className={`text-xs ${getPriorityColor(item.priority)}`}>
                                      {item.priority} priority
                                    </Badge>
                                    <Badge className={`text-xs ${getStatusColor(item.status)}`}>
                                      {item.status}
                                    </Badge>
                                  </div>
                                </div>

                                {/* Voting */}
                                <div className="flex items-center space-x-2">
                                  <div className="flex items-center space-x-1">
                                    <Button
                                      variant={userVote?.vote === 'up' ? 'default' : 'outline'}
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={() => voteOnItem(selectedList.id, item.id, 'up')}
                                    >
                                      <ThumbsUp className="h-3 w-3" />
                                    </Button>
                                    <span className={`text-sm font-medium ${
                                      voteScore > 0 ? 'text-green-600' : 
                                      voteScore < 0 ? 'text-red-600' : 'text-muted-foreground'
                                    }`}>
                                      {voteScore}
                                    </span>
                                    <Button
                                      variant={userVote?.vote === 'down' ? 'default' : 'outline'}
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={() => voteOnItem(selectedList.id, item.id, 'down')}
                                    >
                                      <ThumbsDown className="h-3 w-3" />
                                    </Button>
                                  </div>
                                  <Button variant="outline" size="sm">
                                    <ShoppingCart className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>

                              {/* Notes */}
                              {item.notes && (
                                <p className="text-sm text-muted-foreground mt-2 bg-muted p-2 rounded">
                                  {item.notes}
                                </p>
                              )}

                              {/* Added By */}
                              <div className="flex items-center space-x-2 mt-2">
                                <Avatar className="w-5 h-5">
                                  <AvatarFallback className="text-xs">
                                    {getUserInitials(item.addedByName)}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-xs text-muted-foreground">
                                  Added by {item.addedByName} {formatDistanceToNow(new Date(item.addedAt), { addSuffix: true })}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </>
        ) : (
          <CardContent className="flex flex-col items-center justify-center h-64 text-muted-foreground">
            <List className="h-12 w-12 mb-4 opacity-50" />
            <p className="text-lg font-medium">No list selected</p>
            <p className="text-sm">Choose a list from the sidebar to view its items</p>
          </CardContent>
        )}
      </Card>
    </div>
  );
}

// Mock data generator for development
function generateMockLists(): CollaborativeList[] {
  return [
    {
      id: 'list_1',
      groupId: 'group_1',
      name: 'Tech Essentials',
      description: 'Essential tech items for our group buying',
      createdBy: 'user_1',
      createdByName: 'Alice Johnson',
      items: [
        {
          id: 'item_1',
          productId: 'prod_1',
          productName: 'Wireless Headphones',
          productImage: '/products/headphones.jpg',
          productPrice: 299.99,
          quantity: 2,
          priority: 'high',
          addedBy: 'user_1',
          addedByName: 'Alice Johnson',
          votes: [
            { userId: 'user_2', userName: 'Bob Smith', vote: 'up', createdAt: new Date() },
            { userId: 'user_3', userName: 'Carol Davis', vote: 'up', createdAt: new Date() }
          ],
          notes: 'Great reviews and perfect for our budget',
          status: 'approved',
          addedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          id: 'item_2',
          productId: 'prod_2',
          productName: 'Smart Watch',
          productImage: '/products/smartwatch.jpg',
          productPrice: 199.99,
          quantity: 1,
          priority: 'medium',
          addedBy: 'user_2',
          addedByName: 'Bob Smith',
          votes: [
            { userId: 'user_1', userName: 'Alice Johnson', vote: 'up', createdAt: new Date() }
          ],
          notes: 'Good fitness tracking features',
          status: 'pending',
          addedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
        }
      ],
      collaborators: ['user_1', 'user_2', 'user_3'],
      isPublic: true,
      tags: ['electronics', 'tech'],
      status: 'active',
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: 'list_2',
      groupId: 'group_1',
      name: 'Home Essentials',
      description: 'Home and kitchen items for bulk buying',
      createdBy: 'user_3',
      createdByName: 'Carol Davis',
      items: [],
      collaborators: ['user_3', 'user_4'],
      isPublic: true,
      tags: ['home', 'kitchen'],
      status: 'active',
      createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000)
    }
  ];
}
