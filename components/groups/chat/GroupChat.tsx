"use client"

import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Send, 
  Smile, 
  Paperclip, 
  Image as ImageIcon, 
  MoreVertical,
  Reply,
  Heart,
  ThumbsUp,
  Laugh,
  Users,
  MessageCircle,
  Clock,
  CheckCheck,
  Mic,
  MicOff
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { formatDistanceToNow } from 'date-fns';

// Types
interface GroupMessage {
  id: string;
  groupId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'product' | 'system';
  metadata?: {
    productId?: string;
    fileName?: string;
    fileUrl?: string;
    imageUrl?: string;
    productName?: string;
    productPrice?: number;
    productImage?: string;
  };
  reactions: MessageReaction[];
  threadId?: string;
  replyTo?: string;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface MessageReaction {
  userId: string;
  userName: string;
  emoji: string;
  createdAt: Date;
}

interface OnlineMember {
  userId: string;
  userName: string;
  userAvatar?: string;
  isOnline: boolean;
}

interface GroupChatProps {
  groupId: string;
  groupName: string;
}

export function GroupChat({ groupId, groupName }: GroupChatProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<GroupMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [onlineMembers, setOnlineMembers] = useState<OnlineMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<GroupMessage | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Fetch messages and online members
  const fetchChatData = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      
      const [messagesResponse, membersResponse] = await Promise.all([
        fetch(`/api/groups/${groupId}/chat/messages`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`/api/groups/${groupId}/members/online`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (messagesResponse.ok && membersResponse.ok) {
        const [messagesData, membersData] = await Promise.all([
          messagesResponse.json(),
          membersResponse.json()
        ]);
        
        setMessages(messagesData.data || []);
        setOnlineMembers(membersData.data || []);
      }
    } catch (error) {
      console.error('Error fetching chat data:', error);
      // Mock data for development
      setMessages(generateMockMessages());
      setOnlineMembers(generateMockOnlineMembers());
    } finally {
      setIsLoading(false);
    }
  };

  // Send message
  const sendMessage = async () => {
    if (!newMessage.trim() || !user || isSending) return;

    setIsSending(true);
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/groups/${groupId}/chat/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: newMessage,
          type: 'text',
          replyTo: replyingTo?.id
        })
      });

      if (response.ok) {
        const result = await response.json();
        setMessages(prev => [result.data, ...prev]);
        setNewMessage('');
        setReplyingTo(null);
        scrollToBottom();
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Add message optimistically for demo
      const mockMessage: GroupMessage = {
        id: `msg_${Date.now()}`,
        groupId,
        senderId: user._id,
        senderName: user.name,
        senderAvatar: user.avatar,
        content: newMessage,
        type: 'text',
        reactions: [],
        isEdited: false,
        isDeleted: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setMessages(prev => [mockMessage, ...prev]);
      setNewMessage('');
      setReplyingTo(null);
    } finally {
      setIsSending(false);
    }
  };

  // Add reaction to message
  const addReaction = async (messageId: string, emoji: string) => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/groups/${groupId}/chat/messages/${messageId}/reactions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ emoji })
      });

      if (response.ok) {
        // Update message reactions locally
        setMessages(prev => prev.map(msg => {
          if (msg.id === messageId) {
            const existingReaction = msg.reactions.find(r => r.userId === user?._id);
            if (existingReaction) {
              // Update existing reaction
              return {
                ...msg,
                reactions: msg.reactions.map(r => 
                  r.userId === user?._id ? { ...r, emoji } : r
                )
              };
            } else {
              // Add new reaction
              return {
                ...msg,
                reactions: [...msg.reactions, {
                  userId: user?._id || '',
                  userName: user?.name || '',
                  emoji,
                  createdAt: new Date()
                }]
              };
            }
          }
          return msg;
        }));
      }
    } catch (error) {
      console.error('Error adding reaction:', error);
    }
    setShowEmojiPicker(null);
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Format message time
  const formatMessageTime = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  // Get user initials
  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Common emoji reactions
  const commonEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡'];

  useEffect(() => {
    fetchChatData();
    
    // Set up real-time updates (WebSocket would be implemented here)
    const interval = setInterval(fetchChatData, 5000); // Poll every 5 seconds for demo
    
    return () => clearInterval(interval);
  }, [groupId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full flex flex-col">
      {/* Chat Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageCircle className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Group Chat</CardTitle>
            <Badge variant="secondary" className="ml-2">
              <Users className="h-3 w-3 mr-1" />
              {onlineMembers.filter(m => m.isOnline).length} online
            </Badge>
          </div>
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      {/* Online Members */}
      <div className="px-6 pb-3">
        <div className="flex items-center space-x-2 overflow-x-auto">
          {onlineMembers.filter(m => m.isOnline).map((member) => (
            <div key={member.userId} className="flex items-center space-x-1 bg-green-50 rounded-full px-2 py-1 min-w-fit">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-green-700">{member.userName}</span>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Messages Area */}
      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full px-6 py-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.senderId === user?._id ? 'justify-end' : 'justify-start'}`}>
                <div className={`flex space-x-3 max-w-[70%] ${message.senderId === user?._id ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  {/* Avatar */}
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={message.senderAvatar} />
                    <AvatarFallback className="text-xs">
                      {getUserInitials(message.senderName)}
                    </AvatarFallback>
                  </Avatar>

                  {/* Message Content */}
                  <div className={`flex flex-col space-y-1 ${message.senderId === user?._id ? 'items-end' : 'items-start'}`}>
                    {/* Sender Name and Time */}
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                      <span className="font-medium">{message.senderName}</span>
                      <span>{formatMessageTime(message.createdAt)}</span>
                    </div>

                    {/* Reply Context */}
                    {message.replyTo && (
                      <div className="bg-muted p-2 rounded text-xs border-l-2 border-primary">
                        <span className="text-muted-foreground">Replying to a message</span>
                      </div>
                    )}

                    {/* Message Bubble */}
                    <div className={`relative group rounded-lg px-3 py-2 ${
                      message.senderId === user?._id 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted'
                    }`}>
                      <p className="text-sm">{message.content}</p>

                      {/* Message Actions */}
                      <div className="absolute -top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex items-center space-x-1 bg-background border rounded-full px-2 py-1 shadow-sm">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => setShowEmojiPicker(showEmojiPicker === message.id ? null : message.id)}
                          >
                            <Smile className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => setReplyingTo(message)}
                          >
                            <Reply className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Emoji Picker */}
                      {showEmojiPicker === message.id && (
                        <div className="absolute top-full mt-1 bg-background border rounded-lg shadow-lg p-2 z-10">
                          <div className="flex space-x-1">
                            {commonEmojis.map((emoji) => (
                              <Button
                                key={emoji}
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => addReaction(message.id, emoji)}
                              >
                                {emoji}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Reactions */}
                    {message.reactions.length > 0 && (
                      <div className="flex items-center space-x-1 mt-1">
                        {Object.entries(
                          message.reactions.reduce((acc, reaction) => {
                            acc[reaction.emoji] = (acc[reaction.emoji] || 0) + 1;
                            return acc;
                          }, {} as Record<string, number>)
                        ).map(([emoji, count]) => (
                          <Badge
                            key={emoji}
                            variant="secondary"
                            className="text-xs px-2 py-0 cursor-pointer hover:bg-secondary/80"
                            onClick={() => addReaction(message.id, emoji)}
                          >
                            {emoji} {count}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>

      <Separator />

      {/* Message Input */}
      <div className="p-4">
        {/* Reply Context */}
        {replyingTo && (
          <div className="mb-2 p-2 bg-muted rounded-lg border-l-2 border-primary">
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">
                <span className="font-medium">Replying to {replyingTo.senderName}</span>
                <p className="truncate mt-1">{replyingTo.content}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setReplyingTo(null)}
              >
                ×
              </Button>
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Paperclip className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <ImageIcon className="h-4 w-4" />
          </Button>
          
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="pr-10"
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2"
              onClick={() => setShowEmojiPicker(showEmojiPicker === 'input' ? null : 'input')}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          <Button
            variant="ghost"
            size="sm"
            className={isRecording ? 'text-red-500' : ''}
            onClick={() => setIsRecording(!isRecording)}
          >
            {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
          </Button>

          <Button 
            onClick={sendMessage} 
            disabled={!newMessage.trim() || isSending}
            size="sm"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {/* Emoji Picker for Input */}
        {showEmojiPicker === 'input' && (
          <div className="absolute bottom-full mb-2 right-4 bg-background border rounded-lg shadow-lg p-2 z-10">
            <div className="grid grid-cols-6 gap-1">
              {commonEmojis.map((emoji) => (
                <Button
                  key={emoji}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    setNewMessage(prev => prev + emoji);
                    setShowEmojiPicker(null);
                    inputRef.current?.focus();
                  }}
                >
                  {emoji}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}

// Mock data generators for development
function generateMockMessages(): GroupMessage[] {
  return [
    {
      id: 'msg_1',
      groupId: 'group_1',
      senderId: 'user_1',
      senderName: 'Alice Johnson',
      senderAvatar: '/avatars/alice.jpg',
      content: 'Hey everyone! I found some great deals on electronics. Should we add them to our group order?',
      type: 'text',
      reactions: [
        { userId: 'user_2', userName: 'Bob Smith', emoji: '👍', createdAt: new Date() },
        { userId: 'user_3', userName: 'Carol Davis', emoji: '❤️', createdAt: new Date() }
      ],
      isEdited: false,
      isDeleted: false,
      createdAt: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
      updatedAt: new Date(Date.now() - 10 * 60 * 1000)
    },
    {
      id: 'msg_2',
      groupId: 'group_1',
      senderId: 'user_2',
      senderName: 'Bob Smith',
      content: 'That sounds great! What kind of electronics are you thinking about?',
      type: 'text',
      reactions: [],
      isEdited: false,
      isDeleted: false,
      createdAt: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
      updatedAt: new Date(Date.now() - 8 * 60 * 1000)
    },
    {
      id: 'msg_3',
      groupId: 'group_1',
      senderId: 'user_3',
      senderName: 'Carol Davis',
      content: 'I\'m interested! We\'re so close to the next discount tier too 🎉',
      type: 'text',
      reactions: [
        { userId: 'user_1', userName: 'Alice Johnson', emoji: '🎉', createdAt: new Date() }
      ],
      isEdited: false,
      isDeleted: false,
      createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      updatedAt: new Date(Date.now() - 5 * 60 * 1000)
    }
  ];
}

function generateMockOnlineMembers(): OnlineMember[] {
  return [
    { userId: 'user_1', userName: 'Alice Johnson', userAvatar: '/avatars/alice.jpg', isOnline: true },
    { userId: 'user_2', userName: 'Bob Smith', isOnline: true },
    { userId: 'user_3', userName: 'Carol Davis', isOnline: false },
    { userId: 'user_4', userName: 'David Wilson', isOnline: true },
    { userId: 'user_5', userName: 'Eva Brown', isOnline: false }
  ];
}
