"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Activity,
  ShoppingCart,
  UserPlus,
  UserMinus,
  Target,
  Trophy,
  MessageSquare,
  ThumbsUp,
  Package,
  DollarSign,
  TrendingUp,
  Calendar,
  Clock,
  RefreshCw,
  Filter,
  Eye,
  MoreHorizontal
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/context/AuthContext';

// Types
interface GroupActivity {
  id: string;
  groupId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  type: 'member_joined' | 'member_left' | 'product_added' | 'product_removed' | 'order_placed' | 'discount_achieved' | 'milestone_reached' | 'chat_message' | 'product_voted' | 'list_created' | 'challenge_completed';
  title: string;
  description: string;
  metadata?: {
    productId?: string;
    productName?: string;
    productImage?: string;
    quantity?: number;
    price?: number;
    discountTier?: string;
    milestone?: string;
    challengeId?: string;
    listId?: string;
    orderId?: string;
  };
  isPublic: boolean;
  createdAt: Date;
}

interface ActivityFilter {
  type: 'all' | 'members' | 'products' | 'orders' | 'milestones' | 'chat';
  timeRange: 'today' | 'week' | 'month' | 'all';
}

interface GroupActivityFeedProps {
  groupId: string;
  showFilters?: boolean;
  maxHeight?: string;
  limit?: number;
}

export function GroupActivityFeed({ 
  groupId, 
  showFilters = true, 
  maxHeight = "400px",
  limit = 20 
}: GroupActivityFeedProps) {
  const { user } = useAuth();
  const [activities, setActivities] = useState<GroupActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [filter, setFilter] = useState<ActivityFilter>({
    type: 'all',
    timeRange: 'week'
  });

  // Fetch activities
  const fetchActivities = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      
      const queryParams = new URLSearchParams({
        type: filter.type,
        timeRange: filter.timeRange,
        limit: limit.toString()
      });

      const response = await fetch(`/api/groups/${groupId}/activities?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setActivities(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
      // Mock data for development
      setActivities(generateMockActivities());
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh activities
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchActivities();
    setIsRefreshing(false);
  };

  // Get activity icon
  const getActivityIcon = (type: GroupActivity['type']) => {
    switch (type) {
      case 'member_joined':
        return <UserPlus className="h-4 w-4 text-green-600" />;
      case 'member_left':
        return <UserMinus className="h-4 w-4 text-red-600" />;
      case 'product_added':
        return <ShoppingCart className="h-4 w-4 text-blue-600" />;
      case 'product_removed':
        return <Package className="h-4 w-4 text-orange-600" />;
      case 'order_placed':
        return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'discount_achieved':
        return <Target className="h-4 w-4 text-purple-600" />;
      case 'milestone_reached':
        return <Trophy className="h-4 w-4 text-yellow-600" />;
      case 'chat_message':
        return <MessageSquare className="h-4 w-4 text-blue-600" />;
      case 'product_voted':
        return <ThumbsUp className="h-4 w-4 text-green-600" />;
      case 'list_created':
        return <Calendar className="h-4 w-4 text-indigo-600" />;
      case 'challenge_completed':
        return <Trophy className="h-4 w-4 text-gold-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  // Get activity color
  const getActivityColor = (type: GroupActivity['type']) => {
    switch (type) {
      case 'member_joined':
      case 'product_added':
      case 'order_placed':
      case 'product_voted':
        return 'bg-green-50 border-green-200';
      case 'member_left':
        return 'bg-red-50 border-red-200';
      case 'discount_achieved':
      case 'milestone_reached':
      case 'challenge_completed':
        return 'bg-purple-50 border-purple-200';
      case 'chat_message':
        return 'bg-blue-50 border-blue-200';
      case 'list_created':
        return 'bg-indigo-50 border-indigo-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  // Format activity time
  const formatActivityTime = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  // Get user initials
  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Filter activities
  const filteredActivities = activities.filter(activity => {
    if (filter.type === 'all') return true;
    
    switch (filter.type) {
      case 'members':
        return ['member_joined', 'member_left'].includes(activity.type);
      case 'products':
        return ['product_added', 'product_removed', 'product_voted'].includes(activity.type);
      case 'orders':
        return ['order_placed', 'discount_achieved'].includes(activity.type);
      case 'milestones':
        return ['milestone_reached', 'challenge_completed'].includes(activity.type);
      case 'chat':
        return activity.type === 'chat_message';
      default:
        return true;
    }
  });

  useEffect(() => {
    fetchActivities();
  }, [groupId, filter]);

  return (
    <Card className="h-full">
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Group Activity</CardTitle>
            <Badge variant="secondary">{filteredActivities.length}</Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="flex flex-wrap gap-2 mt-3">
            <div className="flex items-center space-x-1">
              <Filter className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Type:</span>
            </div>
            {['all', 'members', 'products', 'orders', 'milestones', 'chat'].map((type) => (
              <Button
                key={type}
                variant={filter.type === type ? 'default' : 'outline'}
                size="sm"
                className="h-6 text-xs"
                onClick={() => setFilter(prev => ({ ...prev, type: type as any }))}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Button>
            ))}
          </div>
        )}
      </CardHeader>

      <Separator />

      {/* Activities List */}
      <CardContent className="p-0">
        <ScrollArea className="h-full" style={{ maxHeight }}>
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : filteredActivities.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <Activity className="h-8 w-8 mb-2 opacity-50" />
              <p className="text-sm">No activities found</p>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {filteredActivities.map((activity, index) => (
                <div key={activity.id}>
                  <div className={`flex items-start space-x-3 p-3 rounded-lg border ${getActivityColor(activity.type)}`}>
                    {/* Activity Icon */}
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.type)}
                    </div>

                    {/* Activity Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          {/* User Info */}
                          <div className="flex items-center space-x-2 mb-1">
                            <Avatar className="w-6 h-6">
                              <AvatarImage src={activity.userAvatar} />
                              <AvatarFallback className="text-xs">
                                {getUserInitials(activity.userName)}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-sm font-medium">{activity.userName}</span>
                            <span className="text-xs text-muted-foreground">
                              {formatActivityTime(activity.createdAt)}
                            </span>
                          </div>

                          {/* Activity Title */}
                          <h4 className="text-sm font-medium text-foreground mb-1">
                            {activity.title}
                          </h4>

                          {/* Activity Description */}
                          <p className="text-xs text-muted-foreground">
                            {activity.description}
                          </p>

                          {/* Activity Metadata */}
                          {activity.metadata && (
                            <div className="mt-2 flex flex-wrap gap-1">
                              {activity.metadata.productName && (
                                <Badge variant="outline" className="text-xs">
                                  {activity.metadata.productName}
                                </Badge>
                              )}
                              {activity.metadata.price && (
                                <Badge variant="outline" className="text-xs">
                                  R{activity.metadata.price.toFixed(2)}
                                </Badge>
                              )}
                              {activity.metadata.quantity && (
                                <Badge variant="outline" className="text-xs">
                                  Qty: {activity.metadata.quantity}
                                </Badge>
                              )}
                              {activity.metadata.discountTier && (
                                <Badge variant="secondary" className="text-xs">
                                  {activity.metadata.discountTier}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Action Button */}
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Separator */}
                  {index < filteredActivities.length - 1 && (
                    <Separator className="my-2" />
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

// Mock data generator for development
function generateMockActivities(): GroupActivity[] {
  return [
    {
      id: 'act_1',
      groupId: 'group_1',
      userId: 'user_1',
      userName: 'Alice Johnson',
      userAvatar: '/avatars/alice.jpg',
      type: 'product_added',
      title: 'Added product to cart',
      description: 'Added Wireless Headphones to the group cart',
      metadata: {
        productId: 'prod_1',
        productName: 'Wireless Headphones',
        productImage: '/products/headphones.jpg',
        quantity: 2,
        price: 299.99
      },
      isPublic: true,
      createdAt: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
    },
    {
      id: 'act_2',
      groupId: 'group_1',
      userId: 'user_2',
      userName: 'Bob Smith',
      type: 'member_joined',
      title: 'Joined the group',
      description: 'Welcome to the Electronics Buyers group!',
      isPublic: true,
      createdAt: new Date(Date.now() - 15 * 60 * 1000) // 15 minutes ago
    },
    {
      id: 'act_3',
      groupId: 'group_1',
      userId: 'user_3',
      userName: 'Carol Davis',
      type: 'discount_achieved',
      title: 'Discount tier unlocked!',
      description: 'Group reached 10% discount tier with total order value of R5,000',
      metadata: {
        discountTier: '10% Discount Tier'
      },
      isPublic: true,
      createdAt: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
    },
    {
      id: 'act_4',
      groupId: 'group_1',
      userId: 'user_4',
      userName: 'David Wilson',
      type: 'product_voted',
      title: 'Voted on product',
      description: 'Voted up Smart Watch in the group wishlist',
      metadata: {
        productName: 'Smart Watch'
      },
      isPublic: true,
      createdAt: new Date(Date.now() - 45 * 60 * 1000) // 45 minutes ago
    },
    {
      id: 'act_5',
      groupId: 'group_1',
      userId: 'user_1',
      userName: 'Alice Johnson',
      type: 'list_created',
      title: 'Created shopping list',
      description: 'Created "Tech Essentials" collaborative shopping list',
      metadata: {
        listId: 'list_1'
      },
      isPublic: true,
      createdAt: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
    },
    {
      id: 'act_6',
      groupId: 'group_1',
      userId: 'user_5',
      userName: 'Eva Brown',
      type: 'milestone_reached',
      title: 'Milestone achieved!',
      description: 'Group reached 50 total orders milestone',
      metadata: {
        milestone: '50 Orders'
      },
      isPublic: true,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
    }
  ];
}
