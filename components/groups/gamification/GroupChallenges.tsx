"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Trophy,
  Target,
  Users,
  Clock,
  Star,
  Zap,
  Gift,
  Crown,
  Medal,
  Flame,
  TrendingUp,
  Calendar,
  Play,
  CheckCircle,
  XCircle,
  Plus,
  Eye,
  Share2,
  MoreHorizontal
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { useAuth } from '@/context/AuthContext';

// Types
interface GroupChallenge {
  id: string;
  groupId: string;
  title: string;
  description: string;
  type: 'savings_target' | 'bulk_purchase' | 'member_growth' | 'activity_streak' | 'product_discovery' | 'collaboration_score' | 'speed_challenge' | 'eco_friendly';
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary';
  targetValue: number;
  currentValue: number;
  unit: string;
  startDate: Date;
  endDate: Date;
  reward: ChallengeReward;
  participants: ChallengeParticipant[];
  status: 'upcoming' | 'active' | 'completed' | 'expired' | 'cancelled';
  rules: string[];
  milestones: ChallengeMilestone[];
  leaderboard: ChallengeLeaderboardEntry[];
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
}

interface ChallengeReward {
  type: 'discount' | 'points' | 'badge' | 'feature_unlock' | 'cash_reward' | 'product_voucher';
  value: number;
  description: string;
  imageUrl?: string;
  expiresAt?: Date;
}

interface ChallengeParticipant {
  userId: string;
  userName: string;
  userAvatar?: string;
  joinedAt: Date;
  progress: number;
  rank: number;
  isActive: boolean;
}

interface ChallengeMilestone {
  id: string;
  title: string;
  description: string;
  targetValue: number;
  reward: ChallengeReward;
  isCompleted: boolean;
  completedBy: string[];
  completedAt?: Date;
}

interface ChallengeLeaderboardEntry {
  userId: string;
  userName: string;
  userAvatar?: string;
  score: number;
  rank: number;
  progress: number;
  badges: string[];
  lastActivity: Date;
}

interface GroupChallengesProps {
  groupId: string;
}

export function GroupChallenges({ groupId }: GroupChallengesProps) {
  const { user } = useAuth();
  const [challenges, setChallenges] = useState<GroupChallenge[]>([]);
  const [selectedChallenge, setSelectedChallenge] = useState<GroupChallenge | null>(null);
  const [activeTab, setActiveTab] = useState('active');
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Fetch group challenges
  const fetchChallenges = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');

      const response = await fetch(`/api/groups/${groupId}/challenges`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setChallenges(result.data || []);
        if (result.data && result.data.length > 0 && !selectedChallenge) {
          setSelectedChallenge(result.data[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching challenges:', error);
      // Mock data for development
      const mockChallenges = generateMockChallenges();
      setChallenges(mockChallenges);
      if (mockChallenges.length > 0) {
        setSelectedChallenge(mockChallenges[0]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Join challenge
  const joinChallenge = async (challengeId: string) => {
    if (!user) return;

    try {
      const token = localStorage.getItem('token');

      const response = await fetch(`/api/groups/${groupId}/challenges/${challengeId}/join`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Update local state
        setChallenges(prev => prev.map(challenge => {
          if (challenge.id === challengeId) {
            const newParticipant: ChallengeParticipant = {
              userId: user._id,
              userName: user.name,
              userAvatar: user.avatar,
              joinedAt: new Date(),
              progress: 0,
              rank: challenge.participants.length + 1,
              isActive: true
            };
            return {
              ...challenge,
              participants: [...challenge.participants, newParticipant]
            };
          }
          return challenge;
        }));

        // Update selected challenge if it's the current one
        if (selectedChallenge && selectedChallenge.id === challengeId) {
          setSelectedChallenge(prev => {
            if (!prev) return null;
            const newParticipant: ChallengeParticipant = {
              userId: user._id,
              userName: user.name,
              userAvatar: user.avatar,
              joinedAt: new Date(),
              progress: 0,
              rank: prev.participants.length + 1,
              isActive: true
            };
            return {
              ...prev,
              participants: [...prev.participants, newParticipant]
            };
          });
        }
      }
    } catch (error) {
      console.error('Error joining challenge:', error);
    }
  };

  // Get challenge type icon
  const getChallengeTypeIcon = (type: string) => {
    switch (type) {
      case 'savings_target': return Target;
      case 'bulk_purchase': return TrendingUp;
      case 'member_growth': return Users;
      case 'activity_streak': return Flame;
      case 'product_discovery': return Star;
      case 'collaboration_score': return Users;
      case 'speed_challenge': return Zap;
      case 'eco_friendly': return Star;
      default: return Trophy;
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'legendary': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'upcoming': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-purple-100 text-purple-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get reward icon
  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'discount': return Target;
      case 'points': return Star;
      case 'badge': return Medal;
      case 'cash_reward': return Gift;
      case 'product_voucher': return Gift;
      default: return Gift;
    }
  };

  // Check if user is participating
  const isUserParticipating = (challenge: GroupChallenge) => {
    return challenge.participants.some(p => p.userId === user?._id);
  };

  // Get user progress in challenge
  const getUserProgress = (challenge: GroupChallenge) => {
    const participant = challenge.participants.find(p => p.userId === user?._id);
    return participant ? participant.progress : 0;
  };

  // Get user rank in challenge
  const getUserRank = (challenge: GroupChallenge) => {
    const participant = challenge.participants.find(p => p.userId === user?._id);
    return participant ? participant.rank : null;
  };

  // Calculate progress percentage
  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  // Get user initials
  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Filter challenges by status
  const getFilteredChallenges = (status: string) => {
    if (status === 'active') {
      return challenges.filter(c => c.status === 'active');
    } else if (status === 'upcoming') {
      return challenges.filter(c => c.status === 'upcoming');
    } else if (status === 'completed') {
      return challenges.filter(c => c.status === 'completed');
    }
    return challenges;
  };

  useEffect(() => {
    fetchChallenges();
  }, [groupId]);

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <Trophy className="h-6 w-6 text-yellow-600" />
          <div>
            <h2 className="text-2xl font-bold">Group Challenges</h2>
            <p className="text-muted-foreground">Compete, collaborate, and earn rewards together</p>
          </div>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Challenge
        </Button>
      </div>

      {/* Challenge Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="active" className="flex items-center">
            <Play className="h-4 w-4 mr-2" />
            Active ({getFilteredChallenges('active').length})
          </TabsTrigger>
          <TabsTrigger value="upcoming" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            Upcoming ({getFilteredChallenges('upcoming').length})
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center">
            <CheckCircle className="h-4 w-4 mr-2" />
            Completed ({getFilteredChallenges('completed').length})
          </TabsTrigger>
          <TabsTrigger value="leaderboard" className="flex items-center">
            <Crown className="h-4 w-4 mr-2" />
            Leaderboard
          </TabsTrigger>
        </TabsList>

        {/* Active Challenges */}
        <TabsContent value="active" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {getFilteredChallenges('active').map((challenge) => {
              const TypeIcon = getChallengeTypeIcon(challenge.type);
              const RewardIcon = getRewardIcon(challenge.reward.type);
              const isParticipating = isUserParticipating(challenge);
              const userProgress = getUserProgress(challenge);
              const userRank = getUserRank(challenge);

              return (
                <Card key={challenge.id} className="relative overflow-hidden">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        <TypeIcon className="h-5 w-5 text-primary" />
                        <Badge className={getDifficultyColor(challenge.difficulty)}>
                          {challenge.difficulty}
                        </Badge>
                      </div>
                      <Badge className={getStatusColor(challenge.status)}>
                        {challenge.status}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{challenge.title}</CardTitle>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {challenge.description}
                    </p>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Progress */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span className="font-medium">
                          {challenge.currentValue.toLocaleString()} / {challenge.targetValue.toLocaleString()} {challenge.unit}
                        </span>
                      </div>
                      <Progress value={getProgressPercentage(challenge.currentValue, challenge.targetValue)} />
                      <div className="text-xs text-muted-foreground">
                        {getProgressPercentage(challenge.currentValue, challenge.targetValue).toFixed(1)}% complete
                      </div>
                    </div>

                    {/* Participants */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{challenge.participants.length} participants</span>
                      </div>
                      <div className="flex -space-x-2">
                        {challenge.participants.slice(0, 3).map((participant) => (
                          <Avatar key={participant.userId} className="w-6 h-6 border-2 border-background">
                            <AvatarImage src={participant.userAvatar} />
                            <AvatarFallback className="text-xs">
                              {getUserInitials(participant.userName)}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                        {challenge.participants.length > 3 && (
                          <div className="w-6 h-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                            <span className="text-xs">+{challenge.participants.length - 3}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Reward */}
                    <div className="flex items-center space-x-2 p-2 bg-muted rounded-lg">
                      <RewardIcon className="h-4 w-4 text-yellow-600" />
                      <div className="flex-1">
                        <div className="text-sm font-medium">{challenge.reward.description}</div>
                        <div className="text-xs text-muted-foreground">
                          {challenge.reward.type === 'discount' && `${challenge.reward.value}% off`}
                          {challenge.reward.type === 'points' && `${challenge.reward.value} points`}
                          {challenge.reward.type === 'cash_reward' && `R${challenge.reward.value}`}
                        </div>
                      </div>
                    </div>

                    {/* User Status */}
                    {isParticipating && (
                      <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm text-green-800">Participating</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-green-800">
                            {userProgress.toLocaleString()} {challenge.unit}
                          </div>
                          {userRank && (
                            <div className="text-xs text-green-600">
                              Rank #{userRank}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Time Remaining */}
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>Ends {formatDistanceToNow(new Date(challenge.endDate), { addSuffix: true })}</span>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      {!isParticipating ? (
                        <Button
                          className="flex-1"
                          onClick={() => joinChallenge(challenge.id)}
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Join Challenge
                        </Button>
                      ) : (
                        <Button variant="outline" className="flex-1">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Upcoming Challenges */}
        <TabsContent value="upcoming" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {getFilteredChallenges('upcoming').map((challenge) => {
              const TypeIcon = getChallengeTypeIcon(challenge.type);

              return (
                <Card key={challenge.id} className="relative">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        <TypeIcon className="h-5 w-5 text-primary" />
                        <Badge className={getDifficultyColor(challenge.difficulty)}>
                          {challenge.difficulty}
                        </Badge>
                      </div>
                      <Badge className={getStatusColor(challenge.status)}>
                        {challenge.status}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{challenge.title}</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {challenge.description}
                    </p>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="text-center p-4 bg-muted rounded-lg">
                      <Calendar className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <div className="text-sm font-medium">Starts {format(new Date(challenge.startDate), 'MMM dd, yyyy')}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(challenge.startDate), { addSuffix: true })}
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span>Target:</span>
                      <span className="font-medium">{challenge.targetValue.toLocaleString()} {challenge.unit}</span>
                    </div>

                    <Button className="w-full" disabled>
                      <Clock className="h-4 w-4 mr-2" />
                      Coming Soon
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Completed Challenges */}
        <TabsContent value="completed" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {getFilteredChallenges('completed').map((challenge) => {
              const TypeIcon = getChallengeTypeIcon(challenge.type);
              const isParticipating = isUserParticipating(challenge);
              const userRank = getUserRank(challenge);

              return (
                <Card key={challenge.id} className="relative opacity-75">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        <TypeIcon className="h-5 w-5 text-primary" />
                        <Badge className={getDifficultyColor(challenge.difficulty)}>
                          {challenge.difficulty}
                        </Badge>
                      </div>
                      <Badge className={getStatusColor(challenge.status)}>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {challenge.status}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{challenge.title}</CardTitle>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                      <Trophy className="h-8 w-8 mx-auto mb-2 text-green-600" />
                      <div className="text-sm font-medium text-green-800">Challenge Completed!</div>
                      <div className="text-xs text-green-600">
                        {challenge.completedAt && format(new Date(challenge.completedAt), 'MMM dd, yyyy')}
                      </div>
                    </div>

                    {isParticipating && userRank && (
                      <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <Medal className="h-6 w-6 mx-auto mb-1 text-yellow-600" />
                        <div className="text-sm font-medium text-yellow-800">
                          You finished #{userRank}
                        </div>
                        {userRank <= 3 && (
                          <div className="text-xs text-yellow-600">
                            🎉 Top 3 finish!
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex items-center justify-between text-sm">
                      <span>Final Result:</span>
                      <span className="font-medium">
                        {challenge.currentValue.toLocaleString()} / {challenge.targetValue.toLocaleString()} {challenge.unit}
                      </span>
                    </div>

                    <Button variant="outline" className="w-full">
                      <Eye className="h-4 w-4 mr-2" />
                      View Results
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Leaderboard */}
        <TabsContent value="leaderboard" className="space-y-4">
          {selectedChallenge && selectedChallenge.status === 'active' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Crown className="h-5 w-5 mr-2 text-yellow-600" />
                  {selectedChallenge.title} - Leaderboard
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {selectedChallenge.leaderboard.map((entry, index) => (
                      <div key={entry.userId} className={`flex items-center space-x-3 p-3 rounded-lg ${
                        entry.userId === user?._id ? 'bg-primary/10 border border-primary/20' : 'bg-muted/50'
                      }`}>
                        {/* Rank */}
                        <div className="flex items-center justify-center w-8 h-8">
                          {entry.rank === 1 && <Crown className="h-5 w-5 text-yellow-500" />}
                          {entry.rank === 2 && <Medal className="h-5 w-5 text-gray-400" />}
                          {entry.rank === 3 && <Medal className="h-5 w-5 text-amber-600" />}
                          {entry.rank > 3 && (
                            <span className="text-sm font-medium text-muted-foreground">#{entry.rank}</span>
                          )}
                        </div>

                        {/* User Info */}
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={entry.userAvatar} />
                          <AvatarFallback>
                            {getUserInitials(entry.userName)}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1">
                          <div className="font-medium">{entry.userName}</div>
                          <div className="text-sm text-muted-foreground">
                            {entry.score.toLocaleString()} {selectedChallenge.unit}
                          </div>
                        </div>

                        {/* Progress */}
                        <div className="text-right">
                          <div className="text-sm font-medium">{entry.progress.toFixed(1)}%</div>
                          <div className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(entry.lastActivity), { addSuffix: true })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Mock data generator for development
function generateMockChallenges(): GroupChallenge[] {
  return [
    {
      id: 'challenge_1',
      groupId: 'group_1',
      title: 'Savings Sprint Challenge',
      description: 'Save R10,000 as a group in the next 30 days through bulk purchases and smart shopping',
      type: 'savings_target',
      difficulty: 'medium',
      targetValue: 10000,
      currentValue: 6500,
      unit: 'ZAR',
      startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
      endDate: new Date(Date.now() + 20 * 24 * 60 * 60 * 1000), // 20 days from now
      reward: {
        type: 'discount',
        value: 15,
        description: '15% extra discount on next group order'
      },
      participants: [
        {
          userId: 'user_1',
          userName: 'Alice Johnson',
          userAvatar: '/avatars/alice.jpg',
          joinedAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000),
          progress: 2500,
          rank: 1,
          isActive: true
        },
        {
          userId: 'user_2',
          userName: 'Bob Smith',
          joinedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
          progress: 2000,
          rank: 2,
          isActive: true
        },
        {
          userId: 'user_3',
          userName: 'Carol Davis',
          joinedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          progress: 1500,
          rank: 3,
          isActive: true
        }
      ],
      status: 'active',
      rules: [
        'All purchases must be made through group orders',
        'Savings calculated based on bulk discounts received',
        'Individual contributions count towards group total'
      ],
      milestones: [
        {
          id: 'milestone_1',
          title: 'Quarter Way There',
          description: 'Reach 25% of savings target',
          targetValue: 2500,
          reward: {
            type: 'badge',
            value: 1,
            description: 'Early Bird Badge'
          },
          isCompleted: true,
          completedBy: ['user_1', 'user_2', 'user_3'],
          completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
        },
        {
          id: 'milestone_2',
          title: 'Halfway Hero',
          description: 'Reach 50% of savings target',
          targetValue: 5000,
          reward: {
            type: 'points',
            value: 500,
            description: '500 Bonus Points'
          },
          isCompleted: true,
          completedBy: ['user_1', 'user_2', 'user_3'],
          completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        }
      ],
      leaderboard: [
        {
          userId: 'user_1',
          userName: 'Alice Johnson',
          userAvatar: '/avatars/alice.jpg',
          score: 2500,
          rank: 1,
          progress: 25,
          badges: ['early_bird'],
          lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          userId: 'user_2',
          userName: 'Bob Smith',
          score: 2000,
          rank: 2,
          progress: 20,
          badges: ['early_bird'],
          lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000)
        },
        {
          userId: 'user_3',
          userName: 'Carol Davis',
          score: 1500,
          rank: 3,
          progress: 15,
          badges: ['early_bird'],
          lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000)
        }
      ],
      createdBy: 'user_1',
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'challenge_2',
      groupId: 'group_1',
      title: 'Member Growth Challenge',
      description: 'Grow our group to 20 members and unlock exclusive group features',
      type: 'member_growth',
      difficulty: 'easy',
      targetValue: 20,
      currentValue: 12,
      unit: 'members',
      startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      endDate: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000), // 37 days from now
      reward: {
        type: 'feature_unlock',
        value: 1,
        description: 'Unlock premium group features'
      },
      participants: [],
      status: 'upcoming',
      rules: [
        'New members must be active for at least 7 days',
        'Referrals count double towards your personal score',
        'All group members can participate'
      ],
      milestones: [],
      leaderboard: [],
      createdBy: 'user_1',
      createdAt: new Date()
    }
  ];
}