"use client"

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Medal,
  Star,
  Trophy,
  Crown,
  Shield,
  Zap,
  Target,
  Users,
  Heart,
  Flame,
  Gift,
  Lock,
  CheckCircle,
  Clock,
  TrendingUp,
  Award,
  Sparkles
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/context/AuthContext';

// Types
interface UserBadge {
  id: string;
  name: string;
  description: string;
  category: 'achievement' | 'milestone' | 'social' | 'savings' | 'collaboration' | 'special';
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  iconUrl: string;
  earnedAt: Date;
  earnedFrom: string;
  metadata?: Record<string, unknown>;
}

interface UserAchievement {
  id: string;
  title: string;
  description: string;
  type: 'first_time' | 'milestone' | 'streak' | 'social' | 'savings' | 'collaboration';
  points: number;
  unlockedAt: Date;
  requirements: AchievementRequirement[];
  progress: number;
  isCompleted: boolean;
}

interface AchievementRequirement {
  type: 'action_count' | 'value_threshold' | 'time_based' | 'social_interaction';
  target: number;
  current: number;
  description: string;
}

interface UserReward {
  id: string;
  userId: string;
  type: 'discount' | 'points' | 'cash' | 'voucher' | 'feature_access';
  value: number;
  description: string;
  source: string;
  status: 'pending' | 'claimed' | 'expired' | 'used';
  expiresAt?: Date;
  claimedAt?: Date;
  usedAt?: Date;
}

interface BadgesAndAchievementsProps {
  userId?: string;
  groupId?: string;
  showUserProfile?: boolean;
}

export function BadgesAndAchievements({ 
  userId, 
  groupId, 
  showUserProfile = false 
}: BadgesAndAchievementsProps) {
  const { user } = useAuth();
  const [badges, setBadges] = useState<UserBadge[]>([]);
  const [achievements, setAchievements] = useState<UserAchievement[]>([]);
  const [rewards, setRewards] = useState<UserReward[]>([]);
  const [activeTab, setActiveTab] = useState('badges');
  const [isLoading, setIsLoading] = useState(true);

  const targetUserId = userId || user?._id;

  // Fetch user badges and achievements
  const fetchUserData = async () => {
    if (!targetUserId) return;

    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      
      const [badgesResponse, achievementsResponse, rewardsResponse] = await Promise.all([
        fetch(`/api/users/${targetUserId}/badges`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`/api/users/${targetUserId}/achievements`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`/api/users/${targetUserId}/rewards`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (badgesResponse.ok && achievementsResponse.ok && rewardsResponse.ok) {
        const [badgesData, achievementsData, rewardsData] = await Promise.all([
          badgesResponse.json(),
          achievementsResponse.json(),
          rewardsResponse.json()
        ]);
        
        setBadges(badgesData.data || []);
        setAchievements(achievementsData.data || []);
        setRewards(rewardsData.data || []);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Mock data for development
      setBadges(generateMockBadges());
      setAchievements(generateMockAchievements());
      setRewards(generateMockRewards());
    } finally {
      setIsLoading(false);
    }
  };

  // Claim reward
  const claimReward = async (rewardId: string) => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/users/${targetUserId}/rewards/${rewardId}/claim`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Update local state
        setRewards(prev => prev.map(reward => 
          reward.id === rewardId 
            ? { ...reward, status: 'claimed', claimedAt: new Date() }
            : reward
        ));
      }
    } catch (error) {
      console.error('Error claiming reward:', error);
    }
  };

  // Get rarity color
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'uncommon': return 'bg-green-100 text-green-800 border-green-200';
      case 'rare': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'epic': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'legendary': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'achievement': return Trophy;
      case 'milestone': return Target;
      case 'social': return Users;
      case 'savings': return TrendingUp;
      case 'collaboration': return Heart;
      case 'special': return Crown;
      default: return Medal;
    }
  };

  // Get achievement type icon
  const getAchievementTypeIcon = (type: string) => {
    switch (type) {
      case 'first_time': return Star;
      case 'milestone': return Target;
      case 'streak': return Flame;
      case 'social': return Users;
      case 'savings': return TrendingUp;
      case 'collaboration': return Heart;
      default: return Award;
    }
  };

  // Get reward type icon
  const getRewardTypeIcon = (type: string) => {
    switch (type) {
      case 'discount': return Target;
      case 'points': return Star;
      case 'cash': return Gift;
      case 'voucher': return Gift;
      case 'feature_access': return Zap;
      default: return Gift;
    }
  };

  // Get reward status color
  const getRewardStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'claimed': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'used': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate total points
  const totalPoints = achievements
    .filter(a => a.isCompleted)
    .reduce((sum, a) => sum + a.points, 0);

  // Calculate completion rate
  const completionRate = achievements.length > 0 
    ? (achievements.filter(a => a.isCompleted).length / achievements.length) * 100 
    : 0;

  useEffect(() => {
    fetchUserData();
  }, [targetUserId]);

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Medal className="h-6 w-6 text-yellow-600" />
          <div>
            <h2 className="text-2xl font-bold">
              {showUserProfile ? 'Your Achievements' : 'Badges & Achievements'}
            </h2>
            <p className="text-muted-foreground">
              Track your progress and unlock rewards
            </p>
          </div>
        </div>
        
        {/* Stats Summary */}
        <div className="flex items-center space-x-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{badges.length}</div>
            <div className="text-xs text-muted-foreground">Badges</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{totalPoints}</div>
            <div className="text-xs text-muted-foreground">Points</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{completionRate.toFixed(0)}%</div>
            <div className="text-xs text-muted-foreground">Complete</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="badges" className="flex items-center">
            <Medal className="h-4 w-4 mr-2" />
            Badges ({badges.length})
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex items-center">
            <Trophy className="h-4 w-4 mr-2" />
            Achievements ({achievements.filter(a => a.isCompleted).length}/{achievements.length})
          </TabsTrigger>
          <TabsTrigger value="rewards" className="flex items-center">
            <Gift className="h-4 w-4 mr-2" />
            Rewards ({rewards.filter(r => r.status === 'pending').length})
          </TabsTrigger>
        </TabsList>

        {/* Badges Tab */}
        <TabsContent value="badges" className="space-y-4">
          {badges.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                <Medal className="h-12 w-12 mb-4 opacity-50" />
                <p className="text-lg font-medium">No badges earned yet</p>
                <p className="text-sm">Complete challenges and activities to earn your first badge!</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {badges.map((badge) => {
                const CategoryIcon = getCategoryIcon(badge.category);
                
                return (
                  <Card key={badge.id} className="relative overflow-hidden">
                    <CardContent className="p-4 text-center">
                      {/* Badge Icon */}
                      <div className="relative mb-3">
                        <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${getRarityColor(badge.rarity)}`}>
                          <CategoryIcon className="h-8 w-8" />
                        </div>
                        {badge.rarity === 'legendary' && (
                          <Sparkles className="absolute -top-1 -right-1 h-4 w-4 text-yellow-500" />
                        )}
                      </div>

                      {/* Badge Info */}
                      <h3 className="font-medium text-sm mb-1">{badge.name}</h3>
                      <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                        {badge.description}
                      </p>

                      {/* Rarity and Category */}
                      <div className="flex justify-center space-x-1 mb-2">
                        <Badge variant="outline" className={`text-xs ${getRarityColor(badge.rarity)}`}>
                          {badge.rarity}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {badge.category}
                        </Badge>
                      </div>

                      {/* Earned Date */}
                      <div className="text-xs text-muted-foreground">
                        Earned {formatDistanceToNow(new Date(badge.earnedAt), { addSuffix: true })}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        {/* Achievements Tab */}
        <TabsContent value="achievements" className="space-y-4">
          <div className="space-y-3">
            {achievements.map((achievement) => {
              const TypeIcon = getAchievementTypeIcon(achievement.type);
              
              return (
                <Card key={achievement.id} className={`relative ${
                  achievement.isCompleted ? 'bg-green-50 border-green-200' : ''
                }`}>
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-4">
                      {/* Achievement Icon */}
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        achievement.isCompleted 
                          ? 'bg-green-100 text-green-600' 
                          : 'bg-muted text-muted-foreground'
                      }`}>
                        {achievement.isCompleted ? (
                          <CheckCircle className="h-6 w-6" />
                        ) : (
                          <TypeIcon className="h-6 w-6" />
                        )}
                      </div>

                      {/* Achievement Details */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-medium">{achievement.title}</h3>
                            <p className="text-sm text-muted-foreground mb-2">
                              {achievement.description}
                            </p>
                            
                            {/* Requirements */}
                            <div className="space-y-1">
                              {achievement.requirements.map((req, index) => (
                                <div key={index} className="text-xs">
                                  <div className="flex justify-between items-center mb-1">
                                    <span className="text-muted-foreground">{req.description}</span>
                                    <span className="font-medium">
                                      {req.current} / {req.target}
                                    </span>
                                  </div>
                                  <Progress 
                                    value={(req.current / req.target) * 100} 
                                    className="h-1"
                                  />
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Points and Status */}
                          <div className="text-right">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge variant="outline" className="text-xs">
                                {achievement.type}
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                {achievement.points} pts
                              </Badge>
                            </div>
                            
                            {achievement.isCompleted ? (
                              <div className="text-xs text-green-600">
                                Completed {formatDistanceToNow(new Date(achievement.unlockedAt), { addSuffix: true })}
                              </div>
                            ) : (
                              <div className="text-xs text-muted-foreground">
                                {achievement.progress.toFixed(0)}% complete
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Rewards Tab */}
        <TabsContent value="rewards" className="space-y-4">
          <div className="space-y-3">
            {rewards.map((reward) => {
              const RewardIcon = getRewardTypeIcon(reward.type);
              
              return (
                <Card key={reward.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-4">
                      {/* Reward Icon */}
                      <div className="w-12 h-12 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center">
                        <RewardIcon className="h-6 w-6" />
                      </div>

                      {/* Reward Details */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-medium">{reward.description}</h3>
                            <p className="text-sm text-muted-foreground">
                              From: {reward.source}
                            </p>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge className={getRewardStatusColor(reward.status)}>
                                {reward.status}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {reward.type}
                              </Badge>
                            </div>
                          </div>

                          {/* Value and Actions */}
                          <div className="text-right">
                            <div className="text-lg font-bold text-yellow-600 mb-2">
                              {reward.type === 'discount' && `${reward.value}% off`}
                              {reward.type === 'points' && `${reward.value} pts`}
                              {reward.type === 'cash' && `R${reward.value}`}
                              {reward.type === 'voucher' && `R${reward.value}`}
                              {reward.type === 'feature_access' && 'Premium'}
                            </div>
                            
                            {reward.status === 'pending' && (
                              <Button 
                                size="sm" 
                                onClick={() => claimReward(reward.id)}
                              >
                                Claim Reward
                              </Button>
                            )}
                            
                            {reward.expiresAt && reward.status === 'pending' && (
                              <div className="text-xs text-muted-foreground mt-1">
                                <Clock className="h-3 w-3 inline mr-1" />
                                Expires {formatDistanceToNow(new Date(reward.expiresAt), { addSuffix: true })}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Mock data generators for development
function generateMockBadges(): UserBadge[] {
  return [
    {
      id: 'badge_1',
      name: 'First Purchase',
      description: 'Made your first group purchase',
      category: 'achievement',
      rarity: 'common',
      iconUrl: '/badges/first_purchase.svg',
      earnedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      earnedFrom: 'First group order'
    },
    {
      id: 'badge_2',
      name: 'Team Player',
      description: 'Participated in 5 group challenges',
      category: 'social',
      rarity: 'uncommon',
      iconUrl: '/badges/team_player.svg',
      earnedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      earnedFrom: 'Challenge participation'
    },
    {
      id: 'badge_3',
      name: 'Savings Master',
      description: 'Saved over R5,000 through group buying',
      category: 'savings',
      rarity: 'rare',
      iconUrl: '/badges/savings_master.svg',
      earnedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      earnedFrom: 'Savings milestone'
    }
  ];
}

function generateMockAchievements(): UserAchievement[] {
  return [
    {
      id: 'achievement_1',
      title: 'Group Starter',
      description: 'Join your first group and make a purchase',
      type: 'first_time',
      points: 100,
      unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      requirements: [
        { type: 'action_count', target: 1, current: 1, description: 'Join a group' },
        { type: 'action_count', target: 1, current: 1, description: 'Make a purchase' }
      ],
      progress: 100,
      isCompleted: true
    },
    {
      id: 'achievement_2',
      title: 'Social Butterfly',
      description: 'Invite 10 friends to join groups',
      type: 'social',
      points: 500,
      unlockedAt: new Date(),
      requirements: [
        { type: 'action_count', target: 10, current: 6, description: 'Invite friends' }
      ],
      progress: 60,
      isCompleted: false
    }
  ];
}

function generateMockRewards(): UserReward[] {
  return [
    {
      id: 'reward_1',
      userId: 'user_1',
      type: 'discount',
      value: 15,
      description: '15% discount on next group order',
      source: 'Savings Sprint Challenge',
      status: 'pending',
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'reward_2',
      userId: 'user_1',
      type: 'points',
      value: 500,
      description: '500 bonus points',
      source: 'Team Player Badge',
      status: 'claimed',
      claimedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    }
  ];
}
