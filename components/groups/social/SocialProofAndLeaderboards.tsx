"use client"

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Crown,
  Trophy,
  TrendingUp,
  Users,
  Star,
  Heart,
  Eye,
  Share2,
  MessageSquare,
  ThumbsUp,
  Medal,
  Target,
  Zap,
  Calendar,
  Award,
  Flame,
  ChevronUp,
  ChevronDown,
  Minus
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/context/AuthContext';

// Types
interface SocialProofItem {
  id: string;
  type: 'group_success' | 'member_achievement' | 'savings_milestone' | 'product_review' | 'challenge_completion';
  title: string;
  description: string;
  imageUrl?: string;
  groupId?: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  metrics: SocialProofMetric[];
  tags: string[];
  isPublic: boolean;
  createdAt: Date;
  engagement: SocialProofEngagement;
}

interface SocialProofMetric {
  label: string;
  value: number;
  unit: string;
  trend?: 'up' | 'down' | 'stable';
}

interface SocialProofEngagement {
  views: number;
  likes: number;
  shares: number;
  comments: number;
}

interface GroupLeaderboard {
  id: string;
  type: 'savings' | 'activity' | 'growth' | 'collaboration' | 'challenges';
  period: 'daily' | 'weekly' | 'monthly' | 'all_time';
  entries: GroupLeaderboardEntry[];
  lastUpdated: Date;
}

interface GroupLeaderboardEntry {
  groupId: string;
  groupName: string;
  groupAvatar?: string;
  score: number;
  rank: number;
  change: number;
  metrics: LeaderboardMetric[];
  memberCount: number;
  isVerified: boolean;
}

interface LeaderboardMetric {
  name: string;
  value: number;
  unit: string;
  displayValue: string;
}

interface SocialProofAndLeaderboardsProps {
  groupId?: string;
  showGlobalLeaderboards?: boolean;
}

export function SocialProofAndLeaderboards({ 
  groupId, 
  showGlobalLeaderboards = true 
}: SocialProofAndLeaderboardsProps) {
  const { user } = useAuth();
  const [socialProofItems, setSocialProofItems] = useState<SocialProofItem[]>([]);
  const [leaderboards, setLeaderboards] = useState<Record<string, GroupLeaderboard>>({});
  const [activeTab, setActiveTab] = useState('social_proof');
  const [selectedLeaderboard, setSelectedLeaderboard] = useState('savings_weekly');
  const [isLoading, setIsLoading] = useState(true);

  // Fetch social proof items
  const fetchSocialProof = async () => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/social-proof', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setSocialProofItems(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching social proof:', error);
      // Mock data for development
      setSocialProofItems(generateMockSocialProof());
    }
  };

  // Fetch leaderboards
  const fetchLeaderboards = async () => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/leaderboards', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        setLeaderboards(result.data || {});
      }
    } catch (error) {
      console.error('Error fetching leaderboards:', error);
      // Mock data for development
      setLeaderboards(generateMockLeaderboards());
    }
  };

  // Like social proof item
  const likeSocialProofItem = async (itemId: string) => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/social-proof/${itemId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Update local state
        setSocialProofItems(prev => prev.map(item => 
          item.id === itemId 
            ? { ...item, engagement: { ...item.engagement, likes: item.engagement.likes + 1 } }
            : item
        ));
      }
    } catch (error) {
      console.error('Error liking social proof item:', error);
    }
  };

  // Get social proof type icon
  const getSocialProofTypeIcon = (type: string) => {
    switch (type) {
      case 'group_success': return Trophy;
      case 'member_achievement': return Star;
      case 'savings_milestone': return Target;
      case 'product_review': return Heart;
      case 'challenge_completion': return Medal;
      default: return Star;
    }
  };

  // Get leaderboard type icon
  const getLeaderboardTypeIcon = (type: string) => {
    switch (type) {
      case 'savings': return Target;
      case 'activity': return Zap;
      case 'growth': return TrendingUp;
      case 'collaboration': return Users;
      case 'challenges': return Trophy;
      default: return Crown;
    }
  };

  // Get trend icon
  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up': return <ChevronUp className="h-3 w-3 text-green-600" />;
      case 'down': return <ChevronDown className="h-3 w-3 text-red-600" />;
      default: return <Minus className="h-3 w-3 text-gray-400" />;
    }
  };

  // Get rank change indicator
  const getRankChangeIndicator = (change: number) => {
    if (change > 0) {
      return <ChevronUp className="h-4 w-4 text-green-600" />;
    } else if (change < 0) {
      return <ChevronDown className="h-4 w-4 text-red-600" />;
    }
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  // Get user initials
  const getUserInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Get current leaderboard
  const getCurrentLeaderboard = () => {
    return leaderboards[selectedLeaderboard];
  };

  useEffect(() => {
    setIsLoading(true);
    Promise.all([fetchSocialProof(), fetchLeaderboards()]).finally(() => {
      setIsLoading(false);
    });
  }, []);

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Crown className="h-6 w-6 text-yellow-600" />
        <div>
          <h2 className="text-2xl font-bold">Community Highlights</h2>
          <p className="text-muted-foreground">
            Celebrate achievements and see how groups are performing
          </p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="social_proof" className="flex items-center">
            <Star className="h-4 w-4 mr-2" />
            Success Stories
          </TabsTrigger>
          <TabsTrigger value="leaderboards" className="flex items-center">
            <Crown className="h-4 w-4 mr-2" />
            Leaderboards
          </TabsTrigger>
        </TabsList>

        {/* Social Proof Tab */}
        <TabsContent value="social_proof" className="space-y-4">
          <div className="space-y-4">
            {socialProofItems.map((item) => {
              const TypeIcon = getSocialProofTypeIcon(item.type);
              
              return (
                <Card key={item.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      {/* Icon */}
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <TypeIcon className="h-6 w-6 text-primary" />
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="font-semibold text-lg">{item.title}</h3>
                            <p className="text-muted-foreground">{item.description}</p>
                            
                            {/* User Attribution */}
                            {item.userName && (
                              <div className="flex items-center space-x-2 mt-2">
                                <Avatar className="w-6 h-6">
                                  <AvatarImage src={item.userAvatar} />
                                  <AvatarFallback className="text-xs">
                                    {getUserInitials(item.userName)}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-sm text-muted-foreground">{item.userName}</span>
                                <span className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(item.createdAt), { addSuffix: true })}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Tags */}
                          <div className="flex flex-wrap gap-1">
                            {item.tags.map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Metrics */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          {item.metrics.map((metric, index) => (
                            <div key={index} className="text-center p-3 bg-muted rounded-lg">
                              <div className="flex items-center justify-center space-x-1">
                                <span className="text-lg font-bold">
                                  {metric.value.toLocaleString()}
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  {metric.unit}
                                </span>
                                {metric.trend && getTrendIcon(metric.trend)}
                              </div>
                              <div className="text-xs text-muted-foreground">{metric.label}</div>
                            </div>
                          ))}
                        </div>

                        {/* Engagement */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Eye className="h-4 w-4" />
                              <span>{item.engagement.views}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <ThumbsUp className="h-4 w-4" />
                              <span>{item.engagement.likes}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <MessageSquare className="h-4 w-4" />
                              <span>{item.engagement.comments}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Share2 className="h-4 w-4" />
                              <span>{item.engagement.shares}</span>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center space-x-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => likeSocialProofItem(item.id)}
                            >
                              <ThumbsUp className="h-4 w-4 mr-1" />
                              Like
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Share2 className="h-4 w-4 mr-1" />
                              Share
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Leaderboards Tab */}
        <TabsContent value="leaderboards" className="space-y-4">
          {/* Leaderboard Selection */}
          <div className="flex flex-wrap gap-2">
            {Object.keys(leaderboards).map((key) => {
              const leaderboard = leaderboards[key];
              const TypeIcon = getLeaderboardTypeIcon(leaderboard.type);
              
              return (
                <Button
                  key={key}
                  variant={selectedLeaderboard === key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedLeaderboard(key)}
                  className="flex items-center"
                >
                  <TypeIcon className="h-4 w-4 mr-2" />
                  {leaderboard.type.charAt(0).toUpperCase() + leaderboard.type.slice(1)} - {leaderboard.period}
                </Button>
              );
            })}
          </div>

          {/* Current Leaderboard */}
          {getCurrentLeaderboard() && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Crown className="h-5 w-5 mr-2 text-yellow-600" />
                    {getCurrentLeaderboard().type.charAt(0).toUpperCase() + getCurrentLeaderboard().type.slice(1)} Leaderboard
                  </CardTitle>
                  <Badge variant="outline">
                    Last updated {formatDistanceToNow(new Date(getCurrentLeaderboard().lastUpdated), { addSuffix: true })}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {getCurrentLeaderboard().entries.map((entry) => (
                      <div key={entry.groupId} className={`flex items-center space-x-4 p-4 rounded-lg border ${
                        entry.groupId === groupId ? 'bg-primary/10 border-primary/20' : 'bg-muted/50'
                      }`}>
                        {/* Rank */}
                        <div className="flex items-center justify-center w-12 h-12">
                          {entry.rank === 1 && <Crown className="h-6 w-6 text-yellow-500" />}
                          {entry.rank === 2 && <Medal className="h-6 w-6 text-gray-400" />}
                          {entry.rank === 3 && <Medal className="h-6 w-6 text-amber-600" />}
                          {entry.rank > 3 && (
                            <span className="text-lg font-bold text-muted-foreground">#{entry.rank}</span>
                          )}
                        </div>

                        {/* Group Info */}
                        <Avatar className="w-12 h-12">
                          <AvatarImage src={entry.groupAvatar} />
                          <AvatarFallback>
                            {getUserInitials(entry.groupName)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-medium">{entry.groupName}</h4>
                            {entry.isVerified && (
                              <Award className="h-4 w-4 text-blue-500" />
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {entry.memberCount} members
                          </div>
                        </div>

                        {/* Metrics */}
                        <div className="text-right">
                          <div className="text-lg font-bold">{entry.score.toLocaleString()}</div>
                          <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                            {getRankChangeIndicator(entry.change)}
                            <span>{Math.abs(entry.change)} from last period</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Mock data generators for development
function generateMockSocialProof(): SocialProofItem[] {
  return [
    {
      id: 'social_1',
      type: 'group_success',
      title: 'Electronics Enthusiasts Saves R25,000!',
      description: 'This amazing group achieved incredible savings through strategic bulk purchasing and smart coordination.',
      groupId: 'group_1',
      userId: 'user_1',
      userName: 'Alice Johnson',
      userAvatar: '/avatars/alice.jpg',
      metrics: [
        { label: 'Total Savings', value: 25000, unit: 'ZAR', trend: 'up' },
        { label: 'Members', value: 15, unit: 'people', trend: 'up' },
        { label: 'Orders', value: 42, unit: 'orders', trend: 'stable' }
      ],
      tags: ['savings', 'electronics', 'teamwork'],
      isPublic: true,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      engagement: {
        views: 234,
        likes: 45,
        shares: 12,
        comments: 8
      }
    },
    {
      id: 'social_2',
      type: 'challenge_completion',
      title: 'Savings Sprint Challenge Completed!',
      description: 'The Home Essentials group smashed their savings target with 3 days to spare.',
      groupId: 'group_2',
      userId: 'user_2',
      userName: 'Bob Smith',
      metrics: [
        { label: 'Target', value: 10000, unit: 'ZAR', trend: 'up' },
        { label: 'Achieved', value: 12500, unit: 'ZAR', trend: 'up' },
        { label: 'Participants', value: 8, unit: 'people', trend: 'stable' }
      ],
      tags: ['challenge', 'home', 'achievement'],
      isPublic: true,
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      engagement: {
        views: 156,
        likes: 32,
        shares: 6,
        comments: 4
      }
    }
  ];
}

function generateMockLeaderboards(): Record<string, GroupLeaderboard> {
  return {
    savings_weekly: {
      id: 'savings_weekly',
      type: 'savings',
      period: 'weekly',
      entries: [
        {
          groupId: 'group_1',
          groupName: 'Electronics Enthusiasts',
          groupAvatar: '/groups/electronics.jpg',
          score: 15000,
          rank: 1,
          change: 2,
          metrics: [
            { name: 'Total Savings', value: 15000, unit: 'ZAR', displayValue: 'R15,000' },
            { name: 'Avg per Member', value: 1250, unit: 'ZAR', displayValue: 'R1,250' }
          ],
          memberCount: 12,
          isVerified: true
        },
        {
          groupId: 'group_2',
          groupName: 'Home Essentials',
          score: 12500,
          rank: 2,
          change: -1,
          metrics: [
            { name: 'Total Savings', value: 12500, unit: 'ZAR', displayValue: 'R12,500' },
            { name: 'Avg per Member', value: 1562, unit: 'ZAR', displayValue: 'R1,562' }
          ],
          memberCount: 8,
          isVerified: false
        },
        {
          groupId: 'group_3',
          groupName: 'Fashion Forward',
          score: 9800,
          rank: 3,
          change: 1,
          metrics: [
            { name: 'Total Savings', value: 9800, unit: 'ZAR', displayValue: 'R9,800' },
            { name: 'Avg per Member', value: 980, unit: 'ZAR', displayValue: 'R980' }
          ],
          memberCount: 10,
          isVerified: true
        }
      ],
      lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    activity_weekly: {
      id: 'activity_weekly',
      type: 'activity',
      period: 'weekly',
      entries: [
        {
          groupId: 'group_2',
          groupName: 'Home Essentials',
          score: 245,
          rank: 1,
          change: 0,
          metrics: [
            { name: 'Messages', value: 156, unit: 'messages', displayValue: '156' },
            { name: 'Activities', value: 89, unit: 'activities', displayValue: '89' }
          ],
          memberCount: 8,
          isVerified: false
        }
      ],
      lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000)
    }
  };
}
