import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Users, ShoppingBag, Truck, Target, Search, Package, Calendar, TrendingUp } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import type { StokvelGroup } from "@/types/stokvelgroup"

// Dummy data for the group dashboard
const recentActivity = [
  { id: "1", member: "<PERSON>", action: "Added items to cart", date: "2024-01-18", amount: 450.0 },
  { id: "2", member: "<PERSON>", action: "Joined group", date: "2024-01-17", amount: null },
  { id: "3", member: "<PERSON>", action: "Made payment", date: "2024-01-16", amount: 300.0 },
]

interface GroupDashboardProps {
  groupDetails: StokvelGroup
}

export default function GroupDashboard({ groupDetails }: GroupDashboardProps) {
  return (
    <div className="space-y-6">
      {/* Search and Welcome Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2
            className="text-3xl text-[#2F4858] font-bold tracking-tight"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            {groupDetails.name}
          </h2>
          <p className="text-muted-foreground">Group Dashboard Overview</p>
        </div>
        <div className="flex w-full md:w-auto gap-2">
          <Input placeholder="Search products..." className="max-w-sm" />
          <Button>
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Saved</p>
                <h3 className="text-2xl font-bold">R{groupDetails.totalSales.toLocaleString()}</h3>
                <p className="text-xs text-muted-foreground mt-1">of R100,000 target</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <Target className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Members</p>
                <h3 className="text-2xl font-bold">{groupDetails.members.length}</h3>
                <p className="text-xs text-muted-foreground mt-1">of 30 capacity</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Orders</p>
                <h3 className="text-2xl font-bold">{groupDetails.activeOrders}</h3>
                <p className="text-xs text-muted-foreground mt-1">Orders in Progress</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Order Value</p>
                <h3 className="text-2xl font-bold">R{groupDetails.avgOrderValue.toLocaleString()}</h3>
                <p className="text-xs text-muted-foreground mt-1">Per Order</p>
              </div>
              <div className="h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center">
                <Truck className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity and Progress Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
            <Button variant="ghost" size="sm">
              View all
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Member</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentActivity.map((activity) => (
                  <TableRow key={activity.id}>
                    <TableCell className="font-medium">{activity.member}</TableCell>
                    <TableCell>{activity.action}</TableCell>
                    <TableCell>{activity.amount ? `R${activity.amount.toFixed(2)}` : "-"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-lg font-medium">Group Progress</CardTitle>
            <Button variant="ghost" size="sm">
              View details
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Savings Progress</span>
                <span className="font-medium">{((groupDetails.totalSales / 100000) * 100).toFixed(0)}%</span>
              </div>
              <div className="h-2 rounded-full bg-slate-100">
                <div
                  className="h-full rounded-full bg-green-500"
                  style={{ width: `${((groupDetails.totalSales / 100000) * 100).toFixed(0)}%` }}
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Member Participation</span>
                <span className="font-medium">{((groupDetails.members.length / 30) * 100).toFixed(0)}%</span>
              </div>
              <div className="h-2 rounded-full bg-slate-100">
                <div
                  className="h-full rounded-full bg-blue-500"
                  style={{ width: `${((groupDetails.members.length / 30) * 100).toFixed(0)}%` }}
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Order Completion</span>
                <span className="font-medium">{((groupDetails.activeOrders / 10) * 100).toFixed(0)}%</span>
              </div>
              <div className="h-2 rounded-full bg-slate-100">
                <div
                  className="h-full rounded-full bg-orange-500"
                  style={{ width: `${((groupDetails.activeOrders / 10) * 100).toFixed(0)}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center mb-4">
                <Package className="h-6 w-6 text-green-500" />
              </div>
              <h4 className="font-semibold mb-1">Browse Products</h4>
              <p className="text-sm text-muted-foreground mb-4">View available products</p>
              <Button variant="outline" className="w-full">
                View Products
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
              <h4 className="font-semibold mb-1">Members</h4>
              <p className="text-sm text-muted-foreground mb-4">View group members</p>
              <Button variant="outline" className="w-full">
                View Members
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center mb-4">
                <Calendar className="h-6 w-6 text-orange-500" />
              </div>
              <h4 className="font-semibold mb-1">Delivery Schedule</h4>
              <p className="text-sm text-muted-foreground mb-4">View delivery dates</p>
              <Button variant="outline" className="w-full">
                View Schedule
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center mb-4">
                <TrendingUp className="h-6 w-6 text-purple-500" />
              </div>
              <h4 className="font-semibold mb-1">Savings Goals</h4>
              <p className="text-sm text-muted-foreground mb-4">Track group progress</p>
              <Button variant="outline" className="w-full">
                View Goals
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

