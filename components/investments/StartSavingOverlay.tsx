

import { motion, AnimatePresence } from "framer-motion"
import { X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { MultiStepForm } from "./MultiStepForm"
import type React from "react"

interface StartSavingOverlayProps {
  isOpen: boolean
  onClose: () => void
}

export const StartSavingOverlay: React.FC<StartSavingOverlayProps> = ({ isOpen, onClose }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm p-4 sm:p-6 md:p-8"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="relative w-full max-w-4xl rounded-2xl bg-white bg-opacity-90 shadow-xl overflow-hidden"
          >
            <div className="absolute right-2 top-2 z-10">
              <Button onClick={onClose} className="text-gray-500 hover:text-gray-700" variant="ghost" size="icon">
                <X size={24} />
              </Button>
            </div>
            <div className="max-h-[calc(100vh-2rem)] overflow-y-auto p-4 sm:p-6 md:p-8">
              <MultiStepForm onClose={onClose} />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
