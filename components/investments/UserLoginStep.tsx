"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Loader2, Mail, Lock } from "lucide-react"

const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

type LoginFormData = z.infer<typeof loginSchema>

interface UserLoginStepProps {
  email: string
  onLoginSuccess: (email: string, password: string) => void
  onPrev: () => void
}

export function UserLoginStep({ email, onLoginSuccess, onPrev }: UserLoginStepProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: { email, password: "" },
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsSubmitting(true)
    try {
      await onLoginSuccess(data.email, data.password)
    } catch (error) {
      console.error("Login failed:", error)
      form.setError("root", { message: "Login failed. Please check your credentials." })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-semibold text-[#2F4858]">Login</CardTitle>
        <CardDescription>Welcome back! Please enter your password to continue.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input {...form.register("email")} className="pl-10" disabled />
                </div>
              </FormControl>
            </FormItem>
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input type="password" {...form.register("password")} className="pl-10" />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
            {form.formState.errors.root && <p className="text-sm text-red-600">{form.formState.errors.root.message}</p>}
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button type="button" onClick={onPrev} variant="outline" className="rounded-full px-6 py-2 text-sm">
          Back
        </Button>
        <Button
          type="submit"
          onClick={form.handleSubmit(onSubmit)}
          disabled={isSubmitting}
          className="rounded-full bg-[#2A7C6C] px-6 py-2 text-sm text-white hover:bg-[#236358]"
        >
          {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin" /> : "Login"}
        </Button>
      </CardFooter>
    </Card>
  )
}
