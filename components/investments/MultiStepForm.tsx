"use client"

import React, { useCallback } from "react"
import { useMultiStepForm } from "@/hooks/useMultiStepForm"
import { StepSelector } from "./StepSelector"
import { PricingPlans } from "./PricingPlans"
import { UserRegistrationStep } from "./UserRegistrationStep"
import { FormSummary } from "./FormSummary"
import type { FormData, Beneficiary } from "./StepComponentProps"

interface MultiStepFormProps {
  onClose: () => void
}

export const MultiStepForm: React.FC<MultiStepFormProps> = ({ onClose }) => {
  const initialFormData: FormData = {
    investmentType: "",
    selectedPlan: "",
    userId: "",
    email: "",
    name: "",
    phone: "",
    planDuration: "",
    existingUserId: "",
    planPrice: "",
    planTitle: "",
    joiningFee: "",
    benefits: [],
    beneficiaries: []
  }

  const { currentStepIndex, next, back, formData, updateFormData } = useMultiStepForm<FormData>(initialFormData)

  const handleFormUpdate = useCallback((key: keyof FormData, value: string | string[] | Beneficiary[]) => {
    updateFormData({ [key]: value })
  }, [updateFormData])

  const handleNext = useCallback(() => {
    next()
  }, [next])

  const handleBack = useCallback(() => {
    back()
  }, [back])

  // Define steps as functions that return JSX with current formData
  const getStepComponent = () => {
    switch (currentStepIndex) {
      case 0:
        return (
          <StepSelector
            formData={formData}
            updateFormData={handleFormUpdate}
            onNext={handleNext}
            onPrev={handleBack}
          />
        )
      case 1:
        return (
          <PricingPlans
            formData={formData}
            updateFormData={handleFormUpdate}
            onNext={handleNext}
            onPrev={handleBack}
          />
        )
      case 2:
        return (
          <UserRegistrationStep
            formData={formData}
            updateFormData={handleFormUpdate}
            onNext={handleNext}
            onPrev={handleBack}
          />
        )
      case 3:
        return (
          <FormSummary
            formData={formData}
            onClose={onClose}
            onPrev={handleBack}
          />
        )
      default:
        return (
          <StepSelector
            formData={formData}
            updateFormData={handleFormUpdate}
            onNext={handleNext}
            onPrev={handleBack}
          />
        )
    }
  }

  return (
    <div className="flex flex-col items-center justify-center">
      <h2 className="mb-6 text-2xl font-bold text-[#2F4858]">Start Saving with Stokvel</h2>
      {getStepComponent()}
    </div>
  )
}