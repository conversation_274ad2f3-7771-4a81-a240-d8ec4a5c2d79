"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card"
import { Loader2, CheckCircle, XCircle } from "lucide-react"
// Removed useGroupSavings import as it's not needed
import type { FormSummaryProps } from "./StepComponentProps"
import type {
  SubscriptionType,
  SubscriptionStatus,
  CreateSubscriptionData,
  BundleSize,
  DeliveryFrequency,
  YearEndBundleType,
  GrocerySchoolBundleType,
  MonthlyGroceriesType,
  FuneralBenefitsType,
} from "@/types/subscriptionTypes"

const investmentTypeMap: Record<string, string> = {
  yearEndBundle: "Monthly Year-end Stokvel Bundle",
  grocerySchoolBundle: "Grocery + Back to School Bundle",
  monthlyGroceries: "Monthly Groceries",
  funeralBenefits: "Funeral Benefits",
}

const subscriptionTypeMap: Record<string, "yearEnd" | "grocerySchool" | "monthlyGroceries" | "funeralBenefits"> = {
  yearEndBundle: "yearEnd",
  grocerySchoolBundle: "grocerySchool",
  monthlyGroceries: "monthlyGroceries",
  funeralBenefits: "funeralBenefits",
}

const formatCurrency = (amount: string | number) => {
  const numAmount = typeof amount === 'string' ? Number(amount) || 0 : amount
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR'
  }).format(numAmount)
}

export function FormSummary({ formData, onClose, onPrev }: FormSummaryProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submissionStatus, setSubmissionStatus] = useState<"idle" | "success" | "error">("idle")
  const [error, setError] = useState<string | null>(null)
  // Mock createSubscription function since it's not available in useGroupSavings
  const createSubscription = async (_type: string, _data: Record<string, unknown>) => {
    // This is a placeholder function
    console.log("Creating subscription:", _type, _data)
    return Promise.resolve({ success: true })
  }

  const submitForm = async () => {
    setIsSubmitting(true)
    setSubmissionStatus("idle")
    setError(null)
    try {
      const subscriptionType = subscriptionTypeMap[formData.investmentType]
      if (!subscriptionType) {
        throw new Error(`Invalid investment type: ${formData.investmentType}`)
      }

      if (!formData.userId) {
        throw new Error("User ID is required")
      }

      const baseSubscriptionData = {
        userId: formData.userId,
        name: `${formData.name}'s ${investmentTypeMap[formData.investmentType]} Subscription`,
        description: `Subscription for ${investmentTypeMap[formData.investmentType]} - ${formData.planTitle}`,
        price: Number(formData.planPrice) || 0,
        monthlyContribution: Number(formData.monthlyContribution || formData.planPrice || "0"),
        subscriptionType: formData.investmentType,
        status: "pending" as SubscriptionStatus,
        startDate: new Date(),
        endDate: calculateEndDate(formData.planDuration || "1 year"),
      }

      let subscriptionData: CreateSubscriptionData<SubscriptionType>

      switch (subscriptionType) {
        case "yearEnd": {
          // Calculate monthly contribution if not provided
          const price = Number(formData.planPrice) || 0
          const monthsUntilDelivery = Number(formData.deliveryMonth || "12") - new Date().getMonth()
          const defaultMonthlyContribution = monthsUntilDelivery > 0 ? price / monthsUntilDelivery : price

          const yearEndData = {
            ...baseSubscriptionData,
            bundleSize: (formData.bundleSize as BundleSize) || "standard",
            itemList: Array.isArray(formData.itemList) ? formData.itemList : [],
            deliveryMonth: Number(formData.deliveryMonth || "12"),
            monthlyContribution: Number(formData.monthlyContribution || defaultMonthlyContribution),
            totalSavingsGoal: Number(formData.totalSavingsGoal || price || "0"),
          }

          // Validate required fields
          if (!yearEndData.userId || !yearEndData.name || !yearEndData.description ||
              yearEndData.price === undefined || yearEndData.monthlyContribution === undefined ||
              !yearEndData.status || !yearEndData.startDate || !yearEndData.endDate ||
              !yearEndData.bundleSize || !yearEndData.deliveryMonth || yearEndData.totalSavingsGoal === undefined ||
              !yearEndData.subscriptionType) {
            throw new Error("All fields are required for Year End Bundle subscription")
          }

          subscriptionData = yearEndData as CreateSubscriptionData<YearEndBundleType>
          break
        }
        case "grocerySchool": {
          const price = Number(formData.planPrice) || 0
          const grocerySchoolData = {
            ...baseSubscriptionData,
            bundleSize: (formData.bundleSize as BundleSize) || "standard",
            groceryItems: Array.isArray(formData.groceryItems) ? formData.groceryItems : [],
            schoolItems: Array.isArray(formData.schoolItems) ? formData.schoolItems : [],
            deliveryFrequency: (formData.deliveryFrequency as DeliveryFrequency) || "monthly",
            groceryAllocation: Number(formData.groceryAllocation || price * 0.6 || "0"),
            schoolSuppliesAllocation: Number(formData.schoolSuppliesAllocation || price * 0.4 || "0"),
          }

          // Validate required fields
          if (!grocerySchoolData.userId || !grocerySchoolData.name || !grocerySchoolData.description ||
              grocerySchoolData.price === undefined || grocerySchoolData.monthlyContribution === undefined ||
              !grocerySchoolData.status || !grocerySchoolData.startDate || !grocerySchoolData.endDate ||
              !grocerySchoolData.bundleSize || !grocerySchoolData.deliveryFrequency ||
              grocerySchoolData.groceryAllocation === undefined || grocerySchoolData.schoolSuppliesAllocation === undefined ||
              !grocerySchoolData.subscriptionType) {
            throw new Error("All fields are required for Grocery School Bundle subscription")
          }

          subscriptionData = grocerySchoolData as CreateSubscriptionData<GrocerySchoolBundleType>
          break
        }
        case "monthlyGroceries": {
          const monthlyGroceriesData = {
            ...baseSubscriptionData,
            groceryList: Array.isArray(formData.groceryList) ? formData.groceryList : [],
            deliveryDate: Number(formData.deliveryDate || "1"),
            preferredStores: Array.isArray(formData.preferredStores) ? formData.preferredStores : [],
          }

          // Validate required fields
          if (!monthlyGroceriesData.userId || !monthlyGroceriesData.name || !monthlyGroceriesData.description ||
              monthlyGroceriesData.price === undefined || monthlyGroceriesData.monthlyContribution === undefined ||
              !monthlyGroceriesData.status || !monthlyGroceriesData.startDate || !monthlyGroceriesData.endDate ||
              !monthlyGroceriesData.deliveryDate || !monthlyGroceriesData.subscriptionType) {
            throw new Error("All fields are required for Monthly Groceries subscription")
          }

          subscriptionData = monthlyGroceriesData as CreateSubscriptionData<MonthlyGroceriesType>
          break
        }
        case "funeralBenefits": {
          const beneficiariesData = Array.isArray(formData.beneficiaries)
            ? formData.beneficiaries.map(beneficiary => {
                if (typeof beneficiary === 'object' && beneficiary !== null) {
                  return {
                    name: String(beneficiary.name || ''),
                    relationship: String(beneficiary.relationship || ''),
                    contactNumber: String(beneficiary.contactNumber || '')
                  }
                }
                return {
                  name: '',
                  relationship: '',
                  contactNumber: ''
                }
              })
            : []

          const funeralBenefitsData = {
            ...baseSubscriptionData,
            coverageAmount: Number(formData.coverageAmount || formData.planPrice || "0"),
            beneficiaries: beneficiariesData,
            waitingPeriod: Number(formData.waitingPeriod || "0"),
          }

          // Validate required fields
          if (!funeralBenefitsData.userId || !funeralBenefitsData.name || !funeralBenefitsData.description ||
              funeralBenefitsData.price === undefined || funeralBenefitsData.monthlyContribution === undefined ||
              !funeralBenefitsData.status || !funeralBenefitsData.startDate || !funeralBenefitsData.endDate ||
              funeralBenefitsData.coverageAmount === undefined || !funeralBenefitsData.beneficiaries.length ||
              funeralBenefitsData.waitingPeriod === undefined || !funeralBenefitsData.subscriptionType) {
            throw new Error("All fields are required for Funeral Benefits subscription")
          }

          subscriptionData = funeralBenefitsData as CreateSubscriptionData<FuneralBenefitsType>
          break
        }
        default:
          throw new Error(`Invalid subscription type: ${subscriptionType}`)
      }

      console.log("Submitting subscription data:", subscriptionData)
      await createSubscription(subscriptionType, subscriptionData)
      console.log("Subscription created successfully")
      setSubmissionStatus("success")
      setTimeout(() => {
        onClose()
      }, 2000)
    } catch (error) {
      console.error("Error submitting form:", error)
      setError(error instanceof Error ? error.message : "An unknown error occurred")
      setSubmissionStatus("error")
    } finally {
      setIsSubmitting(false)
    }
  }

  const calculateEndDate = (duration: string): Date => {
    const now = new Date()
    const parts = duration.split(" ")
    const amount = parts[0]
    const unit = parts[1]

    if (!amount || !unit) {
      console.warn("Invalid duration format. Defaulting to 1 year.")
      now.setFullYear(now.getFullYear() + 1)
      return now
    }

    const numAmount = Number.parseInt(amount, 10)

    switch (unit.toLowerCase()) {
      case "month":
      case "months":
        now.setMonth(now.getMonth() + numAmount)
        break
      case "year":
      case "years":
        now.setFullYear(now.getFullYear() + numAmount)
        break
      default:
        console.warn("Unrecognized duration unit. Defaulting to 1 year.")
        now.setFullYear(now.getFullYear() + 1)
    }

    return now
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="w-full max-w-2xl mx-auto"
    >
      <Card className="bg-white shadow-lg">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="text-2xl font-semibold text-[#2F4858]">Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          <SummarySection title="User Information">
            <SummaryItem label="Name" value={formData.name || ""} />
            <SummaryItem label="Email" value={formData.email || ""} />
            <SummaryItem label="Phone" value={formData.phone || ""} />
          </SummarySection>
          <SummarySection title="Investment Details">
            <SummaryItem label="Type" value={investmentTypeMap[formData.investmentType] || ""} />
            <SummaryItem label="Plan" value={formData.planTitle || ""} />
            <SummaryItem label="Price" value={formatCurrency(formData.planPrice)} />
            <SummaryItem label="Joining Fee" value={formatCurrency(formData.joiningFee)} />
            <SummaryItem label="Duration" value={formData.planDuration || ""} />
          </SummarySection>
          <SummarySection title="Plan Benefits">
            <ul className="space-y-2">
              {Array.isArray(formData.benefits) && formData.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <svg className="mr-2 h-5 w-5 mt-0.5 flex-shrink-0 text-[#2A7C6C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-700">{benefit}</span>
                </li>
              ))}
            </ul>
          </SummarySection>
        </CardContent>
        <CardFooter className="flex flex-col items-center bg-gray-50 border-t p-4">
          <div className="flex justify-between w-full mb-4">
            <Button onClick={onPrev} variant="outline" className="rounded-full px-6 py-2 text-sm">
              Back
            </Button>
            <Button
              onClick={submitForm}
              className="rounded-full bg-[#2A7C6C] px-6 py-2 text-sm text-white hover:bg-[#236358]"
              disabled={isSubmitting || submissionStatus !== "idle"}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...
                </>
              ) : (
                "Confirm & Submit"
              )}
            </Button>
          </div>
          {submissionStatus === "success" && <SuccessMessage />}
          {submissionStatus === "error" && <ErrorMessage message={error} />}
        </CardFooter>
      </Card>
    </motion.div>
  )
}

function SummarySection({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div className="rounded-lg bg-gray-50 p-4">
      <h4 className="mb-2 text-lg font-semibold text-[#2F4858]">{title}</h4>
      <div className="space-y-2">{children}</div>
    </div>
  )
}

function SummaryItem({ label, value }: { label: string; value: string }) {
  return (
    <p className="flex justify-between">
      <span className="font-medium text-gray-600">{label}:</span>
      <span className="text-gray-800">{value}</span>
    </p>
  )
}

function SuccessMessage() {
  return (
    <div className="flex items-center text-green-600">
      <CheckCircle className="mr-2 h-5 w-5" />
      <span>Subscription created successfully!</span>
    </div>
  )
}

function ErrorMessage({ message }: { message: string | null }) {
  return (
    <div className="flex items-center text-red-600">
      <XCircle className="mr-2 h-5 w-5" />
      <span>{message || "Error creating subscription. Please try again."}</span>
    </div>
  )
}