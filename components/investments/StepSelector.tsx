


"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import type { StepComponentProps } from "./StepComponentProps";
import { useRouter } from "next/navigation"; // if you're using the app router; if not, use next/router

const investmentOptions = [
  { id: "yearEndBundle", title: "Monthly Year-end Stokvel Bundle", icon: "🎁" },
  { id: "grocerySchoolBundle", title: "Grocery + Back to School Bundle", icon: "🛒🎒" },
  { id: "monthlyGroceries", title: "Monthly Groceries", icon: "🛒" },
  { id: "funeralBenefits", title: "Funeral Benefits", icon: "💐" },
];

export const StepSelector: React.FC<StepComponentProps> = ({
  formData,
  updateFormData,
  onNext,
}) => {
  const router = useRouter();

  const handleOptionClick = (optionId: string) => {
    if (optionId === "monthlyGroceries") {
      // Immediately redirect the user to the /store page.
      router.push("/store");
    } else {
      // Update the form state and let the multi-step flow continue.
      updateFormData("investmentType", optionId);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="w-full max-w-2xl"
    >
      <h3 className="mb-6 text-2xl font-semibold text-[#2F4858]">
        Choose Your Investment Type
      </h3>
      <div className="grid gap-4 md:grid-cols-2">
        {investmentOptions.map((option) => (
          <Button
            key={option.id}
            onClick={() => handleOptionClick(option.id)}
            className={`flex h-32 flex-col items-center justify-center rounded-lg p-4 text-lg transition-all ${
              formData.investmentType === option.id
                ? "bg-[#2A7C6C] text-white"
                : "bg-white text-[#2F4858] hover:bg-[#2A7C6C] hover:text-white"
            }`}
          >
            <span className="mb-2 text-4xl">{option.icon}</span>
            {option.title}
          </Button>
        ))}
      </div>
      {/* If an option other than "monthlyGroceries" was selected, onNext will be enabled */}
      <Button
        onClick={onNext}
        disabled={!formData.investmentType || formData.investmentType === "monthlyGroceries"}
        className="mt-8 w-29 rounded-full bg-[#2A7C6C] px-8 py-4 text-lg text-white hover:bg-[#236358]"
      >
        Next
      </Button>
    </motion.div>
  );
};

