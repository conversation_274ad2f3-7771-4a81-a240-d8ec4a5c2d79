import type React from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import type { StepComponentProps } from "./StepComponentProps"

const pricingPlans = {
  yearEndBundle: [
    { id: "basic", title: "Basic", price: 350, joiningFee: 35, benefits: ["Monthly year-end stokvel bundle"] },
    {
      id: "standard",
      title: "Standard",
      price: 500,
      joiningFee: 50,
      benefits: ["Enhanced monthly year-end stokvel bundle"],
    },
    {
      id: "premium",
      title: "Premium",
      price: 1000,
      joiningFee: 100,
      benefits: ["Premium monthly year-end stokvel bundle"],
    },
  ],
  grocerySchoolBundle: [
    { id: "basic", title: "Basic", price: 300, joiningFee: 30, benefits: ["Basic grocery and back to school bundle"] },
    {
      id: "standard",
      title: "Standard",
      price: 500,
      joiningFee: 50,
      benefits: ["Standard grocery and back to school bundle"],
    },
    {
      id: "premium",
      title: "Premium",
      price: 700,
      joiningFee: 70,
      benefits: ["Premium grocery and back to school bundle"],
    },
  ],
  monthlyGroceries: [
    {
      id: "groceries",
      title: "Monthly Groceries",
      price: 0,
      joiningFee: 0,
      benefits: ["Access to store page", "Items on sale", "Join local groups for buying"],
    },
  ],
  funeralBenefits: [
    {
      id: "funeral",
      title: "Funeral Benefits",
      price: 0,
      joiningFee: 0,
      benefits: ["6 months waiting period", "1 main member", "9 beneficiaries"],
    },
  ],
}

export const PricingPlans: React.FC<StepComponentProps> = ({ formData, updateFormData, onNext, onPrev }) => {
  const plans = pricingPlans[formData.investmentType as keyof typeof pricingPlans]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="w-full max-w-4xl"
    >
      <h3 className="mb-6 text-2xl font-semibold text-[#2F4858]">Choose Your Plan</h3>
      <div className="grid gap-6 md:grid-cols-3">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`rounded-lg bg-white p-6 shadow-lg transition-all ${
              formData.selectedPlan === plan.id ? "ring-4 ring-[#2A7C6C]" : ""
            }`}
          >
            <h4 className="mb-4 text-xl font-semibold text-[#2F4858]">{plan.title}</h4>
            <p className="mb-4 text-3xl font-bold text-[#2A7C6C]">R{plan.price}</p>
            {plan.joiningFee > 0 && <p className="mb-4 text-sm text-gray-600">Joining fee: R{plan.joiningFee}</p>}
            <ul className="mb-6 space-y-2">
              {plan.benefits.map((benefit, i) => (
                <li key={i} className="flex items-center text-[#2F4858]">
                  <svg className="mr-2 h-5 w-5 text-[#2A7C6C]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  {benefit}
                </li>
              ))}
            </ul>
            <Button
              onClick={() => {
                updateFormData("selectedPlan", plan.id);
                updateFormData("planPrice", plan.price.toString());
                updateFormData("planTitle", plan.title);
                updateFormData("joiningFee", plan.joiningFee.toString());
                updateFormData("benefits", plan.benefits);
              }}
              className={`w-full rounded-full py-2 text-lg ${
                formData.selectedPlan === plan.id
                  ? "bg-[#2A7C6C] text-white"
                  : "bg-gray-200 text-[#2F4858] hover:bg-[#2A7C6C] hover:text-white"
              }`}
            >
              {formData.selectedPlan === plan.id ? "Selected" : "Select"}
            </Button>
          </motion.div>
        ))}
      </div>
      <div className="mt-8 flex justify-between">
        <Button
          onClick={onPrev}
          className="rounded-full bg-gray-200 px-8 py-4 text-lg text-[#2F4858] hover:bg-gray-300"
        >
          Back
        </Button>
        <Button
          onClick={onNext}
          disabled={!formData.selectedPlan}
          className="rounded-full bg-[#2A7C6C] px-8 py-4 text-lg text-white hover:bg-[#236358]"
        >
          Next
        </Button>
      </div>
    </motion.div>
  )
}