// ExistingUserLogin.tsx

"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import type { StepComponentProps } from "./StepComponentProps";

export const ExistingUserLogin: React.FC<StepComponentProps> = ({ formData, updateFormData, onNext, onPrev }) => {
  const { login } = useAuth();
  const [password, setPassword] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState("");

  const handleLogin = async () => {
    setSubmitting(true);
    setError("");
    try {
      if (!password) {
        setError("Password is required");
        setSubmitting(false);
        return;
      }
      // Pass false for redirectToGroup since we handle navigation manually in the investment flow
      const user = await login(formData.email, password, true, false);
      if (!user) {
        setError("Login failed, please try again");
        setSubmitting(false);
        return;
      }
      // Update parent's state using updateFormData.
      updateFormData("name", user.name);
      updateFormData("phone", user.phone || "");
      updateFormData("existingUserId", user._id);
      onNext();
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("An error occurred during login");
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Existing User Login</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium">Email</label>
          <Input value={formData.email} disabled className="mt-1" />
        </div>
        <div>
          <label className="block text-sm font-medium">Password</label>
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            className="mt-1"
          />
          {error && <p className="mt-1 text-xs text-red-600">{error}</p>}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button onClick={onPrev} variant="outline">
          Back
        </Button>
        <Button onClick={handleLogin} disabled={submitting} className="rounded-full bg-[#2A7C6C] px-6 py-2 text-sm text-white hover:bg-[#236358]">
          {submitting ? <Loader2 className="animate-spin h-4 w-4" /> : "Next"}
        </Button>
      </CardFooter>
    </Card>
  );
};