

import type React from "react"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import type { StepComponentProps } from "./StepComponentProps" 

const planDurations = [
  { id: "monthly", title: "Monthly" },
  { id: "quarterly", title: "Quarterly" },
  { id: "annually", title: "Annually" },
]

export const PlanSelector: React.FC<StepComponentProps> = ({ formData, updateFormData, onNext, onPrev }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="w-full max-w-2xl"
    >
      <h3 className="mb-6 text-2xl font-semibold text-[#2F4858]">Select Your Plan Duration</h3>
      <div className="grid gap-4 md:grid-cols-3">
        {planDurations.map((duration) => (
          <Button
            key={duration.id}
            onClick={() => updateFormData("planDuration", duration.id)}
            className={`h-20 text-lg transition-all ${
              formData.planDuration === duration.id
                ? "bg-[#2A7C6C] text-white"
                : "bg-white text-[#2F4858] hover:bg-[#2A7C6C] hover:text-white"
            }`}
          >
            {duration.title}
          </Button>
        ))}
      </div>
      <div className="mt-8 flex justify-between">
        <Button
          onClick={onPrev}
          className="rounded-full bg-gray-200 px-8 py-4 text-lg text-[#2F4858] hover:bg-gray-300"
        >
          Back
        </Button>
        <Button
          onClick={onNext}
          disabled={!formData.planDuration}
          className="rounded-full bg-[#2A7C6C] px-8 py-4 text-lg text-white hover:bg-[#236358]"
        >
          Next
        </Button>
      </div>
    </motion.div>
  )
}
