// components/modals/JoinGroupModal.tsx

"use client";
import { AnimatePresence, motion } from "framer-motion";
import { X, Users, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { JoinGroupForm } from "./JoinGroupForm";
import { createPortal } from "react-dom";
import { useRouter } from "next/navigation";

interface JoinGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId?: string;
}

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 50
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
      duration: 0.5
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: 50,
    transition: {
      duration: 0.3
    }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { duration: 0.3 }
  },
  exit: {
    opacity: 0,
    transition: { duration: 0.3 }
  }
};

export function JoinGroupModal({ isOpen, onClose, productId }: JoinGroupModalProps) {
  const router = useRouter();

  if (!isOpen) return null;

  // Handle successful registration and group joining
  const handleSuccess = (groupId?: string) => {
    onClose();

    // Redirect to the group page after successful joining
    if (groupId) {
      console.log(`Redirecting to group page: /groups/${groupId}`);
      // If a product was provided, go to new-order page, otherwise go to group main page
      const redirectPath = productId
        ? `/groups/${groupId}/new-order`
        : `/groups/${groupId}`;

      setTimeout(() => {
        router.push(redirectPath as any);
      }, 500);
    }
  };

  return createPortal(
    <AnimatePresence mode="wait">
      <motion.div
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-md"
        onClick={onClose}
      >
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="relative w-full max-w-2xl mx-4 max-h-[95vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Glassmorphism Container */}
          <div className="relative bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20">
            {/* Gradient Background Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-emerald-50/50 rounded-3xl" />

            {/* Decorative Elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-emerald-400/20 to-teal-400/20 rounded-full blur-2xl" />

            {/* Close Button */}
            <Button
              onClick={onClose}
              className="absolute right-6 top-6 z-10 h-10 w-10 rounded-full bg-white/80 backdrop-blur-sm border border-gray-200/50 text-gray-600 hover:text-gray-800 hover:bg-white/90 shadow-lg transition-all duration-200"
              variant="ghost"
              size="icon"
            >
              <X size={20} />
            </Button>

            {/* Header Section */}
            <div className="relative px-8 pt-8 pb-6 border-b border-gray-200/30">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="flex items-center gap-4 mb-4"
              >
                <div className="relative">
                  <div className="h-12 w-12 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 180, 360]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="absolute -top-1 -right-1"
                  >
                    <Sparkles className="h-4 w-4 text-yellow-500" />
                  </motion.div>
                </div>
                <div>
                  <h2
                    className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent"
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    Join Your Community
                  </h2>
                  <p
                    className="text-gray-600 mt-1"
                    style={{ fontFamily: "Avenir, sans-serif" }}
                  >
                    Connect with your local Stokvel group and start saving together
                  </p>
                </div>
              </motion.div>
            </div>

            {/* Form Container */}
            <div className="relative px-8 py-6 max-h-[70vh] overflow-y-auto scrollbar-hide">
              <JoinGroupForm onSuccess={handleSuccess} productId={productId} />
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>,
    document.body
  );
}
