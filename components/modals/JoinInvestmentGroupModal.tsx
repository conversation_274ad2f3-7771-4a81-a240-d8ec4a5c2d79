// components/modals/JoinInvestmentGroupModal.tsx
import { useState } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { X } from "lucide-react"
import { Button } from "@/components/ui/button"

interface JoinGroupModalProps {
  buttonText: string
}

export const JoinInvestmentGroupModal: React.FC<JoinGroupModalProps> = ({ buttonText }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>{buttonText}</Button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative w-full max-w-md rounded-2xl bg-white bg-opacity-90 p-8 shadow-xl"
            >
              <Button
                onClick={() => setIsOpen(false)}
                className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
                variant="ghost"
              >
                <X size={24} />
              </Button>
              <h2 className="mb-4 text-xl font-semibold">Join a Stokvel Group</h2>
              <p>Enter the group code to join.</p>
              <input
                type="text"
                placeholder="Group Code"
                className="mt-4 w-full rounded-md border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#2A7C6C]"
              />
              <Button
                className="mt-6 w-full rounded-full bg-[#2A7C6C] px-8 py-4 text-lg text-white hover:bg-[#236358]"
                onClick={() => setIsOpen(false)}
              >
                Join Group
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

