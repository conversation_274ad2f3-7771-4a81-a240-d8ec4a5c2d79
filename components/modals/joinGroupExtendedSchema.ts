import { z } from "zod";

export const joinGroupExtendedSchema = z.object({
  email: z.string().email("A valid email is required"),
  existingUserId: z.string().optional(), // We'll store the found user ID if user is found

  // Name validation - required for new users
  name: z
    .string()
    .min(3, "Name must be at least 3 characters")
    .optional(),

  // Phone validation - must be between 10-15 digits
  phone: z
    .string()
    .regex(/^\d{10,15}$/, "Phone number must be between 10 and 15 digits")
    .optional(),

  // Password validation - minimum 6 characters
  password: z
    .string()
    .min(6, "Password must be at least 6 characters")
    .optional(),

  // Confirm password validation
  confirmPassword: z
    .string()
    .optional(),

  // Location validation
  location: z
    .string()
    .min(2, "Location must be at least 2 characters"),

  // Group ID validation
  groupId: z
    .string()
    .optional(),

  // Flag to indicate if we're logging in for shopping (without redirect)
  loginForShopping: z
    .string()
    .optional(),
})
// Add custom validation for password confirmation
.refine(data => !data.password || !data.confirmPassword || data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});
