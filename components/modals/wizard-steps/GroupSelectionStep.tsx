// components/modals/wizard-steps/GroupSelectionStep.tsx

"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { MapPin, Users, ArrowRight, ArrowLeft, CheckCircle, Mail, Shield, Building, Home, Map, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { EnhancedProvinceSelect } from "@/components/ui/enhanced-province-select";
import { useGetAllStokvelGroupsQuery } from "@/lib/redux/features/groups/groupsApiSlice";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import type { WizardData, WizardStep } from "../JoinGroupWizard";
import type { StokvelGroup } from "@/types/stokvelgroup";

interface GroupSelectionStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  clearWizardData: () => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: () => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function GroupSelectionStep({
  wizardData,
  updateWizardData,
  clearWizardData,
  goToStep,
  onSuccess,
  productId,
  setIsLoading
}: GroupSelectionStepProps) {
  const [selectedGroupId, setSelectedGroupId] = useState(wizardData.selectedGroupId || "");
  const [availableGroups, setAvailableGroups] = useState<StokvelGroup[]>([]);

  // Location selection hooks
  const {
    selectionData,
    handleProvinceChange,
    handleCityChange,
    handleTownshipChange,
    handleLocationChange,
    isSelectionComplete,
  } = useLocations();

  // API hooks
  const { data: allGroups = [] } = useGetAllStokvelGroupsQuery();

  // Filter groups based on selected location
  useEffect(() => {
    if (!selectionData.selectedLocationId) {
      setAvailableGroups([]);
      return;
    }

    // Filter groups by the selected locationId
    const filtered = allGroups.filter(group =>
      group.locationId === selectionData.selectedLocationId
    );
    setAvailableGroups(filtered);

    // Clear selected group when location changes
    if (selectedGroupId && !filtered.find(g => g._id === selectedGroupId)) {
      setSelectedGroupId("");
      updateWizardData({ selectedGroupId: "" });
    }
  }, [selectionData.selectedLocationId, allGroups, selectedGroupId, updateWizardData]);

  // Handle location selection change
  const handleLocationSelectionChange = (locationId: string) => {
    handleLocationChange(locationId);
    updateWizardData({
      selectedProvinceId: selectionData.selectedProvinceId,
      selectedCityId: selectionData.selectedCityId,
      selectedTownshipId: selectionData.selectedTownshipId,
      selectedLocationId: locationId,
      selectedGroupId: "" // Clear selected group
    });
  };

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroupId(groupId);

    // Update wizard data with selected group
    const updatedData = {
      selectedGroupId: groupId,
      email: wizardData.email // Ensure email is preserved
    };
    updateWizardData(updatedData);

    // Also save to localStorage immediately
    const dataToSave = {
      ...wizardData,
      ...updatedData
    };
    localStorage.setItem('joinGroupWizardData', JSON.stringify(dataToSave));

    // Debug log
    console.log('Group selected:', { groupId, email: wizardData.email, dataToSave });
  };

  const handleContinue = () => {
    if (!isSelectionComplete) return;

    // Prepare the complete wizard data with email and group selection
    const updatedWizardData = {
      email: wizardData.email, // Ensure email is preserved
      selectedProvinceId: selectionData.selectedProvinceId,
      selectedCityId: selectionData.selectedCityId,
      selectedTownshipId: selectionData.selectedTownshipId,
      selectedLocationId: selectionData.selectedLocationId,
      selectedGroupId: selectedGroupId,
      // Preserve all existing wizard data
      isUserKnown: wizardData.isUserKnown,
      isMemberOfAnyGroup: wizardData.isMemberOfAnyGroup,
      membershipData: wizardData.membershipData,
      userByEmailData: wizardData.userByEmailData,
      anyGroupMembershipData: wizardData.anyGroupMembershipData
    };

    // Save to wizard data state
    updateWizardData(updatedWizardData);

    // Also save directly to localStorage to ensure persistence
    const finalDataToSave = {
      ...wizardData,
      ...updatedWizardData
    };
    localStorage.setItem('joinGroupWizardData', JSON.stringify(finalDataToSave));

    // Debug log
    console.log('Continuing to next step with data:', finalDataToSave);

    // Navigate to user registration
    if (wizardData.isUserKnown) {
      // Existing user without group - go to location selection for joining
      goToStep("location-selection");
    } else {
      // New user - go to registration
      goToStep("user-registration");
    }
  };

  const canProceed = isSelectionComplete && selectedGroupId;

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4">
          <Users className="h-8 w-8 text-white" />
        </div>
        <h3
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Choose Your Stokvel Group
        </h3>
        <p
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Select your location to find and join a local Stokvel group
        </p>
      </motion.div>

      {/* Verified Email Display */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="p-4 bg-green-50 border border-green-200 rounded-xl"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Shield className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-green-800">Verified Email Address</p>
            <p className="text-sm text-green-600 flex items-center gap-2">
              <Mail className="h-3 w-3" />
              {wizardData.email}
            </p>
          </div>
          <CheckCircle className="h-5 w-5 text-green-500" />
        </div>
      </motion.div>

      {/* Location Selection Cascade */}
      <div className="space-y-4 sm:space-y-3">
        {/* Province Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-2"
        >
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <Map className="h-4 w-4 text-emerald-600" />
            Province
          </Label>
          <EnhancedProvinceSelect
            provinces={selectionData.availableProvinces}
            value={selectionData.selectedProvinceId}
            onValueChange={handleProvinceChange}
            placeholder="Select your province"
            isLoading={selectionData.isLoading.provinces}
            className="text-base sm:text-sm"
          />
        </motion.div>

        {/* City Selection */}
        {selectionData.selectedProvinceId && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-2"
          >
            <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Building className="h-4 w-4 text-purple-600" />
              City
            </Label>
            <Select value={selectionData.selectedCityId} onValueChange={handleCityChange}>
              <SelectTrigger className="h-12 text-base sm:text-sm">
                <SelectValue placeholder="Select your city" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                {selectionData.isLoading.cities ? (
                  <div className="p-2">
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  selectionData.availableCities.map((city) => (
                    <SelectItem
                      key={city._id}
                      value={city._id}
                      className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                    >
                      {city.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </motion.div>
        )}

        {/* Township Selection */}
        {selectionData.selectedCityId && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-2"
          >
            <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Home className="h-4 w-4 text-orange-600" />
              Township
            </Label>
            <Select value={selectionData.selectedTownshipId} onValueChange={handleTownshipChange}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Select your township" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                {selectionData.isLoading.townships ? (
                  <div className="p-2">
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  selectionData.availableTownships.map((township) => (
                    <SelectItem
                      key={township._id}
                      value={township._id}
                      className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                    >
                      {township.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </motion.div>
        )}

        {/* Location Selection */}
        {selectionData.selectedTownshipId && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="space-y-2"
          >
            <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <MapPin className="h-4 w-4 text-pink-600" />
              Location
            </Label>
            <Select value={selectionData.selectedLocationId} onValueChange={handleLocationSelectionChange}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Select your location" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                {selectionData.isLoading.locations ? (
                  <div className="p-2">
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  selectionData.availableLocations.map((location) => (
                    <SelectItem
                      key={location._id}
                      value={location._id}
                      className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                    >
                      <div className="flex flex-col">
                        <span className="font-medium">{location.name}</span>
                        {location.description && (
                          <span className="text-gray-500 text-xs mt-0.5">
                            {location.description}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </motion.div>
        )}
      </div>

      {/* Available Groups */}
      {selectionData.selectedLocationId && availableGroups.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-4"
        >
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-emerald-600" />
            <h4 className="font-semibold text-gray-800">
              Available Stokvel Groups ({availableGroups.length})
            </h4>
          </div>
          <div className="grid gap-4 max-h-96 overflow-y-auto">
            {availableGroups.map((group, index) => {
              // Build location path for display
              const selectedProvince = selectionData.availableProvinces.find(p => p._id === selectionData.selectedProvinceId);
              const selectedCity = selectionData.availableCities.find(c => c._id === selectionData.selectedCityId);
              const selectedTownship = selectionData.availableTownships.find(t => t._id === selectionData.selectedTownshipId);
              const selectedLocation = selectionData.availableLocations.find(l => l._id === selectionData.selectedLocationId);

              const locationPath = [
                selectedLocation?.name,
                selectedTownship?.name,
                selectedCity?.name,
                selectedProvince?.name
              ].filter(Boolean).join(", ");

              return (
                <motion.div
                  key={group._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="group"
                >
                  <Card
                    className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                      selectedGroupId === group._id
                        ? "ring-2 ring-emerald-500 bg-emerald-50 border-emerald-200 shadow-md"
                        : "hover:shadow-md border-gray-200 hover:border-emerald-300"
                    }`}
                    onClick={() => handleGroupSelect(group._id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 space-y-3">
                          {/* Group Header */}
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                              <Users className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h5 className="font-bold text-lg text-gray-800">
                                {group.name}
                              </h5>
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {locationPath}
                              </p>
                            </div>
                          </div>

                          {/* Group Stats */}
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Users className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-gray-800">
                                  {group.members?.length || 0} Members
                                </p>
                                <p className="text-xs text-gray-500">Active participants</p>
                              </div>
                            </div>

                            {group.totalSales && (
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                  <TrendingUp className="h-4 w-4 text-green-600" />
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-gray-800">
                                    R{group.totalSales.toLocaleString()}
                                  </p>
                                  <p className="text-xs text-gray-500">Total saved</p>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Group Description */}
                          {group.description && (
                            <p className="text-sm text-gray-600 line-clamp-2">
                              {group.description}
                            </p>
                          )}

                          {/* Group Badges */}
                          <div className="flex items-center gap-2 flex-wrap">
                            <Badge className="bg-emerald-100 text-emerald-700 border-emerald-200">
                              Active Group
                            </Badge>
                            {group.isVerified && (
                              <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                                Verified
                              </Badge>
                            )}
                            {group.members?.length >= 10 && (
                              <Badge className="bg-purple-100 text-purple-700 border-purple-200">
                                Popular
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Selection Indicator */}
                        <div className="flex-shrink-0 ml-4">
                          {selectedGroupId === group._id ? (
                            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                              <CheckCircle className="h-5 w-5 text-white" />
                            </div>
                          ) : (
                            <div className="w-8 h-8 border-2 border-gray-300 rounded-full group-hover:border-emerald-400 transition-colors" />
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      )}

      {/* No Groups Message */}
      {selectionData.selectedLocationId && availableGroups.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center py-8"
        >
          <div className="mx-auto w-16 h-16 bg-amber-100 rounded-2xl flex items-center justify-center mb-4">
            <MapPin className="h-8 w-8 text-amber-600" />
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">No Stokvel groups in this location yet</h4>
          <p className="text-sm text-gray-600 mb-4">
            Be the first to start a Stokvel group in your area, or check nearby locations.
          </p>
          <Button
            variant="outline"
            onClick={() => {
              // Clear location selection to try again
              handleLocationChange("");
              setSelectedGroupId("");
            }}
            className="border-amber-300 text-amber-700 hover:bg-amber-50"
          >
            Try Different Location
          </Button>
        </motion.div>
      )}

      {/* Continue Button */}
      {canProceed && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button
            onClick={handleContinue}
            className="w-full h-12 bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            <span className="flex items-center gap-2">
              Continue with Selected Group
              <ArrowRight className="h-4 w-4" />
            </span>
          </Button>
        </motion.div>
      )}

      {/* Back Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="pt-2"
      >
        <Button
          variant="ghost"
          onClick={() => goToStep("email-verification")}
          className="w-full h-10 text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Email Verification
        </Button>
      </motion.div>
    </div>
  );
}
