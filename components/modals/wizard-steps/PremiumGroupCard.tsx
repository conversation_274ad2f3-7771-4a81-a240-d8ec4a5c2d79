// components/modals/wizard-steps/PremiumGroupCard.tsx

"use client";

import { motion } from "framer-motion";
import { Users, TrendingUp, MapPin, Star, CheckCircle, Clock, ShoppingBag } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface GroupData {
  _id: string;
  name: string;
  description?: string;
  members?: string[];
  totalSales?: number;
  avgOrderValue?: number;
  activeOrders?: number;
  locationId?: string;
  geolocation?: string;
  createdAt?: string;
  deliveryStatus?: 'pending' | 'in-transit' | 'delivered';
}

interface PremiumGroupCardProps {
  group: GroupData;
  isSelected: boolean;
  onSelect: (groupId: string) => void;
  locationPath?: string;
  index?: number;
}

export function PremiumGroupCard({ 
  group, 
  isSelected, 
  onSelect, 
  locationPath,
  index = 0 
}: PremiumGroupCardProps) {
  const memberCount = group.members?.length || 0;
  const totalSales = group.totalSales || 0;
  const avgOrderValue = group.avgOrderValue || 0;
  const activeOrders = group.activeOrders || 0;

  // Calculate group activity level
  const getActivityLevel = () => {
    if (activeOrders > 10) return { level: "High", color: "text-green-600", bgColor: "bg-green-100" };
    if (activeOrders > 5) return { level: "Medium", color: "text-yellow-600", bgColor: "bg-yellow-100" };
    if (activeOrders > 0) return { level: "Low", color: "text-blue-600", bgColor: "bg-blue-100" };
    return { level: "New", color: "text-gray-600", bgColor: "bg-gray-100" };
  };

  const activity = getActivityLevel();

  // Calculate savings per member
  const savingsPerMember = memberCount > 0 ? Math.round(totalSales / memberCount) : 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card 
        className={`cursor-pointer transition-all duration-300 ${
          isSelected
            ? "ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200 shadow-lg"
            : "hover:shadow-lg border-gray-200 hover:border-gray-300"
        }`}
        onClick={() => onSelect(group._id)}
      >
        <CardContent className="p-4 sm:p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-semibold text-lg text-gray-800 line-clamp-1">
                  {group.name}
                </h3>
                {isSelected && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="h-4 w-4 text-white" />
                  </motion.div>
                )}
              </div>
              
              {/* Location */}
              <div className="flex items-center gap-1 text-sm text-gray-600 mb-3">
                <MapPin className="h-3 w-3" />
                <span>{locationPath || group.geolocation || "Location not specified"}</span>
              </div>

              {/* Description */}
              {group.description && (
                <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                  {group.description}
                </p>
              )}
            </div>

            {/* Activity Badge */}
            <Badge 
              variant="secondary" 
              className={`${activity.bgColor} ${activity.color} border-0 font-medium`}
            >
              {activity.level} Activity
            </Badge>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-4">
            {/* Members */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-800">{memberCount}</div>
                <div className="text-xs text-gray-500">Members</div>
              </div>
            </div>

            {/* Total Savings */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-800">
                  R{totalSales.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Total Saved</div>
              </div>
            </div>

            {/* Active Orders */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <ShoppingBag className="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-800">{activeOrders}</div>
                <div className="text-xs text-gray-500">Active Orders</div>
              </div>
            </div>

            {/* Avg per Member */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Star className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <div className="text-sm font-semibold text-gray-800">
                  R{savingsPerMember.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">Per Member</div>
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>Bulk buying discounts</span>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>Community support network</span>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>Shared delivery costs</span>
            </div>
          </div>

          {/* Join Button */}
          {isSelected && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3 }}
            >
              <Button 
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
                size="sm"
              >
                Join This Group
              </Button>
            </motion.div>
          )}

          {/* Created Date */}
          {group.createdAt && (
            <div className="flex items-center gap-1 text-xs text-gray-400 mt-3 pt-3 border-t border-gray-100">
              <Clock className="h-3 w-3" />
              <span>
                Created {new Date(group.createdAt).toLocaleDateString()}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
