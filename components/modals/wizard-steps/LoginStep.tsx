// components/modals/wizard-steps/LoginStep.tsx

"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Lock, Mail, ShoppingCart, ArrowRight, ArrowLeft, LogIn, MapPin } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/context/AuthContext";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import type { WizardData, WizardStep } from "../JoinGroupWizard";

interface LoginStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  clearWizardData: () => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: () => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function LoginStep({
  wizardData,
  updateWizardData,
  clearWizardData,
  goToStep,
  onSuccess,
  productId,
  setIsLoading
}: LoginStepProps) {
  const { login, loginForShopping } = useAuth();
  const [addToCart] = useAddToCartMutation();
  
  const [password, setPassword] = useState("");
  const [loginError, setLoginError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginType, setLoginType] = useState<"shopping" | "continue">("continue");

  const handleLogin = async (type: "shopping" | "continue") => {
    if (!password) {
      setLoginError("Please enter your password");
      return;
    }

    setIsSubmitting(true);
    setIsLoading(true);
    setLoginError("");

    try {
      let user;
      
      if (type === "shopping") {
        user = await loginForShopping(wizardData.email, password, true);
      } else {
        user = await login(wizardData.email, password, true, false);
      }

      // Add product to cart if shopping and productId provided
      if (type === "shopping" && productId && user) {
        try {
          const groupId = wizardData.membershipData?.group?._id;
          if (groupId) {
            await addToCart({
              userId: user._id,
              productId,
              quantity: 1,
              groupId
            }).unwrap();
          }
        } catch (error) {
          console.error("Error adding product to cart:", error);
        }
      }

      // Success - clear wizard data and close modal
      clearWizardData();
      setTimeout(() => {
        onSuccess?.();
      }, 500);
    } catch (error) {
      console.error("Login error:", error);
      setLoginError("Login failed. Please check your password.");
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  const handleRelocationClick = () => {
    updateWizardData({ isRelocation: true });
    goToStep("relocation");
  };

  const membershipGroup = wizardData.membershipData?.group;

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-4">
          <LogIn className="h-8 w-8 text-white" />
        </div>
        <h3 
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Welcome back, {wizardData.userByEmailData?.name}!
        </h3>
        <p 
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Please enter your password to continue
        </p>
      </motion.div>

      {/* Current Group Info */}
      {membershipGroup && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center">
                  <MapPin className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-green-800">
                    {membershipGroup.groupName}
                  </h4>
                  <p className="text-sm text-green-700">
                    {membershipGroup.geolocation}
                  </p>
                </div>
                <Badge className="bg-green-100 text-green-800 border-green-300">
                  Current Group
                </Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Login Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        {/* Email Field (Read-only) */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium text-gray-700">
            Email Address
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Mail className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="email"
              value={wizardData.email}
              readOnly
              className="pl-10 h-12 bg-gray-50 text-gray-600"
            />
          </div>
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium text-gray-700">
            Password
          </Label>
          <div className="relative">
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
              <Lock className="h-4 w-4 text-gray-400" />
            </div>
            <Input
              id="password"
              type="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="pl-10 h-12"
              onKeyPress={(e) => {
                if (e.key === "Enter" && password) {
                  handleLogin(loginType);
                }
              }}
            />
          </div>
        </div>

        {/* Error Message */}
        {loginError && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="p-3 bg-red-50 border border-red-200 rounded-xl"
          >
            <p className="text-sm text-red-700">{loginError}</p>
          </motion.div>
        )}
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="space-y-3"
      >
        {/* Primary Actions */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {productId ? (
            <Button
              onClick={() => {
                setLoginType("shopping");
                handleLogin("shopping");
              }}
              disabled={!password || isSubmitting}
              className="h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {isSubmitting && loginType === "shopping" ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Logging in...
                </div>
              ) : (
                <span className="flex items-center gap-2">
                  <ShoppingCart className="h-4 w-4" />
                  Login & Shop
                </span>
              )}
            </Button>
          ) : (
            <Button
              onClick={() => {
                setLoginType("continue");
                handleLogin("continue");
              }}
              disabled={!password || isSubmitting}
              className="h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {isSubmitting && loginType === "continue" ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Logging in...
                </div>
              ) : (
                <span className="flex items-center gap-2">
                  Login & Continue
                  <ArrowRight className="h-4 w-4" />
                </span>
              )}
            </Button>
          )}

          {/* Relocation Button */}
          <Button
            variant="outline"
            onClick={handleRelocationClick}
            className="h-12 border-orange-300 text-orange-700 hover:bg-orange-50 hover:border-orange-400"
          >
            <MapPin className="h-4 w-4 mr-2" />
            Relocate Group
          </Button>
        </div>

        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => goToStep("email-verification")}
          className="w-full h-10 text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Email
        </Button>
      </motion.div>

      {/* Help Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="text-center"
      >
        <p className="text-xs text-gray-500">
          Having trouble? Contact support for assistance
        </p>
      </motion.div>
    </div>
  );
}
