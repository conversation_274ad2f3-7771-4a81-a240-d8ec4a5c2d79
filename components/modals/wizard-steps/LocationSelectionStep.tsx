// components/modals/wizard-steps/LocationSelectionStep.tsx

"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { MapPin, Users, ArrowLeft, CheckCircle, Mail, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { EnhancedProvinceSelect } from "@/components/ui/enhanced-province-select";
import {
  useGetAllStokvelGroupsQuery,
  useJoinGroupMutation,
  useStoreLocationRequestMutation
} from "@/lib/redux/features/groups/groupsApiSlice";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useLocations } from "@/lib/redux/hooks/useLocations";
import { PremiumGroupCard } from "./PremiumGroupCard";
import type { WizardData, WizardStep } from "../JoinGroupWizard";
import type { StokvelGroup } from "@/types/stokvelgroup";

interface LocationSelectionStepProps {
  wizardData: WizardData;
  updateWizardData: (data: Partial<WizardData>) => void;
  clearWizardData: () => void;
  goToStep: (step: WizardStep) => void;
  onSuccess?: (groupId?: string) => void;
  productId?: string;
  setIsLoading: (loading: boolean) => void;
}

export function LocationSelectionStep({
  wizardData,
  updateWizardData,
  clearWizardData,
  goToStep,
  onSuccess,
  productId,
  setIsLoading
}: LocationSelectionStepProps) {
  const [selectedGroupId, setSelectedGroupId] = useState(wizardData.selectedGroupId || "");
  const [availableGroups, setAvailableGroups] = useState<StokvelGroup[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Location selection hooks
  const {
    selectionData,
    handleProvinceChange,
    handleCityChange,
    handleTownshipChange,
    handleLocationChange,
    isSelectionComplete,
  } = useLocations();

  // API hooks
  const { data: allGroups = [] } = useGetAllStokvelGroupsQuery();
  const [joinGroup] = useJoinGroupMutation();
  const [storeLocationRequest] = useStoreLocationRequestMutation();
  const [addToCart] = useAddToCartMutation();

  // Check if we have a pre-selected group from group-selection step
  const hasPreSelectedGroup = wizardData.selectedGroupId && wizardData.selectedLocationId;



  // Filter groups based on selected location
  useEffect(() => {
    if (!selectionData.selectedLocationId) {
      setAvailableGroups([]);
      return;
    }

    // Filter groups by the selected locationId
    const filtered = allGroups.filter(group =>
      group.locationId === selectionData.selectedLocationId
    );
    setAvailableGroups(filtered);

    // Clear selected group when location changes
    if (selectedGroupId && !filtered.find(g => g._id === selectedGroupId)) {
      setSelectedGroupId("");
      updateWizardData({ selectedGroupId: "" });
    }
  }, [selectionData.selectedLocationId, allGroups, selectedGroupId, updateWizardData]);

  // Handle location selection change
  const handleLocationSelectionChange = (locationId: string) => {
    handleLocationChange(locationId);
    updateWizardData({
      location: locationId, // Store locationId instead of text
      selectedGroupId: "" // Clear selected group
    });
  };



  const handleSubmit = useCallback(async () => {
    // Use pre-selected group if available, otherwise use current selection
    const groupToJoin = wizardData.selectedGroupId || selectedGroupId;
    const locationToUse = wizardData.selectedLocationId || selectionData.selectedLocationId;

    if (!groupToJoin && !locationToUse) return;

    setIsSubmitting(true);
    setIsLoading(true);

    try {
      const userId = wizardData.userByEmailData?._id;
      if (!userId) {
        throw new Error("User ID not found");
      }

      if (groupToJoin) {
        // Join selected group
        const result = await joinGroup({
          userId,
          groupId: groupToJoin,
          isRelocation: false
        }).unwrap();

        if (result.success) {
          // Add product to cart if provided
          if (productId) {
            try {
              await addToCart({
                userId,
                productId,
                quantity: 1,
                groupId: groupToJoin
              }).unwrap();
            } catch (error) {
              console.error("Error adding product to cart:", error);
            }
          }

          // Success - clear wizard data and close modal
          clearWizardData();
          setTimeout(() => {
            onSuccess?.(groupToJoin);
          }, 500);
        }
      } else {
        // No matching groups - store location request
        await storeLocationRequest({
          location: locationToUse || "Unknown location",
          userId,
        });

        // Show success message, clear wizard data and close
        clearWizardData();
        setTimeout(() => {
          onSuccess?.();
        }, 500);
      }
    } catch (error) {
      console.error("Error in location selection:", error);
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  }, [
    wizardData.selectedGroupId,
    wizardData.selectedLocationId,
    wizardData.userByEmailData?._id,
    selectedGroupId,
    selectionData.selectedLocationId,
    joinGroup,
    addToCart,
    storeLocationRequest,
    clearWizardData,
    onSuccess,
    productId,
    setIsLoading,
    setIsSubmitting
  ]);

  // Auto-join pre-selected group
  useEffect(() => {
    if (hasPreSelectedGroup && !isSubmitting) {
      // Small delay to show the UI briefly before auto-joining
      const timer = setTimeout(() => {
        handleSubmit();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [hasPreSelectedGroup, isSubmitting, handleSubmit]);

  const canProceed = isSelectionComplete && (selectedGroupId || availableGroups.length === 0);

  // If we have a pre-selected group, show joining confirmation
  if (hasPreSelectedGroup) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-2"
        >
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4">
            <Users className="h-8 w-8 text-white" />
          </div>
          <h3
            className="text-xl font-semibold text-gray-800"
            style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
          >
            Joining Your Selected Group
          </h3>
          <p
            className="text-gray-600"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            We're adding you to your chosen Stokvel group
          </p>
        </motion.div>

        {/* Verified Email Display */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="p-4 bg-green-50 border border-green-200 rounded-xl"
        >
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <Shield className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-green-800">Verified Email Address</p>
              <p className="text-sm text-green-600 flex items-center gap-2">
                <Mail className="h-3 w-3" />
                {wizardData.email}
              </p>
            </div>
            <CheckCircle className="h-5 w-5 text-green-500" />
          </div>
        </motion.div>

        {/* Processing Message */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="text-center py-8"
        >
          <div className="mx-auto w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mb-4">
            <div className="w-8 h-8 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin" />
          </div>
          <h4 className="font-semibold text-gray-800 mb-2">Processing your group membership</h4>
          <p className="text-sm text-gray-600">
            Please wait while we complete your registration...
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-2"
      >
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4">
          <MapPin className="h-8 w-8 text-white" />
        </div>
        <h3
          className="text-xl font-semibold text-gray-800"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          Find Your Community
        </h3>
        <p
          className="text-gray-600"
          style={{ fontFamily: "Avenir, sans-serif" }}
        >
          Select your location to discover local Stokvel groups
        </p>
      </motion.div>

      {/* Verified Email Display */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="p-4 bg-green-50 border border-green-200 rounded-xl"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Shield className="h-4 w-4 text-white" />
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-green-800">Verified Email Address</p>
            <p className="text-sm text-green-600 flex items-center gap-2">
              <Mail className="h-3 w-3" />
              {wizardData.email}
            </p>
          </div>
          <CheckCircle className="h-5 w-5 text-green-500" />
        </div>
      </motion.div>

      {/* Location Selection Cascade */}
      <div className="space-y-4 sm:space-y-3">
        {/* Province Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-2"
        >
          <Label className="text-sm font-medium text-gray-700">Province</Label>
          <EnhancedProvinceSelect
            provinces={selectionData.availableProvinces}
            value={selectionData.selectedProvinceId}
            onValueChange={handleProvinceChange}
            placeholder="Select your province"
            isLoading={selectionData.isLoading.provinces}
            className="text-base sm:text-sm"
          />
        </motion.div>

        {/* City Selection */}
        {selectionData.selectedProvinceId && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-2"
          >
            <Label className="text-sm font-medium text-gray-700">City</Label>
            <Select value={selectionData.selectedCityId} onValueChange={handleCityChange}>
              <SelectTrigger className="h-12 text-base sm:text-sm">
                <SelectValue placeholder="Select your city" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                {selectionData.isLoading.cities ? (
                  <div className="p-2">
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  selectionData.availableCities.map((city) => (
                    <SelectItem
                      key={city._id}
                      value={city._id}
                      className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                    >
                      {city.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </motion.div>
        )}

        {/* Township Selection */}
        {selectionData.selectedCityId && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-2"
          >
            <Label className="text-sm font-medium text-gray-700">Township</Label>
            <Select value={selectionData.selectedTownshipId} onValueChange={handleTownshipChange}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Select your township" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                {selectionData.isLoading.townships ? (
                  <div className="p-2">
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  selectionData.availableTownships.map((township) => (
                    <SelectItem
                      key={township._id}
                      value={township._id}
                      className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                    >
                      {township.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </motion.div>
        )}

        {/* Location Selection */}
        {selectionData.selectedTownshipId && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="space-y-2"
          >
            <Label className="text-sm font-medium text-gray-700">Location</Label>
            <Select value={selectionData.selectedLocationId} onValueChange={handleLocationSelectionChange}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Select your location" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-lg max-h-60 overflow-y-auto">
                {selectionData.isLoading.locations ? (
                  <div className="p-2">
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  selectionData.availableLocations.map((location) => (
                    <SelectItem
                      key={location._id}
                      value={location._id}
                      className="bg-white hover:bg-gray-50 focus:bg-gray-50 text-gray-900 cursor-pointer px-3 py-2 text-sm"
                    >
                      <div className="flex flex-col">
                        <span className="font-medium">{location.name}</span>
                        {location.description && (
                          <span className="text-gray-500 text-xs mt-0.5">
                            {location.description}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </motion.div>
        )}
      </div>

      {/* Available Groups */}
      {selectionData.selectedLocationId && availableGroups.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-3"
        >
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-green-600" />
            <h4 className="font-semibold text-gray-800">
              Available Groups ({availableGroups.length})
            </h4>
          </div>
          <div className="grid gap-4 max-h-96 overflow-y-auto">
            {availableGroups.map((group, index) => {
              // Build location path for display
              const selectedProvince = selectionData.availableProvinces.find(p => p._id === selectionData.selectedProvinceId);
              const selectedCity = selectionData.availableCities.find(c => c._id === selectionData.selectedCityId);
              const selectedTownship = selectionData.availableTownships.find(t => t._id === selectionData.selectedTownshipId);
              const selectedLocation = selectionData.availableLocations.find(l => l._id === selectionData.selectedLocationId);

              const locationPath = [
                selectedLocation?.name,
                selectedTownship?.name,
                selectedCity?.name,
                selectedProvince?.name
              ].filter(Boolean).join(", ");

              return (
                <PremiumGroupCard
                  key={group._id}
                  group={group}
                  isSelected={selectedGroupId === group._id}
                  onSelect={setSelectedGroupId}
                  locationPath={locationPath}
                  index={index}
                />
              );
            })}
          </div>
        </motion.div>
      )}

      {/* No Groups Message */}
      {selectionData.selectedLocationId && availableGroups.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-8"
        >
          <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
            <MapPin className="h-6 w-6 text-gray-400" />
          </div>
          <h4 className="font-medium text-gray-800 mb-1">No groups in this location yet</h4>
          <p className="text-sm text-gray-600">
            We'll notify you when a group becomes available in your area.
          </p>
        </motion.div>
      )}

      {/* Continue Button */}
      {isSelectionComplete && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button
            onClick={handleSubmit}
            disabled={!canProceed || isSubmitting}
            className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Processing...
              </div>
            ) : selectedGroupId ? (
              "Join Selected Group"
            ) : (
              "Request Group for This Location"
            )}
          </Button>
        </motion.div>
      )}

      {/* Back Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="pt-2"
      >
        <Button
          variant="ghost"
          onClick={() => goToStep(wizardData.isUserKnown ? "email-verification" : "user-registration")}
          className="w-full h-10 text-gray-600 hover:text-gray-800"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Previous Step
        </Button>
      </motion.div>
    </div>
  );
}
