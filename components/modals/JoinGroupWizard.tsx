// components/modals/JoinGroupWizard.tsx

"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { EmailVerificationStep } from "./wizard-steps/EmailVerificationStep";
import { GroupSelectionStep } from "./wizard-steps/GroupSelectionStep";
import { UserRegistrationStep } from "./wizard-steps/UserRegistrationStep";
import { LocationSelectionStep } from "./wizard-steps/LocationSelectionStep";
import { LoginStep } from "./wizard-steps/LoginStep";
import { RelocationStep } from "./wizard-steps/RelocationStep";
import type { User } from "@/types/user";

export type WizardStep =
  | "email-verification"
  | "group-selection"
  | "user-registration"
  | "location-selection"
  | "login"
  | "relocation"
  | "success";

// Define types for API responses based on the actual API slice types
interface MembershipData {
  isMember: boolean;
  group: {
    _id: string;
    groupName: string;
    geolocation: string;
  } | null;
}

interface AnyGroupMembershipData {
  isMemberOfAnyGroup: boolean;
  groupIds?: string[];
}

export interface WizardData {
  email: string;
  isUserKnown: boolean;
  isMemberOfAnyGroup: boolean;
  membershipData?: MembershipData;
  userByEmailData?: User;
  name?: string;
  phone?: string;
  password?: string;
  location?: string; // Can be locationId (new) or location text (legacy)
  selectedGroupId?: string;
  isRelocation?: boolean;
  // NEW: Location hierarchy data for enhanced selection
  selectedProvinceId?: string;
  selectedCityId?: string;
  selectedTownshipId?: string;
  selectedLocationId?: string;
  // Additional membership data
  anyGroupMembershipData?: AnyGroupMembershipData;
}

interface JoinGroupWizardProps {
  onSuccess?: (groupId?: string) => void;
  productId?: string;
}

const stepTitles: Record<WizardStep, string> = {
  "email-verification": "Verify Your Email",
  "group-selection": "Choose Your Stokvel Group",
  "user-registration": "Create Your Account",
  "location-selection": "Find Your Group",
  "login": "Welcome Back",
  "relocation": "Relocate to New Group",
  "success": "Welcome to Your Community!"
};

const stepOrder: WizardStep[] = [
  "email-verification",
  "group-selection",
  "user-registration",
  "location-selection",
  "login",
  "relocation"
];

export function JoinGroupWizard({ onSuccess, productId }: JoinGroupWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>("email-verification");
  const [wizardData, setWizardData] = useState<WizardData>({
    email: "",
    isUserKnown: false,
    isMemberOfAnyGroup: false
  });
  const [isLoading, setIsLoading] = useState(false);

  // Load wizard data from localStorage on mount
  useEffect(() => {
    const savedWizardData = localStorage.getItem('joinGroupWizardData');
    if (savedWizardData) {
      try {
        const parsedData = JSON.parse(savedWizardData);
        setWizardData(prev => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error('Error parsing saved wizard data:', error);
        localStorage.removeItem('joinGroupWizardData');
      }
    }
  }, []);

  // Save wizard data to localStorage whenever it changes
  useEffect(() => {
    if (wizardData.email) {
      localStorage.setItem('joinGroupWizardData', JSON.stringify(wizardData));
    }
  }, [wizardData]);

  const updateWizardData = useCallback((data: Partial<WizardData>) => {
    setWizardData(prev => ({ ...prev, ...data }));
  }, []);

  const clearWizardData = useCallback(() => {
    localStorage.removeItem('joinGroupWizardData');
    setWizardData({
      email: "",
      isUserKnown: false,
      isMemberOfAnyGroup: false
    });
  }, []);

  const goToStep = (step: WizardStep) => {
    setCurrentStep(step);
  };

  const getCurrentStepIndex = () => {
    return stepOrder.indexOf(currentStep);
  };

  const getProgressPercentage = () => {
    const currentIndex = getCurrentStepIndex();
    return ((currentIndex + 1) / stepOrder.length) * 100;
  };

  const stepVariants = {
    hidden: { opacity: 0, x: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      x: 0, 
      scale: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 300
      }
    },
    exit: { 
      opacity: 0, 
      x: -50, 
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  const renderCurrentStep = () => {
    const commonProps = {
      wizardData,
      updateWizardData,
      clearWizardData,
      goToStep,
      onSuccess,
      productId,
      setIsLoading
    };

    switch (currentStep) {
      case "email-verification":
        return <EmailVerificationStep {...commonProps} />;
      case "group-selection":
        return <GroupSelectionStep {...commonProps} />;
      case "user-registration":
        return <UserRegistrationStep {...commonProps} />;
      case "location-selection":
        return <LocationSelectionStep {...commonProps} />;
      case "login":
        return <LoginStep {...commonProps} />;
      case "relocation":
        return <RelocationStep {...commonProps} />;
      default:
        return <EmailVerificationStep {...commonProps} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress Bar */}
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <h3 
            className="text-lg font-semibold text-gray-800"
            style={{ fontFamily: "Avenir, sans-serif" }}
          >
            {stepTitles[currentStep]}
          </h3>
          <span className="text-sm text-gray-500">
            Step {getCurrentStepIndex() + 1} of {stepOrder.length}
          </span>
        </div>
        
        <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
          <motion.div
            className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${getProgressPercentage()}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-[400px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            variants={stepVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="space-y-6"
          >
            {renderCurrentStep()}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-3xl"
        >
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-gray-700 font-medium">Processing...</span>
          </div>
        </motion.div>
      )}
    </div>
  );
}
