'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Activity, 
  Database, 
  Server, 
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Settings,
  Download,
  Eye,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { formatNumber } from '@/lib/utils';

interface PerformanceMetrics {
  api: {
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    requestsPerSecond: number;
    slowestEndpoints: Array<{
      endpoint: string;
      averageTime: number;
      requestCount: number;
    }>;
  };
  database: {
    connectionStatus: string;
    responseTime: number;
    activeConnections: number;
    slowQueryCount: number;
    indexEfficiency: number;
  };
  cache: {
    hitRate: number;
    memoryUsage: number;
    totalKeys: number;
    evictions: number;
  };
  system: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    uptime: number;
  };
}

interface Alert {
  type: 'error' | 'warning' | 'info';
  message: string;
  timestamp: Date;
}

export function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  // Fetch performance metrics
  const fetchMetrics = async () => {
    try {
      // In a real implementation, these would be actual API calls
      // For demo, we'll simulate the data
      const mockMetrics: PerformanceMetrics = {
        api: {
          totalRequests: 15420,
          averageResponseTime: 245,
          errorRate: 2.1,
          requestsPerSecond: 12.5,
          slowestEndpoints: [
            { endpoint: '/api/analytics/overview', averageTime: 850, requestCount: 234 },
            { endpoint: '/api/products/search', averageTime: 420, requestCount: 1205 },
            { endpoint: '/api/groups/orders', averageTime: 380, requestCount: 567 },
            { endpoint: '/api/coupons/validate', averageTime: 320, requestCount: 890 },
            { endpoint: '/api/users/profile', averageTime: 280, requestCount: 445 }
          ]
        },
        database: {
          connectionStatus: 'connected',
          responseTime: 15,
          activeConnections: 8,
          slowQueryCount: 3,
          indexEfficiency: 87
        },
        cache: {
          hitRate: 94.2,
          memoryUsage: 68,
          totalKeys: 15420,
          evictions: 12
        },
        system: {
          cpuUsage: 45,
          memoryUsage: 72,
          diskUsage: 34,
          uptime: 99.8
        }
      };

      setMetrics(mockMetrics);

      // Generate alerts based on metrics
      const newAlerts: Alert[] = [];
      
      if (mockMetrics.api.errorRate > 5) {
        newAlerts.push({
          type: 'error',
          message: `High API error rate: ${mockMetrics.api.errorRate}%`,
          timestamp: new Date()
        });
      }

      if (mockMetrics.api.averageResponseTime > 500) {
        newAlerts.push({
          type: 'warning',
          message: `Slow API response time: ${mockMetrics.api.averageResponseTime}ms`,
          timestamp: new Date()
        });
      }

      if (mockMetrics.database.slowQueryCount > 5) {
        newAlerts.push({
          type: 'warning',
          message: `${mockMetrics.database.slowQueryCount} slow database queries detected`,
          timestamp: new Date()
        });
      }

      if (mockMetrics.cache.hitRate < 80) {
        newAlerts.push({
          type: 'warning',
          message: `Low cache hit rate: ${mockMetrics.cache.hitRate}%`,
          timestamp: new Date()
        });
      }

      if (mockMetrics.system.cpuUsage > 80) {
        newAlerts.push({
          type: 'error',
          message: `High CPU usage: ${mockMetrics.system.cpuUsage}%`,
          timestamp: new Date()
        });
      }

      setAlerts(newAlerts);
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchMetrics, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthStatus = () => {
    if (!metrics) return { status: 'unknown', color: 'gray' };

    const issues = alerts.filter(a => a.type === 'error').length;
    const warnings = alerts.filter(a => a.type === 'warning').length;

    if (issues > 0) return { status: 'critical', color: 'red' };
    if (warnings > 0) return { status: 'warning', color: 'yellow' };
    return { status: 'healthy', color: 'green' };
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading performance metrics...</span>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Failed to load performance metrics</p>
        <Button onClick={fetchMetrics} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  const healthStatus = getHealthStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Performance Dashboard</h1>
          <p className="text-gray-600">Real-time system performance monitoring</p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* System Health Status */}
          <div className="flex items-center gap-2">
            {healthStatus.status === 'healthy' && <CheckCircle className="h-5 w-5 text-green-600" />}
            {healthStatus.status === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-600" />}
            {healthStatus.status === 'critical' && <AlertTriangle className="h-5 w-5 text-red-600" />}
            <span className={`font-medium text-${healthStatus.color}-600`}>
              {healthStatus.status.charAt(0).toUpperCase() + healthStatus.status.slice(1)}
            </span>
          </div>

          {/* Auto Refresh Toggle */}
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </Button>

          {/* Manual Refresh */}
          <Button variant="outline" size="sm" onClick={fetchMetrics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>

          {/* Export */}
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <div className="space-y-2">
          {alerts.map((alert, index) => (
            <Alert key={index} className={`border-${alert.type === 'error' ? 'red' : 'yellow'}-200`}>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{alert.message}</AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* API Performance */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">API Response Time</p>
                <p className={`text-3xl font-bold ${getStatusColor(metrics.api.averageResponseTime, { good: 200, warning: 500 })}`}>
                  {metrics.api.averageResponseTime}ms
                </p>
                <p className="text-sm text-gray-500">
                  {formatNumber(metrics.api.requestsPerSecond)} req/s
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        {/* Database Performance */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Database Response</p>
                <p className={`text-3xl font-bold ${getStatusColor(metrics.database.responseTime, { good: 10, warning: 50 })}`}>
                  {metrics.database.responseTime}ms
                </p>
                <p className="text-sm text-gray-500">
                  {metrics.database.activeConnections} connections
                </p>
              </div>
              <Database className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        {/* Cache Performance */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Cache Hit Rate</p>
                <p className={`text-3xl font-bold ${getStatusColor(100 - metrics.cache.hitRate, { good: 10, warning: 30 })}`}>
                  {metrics.cache.hitRate}%
                </p>
                <p className="text-sm text-gray-500">
                  {formatNumber(metrics.cache.totalKeys)} keys
                </p>
              </div>
              <Zap className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        {/* System Health */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">System Uptime</p>
                <p className={`text-3xl font-bold ${getStatusColor(100 - metrics.system.uptime, { good: 1, warning: 5 })}`}>
                  {metrics.system.uptime}%
                </p>
                <p className="text-sm text-gray-500">
                  CPU: {metrics.system.cpuUsage}%
                </p>
              </div>
              <Server className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Tabs defaultValue="api" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api">API Performance</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="cache">Cache</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value="api" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* API Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>API Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Total Requests</span>
                  <span className="font-bold">{formatNumber(metrics.api.totalRequests)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Average Response Time</span>
                  <span className="font-bold">{metrics.api.averageResponseTime}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Error Rate</span>
                  <span className={`font-bold ${getStatusColor(metrics.api.errorRate, { good: 1, warning: 5 })}`}>
                    {metrics.api.errorRate}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Requests per Second</span>
                  <span className="font-bold">{metrics.api.requestsPerSecond}</span>
                </div>
              </CardContent>
            </Card>

            {/* Slowest Endpoints */}
            <Card>
              <CardHeader>
                <CardTitle>Slowest Endpoints</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {metrics.api.slowestEndpoints.map((endpoint, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{endpoint.endpoint}</p>
                        <p className="text-xs text-gray-500">{endpoint.requestCount} requests</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold">{endpoint.averageTime}ms</p>
                        <Badge variant={endpoint.averageTime > 500 ? "destructive" : endpoint.averageTime > 200 ? "secondary" : "default"}>
                          {endpoint.averageTime > 500 ? 'Slow' : endpoint.averageTime > 200 ? 'Medium' : 'Fast'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="database" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Database Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Connection Status</span>
                  <Badge variant={metrics.database.connectionStatus === 'connected' ? 'default' : 'destructive'}>
                    {metrics.database.connectionStatus}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Response Time</span>
                  <span className="font-bold">{metrics.database.responseTime}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Active Connections</span>
                  <span className="font-bold">{metrics.database.activeConnections}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Slow Queries</span>
                  <span className={`font-bold ${getStatusColor(metrics.database.slowQueryCount, { good: 0, warning: 5 })}`}>
                    {metrics.database.slowQueryCount}
                  </span>
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Index Efficiency</span>
                    <span>{metrics.database.indexEfficiency}%</span>
                  </div>
                  <Progress value={metrics.database.indexEfficiency} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Database Optimization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analyze Query Performance
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-2" />
                    Optimize Indexes
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Eye className="h-4 w-4 mr-2" />
                    View Slow Queries
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cache" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Cache Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Hit Rate</span>
                    <span>{metrics.cache.hitRate}%</span>
                  </div>
                  <Progress value={metrics.cache.hitRate} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Memory Usage</span>
                    <span>{metrics.cache.memoryUsage}%</span>
                  </div>
                  <Progress value={metrics.cache.memoryUsage} />
                </div>
                <div className="flex justify-between items-center">
                  <span>Total Keys</span>
                  <span className="font-bold">{formatNumber(metrics.cache.totalKeys)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Evictions</span>
                  <span className="font-bold">{metrics.cache.evictions}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cache Management</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Clear Cache
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <PieChart className="h-4 w-4 mr-2" />
                    Cache Statistics
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-2" />
                    Cache Configuration
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>System Resources</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>CPU Usage</span>
                    <span>{metrics.system.cpuUsage}%</span>
                  </div>
                  <Progress value={metrics.system.cpuUsage} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Memory Usage</span>
                    <span>{metrics.system.memoryUsage}%</span>
                  </div>
                  <Progress value={metrics.system.memoryUsage} />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Disk Usage</span>
                    <span>{metrics.system.diskUsage}%</span>
                  </div>
                  <Progress value={metrics.system.diskUsage} />
                </div>
                <div className="flex justify-between items-center">
                  <span>Uptime</span>
                  <span className="font-bold">{metrics.system.uptime}%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Overall Status</span>
                    <Badge variant={healthStatus.status === 'healthy' ? 'default' : healthStatus.status === 'warning' ? 'secondary' : 'destructive'}>
                      {healthStatus.status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Active Alerts</span>
                    <span className="font-bold">{alerts.length}</span>
                  </div>
                  <Button variant="outline" className="w-full justify-start">
                    <LineChart className="h-4 w-4 mr-2" />
                    View Trends
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
