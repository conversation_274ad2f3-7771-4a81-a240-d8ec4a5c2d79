"use client";

import { useState } from "react";
import { Star } from "lucide-react";
import { motion } from "framer-motion";

interface RatingStarsProps {
  rating: number;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  onRatingChange?: (rating: number) => void;
  showValue?: boolean;
  className?: string;
}

export function RatingStars({
  rating,
  maxRating = 5,
  size = 'md',
  interactive = false,
  onRatingChange,
  showValue = false,
  className = ""
}: RatingStarsProps) {
  const [hoverRating, setHoverRating] = useState(0);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const handleStarClick = (starRating: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(starRating);
    }
  };

  const handleStarHover = (starRating: number) => {
    if (interactive) {
      setHoverRating(starRating);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(0);
    }
  };

  const getStarFill = (starIndex: number) => {
    const currentRating = interactive && hoverRating > 0 ? hoverRating : rating;

    if (starIndex <= Math.floor(currentRating)) {
      return "#FFD700"; // Gold for filled stars
    } else if (starIndex === Math.ceil(currentRating) && currentRating % 1 !== 0) {
      return "url(#halfStar)"; // Half-filled star
    } else {
      return "none"; // Empty star
    }
  };

  const getStarStroke = (starIndex: number) => {
    const currentRating = interactive && hoverRating > 0 ? hoverRating : rating;
    return starIndex <= Math.ceil(currentRating) ? "#FFD700" : "#CBD5E0";
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* SVG definition for half-star gradient */}
      <svg width="0" height="0">
        <defs>
          <linearGradient id="halfStar" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="50%" stopColor="#FFD700" />
            <stop offset="50%" stopColor="transparent" />
          </linearGradient>
        </defs>
      </svg>

      <div 
        className="flex items-center"
        onMouseLeave={handleMouseLeave}
      >
        {Array.from({ length: maxRating }, (_, index) => {
          const starIndex = index + 1;
          return (
            <motion.button
              key={starIndex}
              type="button"
              className={`${interactive ? 'cursor-pointer hover:scale-110' : 'cursor-default'} transition-transform duration-150`}
              onClick={() => handleStarClick(starIndex)}
              onMouseEnter={() => handleStarHover(starIndex)}
              disabled={!interactive}
              whileHover={interactive ? { scale: 1.1 } : {}}
              whileTap={interactive ? { scale: 0.95 } : {}}
            >
              <Star
                className={`${sizeClasses[size]} transition-colors duration-150`}
                fill={getStarFill(starIndex)}
                stroke={getStarStroke(starIndex)}
                strokeWidth={1.5}
              />
            </motion.button>
          );
        })}
      </div>

      {showValue && (
        <span className="ml-2 text-sm font-medium text-gray-700">
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  );
}
