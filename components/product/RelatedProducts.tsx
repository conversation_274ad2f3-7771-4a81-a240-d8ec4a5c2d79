"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, Eye } from "lucide-react";
import { RatingStars } from "./RatingStars";
import { WishlistButton } from "@/components/wishlist/WishlistButton";
import { useGetRelatedProductsQuery } from "@/lib/redux/features/ratings/ratingsApiSlice";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useAuth } from "@/context/AuthContext";
import { motion } from "framer-motion";
import { toast } from "sonner";
import Link from "next/link";
import Image from "next/image";

interface RelatedProductsProps {
  productId: string;
  limit?: number;
}

export function RelatedProducts({ productId, limit = 6 }: RelatedProductsProps) {
  const { user } = useAuth();
  const { data: relatedData, isLoading } = useGetRelatedProductsQuery({ productId, limit });
  const [addToCart] = useAddToCartMutation();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleAddToCart = async (product: any) => {
    if (!user) {
      toast.error('Please log in to add items to cart');
      return;
    }

    try {
      await addToCart({
        userId: user._id,
        productId: product._id,
        quantity: 1
      }).unwrap();
      
      toast.success(`${product.name} added to cart!`);
    } catch (error) {
      console.error('Failed to add to cart:', error);
      toast.error('Failed to add to cart');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Related Products</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-200 aspect-square rounded-lg mb-3"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!relatedData || relatedData.products.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Related Products</span>
          <Badge variant="secondary">
            {relatedData.count} product{relatedData.count !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {relatedData.products.map((product, index) => (
            <motion.div
              key={product._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="group"
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-4">
                  {/* Product Image */}
                  <div className="relative aspect-square mb-4 overflow-hidden rounded-lg bg-gray-100">
                    {product.image && product.image.trim() !== '' ? (
                      <Image
                        src={`/api/images/${product.image}`}
                        alt={product.name || "Product image"}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/placeholder.svg";
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <Eye className="h-12 w-12" />
                      </div>
                    )}
                    
                    {/* Wishlist Button */}
                    {user && (
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <WishlistButton
                          productId={product._id}
                          userId={user._id}
                          size="sm"
                          variant="secondary"
                          showText={false}
                          className="h-8 w-8 rounded-full bg-white/90 backdrop-blur-sm hover:bg-white shadow-md"
                        />
                      </div>
                    )}

                    {/* Stock Badge */}
                    {product.stock <= 0 && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                        <Badge variant="destructive">Out of Stock</Badge>
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div className="space-y-2">
                    <Link href={`/products/${product._id}`}>
                      <h3 className="font-semibold text-sm line-clamp-2 hover:text-purple-600 transition-colors">
                        {product.name}
                      </h3>
                    </Link>

                    {/* Category */}
                    {product.category && (
                      <p className="text-xs text-gray-600">
                        {typeof product.category === 'object' && 'name' in product.category 
                          ? product.category.name 
                          : 'Category'}
                      </p>
                    )}

                    {/* Rating */}
                    {product.averageRating && product.averageRating > 0 && (
                      <div className="flex items-center gap-2">
                        <RatingStars rating={product.averageRating} size="sm" />
                        <span className="text-xs text-gray-600">
                          ({product.reviewCount || 0})
                        </span>
                      </div>
                    )}

                    {/* Price */}
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-bold text-purple-600">
                          {formatCurrency(product.price)}
                        </span>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <span className="text-xs text-gray-500 line-through ml-2">
                            {formatCurrency(product.originalPrice)}
                          </span>
                        )}
                      </div>
                      
                      {product.originalPrice && product.originalPrice > product.price && (
                        <Badge variant="destructive" className="text-xs">
                          {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                        </Badge>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        size="sm"
                        onClick={() => handleAddToCart(product)}
                        disabled={product.stock <= 0}
                        className="flex-1 h-8 text-xs bg-purple-600 hover:bg-purple-700"
                      >
                        <ShoppingCart className="h-3 w-3 mr-1" />
                        {product.stock <= 0 ? 'Out of Stock' : 'Add to Cart'}
                      </Button>
                      
                      <Link href={`/products/${product._id}`}>
                        <Button variant="outline" size="sm" className="h-8 px-2">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* View More Button */}
        {relatedData.count > limit && (
          <div className="text-center mt-6">
            <Link href={`/store?category=${relatedData.products[0]?.category?.name || ''}`}>
              <Button variant="outline">
                View More Related Products
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
