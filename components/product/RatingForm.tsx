"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RatingStars } from "./RatingStars";
import { useCreateRatingMutation, useUpdateRatingMutation } from "@/lib/redux/features/ratings/ratingsApiSlice";
import { IProductRating } from "@/models/ProductRating";
import { toast } from "sonner";
import { motion } from "framer-motion";

interface RatingFormProps {
  productId: string;
  userId: string;
  existingRating?: IProductRating | null;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function RatingForm({ 
  productId, 
  userId, 
  existingRating, 
  onSuccess, 
  onCancel 
}: RatingFormProps) {
  const [rating, setRating] = useState(existingRating?.rating || 0);
  const [title, setTitle] = useState(existingRating?.title || '');
  const [review, setReview] = useState(existingRating?.review || '');
  
  const [createRating, { isLoading: isCreating }] = useCreateRatingMutation();
  const [updateRating, { isLoading: isUpdating }] = useUpdateRatingMutation();
  
  const isLoading = isCreating || isUpdating;
  const isEditing = !!existingRating;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    try {
      if (isEditing && existingRating) {
        await updateRating({
          ratingId: existingRating._id,
          userId,
          rating,
          title: title.trim(),
          review: review.trim()
        }).unwrap();
        
        toast.success('Rating updated successfully!');
      } else {
        await createRating({
          productId,
          userId,
          rating,
          title: title.trim(),
          review: review.trim()
        }).unwrap();
        
        toast.success('Rating submitted successfully!');
      }
      
      onSuccess?.();
    } catch (error) {
      console.error('Failed to submit rating:', error);
      toast.error(isEditing ? 'Failed to update rating' : 'Failed to submit rating');
    }
  };

  const handleCancel = () => {
    setRating(existingRating?.rating || 0);
    setTitle(existingRating?.title || '');
    setReview(existingRating?.review || '');
    onCancel?.();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            {isEditing ? 'Edit Your Rating' : 'Write a Review'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Rating Stars */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Rating *
              </label>
              <RatingStars
                rating={rating}
                interactive={true}
                onRatingChange={setRating}
                size="lg"
                showValue={true}
              />
              {rating > 0 && (
                <p className="text-sm text-gray-600 mt-1">
                  {rating === 1 && "Poor"}
                  {rating === 2 && "Fair"}
                  {rating === 3 && "Good"}
                  {rating === 4 && "Very Good"}
                  {rating === 5 && "Excellent"}
                </p>
              )}
            </div>

            {/* Review Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Review Title (Optional)
              </label>
              <Input
                id="title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Summarize your experience..."
                maxLength={100}
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                {title.length}/100 characters
              </p>
            </div>

            {/* Review Text */}
            <div>
              <label htmlFor="review" className="block text-sm font-medium text-gray-700 mb-2">
                Your Review (Optional)
              </label>
              <Textarea
                id="review"
                value={review}
                onChange={(e) => setReview(e.target.value)}
                placeholder="Tell others about your experience with this product..."
                rows={4}
                maxLength={1000}
                className="w-full resize-none"
              />
              <p className="text-xs text-gray-500 mt-1">
                {review.length}/1000 characters
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="submit"
                disabled={isLoading || rating === 0}
                className="flex-1 bg-purple-600 hover:bg-purple-700"
              >
                {isLoading ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"
                    />
                    {isEditing ? 'Updating...' : 'Submitting...'}
                  </>
                ) : (
                  isEditing ? 'Update Rating' : 'Submit Rating'
                )}
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
