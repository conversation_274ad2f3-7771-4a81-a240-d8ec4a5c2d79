"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { RatingStars } from "./RatingStars";
import { 
  useGetUserProductRatingQuery,
  useCreateRatingMutation,
  useUpdateRatingMutation 
} from "@/lib/redux/features/ratings/ratingsApiSlice";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import { Star, Edit, Check, X } from "lucide-react";

interface QuickRatingSectionProps {
  productId: string;
  userId: string;
}

export function QuickRatingSection({ productId, userId }: QuickRatingSectionProps) {
  const [selectedRating, setSelectedRating] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Fetch user's existing rating
  const { data: userRatingData, isLoading } = useGetUserProductRatingQuery({
    productId,
    userId
  });

  // Mutations
  const [createRating, { isLoading: isCreating }] = useCreateRatingMutation();
  const [updateRating, { isLoading: isUpdating }] = useUpdateRatingMutation();

  const userRating = userRatingData?.rating;
  const hasRated = !!userRating;
  const isSubmitting = isCreating || isUpdating;

  // Initialize selected rating with user's existing rating
  useEffect(() => {
    if (userRating && !isEditing) {
      setSelectedRating(userRating.rating);
    }
  }, [userRating, isEditing]);

  const handleRatingSubmit = async () => {
    if (selectedRating === 0) {
      toast.error('Please select a rating');
      return;
    }

    try {
      if (hasRated && userRating) {
        // Update existing rating
        await updateRating({
          ratingId: userRating._id,
          userId,
          rating: selectedRating
        }).unwrap();
        
        toast.success('Rating updated successfully!');
      } else {
        // Create new rating
        await createRating({
          productId,
          userId,
          rating: selectedRating
        }).unwrap();
        
        toast.success('Rating submitted successfully!');
      }

      setIsEditing(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error('Failed to submit rating:', error);
      toast.error('Failed to submit rating. Please try again.');
    }
  };

  const handleEditClick = () => {
    setIsEditing(true);
    setSelectedRating(userRating?.rating || 0);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setSelectedRating(userRating?.rating || 0);
  };

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="flex items-center gap-2 mb-3">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="w-6 h-6 bg-gray-200 rounded"></div>
          ))}
        </div>
        <div className="h-8 bg-gray-200 rounded w-24"></div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <AnimatePresence mode="wait">
        {showSuccess ? (
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="flex items-center gap-2 text-green-600"
          >
            <Check className="h-5 w-5" />
            <span className="font-medium">Thank you for your rating!</span>
          </motion.div>
        ) : (
          <motion.div
            key="rating"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {/* Rating Stars */}
            <div className="flex items-center gap-3 mb-3">
              <RatingStars
                rating={selectedRating}
                interactive={!hasRated || isEditing}
                onRatingChange={setSelectedRating}
                size="lg"
                className="flex-shrink-0"
              />
              
              {selectedRating > 0 && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="text-sm font-medium text-purple-700"
                >
                  {selectedRating === 1 && "Poor"}
                  {selectedRating === 2 && "Fair"}
                  {selectedRating === 3 && "Good"}
                  {selectedRating === 4 && "Very Good"}
                  {selectedRating === 5 && "Excellent"}
                </motion.span>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {!hasRated || isEditing ? (
                <>
                  <Button
                    size="sm"
                    onClick={handleRatingSubmit}
                    disabled={selectedRating === 0 || isSubmitting}
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    {isSubmitting ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="mr-2 h-3 w-3 border border-white border-t-transparent rounded-full"
                        />
                        {hasRated ? 'Updating...' : 'Submitting...'}
                      </>
                    ) : (
                      <>
                        <Star className="h-3 w-3 mr-1" />
                        {hasRated ? 'Update Rating' : 'Submit Rating'}
                      </>
                    )}
                  </Button>
                  
                  {isEditing && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSubmitting}
                    >
                      <X className="h-3 w-3 mr-1" />
                      Cancel
                    </Button>
                  )}
                </>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-green-600 font-medium">
                    ✓ You rated this {selectedRating} star{selectedRating !== 1 ? 's' : ''}
                  </span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleEditClick}
                    className="text-purple-600 border-purple-200 hover:bg-purple-50"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                </div>
              )}
            </div>

            {/* Helpful Text */}
            {!hasRated && selectedRating === 0 && (
              <p className="text-xs text-gray-500 mt-2">
                Click on the stars to rate this product
              </p>
            )}
            
            {!hasRated && selectedRating > 0 && (
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-xs text-purple-600 mt-2"
              >
                Click "Submit Rating" to share your rating with others
              </motion.p>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Link to Full Review Section */}
      {hasRated && !isEditing && !showSuccess && (
        <div className="pt-2 border-t border-purple-200">
          <p className="text-xs text-purple-600">
            Want to write a detailed review?{' '}
            <button
              onClick={() => {
                // Scroll to the reviews section
                const reviewsSection = document.querySelector('[data-reviews-section]');
                if (reviewsSection) {
                  reviewsSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="underline hover:text-purple-800 font-medium"
            >
              Add a written review
            </button>
          </p>
        </div>
      )}
    </div>
  );
}
