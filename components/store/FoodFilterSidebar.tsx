import { Dispatch, SetStateAction } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"

export interface FoodFilterSidebarProps {
  selectedCuisines: string[]
  setSelectedCuisines: Dispatch<SetStateAction<string[]>>
  selectedDietaryRestrictions: string[]
  setSelectedDietaryRestrictions: Dispatch<SetStateAction<string[]>>
  minPrice: number
  setMinPrice: Dispatch<SetStateAction<number>>
  maxPrice: number
  setMaxPrice: Dispatch<SetStateAction<number>>
}

const cuisines = ["Italian", "Chinese", "Mexican", "Indian"]
const dietaryRestrictions = ["Vegetarian", "Vegan", "Gluten-Free", "Dairy-Free"]

export function FoodFilterSidebar({
  selectedCuisines,
  setSelectedCuisines,
  selectedDietaryRestrictions,
  setSelectedDietaryRestrictions,
  minPrice,
  setMinPrice,
  maxPrice,
  setMaxPrice,
}: FoodFilterSidebarProps) {
  const handleCuisineChange = (cuisine: string) => {
    setSelectedCuisines(prev => 
      prev.includes(cuisine) 
        ? prev.filter(c => c !== cuisine)
        : [...prev, cuisine]
    )
  }

  const handleDietaryRestrictionChange = (restriction: string) => {
    setSelectedDietaryRestrictions(prev => 
      prev.includes(restriction) 
        ? prev.filter(r => r !== restriction)
        : [...prev, restriction]
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Food Filters</CardTitle>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="cuisines">
            <AccordionTrigger>Cuisines</AccordionTrigger>
            <AccordionContent>
              {cuisines.map(cuisine => (
                <div key={cuisine} className="flex items-center space-x-2">
                  <Checkbox 
                    id={cuisine} 
                    checked={selectedCuisines.includes(cuisine)}
                    onCheckedChange={() => handleCuisineChange(cuisine)}
                  />
                  <Label htmlFor={cuisine}>{cuisine}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="dietaryRestrictions">
            <AccordionTrigger>Dietary Restrictions</AccordionTrigger>
            <AccordionContent>
              {dietaryRestrictions.map(restriction => (
                <div key={restriction} className="flex items-center space-x-2">
                  <Checkbox 
                    id={restriction} 
                    checked={selectedDietaryRestrictions.includes(restriction)}
                    onCheckedChange={() => handleDietaryRestrictionChange(restriction)}
                  />
                  <Label htmlFor={restriction}>{restriction}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="price">
            <AccordionTrigger>Price Range</AccordionTrigger>
            <AccordionContent>
              <Slider
                min={0}
                max={100}
                step={1}
                value={[minPrice, maxPrice]}
                onValueChange={([min, max]) => {
                  setMinPrice(min)
                  setMaxPrice(max)
                }}
              />
              <div className="flex justify-between mt-2">
                <span>${minPrice}</span>
                <span>${maxPrice}</span>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}

