"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight, Star, ShoppingCart, Heart } from "lucide-react";
import Image from "next/image";
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useAddToWishlistMutation } from "@/lib/redux/features/wishlist/wishlistApiSlice";
import { useAuth } from "@/context/AuthContext";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import { toast } from "sonner";
import { ConfirmationModal } from "@/components/ConfirmationModal";
import { JoinGroupModal } from "@/components/modals/JoinGroupModal";
import type { Product } from "@/types/product";

export function StoreBanner() {
  const { data: products = [], isLoading } = useGetAllProductsQuery();
  const [addToCart, { isLoading: isAdding }] = useAddToCartMutation();
  const [addToWishlist] = useAddToWishlistMutation();
  const { user } = useAuth();
  const { userGroups } = useGroupMembership(user?._id);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false);
  const [success, setSuccess] = useState(false);
  const [groupId, setGroupId] = useState<string | null>(null);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [wishlistLoading, setWishlistLoading] = useState(false);

  // Get the 4 latest products (sorted by creation date)
  const latestProducts = products
    .filter((product: Product) => product.stock > 0) // Only show products in stock
    .sort((a: Product, b: Product) => {
      const dateA = new Date(a.createdAt || 0).getTime();
      const dateB = new Date(b.createdAt || 0).getTime();
      return dateB - dateA; // Sort by newest first
    })
    .slice(0, 4); // Take only the first 4

  // Get the current group ID from localStorage on the client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedGroupId = localStorage.getItem('currentGroupId');
      if (storedGroupId) {
        setGroupId(storedGroupId);
      } else if (userGroups.length > 0) {
        // If no group ID in localStorage but user has groups, use the first one
        setGroupId(userGroups[0]._id);
        localStorage.setItem('currentGroupId', userGroups[0]._id);
      }
    }
  }, [userGroups]);

  // Reset success state after 2 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // Auto-advance slides every 6 seconds
  useEffect(() => {
    if (latestProducts.length > 1) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % latestProducts.length);
      }, 6000);
      return () => clearInterval(timer);
    }
  }, [latestProducts.length]);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % latestProducts.length);
  }, [latestProducts.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + latestProducts.length) % latestProducts.length);
  }, [latestProducts.length]);

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index);
  }, []);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Handle add to cart functionality
  const handleAddToCart = async (product: Product) => {
    setCurrentProduct(product);

    // If user is not logged in, open the join group modal
    if (!user) {
      setIsJoinGroupOpen(true);
      return;
    }

    // If user is logged in but not in any group, open the join group modal
    if (userGroups.length === 0) {
      setIsJoinGroupOpen(true);
      return;
    }

    // User is logged in and in a group, proceed with adding to cart directly
    try {
      // Validate required fields before making the API call
      if (!user._id || !product._id || !groupId) {
        console.error('Missing required fields:', {
          userId: user._id,
          productId: product._id,
          groupId
        });
        return;
      }

      // Set loading state
      setSuccess(false);

      const cartItem = {
        userId: user._id,
        productId: product._id,
        quantity: 1,
        groupId
      };

      console.log('Adding item to cart from banner:', cartItem);

      // Add to cart
      const result = await addToCart(cartItem).unwrap();

      if (result) {
        console.log('Successfully added to cart from banner:', result);

        // Show success state
        setSuccess(true);

        // Show confirmation modal
        setIsModalOpen(true);
      }
    } catch (error: unknown) {
      console.error('Failed to add item to cart from banner:', error);

      // Extract error message
      const errorMessage =
        typeof error === 'object' && error !== null
          ? (
              'data' in error &&
              typeof error.data === 'object' &&
              error.data !== null &&
              'error' in error.data &&
              typeof error.data.error === 'string'
                ? error.data.error
                : 'error' in error &&
                  typeof error.error === 'object' &&
                  error.error !== null &&
                  'data' in error.error &&
                  typeof error.error.data === 'object' &&
                  error.error.data !== null &&
                  'error' in error.error.data &&
                  typeof error.error.data.error === 'string'
                  ? error.error.data.error
                  : 'Failed to add item to cart. Please try again.'
            )
          : 'Failed to add item to cart. Please try again.';

      // Show error message to the user
      alert(errorMessage);
    }
  };

  // Handle wishlist functionality
  const handleAddToWishlist = async (product: Product) => {
    if (!user) {
      toast.error('Please log in to use wishlist');
      return;
    }

    setWishlistLoading(true);
    try {
      await addToWishlist({
        userId: user._id,
        productId: product._id,
        priority: 'medium'
      }).unwrap();

      toast.success(`${product.name} added to wishlist!`);
    } catch (error) {
      console.error('Failed to add to wishlist:', error);
      toast.error('Failed to add to wishlist. Please try again.');
    } finally {
      setWishlistLoading(false);
    }
  };

  // Loading state
  if (isLoading || latestProducts.length === 0) {
    return (
      <div className="relative w-full h-[400px] overflow-hidden rounded-xl mb-8 bg-gradient-to-r from-purple-600 to-indigo-600">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-lg">Loading featured products...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-[400px] overflow-hidden rounded-xl mb-8">
      <AnimatePresence initial={false}>
        {latestProducts.map((product: Product, index: number) => (
          index === currentSlide && (
            <motion.div
              key={product._id}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="absolute inset-0"
            >
              {/* Product Background Image */}
              <div className="absolute inset-0">
                <Image
                  src={`/api/images/${product.image}`}
                  alt={product.name}
                  fill
                  style={{ objectFit: "cover" }}
                  priority={index === 0}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?q=80&w=2070&auto=format&fit=crop";
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-black/40"></div>
              </div>

              {/* Content */}
              <div className="relative h-full flex items-center px-8 md:px-12">
                <div className="max-w-2xl text-white space-y-6">
                  {/* Product Badge */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <Badge className="bg-white/20 text-white border-white/30 mb-4 backdrop-blur-sm">
                      ✨ Featured Product
                    </Badge>
                  </motion.div>

                  {/* Product Name */}
                  <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                    className="text-3xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight"
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    {product.name}
                  </motion.h1>

                  {/* Product Description */}
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.5 }}
                    className="text-gray-200 text-lg md:text-xl max-w-lg line-clamp-2 leading-relaxed"
                  >
                    {product.description || "Discover this amazing product with premium quality and competitive pricing."}
                  </motion.p>

                  {/* Price and Rating */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                    className="flex items-center gap-6"
                  >
                    <div className="text-3xl md:text-4xl font-bold text-white">
                      {formatCurrency(product.price)}
                    </div>
                    {typeof product.averageRating === 'number' && product.averageRating > 0 && (
                      <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                        <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        <span className="text-white font-medium">
                          {(product.averageRating as number).toFixed(1)}
                        </span>
                      </div>
                    )}
                  </motion.div>

                  {/* Action Buttons */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                    className="flex items-center gap-4 pt-2"
                  >
                    <Button
                      size="lg"
                      onClick={() => handleAddToCart(product)}
                      disabled={isAdding || product.stock === 0}
                      className="bg-white text-black hover:bg-gray-100 px-8 py-4 text-lg font-semibold rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isAdding && currentProduct?._id === product._id ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="mr-2"
                        >
                          <ShoppingCart className="h-5 w-5" />
                        </motion.div>
                      ) : success && currentProduct?._id === product._id ? (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: "spring", stiffness: 500 }}
                          className="mr-2"
                        >
                          ✓
                        </motion.div>
                      ) : (
                        <ShoppingCart className="mr-2 h-5 w-5" />
                      )}
                      {isAdding && currentProduct?._id === product._id
                        ? "Adding..."
                        : success && currentProduct?._id === product._id
                        ? "Added!"
                        : "Add to Cart"
                      }
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => handleAddToWishlist(product)}
                      disabled={wishlistLoading}
                      className="border-2 border-white text-white hover:bg-white hover:text-black px-6 py-4 rounded-2xl backdrop-blur-sm bg-white/10 hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                    >
                      {wishlistLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="mr-2"
                        >
                          <Heart className="h-4 w-4" />
                        </motion.div>
                      ) : (
                        <Heart className="mr-2 h-4 w-4" />
                      )}
                      {wishlistLoading ? "Adding..." : "Add to Wishlist"}
                    </Button>
                  </motion.div>

                  {/* Stock Info */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                    className="text-sm text-gray-300 bg-black/30 backdrop-blur-sm rounded-full px-4 py-2 inline-block"
                  >
                    {product.stock > 10 ? (
                      <span className="text-green-400">✓ In Stock ({product.stock} available)</span>
                    ) : product.stock > 0 ? (
                      <span className="text-yellow-400">⚠ Only {product.stock} left in stock</span>
                    ) : (
                      <span className="text-red-400">✗ Out of Stock</span>
                    )}
                  </motion.div>
                </div>
              </div>
            </motion.div>
          )
        ))}
      </AnimatePresence>

      {/* Navigation Controls */}
      {latestProducts.length > 1 && (
        <>
          {/* Previous/Next Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200"
            aria-label="Previous product"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200"
            aria-label="Next product"
          >
            <ChevronRight className="h-6 w-6" />
          </button>

          {/* Slide Indicators */}
          <div className="absolute bottom-6 left-8 flex space-x-2">
            {latestProducts.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentSlide
                    ? 'bg-white scale-110'
                    : 'bg-white/50 hover:bg-white/70'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>

          {/* Product Counter */}
          <div className="absolute top-6 right-6 bg-black/30 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm font-medium">
            {currentSlide + 1} / {latestProducts.length}
          </div>
        </>
      )}

      {/* Confirmation Modal */}
      {currentProduct && (
        <ConfirmationModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          productName={currentProduct.name}
        />
      )}

      {/* Join Group Modal */}
      <JoinGroupModal
        isOpen={isJoinGroupOpen}
        onClose={() => setIsJoinGroupOpen(false)}
        productId={currentProduct?._id}
      />
    </div>
  );
}
