
import { Button } from "@/components/ui/button"

export function ProductHero() {
  return (
    <div className="relative h-[500px] overflow-hidden">
      <div 
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: "url('/products/pexels-cottonbro-3737623.jpg')"
        }}
      />
      <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
        <h1 className="text-6xl font-bold mb-8">Our Products</h1>
        <Button 
          variant="secondary" 
          className="bg-white text-black hover:bg-gray-100"
        >
          Shop Now
        </Button>
      </div>
      <div className="absolute bottom-0 w-full">
        <svg 
          viewBox="0 0 1440 120" 
          className="w-full h-20 fill-white"
          preserveAspectRatio="none"
        >
          <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z" />
        </svg>
      </div>
    </div>
  )
}

