import { Dispatch, SetStateAction } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"

export interface GroceriesFilterSidebarProps {
  selectedBrands: string[]
  setSelectedBrands: Dispatch<SetStateAction<string[]>>
  selectedCategories: string[]
  setSelectedCategories: Dispatch<SetStateAction<string[]>>
  minPrice: number
  setMinPrice: Dispatch<SetStateAction<number>>
  maxPrice: number
  setMaxPrice: Dispatch<SetStateAction<number>>
}

const brands = ["Brand A", "Brand B", "Brand C", "Brand D"]
const categories = ["Fruits & Vegetables", "Dairy", "Bakery", "Canned Goods"]

export function GroceriesFilterSidebar({
  selectedBrands,
  setSelectedBrands,
  selectedCategories,
  setSelectedCategories,
  minPrice,
  setMinPrice,
  maxPrice,
  setMaxPrice,
}: GroceriesFilterSidebarProps) {
  const handleBrandChange = (brand: string) => {
    setSelectedBrands(prev => 
      prev.includes(brand) 
        ? prev.filter(b => b !== brand)
        : [...prev, brand]
    )
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Groceries Filters</CardTitle>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="brands">
            <AccordionTrigger>Brands</AccordionTrigger>
            <AccordionContent>
              {brands.map(brand => (
                <div key={brand} className="flex items-center space-x-2">
                  <Checkbox 
                    id={brand} 
                    checked={selectedBrands.includes(brand)}
                    onCheckedChange={() => handleBrandChange(brand)}
                  />
                  <Label htmlFor={brand}>{brand}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="categories">
            <AccordionTrigger>Categories</AccordionTrigger>
            <AccordionContent>
              {categories.map(category => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox 
                    id={category} 
                    checked={selectedCategories.includes(category)}
                    onCheckedChange={() => handleCategoryChange(category)}
                  />
                  <Label htmlFor={category}>{category}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="price">
            <AccordionTrigger>Price Range</AccordionTrigger>
            <AccordionContent>
              <Slider
                min={0}
                max={100}
                step={1}
                value={[minPrice, maxPrice]}
                onValueChange={([min, max]) => {
                  setMinPrice(min)
                  setMaxPrice(max)
                }}
              />
              <div className="flex justify-between mt-2">
                <span>${minPrice}</span>
                <span>${maxPrice}</span>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}

