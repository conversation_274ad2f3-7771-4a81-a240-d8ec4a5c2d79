import { Dispatch, SetStateAction } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"

export interface WholesaleFilterSidebarProps {
  selectedCategories: string[]
  setSelectedCategories: Dispatch<SetStateAction<string[]>>
  selectedQuantities: string[]
  setSelectedQuantities: Dispatch<SetStateAction<string[]>>
  minPrice: number
  setMinPrice: Dispatch<SetStateAction<number>>
  maxPrice: number
  setMaxPrice: Dispatch<SetStateAction<number>>
}

const categories = ["Bulk Foods", "Cleaning Supplies", "Office Supplies", "Packaging"]
const quantities = ["Small (1-50 units)", "Medium (51-200 units)", "Large (201-1000 units)", "Extra Large (1000+ units)"]

export function WholesaleFilterSidebar({
  selectedCategories,
  setSelectedCategories,
  selectedQuantities,
  setSelectedQuantities,
  minPrice,
  setMinPrice,
  maxPrice,
  setMaxPrice,
}: WholesaleFilterSidebarProps) {
  const handleCategoryChange = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const handleQuantityChange = (quantity: string) => {
    setSelectedQuantities(prev => 
      prev.includes(quantity) 
        ? prev.filter(q => q !== quantity)
        : [...prev, quantity]
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Wholesale Filters</CardTitle>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="categories">
            <AccordionTrigger>Categories</AccordionTrigger>
            <AccordionContent>
              {categories.map(category => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox 
                    id={category} 
                    checked={selectedCategories.includes(category)}
                    onCheckedChange={() => handleCategoryChange(category)}
                  />
                  <Label htmlFor={category}>{category}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="quantities">
            <AccordionTrigger>Quantities</AccordionTrigger>
            <AccordionContent>
              {quantities.map(quantity => (
                <div key={quantity} className="flex items-center space-x-2">
                  <Checkbox 
                    id={quantity} 
                    checked={selectedQuantities.includes(quantity)}
                    onCheckedChange={() => handleQuantityChange(quantity)}
                  />
                  <Label htmlFor={quantity}>{quantity}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="price">
            <AccordionTrigger>Price Range</AccordionTrigger>
            <AccordionContent>
              <Slider
                min={0}
                max={10000}
                step={100}
                value={[minPrice, maxPrice]}
                onValueChange={([min, max]) => {
                  setMinPrice(min)
                  setMaxPrice(max)
                }}
              />
              <div className="flex justify-between mt-2">
                <span>${minPrice}</span>
                <span>${maxPrice}</span>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}

