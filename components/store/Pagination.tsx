"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, MoreHorizontal, ChevronsLeft, ChevronsRight } from "lucide-react";
import { motion } from "framer-motion";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function Pagination({ currentPage, totalPages, onPageChange }: PaginationProps) {
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;

  const renderPageButtons = () => {
    const pages = [];
    
    // Always show first page
    pages.push(
      <motion.div key="page-1" whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          variant={currentPage === 1 ? "default" : "outline"}
          size="icon"
          className={`w-10 h-10 rounded-full transition-all duration-200 ${
            currentPage === 1
              ? "bg-purple-600 hover:bg-purple-700 text-white shadow-lg"
              : "border-gray-200 hover:bg-purple-50 hover:border-purple-300"
          }`}
          onClick={() => onPageChange(1)}
        >
          1
        </Button>
      </motion.div>
    );

    // Calculate range of pages to show
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);

    // Adjust if we're at the start
    if (currentPage <= 3) {
      endPage = Math.min(4, totalPages - 1);
    }
    
    // Adjust if we're at the end
    if (currentPage >= totalPages - 2) {
      startPage = Math.max(2, totalPages - 3);
    }

    // Add ellipsis if needed at the beginning
    if (startPage > 2) {
      pages.push(
        <Button
          key="start-ellipsis"
          variant="ghost"
          size="icon"
          className="w-10 h-10 cursor-default rounded-full"
          disabled
        >
          <MoreHorizontal className="h-4 w-4 text-gray-400" />
        </Button>
      );
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <motion.div key={`page-${i}`} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant={currentPage === i ? "default" : "outline"}
            size="icon"
            className={`w-10 h-10 rounded-full transition-all duration-200 ${
              currentPage === i
                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-lg"
                : "border-gray-200 hover:bg-purple-50 hover:border-purple-300"
            }`}
            onClick={() => onPageChange(i)}
          >
            {i}
          </Button>
        </motion.div>
      );
    }

    // Add ellipsis if needed at the end
    if (endPage < totalPages - 1) {
      pages.push(
        <Button
          key="end-ellipsis"
          variant="ghost"
          size="icon"
          className="w-10 h-10 cursor-default rounded-full"
          disabled
        >
          <MoreHorizontal className="h-4 w-4 text-gray-400" />
        </Button>
      );
    }

    // Always show last page if there's more than one page
    if (totalPages > 1) {
      pages.push(
        <motion.div key={`page-${totalPages}`} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            variant={currentPage === totalPages ? "default" : "outline"}
            size="icon"
            className={`w-10 h-10 rounded-full transition-all duration-200 ${
              currentPage === totalPages
                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-lg"
                : "border-gray-200 hover:bg-purple-50 hover:border-purple-300"
            }`}
            onClick={() => onPageChange(totalPages)}
          >
            {totalPages}
          </Button>
        </motion.div>
      );
    }

    return pages;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center space-y-4 py-8"
    >
      {/* Page Info */}
      <div className="text-sm text-gray-600 bg-white/70 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200">
        Page {currentPage} of {totalPages}
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center space-x-2">
        {/* First Page */}
        <Button
          variant="outline"
          size="icon"
          className="w-10 h-10 rounded-full border-gray-200 hover:bg-purple-50 hover:border-purple-300 disabled:opacity-50"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          aria-label="First page"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        {/* Previous Page */}
        <Button
          variant="outline"
          size="icon"
          className="w-10 h-10 rounded-full border-gray-200 hover:bg-purple-50 hover:border-purple-300 disabled:opacity-50"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          aria-label="Previous page"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-1">
          {renderPageButtons()}
        </div>

        {/* Next Page */}
        <Button
          variant="outline"
          size="icon"
          className="w-10 h-10 rounded-full border-gray-200 hover:bg-purple-50 hover:border-purple-300 disabled:opacity-50"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          aria-label="Next page"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Last Page */}
        <Button
          variant="outline"
          size="icon"
          className="w-10 h-10 rounded-full border-gray-200 hover:bg-purple-50 hover:border-purple-300 disabled:opacity-50"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          aria-label="Last page"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  );
}
