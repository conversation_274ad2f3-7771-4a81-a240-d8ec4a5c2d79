import { Dispatch, SetStateAction } from 'react';
import { DynamicFilterSidebar } from '@/components/store/DynamicFilterSidebar';
import { useGetCategoriesQuery } from '@/lib/redux/features/categories/categoriesApiSlice';

interface FiltersState {
  category: string;
  minPrice: number;
  maxPrice: number;
  inStock: boolean;
  rating: number | null;
  sortBy: 'newest' | 'price-low-high' | 'price-high-low' | 'popular' | '';
  brands: string[];
  showWishlistOnly: boolean;
}

interface ProductFiltersProps {
  filters: FiltersState;
  setFilters: Dispatch<SetStateAction<FiltersState>>;
}

export function ProductFilters({ filters, setFilters }: ProductFiltersProps) {
  const { data: categories = [] } = useGetCategoriesQuery();

  return (
    <DynamicFilterSidebar
      categories={categories}
      selectedCategory={filters.category}
      selectedSubcategories={[]}
      setSelectedSubcategories={() => {}}
      selectedAttributes={{}}
      setSelectedAttributes={() => {}}
      minPrice={filters.minPrice}
      setMinPrice={(value: number | ((prev: number) => number)) =>
        setFilters(prev => ({
          ...prev,
          minPrice: typeof value === 'function' ? value(prev.minPrice) : value
        }))}
      maxPrice={filters.maxPrice}
      setMaxPrice={(value: number | ((prev: number) => number)) =>
        setFilters(prev => ({
          ...prev,
          maxPrice: typeof value === 'function' ? value(prev.maxPrice) : value
        }))}
      inStock={filters.inStock}
      setInStock={(value: boolean | ((prev: boolean) => boolean)) =>
        setFilters(prev => ({
          ...prev,
          inStock: typeof value === 'function' ? value(prev.inStock) : value
        }))}
      rating={filters.rating}
      setRating={(value: number | null | ((prev: number | null) => number | null)) =>
        setFilters(prev => ({
          ...prev,
          rating: typeof value === 'function' ? value(prev.rating) : value
        }))}
      // sortBy props removed
      brands={filters.brands}
      setBrands={(value: string[] | ((prev: string[]) => string[])) =>
        setFilters(prev => ({
          ...prev,
          brands: typeof value === 'function' ? value(prev.brands) : value
        }))}
    />
  );
}