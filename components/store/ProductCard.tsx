"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { ShoppingCart, CheckCircle, Eye, Heart, Star, Sparkles, TrendingUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { useAuth } from "@/context/AuthContext";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import type { Product } from "@/types/product";
import { ConfirmationModal } from "@/components/ConfirmationModal";
import { WishlistButton } from "@/components/wishlist/WishlistButton";
import { RatingStars } from "@/components/product/RatingStars";
import { useRouter } from "next/navigation";
import { JoinGroupModal } from "@/components/modals/JoinGroupModal";
import { motion, AnimatePresence } from "framer-motion";

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const [addToCart, { isLoading: isAdding }] = useAddToCartMutation();
  const { user } = useAuth();
  const { userGroups } = useGroupMembership(user?._id);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isJoinGroupOpen, setIsJoinGroupOpen] = useState(false);
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const [groupId, setGroupId] = useState<string | null>(null);

  // Get the current group ID from localStorage on the client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedGroupId = localStorage.getItem('currentGroupId');
      if (storedGroupId) {
        setGroupId(storedGroupId);
      } else if (userGroups.length > 0) {
        // If no group ID in localStorage but user has groups, use the first one
        setGroupId(userGroups[0]._id);
        localStorage.setItem('currentGroupId', userGroups[0]._id);
      }
    }
  }, [userGroups]);

  // Determine button text based on user authentication and group membership
  const getButtonText = () => {
    if (!user) {
      return "Join Group";
    }

    if (userGroups.length === 0) {
      return "Join Group";
    }

    return isAdding ? "Adding..." : success ? "Added" : "Add to Cart";
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Check if product is popular (high rating or high stock)
  const isPopular = (product.averageRating && product.averageRating >= 4.5) || product.stock > 50;

  // Check if product is trending (recently added or has discount)
  const isTrending = (product.originalPrice && product.originalPrice > product.price) ||
                    (new Date(product.createdAt).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000);

  const handleAddToCart = async () => {
    // If user is not logged in, open the join group modal
    if (!user) {
      setIsJoinGroupOpen(true);
      return;
    }

    // If user is logged in but not in any group, open the join group modal
    if (userGroups.length === 0) {
      setIsJoinGroupOpen(true);
      return;
    }

    // User is logged in and in a group, proceed with adding to cart directly
    try {
      // Validate required fields before making the API call
      if (!user._id || !product._id || !groupId) {
        console.error('Missing required fields:', {
          userId: user._id,
          productId: product._id,
          groupId
        });
        return;
      }

      // Set loading state
      setSuccess(false);

      const cartItem = {
        userId: user._id,
        productId: product._id,
        quantity: 1,
        groupId
      };

      console.log('Adding item to cart:', cartItem);

      // Add to cart
      const result = await addToCart(cartItem).unwrap();

      if (result) {
        console.log('Successfully added to cart:', result);

        // Show success state
        setSuccess(true);
        setTimeout(() => setSuccess(false), 2000);

        // Show confirmation modal
        setIsModalOpen(true);
      }
    } catch (error: unknown) {
      console.error('Failed to add item to cart:', error);

      // Extract error message
      // Type guard to safely access error properties
      const errorMessage =
        typeof error === 'object' && error !== null
          ? (
              // Check if error has data.error property
              'data' in error &&
              typeof error.data === 'object' &&
              error.data !== null &&
              'error' in error.data &&
              typeof error.data.error === 'string'
                ? error.data.error
                // Check if error has error.data.error property
                : 'error' in error &&
                  typeof error.error === 'object' &&
                  error.error !== null &&
                  'data' in error.error &&
                  typeof error.error.data === 'object' &&
                  error.error.data !== null &&
                  'error' in error.error.data &&
                  typeof error.error.data.error === 'string'
                  ? error.error.data.error
                  : 'Failed to add item to cart. Please try again.'
            )
          : 'Failed to add item to cart. Please try again.';

      // Show error message to the user
      alert(errorMessage);
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ y: -8, scale: 1.02 }}
        transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
        className="group h-full"
      >
        <Card className="h-full flex flex-col overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 rounded-3xl bg-gradient-to-br from-white via-white to-gray-50/30 relative backdrop-blur-sm">
          {/* Gradient Border Effect */}
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-purple-200/20 via-transparent to-blue-200/20 p-[1px]">
            <div className="h-full w-full rounded-3xl bg-white" />
          </div>

          {/* Floating Glow Effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-blue-600/10 rounded-3xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          {/* Product Image Container */}
          <div className="relative h-80 w-full overflow-hidden bg-gradient-to-br from-gray-50/50 via-white to-purple-50/30 rounded-t-3xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-[0.02]">
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.3),transparent_50%)]" />
            </div>

            <Image
              src={product.image && product.image.trim() !== '' ? `/api/images/${product.image}` : "/placeholder.svg"}
              alt={product.name || "Product image"}
              fill
              className="object-contain p-8 group-hover:scale-110 transition-all duration-500 ease-out relative z-10"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder.svg";
              }}
            />

            {/* Top Badges Row */}
            <div className="absolute top-4 left-4 right-4 flex justify-between items-start z-20">
              {/* Left Side Badges */}
              <div className="flex flex-col gap-2">
                {/* Stock Badge */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Badge
                    variant={product.stock > 0 ? "default" : "destructive"}
                    className={`text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-md ${
                      product.stock > 0
                        ? "bg-emerald-500/90 text-white border-0 shadow-emerald-500/25"
                        : "bg-red-500/90 text-white border-0 shadow-red-500/25"
                    }`}
                  >
                    {product.stock > 0 ? 'In Stock' : 'Out of Stock'}
                  </Badge>
                </motion.div>

                {/* Popular Badge */}
                {isPopular && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-semibold px-3 py-1.5 text-xs rounded-full shadow-lg border-0 shadow-yellow-500/25">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Popular
                    </Badge>
                  </motion.div>
                )}

                {/* Trending Badge */}
                {isTrending && !isPopular && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <Badge className="bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold px-3 py-1.5 text-xs rounded-full shadow-lg border-0 shadow-pink-500/25">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Trending
                    </Badge>
                  </motion.div>
                )}
              </div>

              {/* Right Side Badges */}
              <div className="flex flex-col gap-2 items-end">
                {/* Discount Badge */}
                {product.originalPrice && product.originalPrice > product.price && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Badge className="bg-gradient-to-r from-red-500 to-pink-600 text-white font-bold px-3 py-1.5 text-xs rounded-full shadow-lg border-0 shadow-red-500/25">
                      -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                    </Badge>
                  </motion.div>
                )}

                {/* Wishlist Button */}
                <AnimatePresence>
                  {user && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    >
                      <WishlistButton
                        productId={product._id}
                        userId={user._id}
                        size="sm"
                        variant="secondary"
                        showText={false}
                        className="h-10 w-10 rounded-full bg-white/95 backdrop-blur-md hover:bg-white shadow-lg border-0 shadow-black/10"
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
          {/* Product Details */}
          <CardContent className="flex-1 p-8 relative z-10">
            <div className="space-y-5 h-full flex flex-col">
              {/* Product Name */}
              <motion.h3
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="font-bold text-xl text-gray-900 line-clamp-2 leading-tight min-h-[3.5rem] group-hover:text-purple-900 transition-colors duration-300"
                style={{
                  fontFamily: "ClashDisplay-Variable, sans-serif",
                  letterSpacing: "-0.02em",
                  fontWeight: "700"
                }}
              >
                {product.name}
              </motion.h3>

              {/* Category */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.15 }}
                className="flex items-center gap-2"
              >
                <div className="h-1 w-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full" />
                <p className="text-sm text-gray-600 font-semibold uppercase tracking-wider">
                  {product.category && typeof product.category !== 'string' && 'name' in product.category
                    ? product.category.name
                    : 'Uncategorized'}
                </p>
              </motion.div>

              {/* Rating */}
              {(product.averageRating && product.averageRating > 0) && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="flex items-center gap-3"
                >
                  <RatingStars
                    rating={product.averageRating}
                    size="sm"
                    showValue={false}
                  />
                  <span className="text-sm text-gray-600 font-semibold">
                    {product.averageRating.toFixed(1)} ({product.reviewCount || 0} reviews)
                  </span>
                </motion.div>
              )}

              {/* Price Section */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.25 }}
                className="space-y-3 flex-1"
              >
                <div className="flex items-baseline gap-3">
                  <span
                    className="text-3xl font-black bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"
                    style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
                  >
                    {formatPrice(product.price)}
                  </span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-lg text-gray-400 line-through font-medium">
                      {formatPrice(product.originalPrice)}
                    </span>
                  )}
                </div>

                {/* Savings Display */}
                {product.originalPrice && product.originalPrice > product.price && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 }}
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-green-50 to-emerald-50 px-3 py-1.5 rounded-full border border-green-200/50"
                  >
                    <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                    <p className="text-sm text-green-700 font-bold">
                      Save {formatPrice(product.originalPrice - product.price)}
                    </p>
                  </motion.div>
                )}
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="space-y-3 pt-6"
              >
                {/* Main Action Button */}
                <Button
                  onClick={handleAddToCart}
                  disabled={isAdding || product.stock === 0}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 rounded-2xl transition-all duration-300 disabled:opacity-50 shadow-lg hover:shadow-xl hover:shadow-purple-500/25 transform hover:scale-[1.02] active:scale-[0.98]"
                  size="lg"
                >
                  <AnimatePresence mode="wait">
                    {isAdding ? (
                      <motion.div
                        key="loading"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="flex items-center"
                      >
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="mr-2"
                        >
                          <ShoppingCart className="h-5 w-5" />
                        </motion.div>
                        Adding...
                      </motion.div>
                    ) : success ? (
                      <motion.div
                        key="success"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        className="flex items-center"
                      >
                        <CheckCircle className="h-5 w-5 mr-2" />
                        Added!
                      </motion.div>
                    ) : (
                      <motion.div
                        key="default"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="flex items-center"
                      >
                        <ShoppingCart className="h-5 w-5 mr-2" />
                        {getButtonText()}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Button>

                {/* View Details Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-purple-600 hover:text-purple-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 font-semibold rounded-xl transition-all duration-300 border border-purple-200/50 hover:border-purple-300"
                  onClick={() => router.push(`/products/${product._id}`)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        productName={product.name}
      />

      {/* Join Group Modal */}
      <JoinGroupModal
        isOpen={isJoinGroupOpen}
        onClose={() => setIsJoinGroupOpen(false)}
        productId={product._id}
      />
    </>
  );
}