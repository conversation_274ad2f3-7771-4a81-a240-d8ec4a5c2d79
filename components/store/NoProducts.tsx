"use client";

import { ShoppingBag, Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

interface NoProductsProps {
  categoryName?: string;
  onReset: () => void;
}

export function NoProducts({ categoryName = "this category", onReset }: NoProductsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col items-center justify-center py-16 px-8 bg-gradient-to-br from-white to-purple-50/30 rounded-2xl shadow-lg border border-white/20"
    >
      {/* Animated Icon */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        className="relative mb-6"
      >
        <div className="bg-gradient-to-br from-purple-100 to-purple-200 p-6 rounded-full shadow-lg">
          <ShoppingBag className="h-16 w-16 text-purple-600" />
        </div>
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          className="absolute -top-2 -right-2 bg-white rounded-full p-2 shadow-md"
        >
          <Search className="h-6 w-6 text-gray-400" />
        </motion.div>
      </motion.div>

      {/* Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="text-center space-y-4"
      >
        <h3
          className="text-2xl font-bold text-gray-900 mb-2"
          style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
        >
          No products found
        </h3>
        <p className="text-gray-600 text-center max-w-md leading-relaxed">
          We couldn&apos;t find any products in <span className="font-semibold text-purple-600">{categoryName}</span>.
          Try adjusting your filters or browse our full collection.
        </p>
      </motion.div>

      {/* Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex flex-col sm:flex-row gap-3 mt-8"
      >
        <Button
          variant="outline"
          className="border-purple-300 text-purple-600 hover:bg-purple-50 hover:border-purple-400 rounded-full px-6"
          onClick={onReset}
        >
          <Filter className="h-4 w-4 mr-2" />
          Clear Filters
        </Button>
        <Button
          className="bg-purple-600 hover:bg-purple-700 text-white rounded-full px-6"
          onClick={onReset}
        >
          <ShoppingBag className="h-4 w-4 mr-2" />
          View All Products
        </Button>
      </motion.div>
    </motion.div>
  );
}
