

import { Dispatch, SetStateAction } from 'react'
// import { FilterState } from '@/types/filters'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"

export interface FilterSidebarProps {
  selectedCategories: string[]
  setSelectedCategories: Dispatch<SetStateAction<string[]>>
  selectedColors: string[]
  setSelectedColors: Dispatch<SetStateAction<string[]>>
  selectedSizes: string[]
  setSelectedSizes: Dispatch<SetStateAction<string[]>>
  selectedPriceRange: string | null
  setSelectedPriceRange: Dispatch<SetStateAction<string | null>>
  minPrice: number
  setMinPrice: Dispatch<SetStateAction<number>>
  maxPrice: number
  setMaxPrice: Dispatch<SetStateAction<number>>
}

const categories = ["Men's Shoes", "Women's Shoes", "Kids' Shoes", "Accessories"]
const colors = ["Black", "White", "Red", "Blue", "Green"]
const sizes = ["6", "7", "8", "9", "10", "11", "12"]

export function FilterSidebar({
  selectedCategories,
  setSelectedCategories,
  selectedColors,
  setSelectedColors,
  selectedSizes,
  setSelectedSizes,
  selectedPriceRange,
  setSelectedPriceRange,
  minPrice,
  setMinPrice,
  maxPrice,
  setMaxPrice,
}: FilterSidebarProps) {
  const handleCategoryChange = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const handleColorChange = (color: string) => {
    setSelectedColors(prev => 
      prev.includes(color) 
        ? prev.filter(c => c !== color)
        : [...prev, color]
    )
  }

  const handleSizeChange = (size: string) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    )
  }

  const handlePriceRangeChange = (range: string) => {
    setSelectedPriceRange(range);
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Filters</CardTitle>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="categories">
            <AccordionTrigger>Categories</AccordionTrigger>
            <AccordionContent>
              {categories.map(category => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox 
                    id={category} 
                    checked={selectedCategories.includes(category)}
                    onCheckedChange={() => handleCategoryChange(category)}
                  />
                  <Label htmlFor={category}>{category}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="colors">
            <AccordionTrigger>Colors</AccordionTrigger>
            <AccordionContent>
              {colors.map(color => (
                <div key={color} className="flex items-center space-x-2">
                  <Checkbox 
                    id={color} 
                    checked={selectedColors.includes(color)}
                    onCheckedChange={() => handleColorChange(color)}
                  />
                  <Label htmlFor={color}>{color}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="sizes">
            <AccordionTrigger>Sizes</AccordionTrigger>
            <AccordionContent>
              {sizes.map(size => (
                <div key={size} className="flex items-center space-x-2">
                  <Checkbox 
                    id={size} 
                    checked={selectedSizes.includes(size)}
                    onCheckedChange={() => handleSizeChange(size)}
                  />
                  <Label htmlFor={size}>{size}</Label>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="price">
            <AccordionTrigger>Price Range</AccordionTrigger>
            <AccordionContent>
              {["0-100", "101-300", "301-500", "501-1000"].map(range => (
                <div key={range} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`price-${range}`}
                    checked={selectedPriceRange === range}
                    onCheckedChange={() => handlePriceRangeChange(range)}
                  />
                  <Label htmlFor={`price-${range}`}>${range}</Label>
                </div>
              ))}
              <Slider
                min={0}
                max={1000}
                step={10}
                value={[minPrice, maxPrice]}
                onValueChange={([min, max]) => {
                  setMinPrice(min)
                  setMaxPrice(max)
                  setSelectedPriceRange(null) // Reset selected range when using slider
                }}
              />
              <div className="flex justify-between mt-2">
                <span>${minPrice}</span>
                <span>${maxPrice}</span>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}

