"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Heart, 
  Star, 
  ShoppingCart, 
  Eye, 
  X,
  ChevronRight,
  Package,
  TrendingUp
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  useGetWishlistSummaryQuery,
  useGetUserWishlistsQuery,
  useRemoveFromWishlistMutation
} from "@/lib/redux/features/wishlist/wishlistApiSlice";
import { useAddToCartMutation } from "@/lib/redux/features/cart/cartApiSlice";
import { formatWishlistValue, getPriorityConfig } from "@/types/wishlist";
import { toast } from "sonner";
import Link from "next/link";

interface StoreWishlistPanelProps {
  userId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function StoreWishlistPanel({ userId, isOpen, onClose }: StoreWishlistPanelProps) {
  const [addToCart] = useAddToCartMutation();
  const [removeFromWishlist] = useRemoveFromWishlistMutation();
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const { data: wishlistSummary, isLoading: summaryLoading } = useGetWishlistSummaryQuery(userId);
  const { data: wishlists = [], isLoading: wishlistsLoading } = useGetUserWishlistsQuery(userId);

  const defaultWishlist = wishlists.find(w => w.name === 'My Wishlist') || wishlists[0];

  const handleAddToCart = async (productId: string, productName: string) => {
    setIsLoading(productId);
    try {
      await addToCart({
        userId,
        productId,
        quantity: 1
      }).unwrap();
      toast.success(`${productName} added to cart`);
    } catch (error) {
      console.error('Failed to add to cart:', error);
      toast.error('Failed to add to cart');
    } finally {
      setIsLoading(null);
    }
  };

  const handleRemoveFromWishlist = async (productId: string, productName: string) => {
    if (!defaultWishlist) return;
    
    try {
      await removeFromWishlist({
        wishlistId: defaultWishlist._id,
        userId,
        productId
      }).unwrap();
      toast.success(`${productName} removed from wishlist`);
    } catch (error) {
      console.error('Failed to remove from wishlist:', error);
      toast.error('Failed to remove from wishlist');
    }
  };

  if (summaryLoading || wishlistsLoading) {
    return (
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-50 overflow-y-auto"
          >
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/20 z-40"
          />

          {/* Panel */}
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-50 overflow-y-auto"
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                  <Heart className="h-6 w-6 text-red-500" />
                  <h2 className="text-xl font-bold text-gray-900">My Wishlist</h2>
                </div>
                <Button variant="ghost" size="icon" onClick={onClose}>
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Summary Stats */}
              {wishlistSummary && (
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {wishlistSummary.totalItems}
                      </div>
                      <div className="text-xs text-gray-600">Total Items</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {formatWishlistValue(wishlistSummary.totalValue)}
                      </div>
                      <div className="text-xs text-gray-600">Total Value</div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Recent Items */}
              {defaultWishlist && defaultWishlist.items.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">Recent Items</h3>
                    <Link href="/profile/wishlist">
                      <Button variant="ghost" size="sm" className="text-purple-600">
                        View All
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </Link>
                  </div>

                  <div className="space-y-3">
                    {defaultWishlist.items.slice(0, 5).map((item, index) => {
                      const product = typeof item.product === 'object' && 'name' in item.product 
                        ? item.product 
                        : null;
                      
                      if (!product) return null;

                      const priorityConfig = getPriorityConfig(item.priority);

                      return (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="bg-gray-50 rounded-lg p-3"
                        >
                          <div className="flex items-start gap-3">
                            <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center">
                              {product.image ? (
                                <img 
                                  src={product.image} 
                                  alt={product.name}
                                  className="w-10 h-10 object-cover rounded"
                                />
                              ) : (
                                <Package className="h-6 w-6 text-gray-400" />
                              )}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h4 className="font-medium text-sm text-gray-900 truncate">
                                    {product.name}
                                  </h4>
                                  <div className="flex items-center gap-2 mt-1">
                                    <span className="text-sm font-semibold text-purple-600">
                                      {formatWishlistValue(product.price)}
                                    </span>
                                    <Badge 
                                      variant="outline" 
                                      className={`text-xs bg-${priorityConfig.color}-50 text-${priorityConfig.color}-700 border-${priorityConfig.color}-200`}
                                    >
                                      {priorityConfig.icon} {item.priority}
                                    </Badge>
                                  </div>
                                </div>
                                
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleRemoveFromWishlist(product._id, product.name)}
                                  className="h-6 w-6 text-gray-400 hover:text-red-500"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>

                              {/* Action Buttons */}
                              <div className="flex gap-2 mt-3">
                                <Button
                                  size="sm"
                                  onClick={() => handleAddToCart(product._id, product.name)}
                                  disabled={isLoading === product._id || product.stock <= 0}
                                  className="flex-1 h-8 text-xs"
                                >
                                  {isLoading === product._id ? (
                                    'Adding...'
                                  ) : product.stock <= 0 ? (
                                    'Out of Stock'
                                  ) : (
                                    <>
                                      <ShoppingCart className="h-3 w-3 mr-1" />
                                      Add to Cart
                                    </>
                                  )}
                                </Button>
                                <Link href={`/products/${product._id}`}>
                                  <Button variant="outline" size="sm" className="h-8 px-2">
                                    <Eye className="h-3 w-3" />
                                  </Button>
                                </Link>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>

                  {defaultWishlist.items.length > 5 && (
                    <div className="text-center pt-2">
                      <Link href="/profile/wishlist">
                        <Button variant="outline" size="sm">
                          View {defaultWishlist.items.length - 5} more items
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Your wishlist is empty
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Start adding products you love to your wishlist
                  </p>
                  <Button onClick={onClose} className="w-full">
                    Continue Shopping
                  </Button>
                </div>
              )}

              {/* Quick Actions */}
              {defaultWishlist && defaultWishlist.items.length > 0 && (
                <div className="mt-6 pt-6 border-t">
                  <div className="space-y-3">
                    <Link href="/profile/wishlist">
                      <Button variant="outline" className="w-full">
                        <Heart className="h-4 w-4 mr-2" />
                        Manage All Wishlists
                      </Button>
                    </Link>
                    
                    {wishlistSummary && wishlistSummary.topPriority.length > 0 && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex items-center gap-2 text-yellow-800">
                          <Star className="h-4 w-4 fill-current" />
                          <span className="text-sm font-medium">
                            {wishlistSummary.topPriority.length} high priority items
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
