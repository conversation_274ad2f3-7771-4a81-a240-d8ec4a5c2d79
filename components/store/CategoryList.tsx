"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { ChevronLeft, ChevronRight, Grid3X3 } from "lucide-react";
import { motion } from "framer-motion";

export interface CategoryListProps {
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
}

export function CategoryList({ selectedCategory = "All", onCategoryChange }: CategoryListProps) {
  const { data: categories = [], isLoading } = useGetCategoriesQuery();
  const allCategories = [{ _id: "All", name: "All Products" }, ...categories];

  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const [visibleCategories, setVisibleCategories] = useState(7);
  const [startIndex, setStartIndex] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setVisibleCategories(7);
      } else if (window.innerWidth >= 768) {
        setVisibleCategories(5);
      } else {
        setVisibleCategories(3);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    setShowLeftArrow(startIndex > 0);
    setShowRightArrow(startIndex + visibleCategories < allCategories.length);
  }, [startIndex, visibleCategories, allCategories.length]);

  const scroll = (direction: "left" | "right") => {
    if (direction === "left") {
      setStartIndex((prev) => Math.max(0, prev - 1));
    } else {
      setStartIndex((prev) => Math.min(allCategories.length - visibleCategories, prev + 1));
    }
  };

  if (isLoading) {
    return (
      <div className="relative bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center gap-3 mb-4">
          <div className="bg-purple-100 p-2 rounded-lg animate-pulse">
            <Grid3X3 className="h-5 w-5 text-purple-600" />
          </div>
          <h2
            className="text-lg font-semibold text-gray-900"
            style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
          >
            Categories
          </h2>
        </div>
        <div className="flex items-center gap-4 overflow-hidden px-8">
          <div className="animate-pulse flex space-x-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 w-28 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="bg-purple-100 p-2 rounded-lg">
            <Grid3X3 className="h-5 w-5 text-purple-600" />
          </div>
          <h2
            className="text-lg font-semibold text-gray-900"
            style={{ fontFamily: "ClashDisplay-Variable, sans-serif" }}
          >
            Categories
          </h2>
          <Badge variant="secondary" className="bg-purple-100 text-purple-700">
            {allCategories.length}
          </Badge>
        </div>

        {/* Navigation Controls */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            className={`h-10 w-10 rounded-full bg-white/90 backdrop-blur-sm shadow-lg border-gray-200 hover:bg-white hover:border-purple-300 transition-all duration-200 ${
              showLeftArrow ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
            onClick={() => scroll("left")}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className={`h-10 w-10 rounded-full bg-white/90 backdrop-blur-sm shadow-lg border-gray-200 hover:bg-white hover:border-purple-300 transition-all duration-200 ${
              showRightArrow ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
            onClick={() => scroll("right")}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Categories */}
      <div className="flex items-center gap-3 overflow-hidden">
        {allCategories.slice(startIndex, startIndex + visibleCategories).map((category, index) => (
          <motion.div
            key={category._id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.05 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="ghost"
              className={`whitespace-nowrap px-6 py-3 rounded-full transition-all duration-200 font-medium ${
                selectedCategory === category._id
                  ? "bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg hover:from-purple-700 hover:to-indigo-700"
                  : "text-gray-600 hover:bg-purple-50 hover:text-purple-600 border border-gray-200 hover:border-purple-300"
              }`}
              onClick={() => onCategoryChange(category._id)}
            >
              {category.name}
            </Button>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}