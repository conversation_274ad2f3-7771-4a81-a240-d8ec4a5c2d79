import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch } from '@/lib/redux/store';
import {
  createPayFastPayment,
  createRecurringPayFastPayment,
  fetchPayFastPaymentHistory,
  checkPayFastPaymentStatus,
  cancelPayFastSubscription,
  setCurrentPayment,
  addPaymentToHistory,
  setError,
  clearPaymentData,
  clearError,
  selectPayFastState,
  selectPayFastLoading,
  selectPayFastCurrentPayment,
  selectPayFastPaymentHistory,
  selectPayFastError,
  selectPayFastConfig
} from '../store/payFastSlice';
import {
  PayFastCreatePaymentRequest,
  PayFastRecurringPaymentRequest,
  PayFastPaymentStatus,
  PayFastCreatePaymentResponse
} from '../types';

/**
 * Custom hook for PayFast payment operations
 */
export const usePayFast = () => {
  const dispatch = useDispatch<AppDispatch>();
  
  // Selectors
  const state = useSelector(selectPayFastState);
  const isLoading = useSelector(selectPayFastLoading);
  const currentPayment = useSelector(selectPayFastCurrentPayment);
  const paymentHistory = useSelector(selectPayFastPaymentHistory);
  const error = useSelector(selectPayFastError);
  const config = useSelector(selectPayFastConfig);

  // Create standard payment
  const createPayment = useCallback(
    async (request: PayFastCreatePaymentRequest): Promise<PayFastCreatePaymentResponse | null> => {
      try {
        const result = await dispatch(createPayFastPayment(request)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to create PayFast payment:', error);
        return null;
      }
    },
    [dispatch]
  );

  // Create recurring payment
  const createRecurringPayment = useCallback(
    async (request: PayFastRecurringPaymentRequest): Promise<PayFastCreatePaymentResponse | null> => {
      try {
        const result = await dispatch(createRecurringPayFastPayment(request)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to create recurring PayFast payment:', error);
        return null;
      }
    },
    [dispatch]
  );

  // Fetch payment history
  const fetchPaymentHistory = useCallback(
    async (userId: string): Promise<PayFastPaymentStatus[]> => {
      try {
        const result = await dispatch(fetchPayFastPaymentHistory(userId)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to fetch PayFast payment history:', error);
        return [];
      }
    },
    [dispatch]
  );

  // Check payment status
  const checkPaymentStatus = useCallback(
    async (orderId: string): Promise<PayFastPaymentStatus | null> => {
      try {
        const result = await dispatch(checkPayFastPaymentStatus(orderId)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to check PayFast payment status:', error);
        return null;
      }
    },
    [dispatch]
  );

  // Cancel subscription
  const cancelSubscription = useCallback(
    async (subscriptionId: string): Promise<boolean> => {
      try {
        await dispatch(cancelPayFastSubscription(subscriptionId)).unwrap();
        return true;
      } catch (error) {
        console.error('Failed to cancel PayFast subscription:', error);
        return false;
      }
    },
    [dispatch]
  );

  // Process payment (redirect to PayFast)
  const processPayment = useCallback(
    (paymentData: PayFastCreatePaymentResponse) => {
      if (!paymentData.success || !paymentData.paymentData || !paymentData.paymentUrl) {
        throw new Error('Invalid payment data');
      }

      // Create form and submit to PayFast
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = paymentData.paymentUrl;
      form.style.display = 'none';

      // Add all payment data as hidden inputs
      Object.entries(paymentData.paymentData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = key;
          input.value = value.toString();
          form.appendChild(input);
        }
      });

      document.body.appendChild(form);
      form.submit();
    },
    []
  );

  // Create and process payment in one step
  const createAndProcessPayment = useCallback(
    async (request: PayFastCreatePaymentRequest): Promise<boolean> => {
      try {
        const paymentData = await createPayment(request);
        
        if (!paymentData || !paymentData.success) {
          return false;
        }

        processPayment(paymentData);
        return true;
      } catch (error) {
        console.error('Failed to create and process payment:', error);
        return false;
      }
    },
    [createPayment, processPayment]
  );

  // Poll payment status
  const pollPaymentStatus = useCallback(
    (orderId: string, options: {
      interval?: number;
      maxAttempts?: number;
      onStatusUpdate?: (status: PayFastPaymentStatus) => void;
      onComplete?: (status: PayFastPaymentStatus) => void;
      onError?: (error: string) => void;
    } = {}) => {
      const {
        interval = 5000,
        maxAttempts = 12,
        onStatusUpdate,
        onComplete,
        onError
      } = options;

      let attempts = 0;
      let pollInterval: NodeJS.Timeout;

      const poll = async () => {
        attempts++;

        try {
          const status = await checkPaymentStatus(orderId);
          
          if (status) {
            onStatusUpdate?.(status);

            // Check if payment is complete
            if (status.status === 'completed' || status.status === 'failed') {
              clearInterval(pollInterval);
              onComplete?.(status);
              return;
            }
          }

          // Stop polling if max attempts reached
          if (attempts >= maxAttempts) {
            clearInterval(pollInterval);
            onError?.('Payment status polling timeout');
          }
        } catch (error) {
          clearInterval(pollInterval);
          onError?.(error instanceof Error ? error.message : 'Polling error');
        }
      };

      // Start polling
      pollInterval = setInterval(poll, interval);
      
      // Initial check
      poll();

      // Return cleanup function
      return () => clearInterval(pollInterval);
    },
    [checkPaymentStatus]
  );

  // Get payment by order ID
  const getPaymentByOrderId = useCallback(
    (orderId: string): PayFastPaymentStatus | undefined => {
      return paymentHistory.find(payment => payment.orderId === orderId);
    },
    [paymentHistory]
  );

  // Get successful payments
  const getSuccessfulPayments = useCallback(
    (): PayFastPaymentStatus[] => {
      return paymentHistory.filter(payment => payment.status === 'completed');
    },
    [paymentHistory]
  );

  // Get failed payments
  const getFailedPayments = useCallback(
    (): PayFastPaymentStatus[] => {
      return paymentHistory.filter(payment => payment.status === 'failed');
    },
    [paymentHistory]
  );

  // Get pending payments
  const getPendingPayments = useCallback(
    (): PayFastPaymentStatus[] => {
      return paymentHistory.filter(payment => payment.status === 'pending');
    },
    [paymentHistory]
  );

  // Calculate total amount paid
  const getTotalAmountPaid = useCallback(
    (): number => {
      return getSuccessfulPayments().reduce((total, payment) => {
        return total + (payment.amount || 0);
      }, 0);
    },
    [getSuccessfulPayments]
  );

  // Clear current payment
  const clearCurrentPayment = useCallback(() => {
    dispatch(clearPaymentData());
  }, [dispatch]);

  // Clear error
  const clearCurrentError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Add payment to history manually
  const addToHistory = useCallback(
    (payment: PayFastPaymentStatus) => {
      dispatch(addPaymentToHistory(payment));
    },
    [dispatch]
  );

  // Set error manually
  const setCurrentError = useCallback(
    (error: string) => {
      dispatch(setError(error));
    },
    [dispatch]
  );

  return {
    // State
    state,
    isLoading,
    currentPayment,
    paymentHistory,
    error,
    config,

    // Actions
    createPayment,
    createRecurringPayment,
    fetchPaymentHistory,
    checkPaymentStatus,
    cancelSubscription,
    processPayment,
    createAndProcessPayment,
    pollPaymentStatus,

    // Utilities
    getPaymentByOrderId,
    getSuccessfulPayments,
    getFailedPayments,
    getPendingPayments,
    getTotalAmountPaid,
    clearCurrentPayment,
    clearCurrentError,
    addToHistory,
    setCurrentError
  };
};

/**
 * Hook for PayFast payment status polling
 */
export const usePayFastStatusPolling = (
  orderId: string,
  options: {
    enabled?: boolean;
    interval?: number;
    maxAttempts?: number;
    onStatusUpdate?: (status: PayFastPaymentStatus) => void;
    onComplete?: (status: PayFastPaymentStatus) => void;
    onError?: (error: string) => void;
  } = {}
) => {
  const { pollPaymentStatus } = usePayFast();
  const { enabled = true, ...pollOptions } = options;

  useEffect(() => {
    if (!enabled || !orderId) return;

    const cleanup = pollPaymentStatus(orderId, pollOptions);
    return cleanup;
  }, [orderId, enabled, pollPaymentStatus, pollOptions]);
};

/**
 * Hook for PayFast configuration
 */
export const usePayFastConfig = () => {
  const config = useSelector(selectPayFastConfig);
  
  const isConfigured = Boolean(
    config?.merchantId && 
    config?.merchantKey && 
    config?.returnUrl && 
    config?.cancelUrl && 
    config?.notifyUrl
  );

  const isSandbox = config?.sandbox ?? true;

  return {
    config,
    isConfigured,
    isSandbox
  };
};
