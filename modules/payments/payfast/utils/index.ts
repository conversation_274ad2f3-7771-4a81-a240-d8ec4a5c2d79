import crypto from 'crypto';
import {
  PayFastSignatureData,
  PayFastValidationResult,
  PayFastValidationError,
  PayFastSignatureError,
  PayFastConfig
} from '../types';

/**
 * PayFast Utility Functions
 */
export class PayFastUtils {
  /**
   * Generate MD5 signature for PayFast
   */
  static generateSignature(data: PayFastSignatureData, passphrase?: string): string {
    try {
      // Remove signature if present
      const cleanData = { ...data };
      delete cleanData.signature;

      // Create parameter string
      const paramString = Object.keys(cleanData)
        .sort()
        .map(key => `${key}=${encodeURIComponent(cleanData[key]).replace(/%20/g, '+')}`)
        .join('&');

      // Add passphrase if provided
      const stringToHash = passphrase 
        ? `${paramString}&passphrase=${encodeURIComponent(passphrase)}`
        : paramString;

      // Generate MD5 hash
      return crypto.createHash('md5').update(stringToHash).digest('hex');
    } catch (error) {
      throw new PayFastSignatureError('Failed to generate signature');
    }
  }

  /**
   * Verify PayFast signature
   */
  static verifySignature(
    data: PayFastSignatureData, 
    receivedSignature: string, 
    passphrase?: string
  ): boolean {
    try {
      const expectedSignature = this.generateSignature(data, passphrase);
      return crypto.timingSafeEqual(
        Buffer.from(receivedSignature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate payment amount
   */
  static validateAmount(amount: number): PayFastValidationResult {
    const errors: string[] = [];

    if (typeof amount !== 'number' || isNaN(amount)) {
      errors.push('Amount must be a valid number');
    } else if (amount <= 0) {
      errors.push('Amount must be greater than 0');
    } else if (amount > 1000000) {
      errors.push('Amount cannot exceed R1,000,000');
    } else if (Number(amount.toFixed(2)) !== amount) {
      errors.push('Amount cannot have more than 2 decimal places');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate email address
   */
  static validateEmail(email: string): PayFastValidationResult {
    const errors: string[] = [];
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email || typeof email !== 'string') {
      errors.push('Email is required');
    } else if (!emailRegex.test(email)) {
      errors.push('Invalid email format');
    } else if (email.length > 100) {
      errors.push('Email cannot exceed 100 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate order ID
   */
  static validateOrderId(orderId: string): PayFastValidationResult {
    const errors: string[] = [];

    if (!orderId || typeof orderId !== 'string') {
      errors.push('Order ID is required');
    } else if (orderId.length < 1 || orderId.length > 100) {
      errors.push('Order ID must be between 1 and 100 characters');
    } else if (!/^[a-zA-Z0-9_-]+$/.test(orderId)) {
      errors.push('Order ID can only contain letters, numbers, hyphens, and underscores');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate customer name
   */
  static validateCustomerName(name: string): PayFastValidationResult {
    const errors: string[] = [];

    if (!name || typeof name !== 'string') {
      errors.push('Customer name is required');
    } else if (name.trim().length < 2) {
      errors.push('Customer name must be at least 2 characters');
    } else if (name.length > 100) {
      errors.push('Customer name cannot exceed 100 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate PayFast configuration
   */
  static validateConfig(config: PayFastConfig): PayFastValidationResult {
    const errors: string[] = [];

    if (!config.merchantId) {
      errors.push('Merchant ID is required');
    }

    if (!config.merchantKey) {
      errors.push('Merchant Key is required');
    }

    if (!config.returnUrl) {
      errors.push('Return URL is required');
    } else if (!this.isValidUrl(config.returnUrl)) {
      errors.push('Return URL must be a valid URL');
    }

    if (!config.cancelUrl) {
      errors.push('Cancel URL is required');
    } else if (!this.isValidUrl(config.cancelUrl)) {
      errors.push('Cancel URL must be a valid URL');
    }

    if (!config.notifyUrl) {
      errors.push('Notify URL is required');
    } else if (!this.isValidUrl(config.notifyUrl)) {
      errors.push('Notify URL must be a valid URL');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if string is a valid URL
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Format amount for PayFast (2 decimal places)
   */
  static formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  /**
   * Parse customer name into first and last name
   */
  static parseCustomerName(fullName: string): { firstName: string; lastName: string } {
    const nameParts = fullName.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    return {
      firstName: firstName.substring(0, 100), // PayFast limit
      lastName: lastName.substring(0, 100)   // PayFast limit
    };
  }

  /**
   * Get PayFast payment URL based on environment
   */
  static getPaymentUrl(sandbox: boolean): string {
    return sandbox 
      ? 'https://sandbox.payfast.co.za/eng/process'
      : 'https://www.payfast.co.za/eng/process';
  }

  /**
   * Get PayFast API URL based on environment
   */
  static getApiUrl(sandbox: boolean): string {
    return sandbox
      ? 'https://api.sandbox.payfast.co.za'
      : 'https://api.payfast.co.za';
  }

  /**
   * Sanitize string for PayFast (remove special characters)
   */
  static sanitizeString(input: string, maxLength: number = 255): string {
    return input
      .replace(/[^\w\s-_.]/g, '') // Remove special characters except allowed ones
      .trim()
      .substring(0, maxLength);
  }

  /**
   * Generate unique order ID with prefix
   */
  static generateOrderId(prefix: string = 'ORD'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Check if payment status indicates success
   */
  static isPaymentSuccessful(status: string): boolean {
    return status === 'COMPLETE';
  }

  /**
   * Check if payment status indicates failure
   */
  static isPaymentFailed(status: string): boolean {
    return ['FAILED', 'CANCELLED'].includes(status);
  }

  /**
   * Check if payment status indicates pending
   */
  static isPaymentPending(status: string): boolean {
    return !this.isPaymentSuccessful(status) && !this.isPaymentFailed(status);
  }

  /**
   * Convert PayFast frequency to human readable
   */
  static getFrequencyLabel(frequency: number): string {
    const frequencies: Record<number, string> = {
      1: 'Weekly',
      2: 'Bi-weekly',
      3: 'Monthly',
      4: 'Quarterly',
      5: 'Bi-annually',
      6: 'Annually'
    };

    return frequencies[frequency] || 'Unknown';
  }

  /**
   * Validate recurring payment data
   */
  static validateRecurringData(data: {
    frequency: number;
    cycles: number;
    billingDate: Date;
  }): PayFastValidationResult {
    const errors: string[] = [];

    if (![1, 2, 3, 4, 5, 6].includes(data.frequency)) {
      errors.push('Invalid frequency. Must be 1-6');
    }

    if (data.cycles < 0) {
      errors.push('Cycles cannot be negative');
    }

    if (data.billingDate < new Date()) {
      errors.push('Billing date cannot be in the past');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Create form data for PayFast submission
   */
  static createFormData(paymentData: Record<string, string>): string {
    const form = Object.entries(paymentData)
      .map(([key, value]) => 
        `<input type="hidden" name="${key}" value="${value}" />`
      )
      .join('\n');

    return `
      <form id="payfast-form" method="POST" action="${this.getPaymentUrl(false)}">
        ${form}
      </form>
      <script>
        document.getElementById('payfast-form').submit();
      </script>
    `;
  }

  /**
   * Debug signature generation (development only)
   */
  static debugSignature(data: PayFastSignatureData, passphrase?: string): {
    originalData: PayFastSignatureData;
    sortedKeys: string[];
    paramString: string;
    stringToHash: string;
    signature: string;
  } {
    const cleanData = { ...data };
    delete cleanData.signature;

    const sortedKeys = Object.keys(cleanData).sort();
    const paramString = sortedKeys
      .map(key => `${key}=${encodeURIComponent(cleanData[key]).replace(/%20/g, '+')}`)
      .join('&');

    const stringToHash = passphrase 
      ? `${paramString}&passphrase=${encodeURIComponent(passphrase)}`
      : paramString;

    const signature = crypto.createHash('md5').update(stringToHash).digest('hex');

    return {
      originalData: data,
      sortedKeys,
      paramString,
      stringToHash,
      signature
    };
  }
}
