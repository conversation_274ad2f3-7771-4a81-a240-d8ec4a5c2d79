'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  History, 
  Search, 
  Filter, 
  CheckCircle, 
  XCircle, 
  Clock,
  Eye,
  Download,
  RefreshCw,
  Calendar
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePayFast } from '../hooks/usePayFast';
import { PayFastHistoryProps, PayFastPaymentStatus } from '../types';
import { toast } from 'sonner';

export function PayFastHistory({
  userId,
  limit = 10,
  showFilters = true,
  onPaymentSelect
}: PayFastHistoryProps) {
  const {
    fetchPaymentHistory,
    paymentHistory,
    isLoading,
    error
  } = usePayFast();

  const [filteredPayments, setFilteredPayments] = useState<PayFastPaymentStatus[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  // Load payment history on mount
  useEffect(() => {
    if (userId) {
      fetchPaymentHistory(userId);
    }
  }, [userId, fetchPaymentHistory]);

  // Filter payments based on search and filters
  useEffect(() => {
    let filtered = [...paymentHistory];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(payment =>
        payment.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.transactionId?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(payment => payment.status === statusFilter);
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      if (dateFilter !== 'all') {
        filtered = filtered.filter(payment => {
          const paymentDate = payment.paidAt ? new Date(payment.paidAt) : new Date();
          return paymentDate >= filterDate;
        });
      }
    }

    // Apply limit
    if (limit > 0) {
      filtered = filtered.slice(0, limit);
    }

    setFilteredPayments(filtered);
  }, [paymentHistory, searchTerm, statusFilter, dateFilter, limit]);

  // Get status display
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle,
          label: 'Completed'
        };
      case 'failed':
        return {
          color: 'bg-red-100 text-red-800',
          icon: XCircle,
          label: 'Failed'
        };
      case 'cancelled':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: XCircle,
          label: 'Cancelled'
        };
      default:
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: Clock,
          label: 'Pending'
        };
    }
  };

  // Export payments to CSV
  const exportToCSV = () => {
    const headers = ['Order ID', 'Amount', 'Status', 'Transaction ID', 'Date'];
    const csvData = filteredPayments.map(payment => [
      payment.orderId,
      payment.amount?.toFixed(2) || '0.00',
      payment.status,
      payment.transactionId || '',
      payment.paidAt ? new Date(payment.paidAt).toLocaleDateString() : ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `payfast-payments-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    URL.revokeObjectURL(url);

    toast.success('Payment history exported');
  };

  // Refresh history
  const refreshHistory = () => {
    if (userId) {
      fetchPaymentHistory(userId);
      toast.success('Payment history refreshed');
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <History className="h-6 w-6" />
              Payment History
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshHistory}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportToCSV}
                disabled={filteredPayments.length === 0}
              >
                <Download className="h-4 w-4" />
              </Button>
              {showFilters && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Filters Panel */}
          <AnimatePresence>
            {showFiltersPanel && showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-4 p-4 bg-gray-50 rounded-lg border"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Search</label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search by Order ID or Transaction ID"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Status</label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="failed">Failed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Date Range</label>
                    <Select value={dateFilter} onValueChange={setDateFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Time</SelectItem>
                        <SelectItem value="today">Today</SelectItem>
                        <SelectItem value="week">Last 7 Days</SelectItem>
                        <SelectItem value="month">Last Month</SelectItem>
                        <SelectItem value="year">Last Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-2xl font-bold text-blue-600">
                {paymentHistory.filter(p => p.status === 'completed').length}
              </p>
              <p className="text-sm text-blue-800">Completed</p>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <p className="text-2xl font-bold text-yellow-600">
                {paymentHistory.filter(p => p.status === 'pending').length}
              </p>
              <p className="text-sm text-yellow-800">Pending</p>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
              <p className="text-2xl font-bold text-red-600">
                {paymentHistory.filter(p => p.status === 'failed').length}
              </p>
              <p className="text-sm text-red-800">Failed</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
              <p className="text-2xl font-bold text-green-600">
                R{paymentHistory
                  .filter(p => p.status === 'completed')
                  .reduce((sum, p) => sum + (p.amount || 0), 0)
                  .toFixed(2)}
              </p>
              <p className="text-sm text-green-800">Total Paid</p>
            </div>
          </div>

          {/* Payment List */}
          <div className="space-y-3">
            <AnimatePresence>
              {filteredPayments.length > 0 ? (
                filteredPayments.map((payment, index) => {
                  const statusDisplay = getStatusDisplay(payment.status);
                  const StatusIcon = statusDisplay.icon;

                  return (
                    <motion.div
                      key={payment.orderId}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-4 bg-white border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => onPaymentSelect?.(payment)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <Badge className={statusDisplay.color}>
                              <StatusIcon className="h-3 w-3 mr-1" />
                              {statusDisplay.label}
                            </Badge>
                            <span className="font-mono text-sm text-gray-600">
                              {payment.orderId}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600">Amount</p>
                              <p className="font-semibold">
                                R{payment.amount?.toFixed(2) || '0.00'}
                              </p>
                            </div>
                            
                            {payment.transactionId && (
                              <div>
                                <p className="text-gray-600">Transaction ID</p>
                                <p className="font-mono text-xs">
                                  {payment.transactionId}
                                </p>
                              </div>
                            )}
                            
                            {payment.paidAt && (
                              <div>
                                <p className="text-gray-600">Date</p>
                                <p className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {new Date(payment.paidAt).toLocaleDateString()}
                                </p>
                              </div>
                            )}
                            
                            <div className="flex items-center">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onPaymentSelect?.(payment);
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-12"
                >
                  <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">No payment history found</p>
                  <p className="text-sm text-gray-500">
                    {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
                      ? 'Try adjusting your filters'
                      : 'Your payments will appear here once you make a purchase'}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Load More */}
          {paymentHistory.length > filteredPayments.length && (
            <div className="text-center">
              <Button
                variant="outline"
                onClick={() => {
                  // This would typically load more payments
                  toast.info('Load more functionality would be implemented here');
                }}
              >
                Load More Payments
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
