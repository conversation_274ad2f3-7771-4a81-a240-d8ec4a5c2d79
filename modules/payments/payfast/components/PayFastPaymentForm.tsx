'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  User, 
  Mail, 
  FileText, 
  DollarSign,
  Calendar,
  RefreshCw,
  AlertCircle,
  Info
} from 'lucide-react';
import { motion } from 'framer-motion';
import { usePayFast } from '../hooks/usePayFast';
import { PayFastPaymentFormProps } from '../types';
import { PayFastUtils } from '../utils';
import { toast } from 'sonner';

export function PayFastPaymentForm({
  orderId: initialOrderId,
  amount: initialAmount,
  description: initialDescription,
  customerEmail: initialEmail,
  customerName: initialName,
  onSuccess,
  onError,
  onCancel,
  showPaymentMethods = true,
  allowRecurring = false,
  customFields = {},
  className = '',
  disabled = false
}: PayFastPaymentFormProps) {
  const {
    createAndProcessPayment,
    createRecurringPayment,
    isLoading,
    error,
    clearCurrentError
  } = usePayFast();

  // Form state
  const [formData, setFormData] = useState({
    orderId: initialOrderId || PayFastUtils.generateOrderId(),
    amount: initialAmount || 0,
    description: initialDescription || '',
    customerEmail: initialEmail || '',
    customerName: initialName || '',
    isRecurring: false,
    frequency: 3, // Monthly
    cycles: 0, // Indefinite
    billingDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0] // Tomorrow
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isProcessing, setIsProcessing] = useState(false);

  // Clear error when component mounts
  useEffect(() => {
    clearCurrentError();
  }, [clearCurrentError]);

  // Handle error changes
  useEffect(() => {
    if (error) {
      onError?.(error);
      toast.error(error);
    }
  }, [error, onError]);

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Validate amount
    const amountValidation = PayFastUtils.validateAmount(formData.amount);
    if (!amountValidation.isValid) {
      errors.amount = amountValidation.errors[0];
    }

    // Validate email
    const emailValidation = PayFastUtils.validateEmail(formData.customerEmail);
    if (!emailValidation.isValid) {
      errors.customerEmail = emailValidation.errors[0];
    }

    // Validate order ID
    const orderIdValidation = PayFastUtils.validateOrderId(formData.orderId);
    if (!orderIdValidation.isValid) {
      errors.orderId = orderIdValidation.errors[0];
    }

    // Validate customer name
    const nameValidation = PayFastUtils.validateCustomerName(formData.customerName);
    if (!nameValidation.isValid) {
      errors.customerName = nameValidation.errors[0];
    }

    // Validate description
    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }

    // Validate recurring data if applicable
    if (formData.isRecurring) {
      const recurringValidation = PayFastUtils.validateRecurringData({
        frequency: formData.frequency,
        cycles: formData.cycles,
        billingDate: new Date(formData.billingDate)
      });

      if (!recurringValidation.isValid) {
        errors.recurring = recurringValidation.errors[0];
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (disabled || isLoading || isProcessing) return;

    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    try {
      setIsProcessing(true);
      clearCurrentError();

      if (formData.isRecurring) {
        // Create recurring payment
        const result = await createRecurringPayment({
          orderId: formData.orderId,
          amount: formData.amount,
          description: formData.description,
          customerEmail: formData.customerEmail,
          customerName: formData.customerName,
          frequency: formData.frequency as 3 | 6,
          cycles: formData.cycles,
          billingDate: new Date(formData.billingDate),
          customData: customFields
        });

        if (result?.success && result.paymentData && result.paymentUrl) {
          // Process the payment (redirect to PayFast)
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = result.paymentUrl;
          form.style.display = 'none';

          Object.entries(result.paymentData).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
              const input = document.createElement('input');
              input.type = 'hidden';
              input.name = key;
              input.value = value.toString();
              form.appendChild(input);
            }
          });

          document.body.appendChild(form);
          form.submit();

          onSuccess?.({
            orderId: formData.orderId,
            status: 'pending',
            paymentMethod: 'payfast'
          });
        } else {
          throw new Error('Failed to create recurring payment');
        }
      } else {
        // Create standard payment
        const success = await createAndProcessPayment({
          orderId: formData.orderId,
          amount: formData.amount,
          description: formData.description,
          customerEmail: formData.customerEmail,
          customerName: formData.customerName,
          customData: customFields
        });

        if (success) {
          onSuccess?.({
            orderId: formData.orderId,
            status: 'pending',
            paymentMethod: 'payfast'
          });
        } else {
          throw new Error('Failed to create payment');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      onError?.(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Generate new order ID
  const generateNewOrderId = () => {
    const newOrderId = PayFastUtils.generateOrderId();
    handleInputChange('orderId', newOrderId);
    toast.success('New order ID generated');
  };

  const frequencyOptions = [
    { value: 3, label: 'Monthly' },
    { value: 6, label: 'Bi-annually' }
  ];

  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl">
            <CreditCard className="h-6 w-6" />
            PayFast Payment Form
          </CardTitle>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Order Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Order Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="orderId" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Order ID
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="orderId"
                      value={formData.orderId}
                      onChange={(e) => handleInputChange('orderId', e.target.value)}
                      placeholder="Enter order ID"
                      className={validationErrors.orderId ? 'border-red-500' : ''}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={generateNewOrderId}
                      className="px-3"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                  {validationErrors.orderId && (
                    <p className="text-sm text-red-600">{validationErrors.orderId}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="amount" className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Amount (ZAR)
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0.01"
                    max="1000000"
                    value={formData.amount}
                    onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    className={validationErrors.amount ? 'border-red-500' : ''}
                  />
                  {validationErrors.amount && (
                    <p className="text-sm text-red-600">{validationErrors.amount}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter payment description"
                  rows={3}
                  className={validationErrors.description ? 'border-red-500' : ''}
                />
                {validationErrors.description && (
                  <p className="text-sm text-red-600">{validationErrors.description}</p>
                )}
              </div>
            </div>

            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customerName" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Full Name
                  </Label>
                  <Input
                    id="customerName"
                    value={formData.customerName}
                    onChange={(e) => handleInputChange('customerName', e.target.value)}
                    placeholder="Enter customer name"
                    className={validationErrors.customerName ? 'border-red-500' : ''}
                  />
                  {validationErrors.customerName && (
                    <p className="text-sm text-red-600">{validationErrors.customerName}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customerEmail" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email Address
                  </Label>
                  <Input
                    id="customerEmail"
                    type="email"
                    value={formData.customerEmail}
                    onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                    placeholder="Enter email address"
                    className={validationErrors.customerEmail ? 'border-red-500' : ''}
                  />
                  {validationErrors.customerEmail && (
                    <p className="text-sm text-red-600">{validationErrors.customerEmail}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Recurring Payment Options */}
            {allowRecurring && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Recurring Payment</h3>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="recurring"
                      checked={formData.isRecurring}
                      onCheckedChange={(checked) => handleInputChange('isRecurring', checked)}
                    />
                    <Label htmlFor="recurring">Enable recurring payment</Label>
                  </div>
                </div>

                {formData.isRecurring && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="frequency">Frequency</Label>
                        <Select
                          value={formData.frequency.toString()}
                          onValueChange={(value) => handleInputChange('frequency', parseInt(value))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {frequencyOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value.toString()}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cycles">Cycles (0 = indefinite)</Label>
                        <Input
                          id="cycles"
                          type="number"
                          min="0"
                          value={formData.cycles}
                          onChange={(e) => handleInputChange('cycles', parseInt(e.target.value) || 0)}
                          placeholder="0"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="billingDate" className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          First Billing Date
                        </Label>
                        <Input
                          id="billingDate"
                          type="date"
                          value={formData.billingDate}
                          onChange={(e) => handleInputChange('billingDate', e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                        />
                      </div>
                    </div>

                    {validationErrors.recurring && (
                      <Alert className="border-red-200 bg-red-50">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-800">
                          {validationErrors.recurring}
                        </AlertDescription>
                      </Alert>
                    )}
                  </motion.div>
                )}
              </div>
            )}

            {/* Payment Methods Info */}
            {showPaymentMethods && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Available Payment Methods</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {[
                    { name: 'Credit Cards', icon: '💳' },
                    { name: 'Instant EFT', icon: '🏦' },
                    { name: 'SnapScan', icon: '📱' },
                    { name: 'Mobicred', icon: '💰' }
                  ].map((method) => (
                    <div key={method.name} className="text-center p-3 bg-white border rounded-lg">
                      <div className="text-2xl mb-1">{method.icon}</div>
                      <p className="text-xs font-medium">{method.name}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {/* Info Alert */}
            <Alert className="border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                You will be redirected to PayFast to complete your payment securely.
              </AlertDescription>
            </Alert>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <Button
                type="submit"
                disabled={disabled || isLoading || isProcessing}
                className="flex-1 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                {isLoading || isProcessing ? (
                  <>
                    <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Pay R{formData.amount.toFixed(2)}
                    {formData.isRecurring && ` (${PayFastUtils.getFrequencyLabel(formData.frequency)})`}
                  </>
                )}
              </Button>

              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading || isProcessing}
                  className="px-6"
                >
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
