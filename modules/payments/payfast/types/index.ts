// PayFast Module Types
export interface PayFastConfig {
  merchantId: string;
  merchantKey: string;
  passphrase?: string;
  sandbox: boolean;
  returnUrl: string;
  cancelUrl: string;
  notifyUrl: string;
}

export interface PayFastPaymentData {
  merchant_id: string;
  merchant_key: string;
  return_url: string;
  cancel_url: string;
  notify_url: string;
  name_first: string;
  name_last: string;
  email_address: string;
  m_payment_id: string;
  amount: string;
  item_name: string;
  item_description: string;
  custom_int1?: string;
  custom_str1?: string;
  signature: string;
}

export interface PayFastCreatePaymentRequest {
  orderId: string;
  amount: number;
  description: string;
  customerEmail: string;
  customerName: string;
  customData?: {
    int1?: string;
    str1?: string;
  };
}

export interface PayFastCreatePaymentResponse {
  success: boolean;
  paymentData?: PayFastPaymentData;
  paymentUrl?: string;
  error?: string;
}

export interface PayFastNotificationData {
  m_payment_id: string;
  pf_payment_id: string;
  payment_status: 'COMPLETE' | 'FAILED' | 'CANCELLED';
  item_name: string;
  item_description: string;
  amount_gross: string;
  amount_fee: string;
  amount_net: string;
  custom_str1?: string;
  custom_int1?: string;
  name_first: string;
  name_last: string;
  email_address: string;
  merchant_id: string;
  signature: string;
}

export interface PayFastSubscriptionData {
  subscription_type: number;
  billing_date: string;
  recurring_amount: string;
  frequency: number;
  cycles: number;
}

export interface PayFastRecurringPaymentRequest extends PayFastCreatePaymentRequest {
  frequency: 3 | 6; // 3 = monthly, 6 = bi-annually
  cycles: number; // 0 = indefinite
  billingDate: Date;
}

export interface PayFastPaymentStatus {
  orderId: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  transactionId?: string;
  amount?: number;
  paymentMethod: 'payfast';
  paidAt?: Date;
  failureReason?: string;
}

export interface PayFastRefundRequest {
  originalTransactionId: string;
  amount: number;
  reason?: string;
}

export interface PayFastRefundResponse {
  success: boolean;
  refundId?: string;
  error?: string;
}

// Store types for Redux
export interface PayFastState {
  isLoading: boolean;
  currentPayment: PayFastCreatePaymentResponse | null;
  paymentHistory: PayFastPaymentStatus[];
  error: string | null;
  config: PayFastConfig | null;
}

export interface PayFastActions {
  setLoading: (loading: boolean) => void;
  setCurrentPayment: (payment: PayFastCreatePaymentResponse | null) => void;
  addPaymentToHistory: (payment: PayFastPaymentStatus) => void;
  setError: (error: string | null) => void;
  setConfig: (config: PayFastConfig) => void;
  clearPaymentData: () => void;
}

// Component Props
export interface PayFastCheckoutProps {
  orderId: string;
  amount: number;
  description: string;
  customerEmail: string;
  customerName: string;
  onSuccess?: (result: PayFastPaymentStatus) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
  disabled?: boolean;
}

export interface PayFastPaymentFormProps extends PayFastCheckoutProps {
  showPaymentMethods?: boolean;
  allowRecurring?: boolean;
  customFields?: Record<string, any>;
}

export interface PayFastStatusProps {
  orderId: string;
  onStatusUpdate?: (status: PayFastPaymentStatus) => void;
  pollInterval?: number;
  maxAttempts?: number;
}

export interface PayFastHistoryProps {
  userId?: string;
  limit?: number;
  showFilters?: boolean;
  onPaymentSelect?: (payment: PayFastPaymentStatus) => void;
}

// Utility types
export type PayFastEnvironment = 'sandbox' | 'production';

export interface PayFastValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface PayFastSignatureData {
  [key: string]: string | number;
}

// Error types
export class PayFastError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'PayFastError';
  }
}

export class PayFastValidationError extends PayFastError {
  constructor(message: string, public field?: string) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'PayFastValidationError';
  }
}

export class PayFastNetworkError extends PayFastError {
  constructor(message: string, public statusCode?: number) {
    super(message, 'NETWORK_ERROR');
    this.name = 'PayFastNetworkError';
  }
}

export class PayFastSignatureError extends PayFastError {
  constructor(message: string = 'Invalid signature') {
    super(message, 'SIGNATURE_ERROR');
    this.name = 'PayFastSignatureError';
  }
}
