import {
  PayFastConfig,
  PayFastPaymentData,
  PayFastCreatePaymentRequest,
  PayFastCreatePaymentResponse,
  PayFastNotificationData,
  PayFastRecurringPaymentRequest,
  PayFastRefundRequest,
  PayFastRefundResponse,
  PayFastValidationError,
  PayFastNetworkError,
  PayFastSignatureError
} from '../types';
import { PayFastUtils } from '../utils';

/**
 * PayFast Service Class
 * Handles all PayFast payment operations
 */
export class PayFastService {
  private config: PayFastConfig;

  constructor(config: PayFastConfig) {
    // Validate configuration
    const validation = PayFastUtils.validateConfig(config);
    if (!validation.isValid) {
      throw new PayFastValidationError(
        `Invalid PayFast configuration: ${validation.errors.join(', ')}`
      );
    }

    this.config = config;
  }

  /**
   * Create a standard payment
   */
  async createPayment(request: PayFastCreatePaymentRequest): Promise<PayFastCreatePaymentResponse> {
    try {
      // Validate request data
      this.validatePaymentRequest(request);

      // Parse customer name
      const { firstName, lastName } = PayFastUtils.parseCustomerName(request.customerName);

      // Create payment data
      const paymentData: Omit<PayFastPaymentData, 'signature'> = {
        merchant_id: this.config.merchantId,
        merchant_key: this.config.merchantKey,
        return_url: this.config.returnUrl,
        cancel_url: this.config.cancelUrl,
        notify_url: this.config.notifyUrl,
        name_first: firstName,
        name_last: lastName,
        email_address: request.customerEmail,
        m_payment_id: request.orderId,
        amount: PayFastUtils.formatAmount(request.amount),
        item_name: PayFastUtils.sanitizeString(request.description, 100),
        item_description: PayFastUtils.sanitizeString(request.description, 255),
        custom_int1: request.customData?.int1,
        custom_str1: request.customData?.str1 || 'StokvelMarket'
      };

      // Generate signature
      const signature = PayFastUtils.generateSignature(paymentData, this.config.passphrase);
      const finalPaymentData: PayFastPaymentData = {
        ...paymentData,
        signature
      };

      return {
        success: true,
        paymentData: finalPaymentData,
        paymentUrl: PayFastUtils.getPaymentUrl(this.config.sandbox)
      };

    } catch (error) {
      console.error('PayFast payment creation error:', error);
      
      if (error instanceof PayFastValidationError) {
        return {
          success: false,
          error: error.message
        };
      }

      return {
        success: false,
        error: 'Failed to create payment'
      };
    }
  }

  /**
   * Create a recurring payment
   */
  async createRecurringPayment(request: PayFastRecurringPaymentRequest): Promise<PayFastCreatePaymentResponse> {
    try {
      // Validate recurring data
      const recurringValidation = PayFastUtils.validateRecurringData({
        frequency: request.frequency,
        cycles: request.cycles,
        billingDate: request.billingDate
      });

      if (!recurringValidation.isValid) {
        throw new PayFastValidationError(
          `Invalid recurring data: ${recurringValidation.errors.join(', ')}`
        );
      }

      // Create base payment
      const basePayment = await this.createPayment(request);
      
      if (!basePayment.success || !basePayment.paymentData) {
        return basePayment;
      }

      // Add recurring fields
      const recurringData = {
        ...basePayment.paymentData,
        subscription_type: 1,
        billing_date: request.billingDate.toISOString().split('T')[0],
        recurring_amount: PayFastUtils.formatAmount(request.amount),
        frequency: request.frequency,
        cycles: request.cycles
      };

      // Regenerate signature with recurring data
      const signature = PayFastUtils.generateSignature(recurringData, this.config.passphrase);
      recurringData.signature = signature;

      return {
        success: true,
        paymentData: recurringData as PayFastPaymentData,
        paymentUrl: PayFastUtils.getPaymentUrl(this.config.sandbox)
      };

    } catch (error) {
      console.error('PayFast recurring payment creation error:', error);
      return {
        success: false,
        error: error instanceof PayFastValidationError ? error.message : 'Failed to create recurring payment'
      };
    }
  }

  /**
   * Verify payment notification from PayFast
   */
  verifyNotification(notificationData: PayFastNotificationData): boolean {
    try {
      const signature = notificationData.signature;
      const dataWithoutSignature = { ...notificationData };
      delete dataWithoutSignature.signature;

      return PayFastUtils.verifySignature(
        dataWithoutSignature,
        signature,
        this.config.passphrase
      );
    } catch (error) {
      console.error('PayFast notification verification error:', error);
      return false;
    }
  }

  /**
   * Process refund (Note: PayFast doesn't have direct API for refunds)
   */
  async processRefund(request: PayFastRefundRequest): Promise<PayFastRefundResponse> {
    try {
      // PayFast doesn't have a direct refund API
      // This would typically require manual processing through their dashboard
      // or integration with their merchant API if available
      
      console.warn('PayFast refunds must be processed manually through the merchant dashboard');
      
      return {
        success: false,
        error: 'PayFast refunds must be processed manually through the merchant dashboard'
      };

    } catch (error) {
      console.error('PayFast refund processing error:', error);
      return {
        success: false,
        error: 'Failed to process refund'
      };
    }
  }

  /**
   * Cancel recurring subscription
   */
  async cancelSubscription(subscriptionToken: string): Promise<{ success: boolean; error?: string }> {
    try {
      // PayFast subscription cancellation would require API integration
      // This is a placeholder for the actual implementation
      
      const apiUrl = PayFastUtils.getApiUrl(this.config.sandbox);
      const response = await fetch(`${apiUrl}/subscriptions/${subscriptionToken}/cancel`, {
        method: 'PUT',
        headers: {
          'merchant-id': this.config.merchantId,
          'version': 'v1',
          'timestamp': new Date().toISOString(),
          // Add signature header as required by PayFast API
        }
      });

      if (!response.ok) {
        throw new PayFastNetworkError(
          'Failed to cancel subscription',
          response.status
        );
      }

      return { success: true };

    } catch (error) {
      console.error('PayFast subscription cancellation error:', error);
      return {
        success: false,
        error: error instanceof PayFastNetworkError ? error.message : 'Failed to cancel subscription'
      };
    }
  }

  /**
   * Get payment status (if PayFast provides this API)
   */
  async getPaymentStatus(paymentId: string): Promise<{
    success: boolean;
    status?: string;
    error?: string;
  }> {
    try {
      // This would require PayFast API integration
      // Currently PayFast primarily uses webhooks for status updates
      
      console.warn('PayFast payment status checking not available via API');
      
      return {
        success: false,
        error: 'Payment status checking not available via PayFast API'
      };

    } catch (error) {
      console.error('PayFast payment status error:', error);
      return {
        success: false,
        error: 'Failed to get payment status'
      };
    }
  }

  /**
   * Validate payment request data
   */
  private validatePaymentRequest(request: PayFastCreatePaymentRequest): void {
    const errors: string[] = [];

    // Validate amount
    const amountValidation = PayFastUtils.validateAmount(request.amount);
    if (!amountValidation.isValid) {
      errors.push(...amountValidation.errors);
    }

    // Validate email
    const emailValidation = PayFastUtils.validateEmail(request.customerEmail);
    if (!emailValidation.isValid) {
      errors.push(...emailValidation.errors);
    }

    // Validate order ID
    const orderIdValidation = PayFastUtils.validateOrderId(request.orderId);
    if (!orderIdValidation.isValid) {
      errors.push(...orderIdValidation.errors);
    }

    // Validate customer name
    const nameValidation = PayFastUtils.validateCustomerName(request.customerName);
    if (!nameValidation.isValid) {
      errors.push(...nameValidation.errors);
    }

    // Validate description
    if (!request.description || request.description.trim().length < 1) {
      errors.push('Description is required');
    }

    if (errors.length > 0) {
      throw new PayFastValidationError(`Validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Get configuration (for debugging)
   */
  getConfig(): Omit<PayFastConfig, 'merchantKey' | 'passphrase'> {
    return {
      merchantId: this.config.merchantId,
      merchantKey: '***HIDDEN***',
      sandbox: this.config.sandbox,
      returnUrl: this.config.returnUrl,
      cancelUrl: this.config.cancelUrl,
      notifyUrl: this.config.notifyUrl
    };
  }

  /**
   * Test configuration
   */
  async testConfiguration(): Promise<{ success: boolean; error?: string }> {
    try {
      // Create a test payment to validate configuration
      const testPayment = await this.createPayment({
        orderId: 'TEST-' + Date.now(),
        amount: 1.00,
        description: 'Configuration Test',
        customerEmail: '<EMAIL>',
        customerName: 'Test User'
      });

      return {
        success: testPayment.success,
        error: testPayment.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Configuration test failed'
      };
    }
  }
}
