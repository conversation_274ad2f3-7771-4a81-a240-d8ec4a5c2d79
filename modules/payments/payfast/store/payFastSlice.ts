import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  PayFastState,
  PayFastCreatePaymentRequest,
  PayFastCreatePaymentResponse,
  PayFastPaymentStatus,
  PayFastConfig,
  PayFastRecurringPaymentRequest
} from '../types';

// Initial state
const initialState: PayFastState = {
  isLoading: false,
  currentPayment: null,
  paymentHistory: [],
  error: null,
  config: null
};

// Async thunks
export const createPayFastPayment = createAsyncThunk(
  'payfast/createPayment',
  async (request: PayFastCreatePaymentRequest, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/payment/payfast/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Payment creation failed');
      }

      return data as PayFastCreatePaymentResponse;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const createRecurringPayFastPayment = createAsyncThunk(
  'payfast/createRecurringPayment',
  async (request: PayFastRecurringPaymentRequest, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/payment/payfast/recurring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Recurring payment creation failed');
      }

      return data as PayFastCreatePaymentResponse;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchPayFastPaymentHistory = createAsyncThunk(
  'payfast/fetchPaymentHistory',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/payfast/history?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to fetch payment history');
      }

      return data.payments as PayFastPaymentStatus[];
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const checkPayFastPaymentStatus = createAsyncThunk(
  'payfast/checkPaymentStatus',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/payfast/status/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to check payment status');
      }

      return data.status as PayFastPaymentStatus;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const cancelPayFastSubscription = createAsyncThunk(
  'payfast/cancelSubscription',
  async (subscriptionId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/payfast/subscription/${subscriptionId}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to cancel subscription');
      }

      return { subscriptionId, success: true };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

// Slice
const payFastSlice = createSlice({
  name: 'payfast',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCurrentPayment: (state, action: PayloadAction<PayFastCreatePaymentResponse | null>) => {
      state.currentPayment = action.payload;
    },
    addPaymentToHistory: (state, action: PayloadAction<PayFastPaymentStatus>) => {
      const existingIndex = state.paymentHistory.findIndex(
        payment => payment.orderId === action.payload.orderId
      );
      
      if (existingIndex >= 0) {
        state.paymentHistory[existingIndex] = action.payload;
      } else {
        state.paymentHistory.unshift(action.payload);
      }
    },
    updatePaymentStatus: (state, action: PayloadAction<{ orderId: string; status: PayFastPaymentStatus }>) => {
      const payment = state.paymentHistory.find(p => p.orderId === action.payload.orderId);
      if (payment) {
        Object.assign(payment, action.payload.status);
      }
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setConfig: (state, action: PayloadAction<PayFastConfig>) => {
      state.config = action.payload;
    },
    clearPaymentData: (state) => {
      state.currentPayment = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    removePaymentFromHistory: (state, action: PayloadAction<string>) => {
      state.paymentHistory = state.paymentHistory.filter(
        payment => payment.orderId !== action.payload
      );
    }
  },
  extraReducers: (builder) => {
    // Create Payment
    builder
      .addCase(createPayFastPayment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPayFastPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPayment = action.payload;
        state.error = null;
      })
      .addCase(createPayFastPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.currentPayment = null;
      });

    // Create Recurring Payment
    builder
      .addCase(createRecurringPayFastPayment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createRecurringPayFastPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPayment = action.payload;
        state.error = null;
      })
      .addCase(createRecurringPayFastPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.currentPayment = null;
      });

    // Fetch Payment History
    builder
      .addCase(fetchPayFastPaymentHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPayFastPaymentHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.paymentHistory = action.payload;
        state.error = null;
      })
      .addCase(fetchPayFastPaymentHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Check Payment Status
    builder
      .addCase(checkPayFastPaymentStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkPayFastPaymentStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        
        // Update payment in history
        const existingIndex = state.paymentHistory.findIndex(
          payment => payment.orderId === action.payload.orderId
        );
        
        if (existingIndex >= 0) {
          state.paymentHistory[existingIndex] = action.payload;
        } else {
          state.paymentHistory.unshift(action.payload);
        }
        
        state.error = null;
      })
      .addCase(checkPayFastPaymentStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Cancel Subscription
    builder
      .addCase(cancelPayFastSubscription.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(cancelPayFastSubscription.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update subscription status in history if needed
        state.error = null;
      })
      .addCase(cancelPayFastSubscription.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }
});

// Actions
export const {
  setLoading,
  setCurrentPayment,
  addPaymentToHistory,
  updatePaymentStatus,
  setError,
  setConfig,
  clearPaymentData,
  clearError,
  removePaymentFromHistory
} = payFastSlice.actions;

// Selectors
export const selectPayFastState = (state: { payfast: PayFastState }) => state.payfast;
export const selectPayFastLoading = (state: { payfast: PayFastState }) => state.payfast.isLoading;
export const selectPayFastCurrentPayment = (state: { payfast: PayFastState }) => state.payfast.currentPayment;
export const selectPayFastPaymentHistory = (state: { payfast: PayFastState }) => state.payfast.paymentHistory;
export const selectPayFastError = (state: { payfast: PayFastState }) => state.payfast.error;
export const selectPayFastConfig = (state: { payfast: PayFastState }) => state.payfast.config;

// Reducer
export default payFastSlice.reducer;
