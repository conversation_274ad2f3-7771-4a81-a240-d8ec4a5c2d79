// PayFast Payment Module - Client-side exports only
'use client';

// Components (Client-side only)
export { PayFastCheckout } from './components/PayFastCheckout';

// Hooks (Client-side only)
export { usePayFast } from './hooks/usePayFast';

// Store (Client-side only)
export {
  default as payFastReducer,
  createPayFastPayment,
  createRecurringPayFastPayment,
  fetchPayFastPaymentHistory,
  checkPayFastPaymentStatus,
  cancelPayFastSubscription,
  setLoading,
  setCurrentPayment,
  addPaymentToHistory,
  updatePaymentStatus,
  setError,
  setConfig,
  clearPaymentData,
  clearError,
  removePaymentFromHistory,
  selectPayFastState,
  selectPayFastLoading,
  selectPayFastCurrentPayment,
  selectPayFastPaymentHistory,
  selectPayFastError,
  selectPayFastConfig
} from './store/payFastSlice';

// Types (Safe for both client and server)
export * from './types';
