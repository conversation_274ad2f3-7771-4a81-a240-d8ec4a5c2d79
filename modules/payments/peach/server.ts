// Peach Payments Module - Server-side exports only
// This file should be used in API routes and server components

// Types
export * from './types';

// Services (Server-side only)
export { PeachService } from './services/PeachService';

// Utils (Server-side safe)
export { PeachUtils } from './utils';

// Module Configuration (Server-side safe)
export const PeachModule = {
  name: 'Peach Payments',
  version: '1.0.0',
  description: 'Complete Peach Payments integration module for StokvelMarket',
  provider: 'Peach Payments',
  countries: ['South Africa', 'Kenya', 'Nigeria', 'Ghana'],
  currencies: ['ZAR', 'KES', 'NGN', 'GHS', 'USD', 'EUR'],
  features: {
    standardPayments: true,
    recurringPayments: true,
    refunds: true,
    webhooks: true,
    subscriptions: true,
    multiCurrency: true,
    mobileOptimized: true,
    groupPayments: true,
    splitPayments: true,
    tokenization: true,
    threeDSecure: true,
    fraudDetection: true,
    analytics: true
  },
  paymentMethods: [
    'Credit Cards (Visa, Mastercard, Amex)',
    'Debit Cards',
    'Apple Pay',
    'Google Pay',
    'Samsung Pay',
    'Payflex BNPL',
    'ZeroPay BNPL',
    'Float BNPL',
    'Capitec Pay',
    'FNB Pay',
    'Nedbank Pay',
    'Standard Bank Pay',
    'ABSA Pay',
    'Discovery Bank Pay',
    'TymeBank Pay',
    'Bank Zero Pay',
    'African Bank Pay',
    'Bidvest Bank Pay',
    'Investec Pay',
    'PostBank Pay',
    'Sasfin Pay',
    'Ubank Pay'
  ],
  fees: {
    creditCard: '2.4% + R1.50',
    debitCard: '1.9% + R1.50',
    eft: '1.2% (min R1.50, max R8.00)',
    mobileWallet: '2.4% + R1.50',
    bnpl: '3.5% + R2.00'
  },
  limits: {
    minAmount: 0.01,
    maxAmount: ********,
    currencies: ['ZAR', 'USD', 'EUR']
  },
  urls: {
    sandbox: 'https://testapi-v2.peachpayments.com',
    production: 'https://api-v2.peachpayments.com',
    widget: {
      sandbox: 'https://testapi-v2.peachpayments.com/v1/paymentWidgets.js',
      production: 'https://api-v2.peachpayments.com/v1/paymentWidgets.js'
    }
  },
  documentation: {
    integration: '/PAYMENT_INTEGRATION_GUIDE.md',
    api: 'https://developer.peachpayments.com/docs',
    support: 'https://peachpayments.com/support'
  }
};

// Module Utilities (Server-side safe)
export const PeachModuleUtils = {
  /**
   * Get default Peach configuration
   */
  getDefaultConfig: (overrides: any = {}) => ({
    sandbox: true,
    baseUrl: overrides.sandbox !== false ? PeachModule.urls.sandbox : PeachModule.urls.production,
    ...overrides
  }),

  /**
   * Validate module configuration
   */
  validateConfig: (config: any) => {
    return PeachUtils.validateConfig(config);
  },

  /**
   * Get module information
   */
  getModuleInfo: () => PeachModule,

  /**
   * Check if module supports feature
   */
  supportsFeature: (feature: keyof typeof PeachModule.features) => {
    return PeachModule.features[feature];
  },

  /**
   * Get supported payment methods
   */
  getPaymentMethods: () => PeachModule.paymentMethods,

  /**
   * Get fee structure
   */
  getFees: () => PeachModule.fees,

  /**
   * Get payment limits
   */
  getLimits: () => PeachModule.limits,

  /**
   * Generate module-specific order ID
   */
  generateOrderId: (prefix?: string) => {
    return PeachUtils.generateOrderId(prefix || 'PEACH');
  },

  /**
   * Get available payment brands for country
   */
  getPaymentBrandsForCountry: (country: string) => {
    return PeachUtils.getPaymentBrandsForCountry(country);
  },

  /**
   * Calculate processing fee
   */
  calculateFee: (amount: number, paymentMethod: string) => {
    return PeachUtils.calculateProcessingFee(amount, paymentMethod);
  }
};

// Default export for server-side usage
export default {
  ...PeachModule,
  utils: PeachModuleUtils
};
