// Peach Payments Module - Client-side exports only
'use client';

// Components (Client-side only)
export { PeachCheckout } from './components/PeachCheckout';

// Hooks (Client-side only)
export { usePeach } from './hooks/usePeach';

// Store (Client-side only)
export {
  default as peachReducer,
  createPeachPayment,
  createPeachSplitPayment,
  fetchPeachPaymentHistory,
  checkPeachPaymentStatus,
  updatePeachPaymentStatus,
  cancelPeachPayment,
  setLoading,
  setCurrentPayment,
  addPaymentToHistory,
  updatePaymentStatus,
  setError,
  setConfig,
  clearPaymentData,
  clearError,
  removePaymentFromHistory,
  selectPeachState,
  selectPeachLoading,
  selectPeachCurrentPayment,
  selectPeachPaymentHistory,
  selectPeachError,
  selectPeachConfig
} from './store/peachSlice';

// Types (Safe for both client and server)
export * from './types';
