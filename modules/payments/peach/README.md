# 🍑 Peach Payments Module

A complete, modular Peach Payments integration for StokvelMarket with TypeScript support, Redux state management, and React components.

## 📋 **Overview**

This module provides a comprehensive Peach Payments integration including:
- ✅ **Complete Type Safety** - Full TypeScript definitions for 20+ payment methods
- ✅ **Redux Integration** - State management with RTK
- ✅ **React Components** - Pre-built UI components with modern design
- ✅ **Utility Functions** - Validation, payment method configs, etc.
- ✅ **Service Layer** - Clean API abstraction with REST integration
- ✅ **Hooks** - Custom React hooks for easy integration
- ✅ **Split Payments** - Native group payment support for Stokvels
- ✅ **Multi-currency** - Support for ZAR, USD, EUR, GBP, KES, MUR

## 🚀 **Quick Start**

### **1. Installation**

```bash
# The module is self-contained within the project
# No additional dependencies required beyond existing project deps
```

### **2. Environment Setup**

```bash
# .env.local
PEACH_ENTITY_ID=8a8294174b7ecb28014b9699220015ca
PEACH_USERNAME=8a8294174b7ecb28014b9699220015ca
PEACH_PASSWORD=sy6KJsT8
PEACH_BASE_URL=https://testapi-v2.peachpayments.com
PEACH_SUCCESS_URL=http://localhost:3001/payment/success
PEACH_CANCEL_URL=http://localhost:3001/payment/cancel
PEACH_WEBHOOK_URL=http://localhost:3001/api/webhooks/peach
```

### **3. Redux Store Setup**

```typescript
// lib/redux/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { peachReducer } from '@/modules/payments/peach';

export const store = configureStore({
  reducer: {
    // ... other reducers
    peach: peachReducer,
  },
});
```

### **4. Basic Usage**

```typescript
import { PeachCheckout, usePeach } from '@/modules/payments/peach';

function CheckoutPage() {
  return (
    <PeachCheckout
      orderId="ORD-123"
      amount={100.00}
      currency="ZAR"
      description="Test Purchase"
      customerEmail="<EMAIL>"
      customerName="John Doe"
      paymentBrand="CARD"
      onSuccess={(result) => console.log('Payment successful:', result)}
      onError={(error) => console.error('Payment failed:', error)}
    />
  );
}
```

## 📁 **Module Structure**

```
modules/payments/peach/
├── types/
│   └── index.ts              # TypeScript definitions (20+ payment brands)
├── utils/
│   └── index.ts              # Utility functions & payment method configs
├── services/
│   └── PeachService.ts       # Core service class with REST API
├── store/
│   └── peachSlice.ts         # Redux slice with async thunks
├── hooks/
│   └── usePeach.ts           # React hooks
├── components/
│   ├── PeachCheckout.tsx     # Simple checkout component
│   ├── PeachPaymentMethods.tsx # Payment method selector
│   ├── PeachStatus.tsx       # Payment status tracker
│   └── index.ts              # Component exports
├── index.ts                  # Main module export
└── README.md                 # This file
```

## 🔧 **Components**

### **PeachCheckout**
Modern checkout component with 20+ payment methods.

```typescript
<PeachCheckout
  orderId="ORD-123"
  amount={100.00}
  currency="ZAR"
  description="Product Purchase"
  customerEmail="<EMAIL>"
  customerName="John Doe"
  paymentBrand="PAYFLEX"
  onSuccess={(result) => handleSuccess(result)}
  onError={(error) => handleError(error)}
/>
```

### **PeachPaymentMethods**
Interactive payment method selector with categories and search.

```typescript
<PeachPaymentMethods
  selectedMethod={selectedMethod}
  onMethodSelect={setSelectedMethod}
  amount={100.00}
  currency="ZAR"
  availableMethods={['CARD', 'PAYFLEX', 'CAPITECPAY']}
/>
```

### **PeachStatus**
Real-time payment status monitoring with polling.

```typescript
<PeachStatus
  checkoutId="checkout-123"
  orderId="ORD-123"
  onStatusUpdate={(status) => handleStatusUpdate(status)}
  pollInterval={3000}
  maxAttempts={20}
/>
```

## 🎣 **Hooks**

### **usePeach**
Main hook for Peach Payments operations.

```typescript
const {
  // State
  isLoading,
  currentPayment,
  paymentHistory,
  error,

  // Actions
  createPayment,
  createRecurringPayment,
  createSplitPayment,
  fetchPaymentHistory,
  checkPaymentStatus,
  processRefund,

  // Utilities
  getPaymentByOrderId,
  getSuccessfulPayments,
  getTotalAmountPaid,
  getPaymentStatistics
} = usePeach();
```

### **usePeachStatusPolling**
Automatic payment status polling.

```typescript
usePeachStatusPolling({ checkoutId: 'checkout-123' }, {
  enabled: true,
  interval: 3000,
  maxAttempts: 20,
  onStatusUpdate: (status) => console.log('Status:', status),
  onComplete: (status) => console.log('Final status:', status),
  onError: (error) => console.error('Polling error:', error)
});
```

### **usePeachPaymentMethods**
Payment method management.

```typescript
const { getAvailablePaymentMethods } = usePeachPaymentMethods();

const methods = getAvailablePaymentMethods('ZAR', 'ZA');
// Returns: ['CARD', 'CAPITECPAY', 'PAYFLEX', 'MASTERPASS', ...]
```

## 🛠 **Service Layer**

### **PeachService**
Core service for Peach Payments operations.

```typescript
import { PeachService } from '@/modules/payments/peach';

const peach = new PeachService({
  entityId: 'your_entity_id',
  username: 'your_username',
  password: 'your_password',
  sandbox: true,
  baseUrl: 'https://testapi-v2.peachpayments.com',
  successUrl: 'https://yoursite.com/success',
  cancelUrl: 'https://yoursite.com/cancel'
});

// Create payment
const payment = await peach.createPayment({
  orderId: 'ORD-123',
  amount: 100.00,
  currency: 'ZAR',
  description: 'Test Purchase',
  customerEmail: '<EMAIL>',
  customerName: 'John Doe',
  paymentBrand: 'PAYFLEX'
});

// Create split payment for groups
const splitPayment = await peach.createSplitPayment({
  groupOrderId: 'GROUP-123',
  totalAmount: 1000.00,
  currency: 'ZAR',
  splits: [
    { userId: 'user1', amount: 400.00, email: '<EMAIL>', name: 'User 1' },
    { userId: 'user2', amount: 600.00, email: '<EMAIL>', name: 'User 2' }
  ],
  description: 'Group Purchase'
});
```

## 🔧 **Utilities**

### **PeachUtils**
Comprehensive utility functions.

```typescript
import { PeachUtils } from '@/modules/payments/peach';

// Get payment method configurations
const methods = PeachUtils.getPaymentMethodConfigs();

// Filter by criteria
const cardMethods = PeachUtils.filterPaymentMethods({
  category: 'card',
  currency: 'ZAR',
  enabled: true
});

// Validate payment data
const validation = PeachUtils.validateAmount(100.00, 'ZAR');

// Check payment status
const isSuccess = PeachUtils.isPaymentSuccessful('000.000.000');

// Generate order ID
const orderId = PeachUtils.generateOrderId('PEACH'); // "PEACH-**********-ABC123"
```

## 💳 **Payment Methods**

### **Supported Payment Brands**

#### **Cards**
- `CARD` - Credit/Debit Cards (Visa, Mastercard, Amex, Diners)
- `VISA` - Visa cards specifically
- `MASTER` - Mastercard specifically
- `AMEX` - American Express
- `DINERS` - Diners Club

#### **EFT & Banking**
- `CAPITECPAY` - Capitec Pay
- `PAYBYBANK` - Pay by Bank
- `PEACHEFT` - Peach EFT

#### **Buy Now Pay Later**
- `PAYFLEX` - Payflex (4 installments)
- `ZEROPAY` - ZeroPay
- `FLOAT` - Float
- `HAPPYPAY` - Happy Pay

#### **Digital Wallets**
- `APPLEPAY` - Apple Pay
- `GOOGLEPAY` - Google Pay
- `SAMSUNGPAY` - Samsung Pay
- `PAYPAL` - PayPal

#### **QR & Mobile**
- `MASTERPASS` - Scan to Pay
- `MPESA` - M-PESA (Kenya)

#### **Alternative Credit**
- `MOBICRED` - Mobicred
- `RCS` - RCS Cards
- `APLUS` - A+ Store Cards

#### **Vouchers & Crypto**
- `1FORYOU` - 1Voucher
- `MONEYBADGER` - MoneyBadger (Crypto)

## 📊 **State Management**

### **Redux Actions**

```typescript
import { 
  createPeachPayment,
  createPeachSplitPayment,
  fetchPeachPaymentHistory,
  checkPeachPaymentStatus 
} from '@/modules/payments/peach';

// Create payment
dispatch(createPeachPayment({
  orderId: 'ORD-123',
  amount: 100.00,
  currency: 'ZAR',
  description: 'Test',
  customerEmail: '<EMAIL>',
  customerName: 'John Doe',
  paymentBrand: 'PAYFLEX'
}));

// Create split payment
dispatch(createPeachSplitPayment({
  groupOrderId: 'GROUP-123',
  totalAmount: 1000.00,
  currency: 'ZAR',
  splits: [/* split data */],
  description: 'Group Purchase'
}));
```

### **Selectors**

```typescript
import { 
  selectPeachLoading,
  selectPeachCurrentPayment,
  selectPeachPaymentHistory,
  selectPeachError 
} from '@/modules/payments/peach';

const isLoading = useSelector(selectPeachLoading);
const currentPayment = useSelector(selectPeachCurrentPayment);
const history = useSelector(selectPeachPaymentHistory);
const error = useSelector(selectPeachError);
```

## 🔒 **Security Features**

- ✅ **PCI DSS Compliance** - Bank-level security
- ✅ **3D Secure** - Enhanced card authentication
- ✅ **Webhook Verification** - HMAC signature validation
- ✅ **Input Validation** - Comprehensive validation
- ✅ **Type Safety** - Full TypeScript coverage
- ✅ **Fraud Protection** - Built-in risk management

## 🌍 **Multi-currency Support**

```typescript
// Supported currencies
const currencies = ['ZAR', 'USD', 'EUR', 'GBP', 'KES', 'MUR'];

// Currency-specific payment methods
const zarMethods = PeachUtils.filterPaymentMethods({ currency: 'ZAR' });
const usdMethods = PeachUtils.filterPaymentMethods({ currency: 'USD' });
```

## 🎨 **Styling**

Components use Tailwind CSS and shadcn/ui:
- Modern gradient designs
- Responsive layouts
- Dark mode support
- Smooth animations with Framer Motion
- Accessible components

## 🧪 **Testing**

```typescript
// Example test
import { PeachUtils } from '@/modules/payments/peach';

describe('PeachUtils', () => {
  test('validates amount correctly', () => {
    const result = PeachUtils.validateAmount(100.00, 'ZAR');
    expect(result.isValid).toBe(true);
  });

  test('filters payment methods by currency', () => {
    const methods = PeachUtils.filterPaymentMethods({ currency: 'ZAR' });
    expect(methods.length).toBeGreaterThan(0);
  });
});
```

## 🔄 **Integration Examples**

### **Group Buying (Stokvel)**
```typescript
function GroupCheckout({ groupOrder }) {
  const { createSplitPayment } = usePeach();
  
  const handleGroupPayment = async () => {
    const result = await createSplitPayment({
      groupOrderId: groupOrder.id,
      totalAmount: groupOrder.total,
      currency: 'ZAR',
      splits: groupOrder.members.map(member => ({
        userId: member.id,
        amount: member.contribution,
        email: member.email,
        name: member.name
      })),
      description: `Group Order - ${groupOrder.description}`
    });
    
    // Handle individual payment URLs for each member
    result.individualPayments?.forEach(payment => {
      // Send payment link to each member
      sendPaymentLink(payment.userId, payment.paymentUrl);
    });
  };
}
```

### **Multi-currency Checkout**
```typescript
function InternationalCheckout() {
  const [currency, setCurrency] = useState('ZAR');
  const { getAvailablePaymentMethods } = usePeachPaymentMethods();
  
  const availableMethods = getAvailablePaymentMethods(currency, 'ZA');
  
  return (
    <div>
      <CurrencySelector value={currency} onChange={setCurrency} />
      <PeachPaymentMethods
        amount={amount}
        currency={currency}
        availableMethods={availableMethods}
        onMethodSelect={setSelectedMethod}
      />
    </div>
  );
}
```

## 📞 **Support**

- **Documentation**: See `/PEACH_PAYMENTS_INTEGRATION_GUIDE.md`
- **Peach Docs**: https://developer.peachpayments.com/docs/payments-api-overview
- **Peach Support**: https://peachpayments.com/support

## 🔄 **Version History**

- **v1.0.0** - Initial release with full Peach Payments integration
- Complete TypeScript support for 20+ payment methods
- Redux state management with async thunks
- React components with modern design
- Split payment support for group orders
- Multi-currency support
- Comprehensive utilities and validation

---

This module provides everything needed for Peach Payments integration in StokvelMarket while maintaining modularity and reusability across different projects.
