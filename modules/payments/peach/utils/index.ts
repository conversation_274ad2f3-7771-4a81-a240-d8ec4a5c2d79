import crypto from 'crypto';
import {
  PeachValidationR<PERSON>ult,
  PeachValidationError,
  PeachAuthenticationError,
  PeachConfig,
  PeachPaymentBrand,
  PeachPaymentMethodConfig,
  PeachResultCodes
} from '../types';

/**
 * Peach Payments Utility Functions
 */
export class PeachUtils {
  /**
   * Generate Basic Auth token for Peach Payments API
   */
  static generateAuthToken(username: string, password: string): string {
    try {
      return Buffer.from(`${username}:${password}`).toString('base64');
    } catch (error) {
      throw new PeachAuthenticationError('Failed to generate auth token');
    }
  }

  /**
   * Validate payment amount
   */
  static validateAmount(amount: number, currency: string = 'ZAR'): PeachValidationResult {
    const errors: string[] = [];

    if (typeof amount !== 'number' || isNaN(amount)) {
      errors.push('Amount must be a valid number');
    } else if (amount <= 0) {
      errors.push('Amount must be greater than 0');
    } else if (amount > 10000000) {
      errors.push('Amount cannot exceed R10,000,000');
    } else if (Number(amount.toFixed(2)) !== amount) {
      errors.push('Amount cannot have more than 2 decimal places');
    }

    // Currency-specific validations
    if (currency === 'ZAR' && amount < 0.01) {
      errors.push('Minimum amount for ZAR is R0.01');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate email address
   */
  static validateEmail(email: string): PeachValidationResult {
    const errors: string[] = [];
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email || typeof email !== 'string') {
      errors.push('Email is required');
    } else if (!emailRegex.test(email)) {
      errors.push('Invalid email format');
    } else if (email.length > 254) {
      errors.push('Email cannot exceed 254 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate order ID
   */
  static validateOrderId(orderId: string): PeachValidationResult {
    const errors: string[] = [];

    if (!orderId || typeof orderId !== 'string') {
      errors.push('Order ID is required');
    } else if (orderId.length < 1 || orderId.length > 255) {
      errors.push('Order ID must be between 1 and 255 characters');
    } else if (!/^[a-zA-Z0-9_-]+$/.test(orderId)) {
      errors.push('Order ID can only contain letters, numbers, hyphens, and underscores');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate customer name
   */
  static validateCustomerName(name: string): PeachValidationResult {
    const errors: string[] = [];

    if (!name || typeof name !== 'string') {
      errors.push('Customer name is required');
    } else if (name.trim().length < 2) {
      errors.push('Customer name must be at least 2 characters');
    } else if (name.length > 255) {
      errors.push('Customer name cannot exceed 255 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate Peach Payments configuration
   */
  static validateConfig(config: PeachConfig): PeachValidationResult {
    const errors: string[] = [];

    if (!config.entityId) {
      errors.push('Entity ID is required');
    }

    if (!config.username) {
      errors.push('Username is required');
    }

    if (!config.password) {
      errors.push('Password is required');
    }

    if (!config.baseUrl) {
      errors.push('Base URL is required');
    } else if (!this.isValidUrl(config.baseUrl)) {
      errors.push('Base URL must be a valid URL');
    }

    if (!config.successUrl) {
      errors.push('Success URL is required');
    } else if (!this.isValidUrl(config.successUrl)) {
      errors.push('Success URL must be a valid URL');
    }

    if (!config.cancelUrl) {
      errors.push('Cancel URL is required');
    } else if (!this.isValidUrl(config.cancelUrl)) {
      errors.push('Cancel URL must be a valid URL');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if string is a valid URL
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Format amount for Peach Payments (2 decimal places)
   */
  static formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  /**
   * Parse customer name into first and last name
   */
  static parseCustomerName(fullName: string): { firstName: string; lastName: string } {
    const nameParts = fullName.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    return {
      firstName: firstName.substring(0, 255), // Peach limit
      lastName: lastName.substring(0, 255)   // Peach limit
    };
  }

  /**
   * Get Peach Payments API URL based on environment
   */
  static getApiUrl(sandbox: boolean): string {
    return sandbox 
      ? 'https://testapi-v2.peachpayments.com'
      : 'https://api-v2.peachpayments.com';
  }

  /**
   * Sanitize string for Peach Payments
   */
  static sanitizeString(input: string, maxLength: number = 255): string {
    return input
      .replace(/[^\w\s-_.]/g, '') // Remove special characters except allowed ones
      .trim()
      .substring(0, maxLength);
  }

  /**
   * Generate unique order ID with prefix
   */
  static generateOrderId(prefix: string = 'PEACH'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Check if payment status indicates success
   */
  static isPaymentSuccessful(statusCode: string): boolean {
    const successCodes = [
      '000.000.000', // Transaction succeeded
      '000.000.100', // Successfully created checkout
      '000.100.110', // Request successfully processed
      '000.100.111', // Request successfully processed
      '000.100.112', // Request successfully processed
      '000.300.000', // Two-step transaction succeeded
    ];

    return successCodes.includes(statusCode);
  }

  /**
   * Check if payment status indicates failure
   */
  static isPaymentFailed(statusCode: string): boolean {
    // Failure codes start with 100, 200, 300, 400, 500, 600, 700, 800, 900
    return /^[1-9]\d{2}\.\d{3}\.\d{3}$/.test(statusCode) && 
           !this.isPaymentSuccessful(statusCode) && 
           !this.isPaymentPending(statusCode);
  }

  /**
   * Check if payment status indicates pending
   */
  static isPaymentPending(statusCode: string): boolean {
    const pendingCodes = [
      '000.200.000', // Transaction pending
      '800.400.500', // Waiting for shopper to complete payment
      '800.400.501', // Waiting for shopper to complete payment
    ];

    return pendingCodes.includes(statusCode);
  }

  /**
   * Get payment method configurations
   */
  static getPaymentMethodConfigs(): PeachPaymentMethodConfig[] {
    return [
      {
        id: 'CARD',
        name: 'Credit/Debit Card',
        description: 'Visa, Mastercard, Amex, Diners',
        icon: '💳',
        category: 'card',
        currencies: ['ZAR', 'USD', 'EUR', 'GBP'],
        countries: ['ZA', 'KE', 'MU'],
        fees: '2.4% + R1.50',
        popular: true,
        enabled: true
      },
      {
        id: 'CAPITECPAY',
        name: 'Capitec Pay',
        description: 'Pay with your Capitec account',
        icon: '🏦',
        category: 'eft',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '0.95%',
        popular: true,
        enabled: true
      },
      {
        id: 'PAYFLEX',
        name: 'Payflex',
        description: 'Buy now, pay later in 4 installments',
        icon: '📱',
        category: 'bnpl',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: 'No extra fees',
        limits: { min: 10, max: 50000 },
        popular: true,
        enabled: true
      },
      {
        id: 'MASTERPASS',
        name: 'Scan to Pay',
        description: 'QR code payment',
        icon: '📱',
        category: 'qr',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '1.5%',
        enabled: true
      },
      {
        id: 'APPLEPAY',
        name: 'Apple Pay',
        description: 'Pay with Touch ID or Face ID',
        icon: '🍎',
        category: 'wallet',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '2.4% + R1.50',
        enabled: true
      },
      {
        id: 'GOOGLEPAY',
        name: 'Google Pay',
        description: 'Pay with your Google account',
        icon: '🔍',
        category: 'wallet',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '2.4% + R1.50',
        enabled: true
      },
      {
        id: 'MOBICRED',
        name: 'Mobicred',
        description: 'Credit facility',
        icon: '💰',
        category: 'alternative',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '2.5%',
        enabled: true
      },
      {
        id: 'ZEROPAY',
        name: 'ZeroPay',
        description: 'Buy now, pay later',
        icon: '💳',
        category: 'bnpl',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '2.0%',
        enabled: true
      },
      {
        id: 'FLOAT',
        name: 'Float',
        description: 'Flexible payment terms',
        icon: '🎈',
        category: 'bnpl',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '2.5%',
        enabled: true
      },
      {
        id: 'MONEYBADGER',
        name: 'MoneyBadger',
        description: 'Crypto payments',
        icon: '₿',
        category: 'crypto',
        currencies: ['ZAR'],
        countries: ['ZA'],
        fees: '1.0%',
        enabled: true
      }
    ];
  }

  /**
   * Get payment method by ID
   */
  static getPaymentMethodConfig(id: PeachPaymentBrand): PeachPaymentMethodConfig | undefined {
    return this.getPaymentMethodConfigs().find(method => method.id === id);
  }

  /**
   * Filter payment methods by criteria
   */
  static filterPaymentMethods(criteria: {
    currency?: string;
    country?: string;
    category?: string;
    enabled?: boolean;
  }): PeachPaymentMethodConfig[] {
    return this.getPaymentMethodConfigs().filter(method => {
      if (criteria.currency && !method.currencies.includes(criteria.currency)) {
        return false;
      }
      if (criteria.country && !method.countries.includes(criteria.country)) {
        return false;
      }
      if (criteria.category && method.category !== criteria.category) {
        return false;
      }
      if (criteria.enabled !== undefined && method.enabled !== criteria.enabled) {
        return false;
      }
      return true;
    });
  }

  /**
   * Validate recurring payment data
   */
  static validateRecurringData(data: {
    frequency: string;
    startDate: Date;
    endDate?: Date;
  }): PeachValidationResult {
    const errors: string[] = [];

    if (!['DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY'].includes(data.frequency)) {
      errors.push('Invalid frequency. Must be DAILY, WEEKLY, MONTHLY, or YEARLY');
    }

    if (data.startDate < new Date()) {
      errors.push('Start date cannot be in the past');
    }

    if (data.endDate && data.endDate <= data.startDate) {
      errors.push('End date must be after start date');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate webhook signature for verification
   */
  static generateWebhookSignature(payload: string, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
  }

  /**
   * Verify webhook signature
   */
  static verifyWebhookSignature(
    payload: string, 
    signature: string, 
    secret: string
  ): boolean {
    try {
      const expectedSignature = this.generateWebhookSignature(payload, secret);
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Get result codes for status checking
   */
  static getResultCodes(): PeachResultCodes {
    return {
      success: [
        '000.000.000', // Transaction succeeded
        '000.000.100', // Successfully created checkout
        '000.100.110', // Request successfully processed
        '000.100.111', // Request successfully processed
        '000.100.112', // Request successfully processed
        '000.300.000', // Two-step transaction succeeded
      ],
      pending: [
        '000.200.000', // Transaction pending
        '800.400.500', // Waiting for shopper to complete payment
        '800.400.501', // Waiting for shopper to complete payment
      ],
      failure: [
        // All other codes are considered failures
      ]
    };
  }

  /**
   * Convert frequency to human readable
   */
  static getFrequencyLabel(frequency: string): string {
    const frequencies: Record<string, string> = {
      'DAILY': 'Daily',
      'WEEKLY': 'Weekly',
      'MONTHLY': 'Monthly',
      'YEARLY': 'Yearly'
    };

    return frequencies[frequency] || 'Unknown';
  }

  /**
   * Debug payment data (development only)
   */
  static debugPaymentData(data: Record<string, any>): {
    originalData: Record<string, any>;
    sanitizedData: Record<string, any>;
    validationResults: Record<string, PeachValidationResult>;
  } {
    const validationResults: Record<string, PeachValidationResult> = {};

    if (data.amount) {
      validationResults.amount = this.validateAmount(data.amount, data.currency);
    }

    if (data.customerEmail) {
      validationResults.email = this.validateEmail(data.customerEmail);
    }

    if (data.orderId) {
      validationResults.orderId = this.validateOrderId(data.orderId);
    }

    if (data.customerName) {
      validationResults.customerName = this.validateCustomerName(data.customerName);
    }

    const sanitizedData = {
      ...data,
      amount: data.amount ? this.formatAmount(data.amount) : undefined,
      description: data.description ? this.sanitizeString(data.description) : undefined
    };

    return {
      originalData: data,
      sanitizedData,
      validationResults
    };
  }
}
