// Peach Payments Module Types
export interface PeachConfig {
  entityId: string;
  username: string;
  password: string;
  sandbox: boolean;
  baseUrl: string;
  webhookUrl?: string;
  successUrl: string;
  cancelUrl: string;
}

export interface PeachPaymentData {
  entityId: string;
  amount: string;
  currency: string;
  paymentType: 'DB' | 'PA' | 'RF' | 'RV' | 'CP' | 'RG';
  merchantTransactionId: string;
  'customer.email'?: string;
  'customer.givenName'?: string;
  'customer.surname'?: string;
  paymentBrand?: string;
  merchantInvoiceId?: string;
  'cart.items[0].name'?: string;
  'cart.items[0].merchantItemId'?: string;
  'cart.items[0].quantity'?: string;
  'cart.items[0].type'?: string;
  'cart.items[0].price'?: string;
  'cart.items[0].currency'?: string;
  shopperResultUrl?: string;
  'customParameters[SHOPPER_EndToEndIdentity]'?: string;
  recurringType?: 'INITIAL' | 'REPEATED';
  'job.frequency'?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
  'job.startDate'?: string;
  'job.endDate'?: string;
}

export interface PeachCreatePaymentRequest {
  orderId: string;
  amount: number;
  currency?: string;
  description: string;
  customerEmail: string;
  customerName: string;
  paymentBrand?: PeachPaymentBrand;
  customData?: Record<string, any>;
}

export interface PeachCreatePaymentResponse {
  success: boolean;
  checkoutId?: string;
  redirectUrl?: string;
  result?: {
    code: string;
    description: string;
  };
  error?: string;
}

export interface PeachRecurringPaymentRequest extends PeachCreatePaymentRequest {
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
  startDate: Date;
  endDate?: Date;
}

export interface PeachPaymentStatus {
  orderId: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  checkoutId?: string;
  transactionId?: string;
  amount?: number;
  currency?: string;
  paymentMethod: 'peach';
  paymentBrand?: string;
  paidAt?: Date;
  failureReason?: string;
  statusCode?: string;
  description?: string;
}

export interface PeachRefundRequest {
  originalPaymentId: string;
  amount: number;
  reason?: string;
}

export interface PeachRefundResponse {
  success: boolean;
  refundId?: string;
  status?: string;
  description?: string;
  error?: string;
}

export interface PeachWebhookData {
  id: string;
  paymentType: string;
  paymentBrand: string;
  amount: string;
  currency: string;
  descriptor: string;
  merchantTransactionId: string;
  result: {
    code: string;
    description: string;
  };
  resultDetails: {
    ConnectorTxID1: string;
    ConnectorTxID3: string;
  };
  card?: {
    bin: string;
    last4Digits: string;
    holder: string;
    expiryMonth: string;
    expiryYear: string;
  };
  customer?: {
    email: string;
    givenName: string;
    surname: string;
  };
  customParameters?: Record<string, string>;
  risk?: {
    score: string;
  };
  timestamp: string;
}

// Payment Brands supported by Peach Payments
export type PeachPaymentBrand = 
  | 'CARD'           // Credit/Debit Cards
  | 'VISA'           // Visa
  | 'MASTER'         // Mastercard
  | 'AMEX'           // American Express
  | 'DINERS'         // Diners Club
  | 'CAPITECPAY'     // Capitec Pay
  | 'PAYFLEX'        // Payflex
  | 'ZEROPAY'        // ZeroPay
  | 'MASTERPASS'     // Scan to Pay
  | 'MOBICRED'       // Mobicred
  | 'APPLEPAY'       // Apple Pay
  | 'GOOGLEPAY'      // Google Pay
  | 'SAMSUNGPAY'     // Samsung Pay
  | 'PAYPAL'         // PayPal
  | '1FORYOU'        // 1Voucher
  | 'RCS'            // RCS Cards
  | 'APLUS'          // A+ Store Cards
  | 'MONEYBADGER'    // MoneyBadger (Crypto)
  | 'FLOAT'          // Float
  | 'HAPPYPAY'       // Happy Pay
  | 'PAYBYBANK'      // Pay by Bank
  | 'PEACHEFT'       // Peach EFT
  | 'MPESA'          // M-PESA (Kenya)
  | 'BLINKBYEMTEL'   // blink by Emtel (Mauritius)
  | 'MCBJUICE'       // MCB Juice (Mauritius)
  | 'MAUCAS';        // MauCAS (Mauritius)

// Store types for Redux
export interface PeachState {
  isLoading: boolean;
  currentPayment: PeachCreatePaymentResponse | null;
  paymentHistory: PeachPaymentStatus[];
  error: string | null;
  config: PeachConfig | null;
}

export interface PeachActions {
  setLoading: (loading: boolean) => void;
  setCurrentPayment: (payment: PeachCreatePaymentResponse | null) => void;
  addPaymentToHistory: (payment: PeachPaymentStatus) => void;
  setError: (error: string | null) => void;
  setConfig: (config: PeachConfig) => void;
  clearPaymentData: () => void;
}

// Component Props
export interface PeachCheckoutProps {
  orderId: string;
  amount: number;
  currency?: string;
  description: string;
  customerEmail: string;
  customerName: string;
  paymentBrand?: PeachPaymentBrand;
  onSuccess?: (result: PeachPaymentStatus) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
  disabled?: boolean;
}

export interface PeachPaymentFormProps extends PeachCheckoutProps {
  showPaymentMethods?: boolean;
  allowRecurring?: boolean;
  customFields?: Record<string, any>;
  availablePaymentMethods?: PeachPaymentBrand[];
}

export interface PeachStatusProps {
  checkoutId?: string;
  orderId?: string;
  onStatusUpdate?: (status: PeachPaymentStatus) => void;
  pollInterval?: number;
  maxAttempts?: number;
}

export interface PeachHistoryProps {
  userId?: string;
  limit?: number;
  showFilters?: boolean;
  onPaymentSelect?: (payment: PeachPaymentStatus) => void;
}

export interface PeachPaymentMethodProps {
  selectedMethod?: PeachPaymentBrand;
  onMethodSelect: (method: PeachPaymentBrand) => void;
  availableMethods?: PeachPaymentBrand[];
  amount: number;
  currency?: string;
}

// Utility types
export type PeachEnvironment = 'sandbox' | 'production';

export interface PeachValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface PeachApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

// Error types
export class PeachError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'PeachError';
  }
}

export class PeachValidationError extends PeachError {
  constructor(message: string, public field?: string) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'PeachValidationError';
  }
}

export class PeachNetworkError extends PeachError {
  constructor(message: string, public statusCode?: number) {
    super(message, 'NETWORK_ERROR');
    this.name = 'PeachNetworkError';
  }
}

export class PeachAuthenticationError extends PeachError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTH_ERROR');
    this.name = 'PeachAuthenticationError';
  }
}

// Payment method configurations
export interface PeachPaymentMethodConfig {
  id: PeachPaymentBrand;
  name: string;
  description: string;
  icon: string;
  category: 'card' | 'eft' | 'wallet' | 'bnpl' | 'qr' | 'voucher' | 'crypto' | 'alternative';
  currencies: string[];
  countries: string[];
  fees?: string;
  limits?: {
    min?: number;
    max?: number;
  };
  features?: string[];
  popular?: boolean;
  enabled?: boolean;
}

// Success/Failure codes
export interface PeachResultCodes {
  success: string[];
  pending: string[];
  failure: string[];
}

// Webhook signature verification
export interface PeachWebhookSignature {
  signature: string;
  timestamp: string;
  payload: string;
}

// Tokenization
export interface PeachTokenizationRequest {
  customerId: string;
  paymentBrand: PeachPaymentBrand;
  createRegistration: boolean;
}

export interface PeachTokenizationResponse {
  success: boolean;
  registrationId?: string;
  token?: string;
  error?: string;
}

// Subscription management
export interface PeachSubscriptionRequest {
  registrationId: string;
  amount: number;
  currency: string;
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
  startDate: Date;
  endDate?: Date;
  description: string;
}

export interface PeachSubscriptionResponse {
  success: boolean;
  subscriptionId?: string;
  nextPaymentDate?: Date;
  error?: string;
}

// Split payments for group orders
export interface PeachSplitPaymentRequest {
  groupOrderId: string;
  totalAmount: number;
  currency: string;
  splits: Array<{
    userId: string;
    amount: number;
    email: string;
    name: string;
    percentage?: number;
  }>;
  description: string;
}

export interface PeachSplitPaymentResponse {
  success: boolean;
  groupPaymentId?: string;
  individualPayments?: Array<{
    userId: string;
    checkoutId: string;
    amount: number;
    paymentUrl?: string;
  }>;
  error?: string;
}
