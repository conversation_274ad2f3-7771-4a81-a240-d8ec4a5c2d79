import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  PeachState,
  PeachCreatePaymentRequest,
  PeachCreatePaymentResponse,
  PeachRecurringPaymentRequest,
  PeachPaymentStatus,
  PeachConfig,
  PeachSplitPaymentRequest,
  PeachRefundRequest
} from '../types';

// Initial state
const initialState: PeachState = {
  isLoading: false,
  currentPayment: null,
  paymentHistory: [],
  error: null,
  config: null
};

// Async thunks
export const createPeachPayment = createAsyncThunk(
  'peach/createPayment',
  async (request: PeachCreatePaymentRequest, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/payment/peach/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Payment creation failed');
      }

      return data as PeachCreatePaymentResponse;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const createRecurringPeachPayment = createAsyncThunk(
  'peach/createRecurringPayment',
  async (request: PeachRecurringPaymentRequest, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/payment/peach/recurring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Recurring payment creation failed');
      }

      return data as PeachCreatePaymentResponse;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const createPeachSplitPayment = createAsyncThunk(
  'peach/createSplitPayment',
  async (request: PeachSplitPaymentRequest, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/payment/peach/split', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Split payment creation failed');
      }

      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchPeachPaymentHistory = createAsyncThunk(
  'peach/fetchPaymentHistory',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/peach/history?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to fetch payment history');
      }

      return data.payments as PeachPaymentStatus[];
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const checkPeachPaymentStatus = createAsyncThunk(
  'peach/checkPaymentStatus',
  async (params: { checkoutId?: string; orderId?: string }, { rejectWithValue }) => {
    try {
      const queryParam = params.checkoutId 
        ? `checkoutId=${params.checkoutId}` 
        : `orderId=${params.orderId}`;
        
      const response = await fetch(`/api/payment/peach/status?${queryParam}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to check payment status');
      }

      return data.status as PeachPaymentStatus;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const processPeachRefund = createAsyncThunk(
  'peach/processRefund',
  async (request: PeachRefundRequest, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/payment/peach/refund', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to process refund');
      }

      return data;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const cancelPeachSubscription = createAsyncThunk(
  'peach/cancelSubscription',
  async (subscriptionId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/peach/subscription/${subscriptionId}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to cancel subscription');
      }

      return { subscriptionId, success: true };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

// Slice
const peachSlice = createSlice({
  name: 'peach',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCurrentPayment: (state, action: PayloadAction<PeachCreatePaymentResponse | null>) => {
      state.currentPayment = action.payload;
    },
    addPaymentToHistory: (state, action: PayloadAction<PeachPaymentStatus>) => {
      const existingIndex = state.paymentHistory.findIndex(
        payment => payment.orderId === action.payload.orderId
      );
      
      if (existingIndex >= 0) {
        state.paymentHistory[existingIndex] = action.payload;
      } else {
        state.paymentHistory.unshift(action.payload);
      }
    },
    updatePaymentStatus: (state, action: PayloadAction<{ orderId: string; status: PeachPaymentStatus }>) => {
      const payment = state.paymentHistory.find(p => p.orderId === action.payload.orderId);
      if (payment) {
        Object.assign(payment, action.payload.status);
      }
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setConfig: (state, action: PayloadAction<PeachConfig>) => {
      state.config = action.payload;
    },
    clearPaymentData: (state) => {
      state.currentPayment = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    removePaymentFromHistory: (state, action: PayloadAction<string>) => {
      state.paymentHistory = state.paymentHistory.filter(
        payment => payment.orderId !== action.payload
      );
    },
    updatePaymentInHistory: (state, action: PayloadAction<PeachPaymentStatus>) => {
      const index = state.paymentHistory.findIndex(
        payment => payment.orderId === action.payload.orderId || 
                   payment.checkoutId === action.payload.checkoutId
      );
      
      if (index >= 0) {
        state.paymentHistory[index] = action.payload;
      }
    }
  },
  extraReducers: (builder) => {
    // Create Payment
    builder
      .addCase(createPeachPayment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPeachPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPayment = action.payload;
        state.error = null;
      })
      .addCase(createPeachPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.currentPayment = null;
      });

    // Create Recurring Payment
    builder
      .addCase(createRecurringPeachPayment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createRecurringPeachPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPayment = action.payload;
        state.error = null;
      })
      .addCase(createRecurringPeachPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.currentPayment = null;
      });

    // Create Split Payment
    builder
      .addCase(createPeachSplitPayment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPeachSplitPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null;
        // Handle split payment success
      })
      .addCase(createPeachSplitPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Payment History
    builder
      .addCase(fetchPeachPaymentHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPeachPaymentHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.paymentHistory = action.payload;
        state.error = null;
      })
      .addCase(fetchPeachPaymentHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Check Payment Status
    builder
      .addCase(checkPeachPaymentStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkPeachPaymentStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        
        // Update payment in history
        const existingIndex = state.paymentHistory.findIndex(
          payment => payment.orderId === action.payload.orderId ||
                     payment.checkoutId === action.payload.checkoutId
        );
        
        if (existingIndex >= 0) {
          state.paymentHistory[existingIndex] = action.payload;
        } else {
          state.paymentHistory.unshift(action.payload);
        }
        
        state.error = null;
      })
      .addCase(checkPeachPaymentStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Process Refund
    builder
      .addCase(processPeachRefund.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(processPeachRefund.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(processPeachRefund.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Cancel Subscription
    builder
      .addCase(cancelPeachSubscription.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(cancelPeachSubscription.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(cancelPeachSubscription.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }
});

// Actions
export const {
  setLoading,
  setCurrentPayment,
  addPaymentToHistory,
  updatePaymentStatus,
  setError,
  setConfig,
  clearPaymentData,
  clearError,
  removePaymentFromHistory,
  updatePaymentInHistory
} = peachSlice.actions;

// Selectors
export const selectPeachState = (state: { peach: PeachState }) => state.peach;
export const selectPeachLoading = (state: { peach: PeachState }) => state.peach.isLoading;
export const selectPeachCurrentPayment = (state: { peach: PeachState }) => state.peach.currentPayment;
export const selectPeachPaymentHistory = (state: { peach: PeachState }) => state.peach.paymentHistory;
export const selectPeachError = (state: { peach: PeachState }) => state.peach.error;
export const selectPeachConfig = (state: { peach: PeachState }) => state.peach.config;

// Reducer
export default peachSlice.reducer;
