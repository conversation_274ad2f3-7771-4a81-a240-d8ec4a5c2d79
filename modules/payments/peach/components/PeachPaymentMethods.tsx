'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  Smartphone, 
  QrCode, 
  Banknote,
  Wallet,
  Gift,
  Bitcoin,
  Search,
  Filter,
  Star
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { PeachPaymentMethodProps, PeachPaymentBrand } from '../types';
import { PeachUtils } from '../utils';

export function PeachPaymentMethods({
  selectedMethod,
  onMethodSelect,
  availableMethods,
  amount,
  currency = 'ZAR'
}: PeachPaymentMethodProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Get all payment method configurations
  const allMethods = PeachUtils.getPaymentMethodConfigs();
  
  // Filter methods based on availability and search
  const filteredMethods = allMethods.filter(method => {
    // Check if method is in available methods list (if provided)
    if (availableMethods && !availableMethods.includes(method.id)) {
      return false;
    }

    // Check if method supports the currency
    if (!method.currencies.includes(currency)) {
      return false;
    }

    // Check if method is enabled
    if (!method.enabled) {
      return false;
    }

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      if (!method.name.toLowerCase().includes(searchLower) &&
          !method.description.toLowerCase().includes(searchLower)) {
        return false;
      }
    }

    // Category filter
    if (selectedCategory !== 'all' && method.category !== selectedCategory) {
      return false;
    }

    return true;
  });

  // Group methods by category
  const methodsByCategory = filteredMethods.reduce((acc, method) => {
    if (!acc[method.category]) {
      acc[method.category] = [];
    }
    acc[method.category].push(method);
    return acc;
  }, {} as Record<string, typeof filteredMethods>);

  // Get category icons
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'card': return CreditCard;
      case 'eft': return Banknote;
      case 'wallet': return Wallet;
      case 'bnpl': return Smartphone;
      case 'qr': return QrCode;
      case 'voucher': return Gift;
      case 'crypto': return Bitcoin;
      default: return CreditCard;
    }
  };

  // Get category labels
  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'card': return 'Cards';
      case 'eft': return 'Bank Transfer';
      case 'wallet': return 'Digital Wallets';
      case 'bnpl': return 'Buy Now Pay Later';
      case 'qr': return 'QR Payments';
      case 'voucher': return 'Vouchers';
      case 'crypto': return 'Cryptocurrency';
      case 'alternative': return 'Alternative';
      default: return category;
    }
  };

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(filteredMethods.map(m => m.category)))];

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl">
            <CreditCard className="h-6 w-6" />
            Choose Payment Method
          </CardTitle>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}
            </p>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search" className="sr-only">Search payment methods</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search payment methods..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="sm:w-48">
              <Label htmlFor="category" className="sr-only">Filter by category</Label>
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <select
                  id="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="all">All Categories</option>
                  {categories.slice(1).map(category => (
                    <option key={category} value={category}>
                      {getCategoryLabel(category)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Payment Methods */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
              <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
              {categories.slice(1, 7).map(category => {
                const Icon = getCategoryIcon(category);
                return (
                  <TabsTrigger key={category} value={category} className="text-xs">
                    <Icon className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">{getCategoryLabel(category)}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            <TabsContent value={selectedCategory} className="mt-6">
              <AnimatePresence>
                {Object.keys(methodsByCategory).length > 0 ? (
                  <div className="space-y-6">
                    {Object.entries(methodsByCategory).map(([category, methods]) => (
                      <motion.div
                        key={category}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="space-y-3"
                      >
                        {selectedCategory === 'all' && (
                          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                            {React.createElement(getCategoryIcon(category), { className: "h-5 w-5" })}
                            {getCategoryLabel(category)}
                          </h3>
                        )}
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                          {methods.map((method, index) => (
                            <motion.button
                              key={method.id}
                              initial={{ opacity: 0, scale: 0.9 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: index * 0.1 }}
                              onClick={() => onMethodSelect(method.id)}
                              className={`p-4 border-2 rounded-lg text-left transition-all hover:shadow-md ${
                                selectedMethod === method.id
                                  ? 'border-orange-500 bg-orange-50 shadow-md'
                                  : 'border-gray-200 hover:border-orange-300 bg-white'
                              }`}
                            >
                              <div className="flex items-start justify-between mb-3">
                                <div className="flex items-center gap-3">
                                  <span className="text-3xl">{method.icon}</span>
                                  <div>
                                    <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                                      {method.name}
                                      {method.popular && (
                                        <Badge className="bg-orange-100 text-orange-800 text-xs">
                                          <Star className="h-3 w-3 mr-1" />
                                          Popular
                                        </Badge>
                                      )}
                                    </h4>
                                    <p className="text-sm text-gray-600">{method.description}</p>
                                  </div>
                                </div>
                                
                                {selectedMethod === method.id && (
                                  <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                                    <div className="w-2 h-2 bg-white rounded-full"></div>
                                  </div>
                                )}
                              </div>

                              <div className="space-y-2">
                                {method.fees && (
                                  <div className="flex justify-between text-sm">
                                    <span className="text-gray-600">Fees:</span>
                                    <span className="font-medium text-gray-900">{method.fees}</span>
                                  </div>
                                )}
                                
                                {method.limits && (
                                  <div className="flex justify-between text-sm">
                                    <span className="text-gray-600">Limits:</span>
                                    <span className="font-medium text-gray-900">
                                      {method.limits.min && `Min: ${currency} ${method.limits.min}`}
                                      {method.limits.min && method.limits.max && ' | '}
                                      {method.limits.max && `Max: ${currency} ${method.limits.max}`}
                                    </span>
                                  </div>
                                )}

                                {method.features && method.features.length > 0 && (
                                  <div className="flex flex-wrap gap-1 mt-2">
                                    {method.features.slice(0, 2).map((feature, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs">
                                        {feature}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </motion.button>
                          ))}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-12"
                  >
                    <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">No payment methods found</p>
                    <p className="text-sm text-gray-500">
                      Try adjusting your search or filter criteria
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </TabsContent>
          </Tabs>

          {/* Selected Method Summary */}
          {selectedMethod && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-4 bg-orange-50 border border-orange-200 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">
                    {PeachUtils.getPaymentMethodConfig(selectedMethod)?.icon}
                  </span>
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {PeachUtils.getPaymentMethodConfig(selectedMethod)?.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {PeachUtils.getPaymentMethodConfig(selectedMethod)?.description}
                    </p>
                  </div>
                </div>
                <Badge className="bg-orange-100 text-orange-800">
                  Selected
                </Badge>
              </div>
            </motion.div>
          )}

          {/* Security Notice */}
          <div className="text-center p-4 bg-gray-50 rounded-lg border">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Badge className="bg-green-100 text-green-800 text-xs">
                <CreditCard className="h-3 w-3 mr-1" />
                PCI Compliant
              </Badge>
              <Badge className="bg-blue-100 text-blue-800 text-xs">
                <Smartphone className="h-3 w-3 mr-1" />
                Mobile Optimized
              </Badge>
            </div>
            <p className="text-xs text-gray-600">
              All payment methods are secured with bank-level encryption and fraud protection
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
