'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw, 
  AlertCircle,
  Eye,
  Copy,
  ExternalLink,
  CreditCard,
  TrendingUp
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePeach, usePeachStatusPolling } from '../hooks/usePeach';
import { PeachStatusProps, PeachPaymentStatus } from '../types';
import { PeachUtils } from '../utils';
import { toast } from 'sonner';

export function PeachStatus({
  checkoutId,
  orderId,
  onStatusUpdate,
  pollInterval = 5000,
  maxAttempts = 12
}: PeachStatusProps) {
  const {
    checkPaymentStatus,
    getPaymentByOrderId,
    getPaymentByCheckoutId,
    isLoading,
    error
  } = usePeach();

  const [paymentStatus, setPaymentStatus] = useState<PeachPaymentStatus | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [attempts, setAttempts] = useState(0);

  // Get initial status from store
  useEffect(() => {
    let existingPayment: PeachPaymentStatus | undefined;
    
    if (checkoutId) {
      existingPayment = getPaymentByCheckoutId(checkoutId);
    } else if (orderId) {
      existingPayment = getPaymentByOrderId(orderId);
    }
    
    if (existingPayment) {
      setPaymentStatus(existingPayment);
    }
  }, [checkoutId, orderId, getPaymentByCheckoutId, getPaymentByOrderId]);

  // Status polling hook
  usePeachStatusPolling({ checkoutId, orderId }, {
    enabled: isPolling,
    interval: pollInterval,
    maxAttempts,
    onStatusUpdate: (status) => {
      setPaymentStatus(status);
      setAttempts(prev => prev + 1);
      onStatusUpdate?.(status);
    },
    onComplete: (status) => {
      setIsPolling(false);
      setPaymentStatus(status);
      onStatusUpdate?.(status);
      
      if (status.status === 'completed') {
        toast.success('Payment completed successfully!');
      } else if (status.status === 'failed') {
        toast.error('Payment failed');
      }
    },
    onError: (error) => {
      setIsPolling(false);
      toast.error(error);
    }
  });

  // Manual status check
  const handleManualCheck = async () => {
    try {
      const status = await checkPaymentStatus({ checkoutId, orderId });
      if (status) {
        setPaymentStatus(status);
        onStatusUpdate?.(status);
        toast.success('Status updated');
      }
    } catch (error) {
      toast.error('Failed to check status');
    }
  };

  // Start polling
  const startPolling = () => {
    setIsPolling(true);
    setAttempts(0);
    toast.info('Started monitoring payment status');
  };

  // Stop polling
  const stopPolling = () => {
    setIsPolling(false);
    toast.info('Stopped monitoring payment status');
  };

  // Copy ID
  const copyId = async (id: string, type: string) => {
    try {
      await navigator.clipboard.writeText(id);
      toast.success(`${type} copied to clipboard`);
    } catch (error) {
      toast.error(`Failed to copy ${type.toLowerCase()}`);
    }
  };

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: CheckCircle,
          iconColor: 'text-green-600',
          bgGradient: 'from-green-50 to-emerald-50'
        };
      case 'failed':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: XCircle,
          iconColor: 'text-red-600',
          bgGradient: 'from-red-50 to-pink-50'
        };
      case 'cancelled':
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: XCircle,
          iconColor: 'text-gray-600',
          bgGradient: 'from-gray-50 to-slate-50'
        };
      default:
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: Clock,
          iconColor: 'text-yellow-600',
          bgGradient: 'from-yellow-50 to-orange-50'
        };
    }
  };

  const statusDisplay = paymentStatus ? getStatusDisplay(paymentStatus.status) : null;
  const StatusIcon = statusDisplay?.icon || Clock;

  // Get payment method info
  const paymentMethodInfo = paymentStatus?.paymentBrand 
    ? PeachUtils.getPaymentMethodConfig(paymentStatus.paymentBrand)
    : null;

  return (
    <div className="w-full max-w-md mx-auto">
      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Eye className="h-5 w-5" />
            Payment Status
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* ID Display */}
          <div className="space-y-3">
            {orderId && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Order ID</p>
                    <p className="font-mono text-sm font-medium">{orderId}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyId(orderId, 'Order ID')}
                    className="px-3"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {checkoutId && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Checkout ID</p>
                    <p className="font-mono text-sm font-medium">{checkoutId}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyId(checkoutId, 'Checkout ID')}
                    className="px-3"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Status Display */}
          <AnimatePresence mode="wait">
            {paymentStatus ? (
              <motion.div
                key="status-found"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                {/* Status Badge */}
                <div className={`text-center p-6 rounded-xl bg-gradient-to-r ${statusDisplay?.bgGradient}`}>
                  <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full border ${statusDisplay?.color}`}>
                    <StatusIcon className={`h-5 w-5 ${statusDisplay?.iconColor}`} />
                    <span className="font-semibold capitalize">
                      {paymentStatus.status}
                    </span>
                  </div>
                  
                  {paymentStatus.statusCode && (
                    <p className="text-xs text-gray-600 mt-2">
                      Code: {paymentStatus.statusCode}
                    </p>
                  )}
                </div>

                {/* Payment Details */}
                <div className="space-y-3">
                  {paymentStatus.amount && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount:</span>
                      <span className="font-semibold">
                        {paymentStatus.currency === 'ZAR' ? 'R' : paymentStatus.currency} {paymentStatus.amount.toFixed(2)}
                      </span>
                    </div>
                  )}
                  
                  {paymentMethodInfo && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Payment Method:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{paymentMethodInfo.icon}</span>
                        <span className="font-semibold">{paymentMethodInfo.name}</span>
                      </div>
                    </div>
                  )}
                  
                  {paymentStatus.transactionId && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Transaction ID:</span>
                      <span className="font-mono text-sm">{paymentStatus.transactionId}</span>
                    </div>
                  )}
                  
                  {paymentStatus.paidAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Paid At:</span>
                      <span className="text-sm">
                        {new Date(paymentStatus.paidAt).toLocaleString()}
                      </span>
                    </div>
                  )}
                  
                  {paymentStatus.description && (
                    <div className="space-y-1">
                      <span className="text-gray-600 text-sm">Description:</span>
                      <p className="text-sm bg-gray-50 p-2 rounded">
                        {paymentStatus.description}
                      </p>
                    </div>
                  )}
                  
                  {paymentStatus.failureReason && (
                    <div className="space-y-1">
                      <span className="text-gray-600 text-sm">Failure Reason:</span>
                      <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                        {paymentStatus.failureReason}
                      </p>
                    </div>
                  )}
                </div>

                {/* Status-specific Messages */}
                {paymentStatus.status === 'pending' && (
                  <Alert className="border-yellow-200 bg-yellow-50">
                    <Clock className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-yellow-800">
                      Payment is being processed. This usually takes a few seconds to a few minutes.
                    </AlertDescription>
                  </Alert>
                )}

                {paymentStatus.status === 'failed' && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      Payment failed. Please try again or contact support if the issue persists.
                    </AlertDescription>
                  </Alert>
                )}

                {paymentStatus.status === 'completed' && (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      Payment completed successfully! Thank you for your purchase.
                    </AlertDescription>
                  </Alert>
                )}
              </motion.div>
            ) : (
              <motion.div
                key="status-loading"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="text-center py-8"
              >
                <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No payment status found</p>
                <p className="text-sm text-gray-500">
                  Click "Check Status" to refresh
                </p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Error Display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Polling Status */}
          {isPolling && (
            <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-center gap-2 mb-2">
                <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
                <span className="text-sm font-medium text-blue-800">
                  Monitoring payment status...
                </span>
              </div>
              <p className="text-xs text-blue-600">
                Attempt {attempts} of {maxAttempts}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <div className="flex gap-2">
              <Button
                onClick={handleManualCheck}
                disabled={isLoading}
                variant="outline"
                className="flex-1"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Checking...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Check Status
                  </>
                )}
              </Button>

              {paymentStatus?.status === 'pending' && (
                <Button
                  onClick={isPolling ? stopPolling : startPolling}
                  variant={isPolling ? "destructive" : "default"}
                  className="flex-1"
                >
                  {isPolling ? (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Stop Monitoring
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      Start Monitoring
                    </>
                  )}
                </Button>
              )}
            </div>

            {/* Peach Dashboard Link */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() => window.open('https://peachpayments.com', '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View on Peach Dashboard
            </Button>
          </div>

          {/* Help Text */}
          <div className="text-center text-xs text-gray-500 space-y-1">
            <p>Status updates automatically via webhooks</p>
            <p>Manual checking available for immediate updates</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
