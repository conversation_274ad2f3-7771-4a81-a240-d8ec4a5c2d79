import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch } from '@/lib/redux/store';
import {
  createPeachPayment,
  createRecurringPeachPayment,
  createPeachSplitPayment,
  fetchPeachPaymentHistory,
  checkPeachPaymentStatus,
  processPeachRefund,
  cancelPeachSubscription,
  setCurrentPayment,
  addPaymentToHistory,
  setError,
  clearPaymentData,
  clearError,
  selectPeachState,
  selectPeachLoading,
  selectPeachCurrentPayment,
  selectPeachPaymentHistory,
  selectPeachError,
  selectPeachConfig
} from '../store/peachSlice';
import {
  PeachCreatePaymentRequest,
  PeachCreatePaymentResponse,
  PeachRecurringPaymentRequest,
  PeachSplitPaymentRequest,
  PeachPaymentStatus,
  PeachRefundRequest,
  PeachPaymentBrand
} from '../types';

/**
 * Custom hook for Peach Payments operations
 */
export const usePeach = () => {
  const dispatch = useDispatch<AppDispatch>();
  
  // Selectors
  const state = useSelector(selectPeachState);
  const isLoading = useSelector(selectPeachLoading);
  const currentPayment = useSelector(selectPeachCurrentPayment);
  const paymentHistory = useSelector(selectPeachPaymentHistory);
  const error = useSelector(selectPeachError);
  const config = useSelector(selectPeachConfig);

  // Create standard payment
  const createPayment = useCallback(
    async (request: PeachCreatePaymentRequest): Promise<PeachCreatePaymentResponse | null> => {
      try {
        const result = await dispatch(createPeachPayment(request)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to create Peach payment:', error);
        return null;
      }
    },
    [dispatch]
  );

  // Create recurring payment
  const createRecurringPayment = useCallback(
    async (request: PeachRecurringPaymentRequest): Promise<PeachCreatePaymentResponse | null> => {
      try {
        const result = await dispatch(createRecurringPeachPayment(request)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to create recurring Peach payment:', error);
        return null;
      }
    },
    [dispatch]
  );

  // Create split payment for group orders
  const createSplitPayment = useCallback(
    async (request: PeachSplitPaymentRequest): Promise<any> => {
      try {
        const result = await dispatch(createPeachSplitPayment(request)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to create Peach split payment:', error);
        return null;
      }
    },
    [dispatch]
  );

  // Fetch payment history
  const fetchPaymentHistory = useCallback(
    async (userId: string): Promise<PeachPaymentStatus[]> => {
      try {
        const result = await dispatch(fetchPeachPaymentHistory(userId)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to fetch Peach payment history:', error);
        return [];
      }
    },
    [dispatch]
  );

  // Check payment status
  const checkPaymentStatus = useCallback(
    async (params: { checkoutId?: string; orderId?: string }): Promise<PeachPaymentStatus | null> => {
      try {
        const result = await dispatch(checkPeachPaymentStatus(params)).unwrap();
        return result;
      } catch (error) {
        console.error('Failed to check Peach payment status:', error);
        return null;
      }
    },
    [dispatch]
  );

  // Process refund
  const processRefund = useCallback(
    async (request: PeachRefundRequest): Promise<boolean> => {
      try {
        await dispatch(processPeachRefund(request)).unwrap();
        return true;
      } catch (error) {
        console.error('Failed to process Peach refund:', error);
        return false;
      }
    },
    [dispatch]
  );

  // Cancel subscription
  const cancelSubscription = useCallback(
    async (subscriptionId: string): Promise<boolean> => {
      try {
        await dispatch(cancelPeachSubscription(subscriptionId)).unwrap();
        return true;
      } catch (error) {
        console.error('Failed to cancel Peach subscription:', error);
        return false;
      }
    },
    [dispatch]
  );

  // Process payment (redirect or embedded)
  const processPayment = useCallback(
    (paymentData: PeachCreatePaymentResponse, embedded: boolean = false) => {
      if (!paymentData.success || !paymentData.checkoutId) {
        throw new Error('Invalid payment data');
      }

      if (embedded) {
        // For embedded payments, you would initialize the Peach widget here
        // This is a placeholder for the actual implementation
        console.log('Initialize embedded payment widget with checkout ID:', paymentData.checkoutId);
      } else if (paymentData.redirectUrl) {
        // Redirect to Peach payment page
        window.location.href = paymentData.redirectUrl;
      } else {
        throw new Error('No payment URL available');
      }
    },
    []
  );

  // Create and process payment in one step
  const createAndProcessPayment = useCallback(
    async (request: PeachCreatePaymentRequest, embedded: boolean = false): Promise<boolean> => {
      try {
        const paymentData = await createPayment(request);
        
        if (!paymentData || !paymentData.success) {
          return false;
        }

        processPayment(paymentData, embedded);
        return true;
      } catch (error) {
        console.error('Failed to create and process payment:', error);
        return false;
      }
    },
    [createPayment, processPayment]
  );

  // Poll payment status
  const pollPaymentStatus = useCallback(
    (params: { checkoutId?: string; orderId?: string }, options: {
      interval?: number;
      maxAttempts?: number;
      onStatusUpdate?: (status: PeachPaymentStatus) => void;
      onComplete?: (status: PeachPaymentStatus) => void;
      onError?: (error: string) => void;
    } = {}) => {
      const {
        interval = 5000,
        maxAttempts = 12,
        onStatusUpdate,
        onComplete,
        onError
      } = options;

      let attempts = 0;
      let pollInterval: NodeJS.Timeout;

      const poll = async () => {
        attempts++;

        try {
          const status = await checkPaymentStatus(params);
          
          if (status) {
            onStatusUpdate?.(status);

            // Check if payment is complete
            if (status.status === 'completed' || status.status === 'failed') {
              clearInterval(pollInterval);
              onComplete?.(status);
              return;
            }
          }

          // Stop polling if max attempts reached
          if (attempts >= maxAttempts) {
            clearInterval(pollInterval);
            onError?.('Payment status polling timeout');
          }
        } catch (error) {
          clearInterval(pollInterval);
          onError?.(error instanceof Error ? error.message : 'Polling error');
        }
      };

      // Start polling
      pollInterval = setInterval(poll, interval);
      
      // Initial check
      poll();

      // Return cleanup function
      return () => clearInterval(pollInterval);
    },
    [checkPaymentStatus]
  );

  // Get payment by order ID
  const getPaymentByOrderId = useCallback(
    (orderId: string): PeachPaymentStatus | undefined => {
      return paymentHistory.find(payment => payment.orderId === orderId);
    },
    [paymentHistory]
  );

  // Get payment by checkout ID
  const getPaymentByCheckoutId = useCallback(
    (checkoutId: string): PeachPaymentStatus | undefined => {
      return paymentHistory.find(payment => payment.checkoutId === checkoutId);
    },
    [paymentHistory]
  );

  // Get successful payments
  const getSuccessfulPayments = useCallback(
    (): PeachPaymentStatus[] => {
      return paymentHistory.filter(payment => payment.status === 'completed');
    },
    [paymentHistory]
  );

  // Get failed payments
  const getFailedPayments = useCallback(
    (): PeachPaymentStatus[] => {
      return paymentHistory.filter(payment => payment.status === 'failed');
    },
    [paymentHistory]
  );

  // Get pending payments
  const getPendingPayments = useCallback(
    (): PeachPaymentStatus[] => {
      return paymentHistory.filter(payment => payment.status === 'pending');
    },
    [paymentHistory]
  );

  // Get payments by payment brand
  const getPaymentsByBrand = useCallback(
    (brand: PeachPaymentBrand): PeachPaymentStatus[] => {
      return paymentHistory.filter(payment => payment.paymentBrand === brand);
    },
    [paymentHistory]
  );

  // Calculate total amount paid
  const getTotalAmountPaid = useCallback(
    (currency: string = 'ZAR'): number => {
      return getSuccessfulPayments()
        .filter(payment => payment.currency === currency)
        .reduce((total, payment) => {
          return total + (payment.amount || 0);
        }, 0);
    },
    [getSuccessfulPayments]
  );

  // Get payment statistics
  const getPaymentStatistics = useCallback(
    () => {
      const total = paymentHistory.length;
      const successful = getSuccessfulPayments().length;
      const failed = getFailedPayments().length;
      const pending = getPendingPayments().length;
      const totalAmount = getTotalAmountPaid();

      return {
        total,
        successful,
        failed,
        pending,
        successRate: total > 0 ? (successful / total) * 100 : 0,
        totalAmount,
        averageAmount: successful > 0 ? totalAmount / successful : 0
      };
    },
    [paymentHistory, getSuccessfulPayments, getFailedPayments, getPendingPayments, getTotalAmountPaid]
  );

  // Clear current payment
  const clearCurrentPayment = useCallback(() => {
    dispatch(clearPaymentData());
  }, [dispatch]);

  // Clear error
  const clearCurrentError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Add payment to history manually
  const addToHistory = useCallback(
    (payment: PeachPaymentStatus) => {
      dispatch(addPaymentToHistory(payment));
    },
    [dispatch]
  );

  // Set error manually
  const setCurrentError = useCallback(
    (error: string) => {
      dispatch(setError(error));
    },
    [dispatch]
  );

  return {
    // State
    state,
    isLoading,
    currentPayment,
    paymentHistory,
    error,
    config,

    // Actions
    createPayment,
    createRecurringPayment,
    createSplitPayment,
    fetchPaymentHistory,
    checkPaymentStatus,
    processRefund,
    cancelSubscription,
    processPayment,
    createAndProcessPayment,
    pollPaymentStatus,

    // Utilities
    getPaymentByOrderId,
    getPaymentByCheckoutId,
    getSuccessfulPayments,
    getFailedPayments,
    getPendingPayments,
    getPaymentsByBrand,
    getTotalAmountPaid,
    getPaymentStatistics,
    clearCurrentPayment,
    clearCurrentError,
    addToHistory,
    setCurrentError
  };
};

/**
 * Hook for Peach payment status polling
 */
export const usePeachStatusPolling = (
  params: { checkoutId?: string; orderId?: string },
  options: {
    enabled?: boolean;
    interval?: number;
    maxAttempts?: number;
    onStatusUpdate?: (status: PeachPaymentStatus) => void;
    onComplete?: (status: PeachPaymentStatus) => void;
    onError?: (error: string) => void;
  } = {}
) => {
  const { pollPaymentStatus } = usePeach();
  const { enabled = true, ...pollOptions } = options;

  useEffect(() => {
    if (!enabled || (!params.checkoutId && !params.orderId)) return;

    const cleanup = pollPaymentStatus(params, pollOptions);
    return cleanup;
  }, [params.checkoutId, params.orderId, enabled, pollPaymentStatus, pollOptions]);
};

/**
 * Hook for Peach configuration
 */
export const usePeachConfig = () => {
  const config = useSelector(selectPeachConfig);
  
  const isConfigured = Boolean(
    config?.entityId && 
    config?.username && 
    config?.password && 
    config?.baseUrl &&
    config?.successUrl &&
    config?.cancelUrl
  );

  const isSandbox = config?.sandbox ?? true;

  return {
    config,
    isConfigured,
    isSandbox
  };
};

/**
 * Hook for payment method management
 */
export const usePeachPaymentMethods = () => {
  const { config } = usePeachConfig();

  // Get available payment methods based on configuration
  const getAvailablePaymentMethods = useCallback(
    (currency: string = 'ZAR', country: string = 'ZA') => {
      // This would typically come from the Peach API or configuration
      // For now, return a static list based on currency and country
      const allMethods: PeachPaymentBrand[] = [
        'CARD', 'CAPITECPAY', 'PAYFLEX', 'MASTERPASS', 'APPLEPAY', 
        'GOOGLEPAY', 'MOBICRED', 'ZEROPAY', 'FLOAT', 'MONEYBADGER'
      ];

      // Filter based on currency and country
      if (currency === 'ZAR' && country === 'ZA') {
        return allMethods;
      }

      // Return basic card payments for other currencies/countries
      return ['CARD', 'PAYPAL'];
    },
    [config]
  );

  return {
    getAvailablePaymentMethods
  };
};
