import {
  PeachConfig,
  PeachPaymentData,
  PeachCreatePaymentRequest,
  PeachCreatePaymentResponse,
  PeachRecurringPaymentRequest,
  PeachPaymentStatus,
  PeachRefundRequest,
  PeachRefundResponse,
  PeachWebhookData,
  PeachSplitPaymentRequest,
  PeachSplitPaymentResponse,
  PeachTokenizationRequest,
  PeachTokenizationResponse,
  PeachValidationError,
  PeachNetworkError,
  PeachAuthenticationError,
  PeachPaymentBrand
} from '../types';
import { PeachUtils } from '../utils';

/**
 * Peach Payments Service Class
 * Handles all Peach Payments operations
 */
export class PeachService {
  private config: PeachConfig;

  constructor(config: PeachConfig) {
    // Validate configuration
    const validation = PeachUtils.validateConfig(config);
    if (!validation.isValid) {
      throw new PeachValidationError(
        `Invalid Peach configuration: ${validation.errors.join(', ')}`
      );
    }

    this.config = config;
  }

  /**
   * Create a standard payment
   */
  async createPayment(request: PeachCreatePaymentRequest): Promise<PeachCreatePaymentResponse> {
    try {
      // Validate request data
      this.validatePaymentRequest(request);

      // Parse customer name
      const { firstName, lastName } = PeachUtils.parseCustomerName(request.customerName);

      // Create payment data
      const paymentData: PeachPaymentData = {
        entityId: this.config.entityId,
        amount: PeachUtils.formatAmount(request.amount),
        currency: request.currency || 'ZAR',
        paymentType: 'DB', // Debit transaction
        merchantTransactionId: request.orderId,
        'customer.email': request.customerEmail,
        'customer.givenName': firstName,
        'customer.surname': lastName,
        paymentBrand: request.paymentBrand || 'CARD',
        merchantInvoiceId: request.orderId,
        'cart.items[0].name': PeachUtils.sanitizeString(request.description, 255),
        'cart.items[0].merchantItemId': request.orderId,
        'cart.items[0].quantity': '1',
        'cart.items[0].type': 'GOODS',
        'cart.items[0].price': PeachUtils.formatAmount(request.amount),
        'cart.items[0].currency': request.currency || 'ZAR',
        shopperResultUrl: this.config.successUrl,
        'customParameters[SHOPPER_EndToEndIdentity]': request.orderId
      };

      // Add custom data if provided
      if (request.customData) {
        Object.entries(request.customData).forEach(([key, value]) => {
          paymentData[`customParameters[${key}]` as keyof PeachPaymentData] = value;
        });
      }

      const response = await this.makeApiRequest('/payments', 'POST', paymentData);

      if (!response.ok) {
        const errorData = await response.json();
        throw new PeachNetworkError(
          errorData.result?.description || 'Payment creation failed',
          response.status
        );
      }

      const result = await response.json();

      return {
        success: true,
        checkoutId: result.id,
        redirectUrl: result.redirectUrl,
        result: result.result
      };

    } catch (error) {
      console.error('Peach payment creation error:', error);
      
      if (error instanceof PeachValidationError || error instanceof PeachNetworkError) {
        return {
          success: false,
          error: error.message
        };
      }

      return {
        success: false,
        error: 'Failed to create payment'
      };
    }
  }

  /**
   * Create a recurring payment
   */
  async createRecurringPayment(request: PeachRecurringPaymentRequest): Promise<PeachCreatePaymentResponse> {
    try {
      // Validate recurring data
      const recurringValidation = PeachUtils.validateRecurringData({
        frequency: request.frequency,
        startDate: request.startDate,
        endDate: request.endDate
      });

      if (!recurringValidation.isValid) {
        throw new PeachValidationError(
          `Invalid recurring data: ${recurringValidation.errors.join(', ')}`
        );
      }

      // Create base payment
      const basePayment = await this.createPayment(request);
      
      if (!basePayment.success) {
        return basePayment;
      }

      // For recurring payments, we need to create a registration first
      // This is a simplified approach - actual implementation may vary
      const recurringData: PeachPaymentData = {
        entityId: this.config.entityId,
        amount: PeachUtils.formatAmount(request.amount),
        currency: request.currency || 'ZAR',
        paymentType: 'DB',
        merchantTransactionId: request.orderId,
        'customer.email': request.customerEmail,
        'customer.givenName': PeachUtils.parseCustomerName(request.customerName).firstName,
        'customer.surname': PeachUtils.parseCustomerName(request.customerName).lastName,
        paymentBrand: request.paymentBrand || 'CARD',
        recurringType: 'INITIAL',
        'job.frequency': request.frequency,
        'job.startDate': request.startDate.toISOString().split('T')[0],
        shopperResultUrl: this.config.successUrl
      };

      if (request.endDate) {
        recurringData['job.endDate'] = request.endDate.toISOString().split('T')[0];
      }

      const response = await this.makeApiRequest('/payments', 'POST', recurringData);
      const result = await response.json();

      return {
        success: response.ok,
        checkoutId: result.id,
        redirectUrl: result.redirectUrl,
        result: result.result,
        error: response.ok ? undefined : result.result?.description
      };

    } catch (error) {
      console.error('Peach recurring payment creation error:', error);
      return {
        success: false,
        error: error instanceof PeachValidationError ? error.message : 'Failed to create recurring payment'
      };
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(checkoutId: string): Promise<PeachPaymentStatus | null> {
    try {
      const response = await this.makeApiRequest(
        `/payments/${checkoutId}?entityId=${this.config.entityId}`,
        'GET'
      );

      if (!response.ok) {
        throw new PeachNetworkError('Failed to get payment status', response.status);
      }

      const result = await response.json();

      // Determine status based on result code
      let status: 'pending' | 'completed' | 'failed' | 'cancelled' = 'pending';
      
      if (PeachUtils.isPaymentSuccessful(result.result?.code)) {
        status = 'completed';
      } else if (PeachUtils.isPaymentFailed(result.result?.code)) {
        status = 'failed';
      } else if (PeachUtils.isPaymentPending(result.result?.code)) {
        status = 'pending';
      }

      return {
        orderId: result.merchantTransactionId,
        status,
        checkoutId: result.id,
        transactionId: result.id,
        amount: parseFloat(result.amount),
        currency: result.currency,
        paymentMethod: 'peach',
        paymentBrand: result.paymentBrand,
        statusCode: result.result?.code,
        description: result.result?.description,
        paidAt: status === 'completed' ? new Date(result.timestamp) : undefined,
        failureReason: status === 'failed' ? result.result?.description : undefined
      };

    } catch (error) {
      console.error('Peach payment status error:', error);
      return null;
    }
  }

  /**
   * Process refund
   */
  async processRefund(request: PeachRefundRequest): Promise<PeachRefundResponse> {
    try {
      const refundData: PeachPaymentData = {
        entityId: this.config.entityId,
        amount: PeachUtils.formatAmount(request.amount),
        currency: 'ZAR',
        paymentType: 'RF', // Refund transaction
        referencedPaymentId: request.originalPaymentId
      };

      if (request.reason) {
        refundData['customParameters[CTPE_DESCRIPTOR_TEMPLATE]'] = request.reason;
      }

      const response = await this.makeApiRequest('/payments', 'POST', refundData);
      const result = await response.json();

      return {
        success: response.ok,
        refundId: result.id,
        status: result.result?.code,
        description: result.result?.description,
        error: response.ok ? undefined : result.result?.description
      };

    } catch (error) {
      console.error('Peach refund processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Refund failed'
      };
    }
  }

  /**
   * Create split payment for group orders
   */
  async createSplitPayment(request: PeachSplitPaymentRequest): Promise<PeachSplitPaymentResponse> {
    try {
      const individualPayments = [];

      for (const split of request.splits) {
        const paymentRequest: PeachCreatePaymentRequest = {
          orderId: `${request.groupOrderId}-${split.userId}`,
          amount: split.amount,
          currency: request.currency,
          description: `${request.description} - Group Payment`,
          customerEmail: split.email,
          customerName: split.name,
          customData: {
            groupOrderId: request.groupOrderId,
            userId: split.userId,
            splitPercentage: split.percentage
          }
        };

        const payment = await this.createPayment(paymentRequest);
        
        if (payment.success) {
          individualPayments.push({
            userId: split.userId,
            checkoutId: payment.checkoutId!,
            amount: split.amount,
            paymentUrl: payment.redirectUrl
          });
        }
      }

      return {
        success: individualPayments.length === request.splits.length,
        groupPaymentId: request.groupOrderId,
        individualPayments,
        error: individualPayments.length < request.splits.length 
          ? 'Some payments failed to create' 
          : undefined
      };

    } catch (error) {
      console.error('Peach split payment error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Split payment failed'
      };
    }
  }

  /**
   * Create tokenization for recurring payments
   */
  async createTokenization(request: PeachTokenizationRequest): Promise<PeachTokenizationResponse> {
    try {
      const tokenData: PeachPaymentData = {
        entityId: this.config.entityId,
        amount: '0.00', // Zero amount for tokenization
        currency: 'ZAR',
        paymentType: 'DB',
        merchantTransactionId: `TOKEN-${Date.now()}`,
        paymentBrand: request.paymentBrand,
        createRegistration: request.createRegistration,
        'customer.merchantCustomerId': request.customerId,
        shopperResultUrl: this.config.successUrl
      };

      const response = await this.makeApiRequest('/payments', 'POST', tokenData);
      const result = await response.json();

      return {
        success: response.ok,
        registrationId: result.registrationId,
        token: result.id,
        error: response.ok ? undefined : result.result?.description
      };

    } catch (error) {
      console.error('Peach tokenization error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Tokenization failed'
      };
    }
  }

  /**
   * Verify webhook notification
   */
  verifyWebhook(webhookData: PeachWebhookData, signature?: string): boolean {
    try {
      // Basic verification - check if required fields are present
      if (!webhookData.id || !webhookData.result || !webhookData.merchantTransactionId) {
        return false;
      }

      // If signature is provided, verify it
      if (signature && this.config.password) {
        const payload = JSON.stringify(webhookData);
        return PeachUtils.verifyWebhookSignature(payload, signature, this.config.password);
      }

      // Basic validation passed
      return true;

    } catch (error) {
      console.error('Peach webhook verification error:', error);
      return false;
    }
  }

  /**
   * Make API request to Peach Payments
   */
  private async makeApiRequest(
    endpoint: string, 
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    data?: any
  ): Promise<Response> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const authToken = PeachUtils.generateAuthToken(this.config.username, this.config.password);

    const headers: Record<string, string> = {
      'Authorization': `Basic ${authToken}`,
    };

    let body: string | FormData | undefined;

    if (data && method !== 'GET') {
      if (data instanceof FormData) {
        body = data;
      } else {
        // Convert to URLSearchParams for Peach Payments API
        const params = new URLSearchParams();
        Object.entries(data).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
        body = params.toString();
        headers['Content-Type'] = 'application/x-www-form-urlencoded';
      }
    }

    try {
      const response = await fetch(url, {
        method,
        headers,
        body
      });

      return response;

    } catch (error) {
      throw new PeachNetworkError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Validate payment request data
   */
  private validatePaymentRequest(request: PeachCreatePaymentRequest): void {
    const errors: string[] = [];

    // Validate amount
    const amountValidation = PeachUtils.validateAmount(request.amount, request.currency);
    if (!amountValidation.isValid) {
      errors.push(...amountValidation.errors);
    }

    // Validate email
    const emailValidation = PeachUtils.validateEmail(request.customerEmail);
    if (!emailValidation.isValid) {
      errors.push(...emailValidation.errors);
    }

    // Validate order ID
    const orderIdValidation = PeachUtils.validateOrderId(request.orderId);
    if (!orderIdValidation.isValid) {
      errors.push(...orderIdValidation.errors);
    }

    // Validate customer name
    const nameValidation = PeachUtils.validateCustomerName(request.customerName);
    if (!nameValidation.isValid) {
      errors.push(...nameValidation.errors);
    }

    // Validate description
    if (!request.description || request.description.trim().length < 1) {
      errors.push('Description is required');
    }

    if (errors.length > 0) {
      throw new PeachValidationError(`Validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Get configuration (for debugging)
   */
  getConfig(): Omit<PeachConfig, 'password'> {
    return {
      entityId: this.config.entityId,
      username: this.config.username,
      password: '***HIDDEN***',
      sandbox: this.config.sandbox,
      baseUrl: this.config.baseUrl,
      webhookUrl: this.config.webhookUrl,
      successUrl: this.config.successUrl,
      cancelUrl: this.config.cancelUrl
    };
  }

  /**
   * Test configuration
   */
  async testConfiguration(): Promise<{ success: boolean; error?: string }> {
    try {
      // Test with a minimal payment creation (won't be processed)
      const testData: PeachPaymentData = {
        entityId: this.config.entityId,
        amount: '1.00',
        currency: 'ZAR',
        paymentType: 'DB',
        merchantTransactionId: 'TEST-' + Date.now(),
        'customer.email': '<EMAIL>',
        paymentBrand: 'CARD'
      };

      const response = await this.makeApiRequest('/payments', 'POST', testData);
      
      return {
        success: response.ok || response.status === 400, // 400 might be expected for test data
        error: response.ok ? undefined : `HTTP ${response.status}`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Configuration test failed'
      };
    }
  }
}
