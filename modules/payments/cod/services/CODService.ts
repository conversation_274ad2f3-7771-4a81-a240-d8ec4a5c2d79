import {
  CODConfig,
  CO<PERSON>aymentData,
  CODCreatePaymentRequest,
  CODCreatePaymentResponse,
  CODPaymentStatus,
  CODDeliveryUpdate,
  CODValidationError,
  CODDeliveryError
} from '../types';
import { CODUtils } from '../utils';

/**
 * Cash on Delivery Service Class
 * Handles all COD payment operations
 */
export class CODService {
  private config: CODConfig;

  constructor(config: CODConfig) {
    // Validate configuration
    const validation = CODUtils.validateConfig(config);
    if (!validation.isValid) {
      throw new CODValidationError(
        `Invalid COD configuration: ${validation.errors.join(', ')}`
      );
    }

    this.config = config;
  }

  /**
   * Create a COD payment
   */
  async createPayment(request: CODCreatePaymentRequest): Promise<CODCreatePaymentResponse> {
    try {
      // Validate request data
      this.validatePaymentRequest(request);

      // Check COD eligibility
      const eligibilityCheck = CODUtils.validateCODEligibility(
        request.amount,
        request.deliveryAddress,
        this.config
      );

      if (!eligibilityCheck.isValid) {
        throw new CODValidationError(
          `COD not available: ${eligibilityCheck.errors.join(', ')}`
        );
      }

      // Get delivery estimate
      const deliveryEstimate = CODUtils.getDeliveryEstimate(
        request.deliveryAddress,
        request.amount,
        this.config
      );

      if (!deliveryEstimate.isAvailable) {
        throw new CODDeliveryError(
          deliveryEstimate.reason || 'Delivery not available',
          request.deliveryAddress.city
        );
      }

      // Calculate total amount
      const deliveryFee = deliveryEstimate.deliveryFee;
      const totalAmount = request.amount + deliveryFee;

      // Generate payment ID and tracking number
      const paymentId = this.generatePaymentId();
      const trackingNumber = CODUtils.generateTrackingNumber();

      // Create payment data
      const paymentData: CODPaymentData = {
        orderId: request.orderId,
        amount: request.amount,
        deliveryFee,
        totalAmount,
        currency: request.currency || 'ZAR',
        customerName: request.customerName,
        customerEmail: request.customerEmail,
        customerPhone: CODUtils.formatPhoneNumber(request.customerPhone),
        deliveryAddress: request.deliveryAddress,
        specialInstructions: request.specialInstructions,
        preferredDeliveryTime: request.preferredDeliveryTime,
        estimatedDeliveryDate: deliveryEstimate.estimatedDate
      };

      // In a real implementation, you would save this to database
      // For now, we'll simulate a successful creation
      await this.savePaymentData(paymentData, paymentId, trackingNumber);

      return {
        success: true,
        paymentId,
        deliveryFee,
        totalAmount,
        estimatedDeliveryDate: deliveryEstimate.estimatedDate,
        trackingNumber
      };

    } catch (error) {
      console.error('COD payment creation error:', error);
      
      if (error instanceof CODValidationError || error instanceof CODDeliveryError) {
        return {
          success: false,
          error: error.message
        };
      }

      return {
        success: false,
        error: 'Failed to create COD payment'
      };
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(orderId: string): Promise<CODPaymentStatus | null> {
    try {
      // In a real implementation, this would fetch from database
      const paymentData = await this.fetchPaymentData(orderId);
      
      if (!paymentData) {
        return null;
      }

      return paymentData;

    } catch (error) {
      console.error('COD payment status error:', error);
      return null;
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(
    orderId: string, 
    status: CODPaymentStatus['status'],
    notes?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // In a real implementation, this would update the database
      const updated = await this.updatePaymentInDatabase(orderId, status, notes);
      
      if (!updated) {
        return {
          success: false,
          error: 'Payment not found'
        };
      }

      // Send notifications based on status
      await this.sendStatusNotification(orderId, status);

      return { success: true };

    } catch (error) {
      console.error('COD status update error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Status update failed'
      };
    }
  }

  /**
   * Add delivery update
   */
  async addDeliveryUpdate(
    trackingNumber: string,
    update: Omit<CODDeliveryUpdate, 'trackingNumber'>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const deliveryUpdate: CODDeliveryUpdate = {
        trackingNumber,
        ...update
      };

      // In a real implementation, this would save to database
      await this.saveDeliveryUpdate(deliveryUpdate);

      // Send notification to customer
      await this.sendDeliveryNotification(trackingNumber, update);

      return { success: true };

    } catch (error) {
      console.error('COD delivery update error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delivery update failed'
      };
    }
  }

  /**
   * Cancel COD payment
   */
  async cancelPayment(
    orderId: string,
    reason: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const payment = await this.fetchPaymentData(orderId);
      
      if (!payment) {
        return {
          success: false,
          error: 'Payment not found'
        };
      }

      // Check if cancellation is allowed
      if (['delivered', 'cancelled'].includes(payment.status)) {
        return {
          success: false,
          error: `Cannot cancel payment with status: ${payment.status}`
        };
      }

      // Update status to cancelled
      await this.updatePaymentInDatabase(orderId, 'cancelled', reason);

      // Send cancellation notification
      await this.sendCancellationNotification(orderId, reason);

      return { success: true };

    } catch (error) {
      console.error('COD cancellation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Cancellation failed'
      };
    }
  }

  /**
   * Get delivery tracking information
   */
  async getTrackingInfo(trackingNumber: string): Promise<CODDeliveryUpdate[]> {
    try {
      // In a real implementation, this would fetch from database
      return await this.fetchDeliveryUpdates(trackingNumber);
    } catch (error) {
      console.error('COD tracking error:', error);
      return [];
    }
  }

  /**
   * Validate payment request data
   */
  private validatePaymentRequest(request: CODCreatePaymentRequest): void {
    const errors: string[] = [];

    // Validate amount
    const amountValidation = CODUtils.validateAmount(request.amount, this.config);
    if (!amountValidation.isValid) {
      errors.push(...amountValidation.errors);
    }

    // Validate phone number
    const phoneValidation = CODUtils.validatePhoneNumber(request.customerPhone);
    if (!phoneValidation.isValid) {
      errors.push(...phoneValidation.errors);
    }

    // Validate delivery address
    const addressValidation = CODUtils.validateDeliveryAddress(request.deliveryAddress);
    if (!addressValidation.isValid) {
      errors.push(...addressValidation.errors);
    }

    // Validate customer name
    if (!request.customerName || request.customerName.trim().length < 2) {
      errors.push('Customer name must be at least 2 characters');
    }

    // Validate customer email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!request.customerEmail || !emailRegex.test(request.customerEmail)) {
      errors.push('Valid email address is required');
    }

    // Validate description
    if (!request.description || request.description.trim().length < 1) {
      errors.push('Description is required');
    }

    if (errors.length > 0) {
      throw new CODValidationError(`Validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Generate unique payment ID
   */
  private generatePaymentId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `COD-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Save payment data (mock implementation)
   */
  private async savePaymentData(
    paymentData: CODPaymentData,
    paymentId: string,
    trackingNumber: string
  ): Promise<void> {
    // In a real implementation, this would save to database
    console.log('Saving COD payment data:', {
      paymentId,
      trackingNumber,
      paymentData
    });
  }

  /**
   * Fetch payment data (mock implementation)
   */
  private async fetchPaymentData(orderId: string): Promise<CODPaymentStatus | null> {
    // In a real implementation, this would fetch from database
    // For now, return a mock payment status
    return {
      orderId,
      status: 'pending',
      paymentId: `COD-${Date.now()}-MOCK`,
      trackingNumber: CODUtils.generateTrackingNumber(),
      amount: 100,
      deliveryFee: 50,
      totalAmount: 150,
      currency: 'ZAR',
      paymentMethod: 'cod',
      customerName: 'Mock Customer',
      customerPhone: '+27123456789',
      estimatedDeliveryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Update payment in database (mock implementation)
   */
  private async updatePaymentInDatabase(
    orderId: string,
    status: CODPaymentStatus['status'],
    notes?: string
  ): Promise<boolean> {
    // In a real implementation, this would update the database
    console.log('Updating COD payment status:', { orderId, status, notes });
    return true;
  }

  /**
   * Save delivery update (mock implementation)
   */
  private async saveDeliveryUpdate(update: CODDeliveryUpdate): Promise<void> {
    // In a real implementation, this would save to database
    console.log('Saving delivery update:', update);
  }

  /**
   * Fetch delivery updates (mock implementation)
   */
  private async fetchDeliveryUpdates(trackingNumber: string): Promise<CODDeliveryUpdate[]> {
    // In a real implementation, this would fetch from database
    return [
      {
        trackingNumber,
        status: 'confirmed',
        location: 'Warehouse',
        notes: 'Order confirmed and being prepared',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000)
      },
      {
        trackingNumber,
        status: 'picked_up',
        location: 'Distribution Center',
        notes: 'Package picked up by delivery agent',
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
        deliveryAgent: {
          name: 'John Delivery',
          phone: '+27987654321',
          vehicleNumber: 'ABC123GP'
        }
      }
    ];
  }

  /**
   * Send status notification (mock implementation)
   */
  private async sendStatusNotification(
    orderId: string,
    status: CODPaymentStatus['status']
  ): Promise<void> {
    // In a real implementation, this would send SMS/email notifications
    console.log('Sending status notification:', { orderId, status });
  }

  /**
   * Send delivery notification (mock implementation)
   */
  private async sendDeliveryNotification(
    trackingNumber: string,
    update: Omit<CODDeliveryUpdate, 'trackingNumber'>
  ): Promise<void> {
    // In a real implementation, this would send SMS/email notifications
    console.log('Sending delivery notification:', { trackingNumber, update });
  }

  /**
   * Send cancellation notification (mock implementation)
   */
  private async sendCancellationNotification(
    orderId: string,
    reason: string
  ): Promise<void> {
    // In a real implementation, this would send SMS/email notifications
    console.log('Sending cancellation notification:', { orderId, reason });
  }

  /**
   * Get configuration (for debugging)
   */
  getConfig(): CODConfig {
    return { ...this.config };
  }

  /**
   * Test configuration
   */
  async testConfiguration(): Promise<{ success: boolean; error?: string }> {
    try {
      const validation = CODUtils.validateConfig(this.config);
      return {
        success: validation.isValid,
        error: validation.isValid ? undefined : validation.errors.join(', ')
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Configuration test failed'
      };
    }
  }
}
