'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Truck, 
  MapPin, 
  Phone, 
  Clock, 
  DollarSign,
  AlertCircle, 
  CheckCircle,
  Loader2,
  Package
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { CODCheckoutProps, CODDeliveryAddress } from '../types';
import { CODUtils } from '../utils';
import { toast } from 'sonner';

export function CODCheckout({
  orderId,
  amount,
  currency = 'ZAR',
  description,
  customerEmail,
  customerName,
  onSuccess,
  onError,
  onCancel,
  className = '',
  disabled = false
}: CODCheckoutProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'address' | 'confirm' | 'success'>('address');
  const [formData, setFormData] = useState({
    customerPhone: '',
    deliveryAddress: {
      street: '',
      city: '',
      province: '',
      postalCode: '',
      country: 'South Africa',
      landmark: ''
    } as CODDeliveryAddress,
    specialInstructions: '',
    preferredDeliveryTime: ''
  });
  const [deliveryEstimate, setDeliveryEstimate] = useState<any>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Mock COD config
  const codConfig = {
    enabled: true,
    maxAmount: 5000,
    minAmount: 50,
    deliveryFee: 50,
    deliveryFeeType: 'fixed' as const,
    supportedAreas: ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria'],
    estimatedDeliveryDays: 3,
    requiresPhoneVerification: true,
    requiresAddressVerification: true
  };

  // Calculate delivery estimate when address changes
  useEffect(() => {
    if (formData.deliveryAddress.city && formData.deliveryAddress.postalCode) {
      const estimate = CODUtils.getDeliveryEstimate(
        formData.deliveryAddress,
        amount,
        codConfig
      );
      setDeliveryEstimate(estimate);
    }
  }, [formData.deliveryAddress, amount]);

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Validate phone
    const phoneValidation = CODUtils.validatePhoneNumber(formData.customerPhone);
    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.errors[0];
    }

    // Validate address
    const addressValidation = CODUtils.validateDeliveryAddress(formData.deliveryAddress);
    if (!addressValidation.isValid) {
      errors.address = addressValidation.errors[0];
    }

    // Validate amount
    const amountValidation = CODUtils.validateAmount(amount, codConfig);
    if (!amountValidation.isValid) {
      errors.amount = amountValidation.errors[0];
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    if (!deliveryEstimate?.isAvailable) {
      toast.error('Delivery not available in your area');
      return;
    }

    setIsLoading(true);

    try {
      // Create COD payment
      const response = await fetch('/api/payment/cod/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          orderId,
          amount,
          currency,
          description,
          customerName,
          customerEmail,
          customerPhone: formData.customerPhone,
          deliveryAddress: formData.deliveryAddress,
          specialInstructions: formData.specialInstructions,
          preferredDeliveryTime: formData.preferredDeliveryTime
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setStep('success');
        onSuccess?.({
          orderId,
          status: 'pending',
          paymentMethod: 'cod',
          amount,
          deliveryFee: result.deliveryFee,
          totalAmount: result.totalAmount,
          currency,
          trackingNumber: result.trackingNumber,
          estimatedDeliveryDate: new Date(result.estimatedDeliveryDate)
        });
        toast.success('COD order placed successfully!');
      } else {
        throw new Error(result.error || 'Failed to place COD order');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'COD order failed';
      onError?.(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('deliveryAddress.')) {
      const addressField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        deliveryAddress: {
          ...prev.deliveryAddress,
          [addressField]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      <AnimatePresence mode="wait">
        {step === 'address' && (
          <motion.div
            key="address-form"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Truck className="h-6 w-6 text-green-600" />
                  Cash on Delivery
                </CardTitle>
                <p className="text-gray-600">Pay when your order is delivered to your door</p>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Amount Display */}
                <div className="text-center p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                  <p className="text-2xl font-bold text-gray-900 mb-1">
                    {currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600">{description}</p>
                  {deliveryEstimate && (
                    <div className="mt-2 text-sm">
                      <span className="text-gray-600">+ Delivery: </span>
                      <span className="font-semibold">
                        {currency === 'ZAR' ? 'R' : currency} {deliveryEstimate.deliveryFee.toFixed(2)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Phone Number */}
                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.customerPhone}
                    onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                    placeholder="+27 123 456 789"
                    className={validationErrors.phone ? 'border-red-500' : ''}
                  />
                  {validationErrors.phone && (
                    <p className="text-sm text-red-600">{validationErrors.phone}</p>
                  )}
                </div>

                {/* Delivery Address */}
                <div className="space-y-4">
                  <Label className="flex items-center gap-2 text-base font-semibold">
                    <MapPin className="h-4 w-4" />
                    Delivery Address
                  </Label>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <Label htmlFor="street">Street Address</Label>
                      <Input
                        id="street"
                        value={formData.deliveryAddress.street}
                        onChange={(e) => handleInputChange('deliveryAddress.street', e.target.value)}
                        placeholder="123 Main Street"
                        className={validationErrors.address ? 'border-red-500' : ''}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={formData.deliveryAddress.city}
                        onChange={(e) => handleInputChange('deliveryAddress.city', e.target.value)}
                        placeholder="Cape Town"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="province">Province</Label>
                      <Input
                        id="province"
                        value={formData.deliveryAddress.province}
                        onChange={(e) => handleInputChange('deliveryAddress.province', e.target.value)}
                        placeholder="Western Cape"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="postalCode">Postal Code</Label>
                      <Input
                        id="postalCode"
                        value={formData.deliveryAddress.postalCode}
                        onChange={(e) => handleInputChange('deliveryAddress.postalCode', e.target.value)}
                        placeholder="8000"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="landmark">Landmark (Optional)</Label>
                      <Input
                        id="landmark"
                        value={formData.deliveryAddress.landmark}
                        onChange={(e) => handleInputChange('deliveryAddress.landmark', e.target.value)}
                        placeholder="Near shopping mall"
                      />
                    </div>
                  </div>
                  
                  {validationErrors.address && (
                    <p className="text-sm text-red-600">{validationErrors.address}</p>
                  )}
                </div>

                {/* Special Instructions */}
                <div className="space-y-2">
                  <Label htmlFor="instructions">Special Delivery Instructions (Optional)</Label>
                  <Textarea
                    id="instructions"
                    value={formData.specialInstructions}
                    onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                    placeholder="Ring doorbell, leave at gate, etc."
                    rows={3}
                  />
                </div>

                {/* Delivery Estimate */}
                {deliveryEstimate && (
                  <div className={`p-4 rounded-lg border ${
                    deliveryEstimate.isAvailable 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <Clock className={`h-4 w-4 ${
                        deliveryEstimate.isAvailable ? 'text-green-600' : 'text-red-600'
                      }`} />
                      <span className="font-semibold">
                        {deliveryEstimate.isAvailable ? 'Delivery Available' : 'Delivery Not Available'}
                      </span>
                    </div>
                    
                    {deliveryEstimate.isAvailable ? (
                      <div className="space-y-1 text-sm">
                        <p>Estimated delivery: {deliveryEstimate.estimatedDays} business days</p>
                        <p>Delivery date: {deliveryEstimate.estimatedDate.toLocaleDateString()}</p>
                        <p>Delivery fee: {currency === 'ZAR' ? 'R' : currency} {deliveryEstimate.deliveryFee.toFixed(2)}</p>
                      </div>
                    ) : (
                      <p className="text-sm text-red-600">{deliveryEstimate.reason}</p>
                    )}
                  </div>
                )}

                {/* Error Display */}
                {validationErrors.amount && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {validationErrors.amount}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button
                    onClick={() => setStep('confirm')}
                    disabled={disabled || isLoading || !deliveryEstimate?.isAvailable}
                    className="flex-1 h-12 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                  >
                    Continue to Confirmation
                  </Button>

                  {onCancel && (
                    <Button
                      variant="outline"
                      onClick={onCancel}
                      disabled={isLoading}
                      className="px-6"
                    >
                      Cancel
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {step === 'confirm' && (
          <motion.div
            key="confirmation"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  Confirm Your Order
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Order Summary */}
                <div className="p-4 bg-gray-50 rounded-lg space-y-3">
                  <h3 className="font-semibold">Order Summary</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Order Amount:</span>
                      <span>{currency === 'ZAR' ? 'R' : currency} {amount.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Delivery Fee:</span>
                      <span>{currency === 'ZAR' ? 'R' : currency} {deliveryEstimate?.deliveryFee.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-semibold border-t pt-2">
                      <span>Total Amount:</span>
                      <span>{currency === 'ZAR' ? 'R' : currency} {(amount + (deliveryEstimate?.deliveryFee || 0)).toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Delivery Details */}
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold mb-2">Delivery Details</h3>
                  <div className="space-y-1 text-sm">
                    <p><strong>Address:</strong> {CODUtils.formatAddress(formData.deliveryAddress)}</p>
                    <p><strong>Phone:</strong> {formData.customerPhone}</p>
                    <p><strong>Estimated Delivery:</strong> {deliveryEstimate?.estimatedDate.toLocaleDateString()}</p>
                    {formData.specialInstructions && (
                      <p><strong>Instructions:</strong> {formData.specialInstructions}</p>
                    )}
                  </div>
                </div>

                {/* COD Notice */}
                <Alert className="border-green-200 bg-green-50">
                  <Package className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    <strong>Cash on Delivery:</strong> You will pay the total amount in cash when your order is delivered to your address.
                  </AlertDescription>
                </Alert>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={handleSubmit}
                    disabled={disabled || isLoading}
                    className="w-full h-12 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Placing Order...
                      </>
                    ) : (
                      <>
                        Place COD Order
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => setStep('address')}
                    disabled={isLoading}
                    className="w-full"
                  >
                    Back to Address
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {step === 'success' && (
          <motion.div
            key="success"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-0 bg-gradient-to-br from-green-50 to-blue-50">
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Placed Successfully!</h2>
                <p className="text-gray-600 mb-4">
                  Your Cash on Delivery order has been confirmed. You will receive SMS updates about your delivery.
                </p>
                <Badge className="bg-green-100 text-green-800">
                  Order ID: {orderId}
                </Badge>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
