// Cash on Delivery Module - Client-side exports only
'use client';

// Components (Client-side only)
export { CODCheckout } from './components/CODCheckout';

// Store (Client-side only)
export {
  default as codReducer,
  createCODPayment,
  fetchCODPaymentHistory,
  checkCODPaymentStatus,
  updateCODPaymentStatus,
  fetchCODTrackingInfo,
  cancelCODPayment,
  setLoading,
  setCurrentPayment,
  addPaymentToHistory,
  updatePaymentStatus,
  addDeliveryUpdate,
  setError,
  setConfig,
  clearPaymentData,
  clearError,
  removePaymentFromHistory,
  selectCODState,
  selectCODLoading,
  selectCODCurrentPayment,
  selectCODPaymentHistory,
  selectCODDeliveryUpdates,
  selectCODError,
  selectCODConfig
} from './store/codSlice';

// Types (Safe for both client and server)
export * from './types';
