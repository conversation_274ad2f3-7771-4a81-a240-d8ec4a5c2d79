// Cash on Delivery Module Types
export interface CODConfig {
  enabled: boolean;
  maxAmount: number;
  minAmount: number;
  deliveryFee: number;
  deliveryFeeType: 'fixed' | 'percentage';
  supportedAreas: string[];
  estimatedDeliveryDays: number;
  requiresPhoneVerification: boolean;
  requiresAddressVerification: boolean;
}

export interface CODPaymentData {
  orderId: string;
  amount: number;
  deliveryFee: number;
  totalAmount: number;
  currency: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  deliveryAddress: CODDeliveryAddress;
  specialInstructions?: string;
  preferredDeliveryTime?: string;
  estimatedDeliveryDate: Date;
}

export interface CODDeliveryAddress {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  landmark?: string;
  gpsCoordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface CODCreatePaymentRequest {
  orderId: string;
  amount: number;
  currency?: string;
  description: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  deliveryAddress: CODDeliveryAddress;
  specialInstructions?: string;
  preferredDeliveryTime?: string;
  customData?: Record<string, any>;
}

export interface CODCreatePaymentResponse {
  success: boolean;
  paymentId?: string;
  deliveryFee?: number;
  totalAmount?: number;
  estimatedDeliveryDate?: Date;
  trackingNumber?: string;
  error?: string;
}

export interface CODPaymentStatus {
  orderId: string;
  status: 'pending' | 'confirmed' | 'out_for_delivery' | 'delivered' | 'failed' | 'cancelled';
  paymentId?: string;
  trackingNumber?: string;
  amount?: number;
  deliveryFee?: number;
  totalAmount?: number;
  currency?: string;
  paymentMethod: 'cod';
  customerName?: string;
  customerPhone?: string;
  deliveryAddress?: CODDeliveryAddress;
  estimatedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  deliveryNotes?: string;
  failureReason?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CODDeliveryUpdate {
  trackingNumber: string;
  status: 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'failed';
  location?: string;
  notes?: string;
  timestamp: Date;
  deliveryAgent?: {
    name: string;
    phone: string;
    vehicleNumber?: string;
  };
}

// Store types for Redux
export interface CODState {
  isLoading: boolean;
  currentPayment: CODCreatePaymentResponse | null;
  paymentHistory: CODPaymentStatus[];
  deliveryUpdates: Record<string, CODDeliveryUpdate[]>;
  error: string | null;
  config: CODConfig | null;
}

export interface CODActions {
  setLoading: (loading: boolean) => void;
  setCurrentPayment: (payment: CODCreatePaymentResponse | null) => void;
  addPaymentToHistory: (payment: CODPaymentStatus) => void;
  updatePaymentStatus: (orderId: string, status: CODPaymentStatus) => void;
  addDeliveryUpdate: (trackingNumber: string, update: CODDeliveryUpdate) => void;
  setError: (error: string | null) => void;
  setConfig: (config: CODConfig) => void;
  clearPaymentData: () => void;
}

// Component Props
export interface CODCheckoutProps {
  orderId: string;
  amount: number;
  currency?: string;
  description: string;
  customerEmail: string;
  customerName: string;
  onSuccess?: (result: CODPaymentStatus) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
  disabled?: boolean;
}

export interface CODDeliveryFormProps {
  onAddressChange: (address: CODDeliveryAddress) => void;
  onPhoneChange: (phone: string) => void;
  onInstructionsChange: (instructions: string) => void;
  initialAddress?: Partial<CODDeliveryAddress>;
  initialPhone?: string;
  initialInstructions?: string;
  className?: string;
}

export interface CODTrackingProps {
  trackingNumber: string;
  onStatusUpdate?: (update: CODDeliveryUpdate) => void;
  showFullHistory?: boolean;
  className?: string;
}

export interface CODHistoryProps {
  userId?: string;
  limit?: number;
  showFilters?: boolean;
  onPaymentSelect?: (payment: CODPaymentStatus) => void;
}

// Utility types
export interface CODValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface CODDeliveryEstimate {
  estimatedDays: number;
  estimatedDate: Date;
  deliveryFee: number;
  isAvailable: boolean;
  reason?: string;
}

// Error types
export class CODError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'CODError';
  }
}

export class CODValidationError extends CODError {
  constructor(message: string, public field?: string) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'CODValidationError';
  }
}

export class CODDeliveryError extends CODError {
  constructor(message: string, public area?: string) {
    super(message, 'DELIVERY_ERROR');
    this.name = 'CODDeliveryError';
  }
}

// Configuration types
export interface CODAreaConfig {
  name: string;
  postalCodes: string[];
  deliveryFee: number;
  estimatedDays: number;
  enabled: boolean;
}

export interface CODDeliverySlot {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  available: boolean;
  additionalFee?: number;
}

// Analytics types
export interface CODAnalytics {
  totalOrders: number;
  totalAmount: number;
  averageOrderValue: number;
  deliverySuccessRate: number;
  averageDeliveryTime: number;
  topDeliveryAreas: Array<{
    area: string;
    orderCount: number;
    totalAmount: number;
  }>;
  statusBreakdown: Record<string, number>;
  monthlyTrends: Array<{
    month: string;
    orders: number;
    amount: number;
    successRate: number;
  }>;
}
