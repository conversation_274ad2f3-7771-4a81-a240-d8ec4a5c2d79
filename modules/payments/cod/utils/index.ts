import {
  CODValidationResult,
  CODValidationError,
  CODDeliveryError,
  CODConfig,
  CODDeliveryAddress,
  CODDeliveryEstimate,
  CODAreaConfig
} from '../types';

/**
 * Cash on Delivery Utility Functions
 */
export class CODUtils {
  /**
   * Validate payment amount
   */
  static validateAmount(amount: number, config: CODConfig): CODValidationResult {
    const errors: string[] = [];

    if (typeof amount !== 'number' || isNaN(amount)) {
      errors.push('Amount must be a valid number');
    } else if (amount <= 0) {
      errors.push('Amount must be greater than 0');
    } else if (amount < config.minAmount) {
      errors.push(`Minimum amount for COD is R${config.minAmount.toFixed(2)}`);
    } else if (amount > config.maxAmount) {
      errors.push(`Maximum amount for COD is R${config.maxAmount.toFixed(2)}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate phone number (South African format)
   */
  static validatePhoneNumber(phone: string): CODValidationResult {
    const errors: string[] = [];

    if (!phone || typeof phone !== 'string') {
      errors.push('Phone number is required');
    } else {
      // Remove all non-digit characters
      const cleanPhone = phone.replace(/\D/g, '');
      
      // Check South African phone number formats
      const saPhoneRegex = /^(?:\+27|27|0)([1-9]\d{8})$/;
      
      if (!saPhoneRegex.test(cleanPhone)) {
        errors.push('Invalid South African phone number format');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate delivery address
   */
  static validateDeliveryAddress(address: CODDeliveryAddress): CODValidationResult {
    const errors: string[] = [];

    if (!address.street || address.street.trim().length < 5) {
      errors.push('Street address must be at least 5 characters');
    }

    if (!address.city || address.city.trim().length < 2) {
      errors.push('City is required');
    }

    if (!address.province || address.province.trim().length < 2) {
      errors.push('Province is required');
    }

    if (!address.postalCode) {
      errors.push('Postal code is required');
    } else if (!/^\d{4}$/.test(address.postalCode)) {
      errors.push('Postal code must be 4 digits');
    }

    if (!address.country) {
      errors.push('Country is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate COD configuration
   */
  static validateConfig(config: CODConfig): CODValidationResult {
    const errors: string[] = [];

    if (config.maxAmount <= 0) {
      errors.push('Maximum amount must be greater than 0');
    }

    if (config.minAmount < 0) {
      errors.push('Minimum amount cannot be negative');
    }

    if (config.minAmount >= config.maxAmount) {
      errors.push('Minimum amount must be less than maximum amount');
    }

    if (config.deliveryFee < 0) {
      errors.push('Delivery fee cannot be negative');
    }

    if (config.estimatedDeliveryDays <= 0) {
      errors.push('Estimated delivery days must be greater than 0');
    }

    if (!config.supportedAreas || config.supportedAreas.length === 0) {
      errors.push('At least one supported area must be specified');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Format phone number to South African standard
   */
  static formatPhoneNumber(phone: string): string {
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Convert to +27 format
    if (cleanPhone.startsWith('0')) {
      return `+27${cleanPhone.substring(1)}`;
    } else if (cleanPhone.startsWith('27')) {
      return `+${cleanPhone}`;
    } else if (cleanPhone.startsWith('+27')) {
      return cleanPhone;
    }
    
    return `+27${cleanPhone}`;
  }

  /**
   * Calculate delivery fee
   */
  static calculateDeliveryFee(
    amount: number, 
    config: CODConfig, 
    area?: string
  ): number {
    if (config.deliveryFeeType === 'percentage') {
      return (amount * config.deliveryFee) / 100;
    }
    
    return config.deliveryFee;
  }

  /**
   * Check if delivery is available in area
   */
  static isDeliveryAvailable(
    address: CODDeliveryAddress, 
    config: CODConfig
  ): boolean {
    // Check if postal code is in supported areas
    return config.supportedAreas.some(area => 
      address.postalCode.startsWith(area) || 
      address.city.toLowerCase().includes(area.toLowerCase()) ||
      address.province.toLowerCase().includes(area.toLowerCase())
    );
  }

  /**
   * Get delivery estimate
   */
  static getDeliveryEstimate(
    address: CODDeliveryAddress,
    amount: number,
    config: CODConfig
  ): CODDeliveryEstimate {
    const isAvailable = this.isDeliveryAvailable(address, config);
    
    if (!isAvailable) {
      return {
        estimatedDays: 0,
        estimatedDate: new Date(),
        deliveryFee: 0,
        isAvailable: false,
        reason: 'Delivery not available in this area'
      };
    }

    const deliveryFee = this.calculateDeliveryFee(amount, config, address.city);
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + config.estimatedDeliveryDays);

    return {
      estimatedDays: config.estimatedDeliveryDays,
      estimatedDate,
      deliveryFee,
      isAvailable: true
    };
  }

  /**
   * Generate tracking number
   */
  static generateTrackingNumber(prefix: string = 'COD'): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}${timestamp.slice(-6)}${random}`;
  }

  /**
   * Format delivery address for display
   */
  static formatAddress(address: CODDeliveryAddress): string {
    const parts = [
      address.street,
      address.city,
      address.province,
      address.postalCode,
      address.country
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Get default supported areas for South Africa
   */
  static getDefaultSupportedAreas(): CODAreaConfig[] {
    return [
      {
        name: 'Cape Town',
        postalCodes: ['7000', '7001', '7002', '7700', '7800'],
        deliveryFee: 50,
        estimatedDays: 2,
        enabled: true
      },
      {
        name: 'Johannesburg',
        postalCodes: ['2000', '2001', '2002', '2100', '2200'],
        deliveryFee: 60,
        estimatedDays: 2,
        enabled: true
      },
      {
        name: 'Durban',
        postalCodes: ['4000', '4001', '4002', '4100', '4200'],
        deliveryFee: 55,
        estimatedDays: 3,
        enabled: true
      },
      {
        name: 'Pretoria',
        postalCodes: ['0001', '0002', '0100', '0200'],
        deliveryFee: 50,
        estimatedDays: 2,
        enabled: true
      },
      {
        name: 'Port Elizabeth',
        postalCodes: ['6000', '6001', '6002'],
        deliveryFee: 70,
        estimatedDays: 4,
        enabled: true
      }
    ];
  }

  /**
   * Validate order for COD eligibility
   */
  static validateCODEligibility(
    amount: number,
    address: CODDeliveryAddress,
    config: CODConfig
  ): CODValidationResult {
    const errors: string[] = [];

    // Check if COD is enabled
    if (!config.enabled) {
      errors.push('Cash on Delivery is currently not available');
    }

    // Validate amount
    const amountValidation = this.validateAmount(amount, config);
    if (!amountValidation.isValid) {
      errors.push(...amountValidation.errors);
    }

    // Validate address
    const addressValidation = this.validateDeliveryAddress(address);
    if (!addressValidation.isValid) {
      errors.push(...addressValidation.errors);
    }

    // Check delivery availability
    if (!this.isDeliveryAvailable(address, config)) {
      errors.push('Delivery not available in your area');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get status display information
   */
  static getStatusDisplay(status: string): {
    label: string;
    color: string;
    icon: string;
    description: string;
  } {
    switch (status) {
      case 'pending':
        return {
          label: 'Order Placed',
          color: 'yellow',
          icon: '📋',
          description: 'Your order has been placed and is being processed'
        };
      case 'confirmed':
        return {
          label: 'Confirmed',
          color: 'blue',
          icon: '✅',
          description: 'Your order has been confirmed and is being prepared'
        };
      case 'out_for_delivery':
        return {
          label: 'Out for Delivery',
          color: 'orange',
          icon: '🚚',
          description: 'Your order is on the way to your address'
        };
      case 'delivered':
        return {
          label: 'Delivered',
          color: 'green',
          icon: '📦',
          description: 'Your order has been successfully delivered'
        };
      case 'failed':
        return {
          label: 'Delivery Failed',
          color: 'red',
          icon: '❌',
          description: 'Delivery attempt failed. We will contact you to reschedule'
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          color: 'gray',
          icon: '🚫',
          description: 'Your order has been cancelled'
        };
      default:
        return {
          label: 'Unknown',
          color: 'gray',
          icon: '❓',
          description: 'Status unknown'
        };
    }
  }

  /**
   * Calculate estimated delivery date
   */
  static calculateDeliveryDate(
    orderDate: Date,
    estimatedDays: number,
    excludeWeekends: boolean = true
  ): Date {
    const deliveryDate = new Date(orderDate);
    let daysAdded = 0;

    while (daysAdded < estimatedDays) {
      deliveryDate.setDate(deliveryDate.getDate() + 1);
      
      if (excludeWeekends) {
        const dayOfWeek = deliveryDate.getDay();
        // Skip weekends (Saturday = 6, Sunday = 0)
        if (dayOfWeek !== 0 && dayOfWeek !== 6) {
          daysAdded++;
        }
      } else {
        daysAdded++;
      }
    }

    return deliveryDate;
  }

  /**
   * Check if delivery date is valid (not in the past, not on weekends)
   */
  static isValidDeliveryDate(date: Date): boolean {
    const now = new Date();
    const dayOfWeek = date.getDay();
    
    // Check if date is in the future
    if (date <= now) {
      return false;
    }

    // Check if it's not a weekend
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return false;
    }

    return true;
  }

  /**
   * Format currency amount
   */
  static formatAmount(amount: number, currency: string = 'ZAR'): string {
    if (currency === 'ZAR') {
      return `R${amount.toFixed(2)}`;
    }
    return `${currency} ${amount.toFixed(2)}`;
  }

  /**
   * Debug COD data (development only)
   */
  static debugCODData(data: any): {
    originalData: any;
    validationResults: Record<string, CODValidationResult>;
    deliveryEstimate?: CODDeliveryEstimate;
  } {
    const validationResults: Record<string, CODValidationResult> = {};

    if (data.amount && data.config) {
      validationResults.amount = this.validateAmount(data.amount, data.config);
    }

    if (data.phone) {
      validationResults.phone = this.validatePhoneNumber(data.phone);
    }

    if (data.address) {
      validationResults.address = this.validateDeliveryAddress(data.address);
    }

    let deliveryEstimate;
    if (data.address && data.amount && data.config) {
      deliveryEstimate = this.getDeliveryEstimate(data.address, data.amount, data.config);
    }

    return {
      originalData: data,
      validationResults,
      deliveryEstimate
    };
  }
}
