import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  CODState,
  CODCreatePaymentRequest,
  CODCreatePaymentResponse,
  CODPaymentStatus,
  CODDeliveryUpdate,
  CODConfig
} from '../types';

// Initial state
const initialState: CODState = {
  isLoading: false,
  currentPayment: null,
  paymentHistory: [],
  deliveryUpdates: {},
  error: null,
  config: null
};

// Async thunks
export const createCODPayment = createAsyncThunk(
  'cod/createPayment',
  async (request: CODCreatePaymentRequest, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/payment/cod/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'COD payment creation failed');
      }

      return data as CODCreatePaymentResponse;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchCODPaymentHistory = createAsyncThunk(
  'cod/fetchPaymentHistory',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/cod/history?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to fetch COD payment history');
      }

      return data.payments as CODPaymentStatus[];
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const checkCODPaymentStatus = createAsyncThunk(
  'cod/checkPaymentStatus',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/cod/status/${orderId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to check COD payment status');
      }

      return data.status as CODPaymentStatus;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const updateCODPaymentStatus = createAsyncThunk(
  'cod/updatePaymentStatus',
  async (params: { orderId: string; status: CODPaymentStatus['status']; notes?: string }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/cod/status/${params.orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          status: params.status,
          notes: params.notes
        })
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to update COD payment status');
      }

      return { orderId: params.orderId, status: params.status, notes: params.notes };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const fetchCODTrackingInfo = createAsyncThunk(
  'cod/fetchTrackingInfo',
  async (trackingNumber: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/cod/tracking/${trackingNumber}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to fetch tracking info');
      }

      return { trackingNumber, updates: data.updates as CODDeliveryUpdate[] };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

export const cancelCODPayment = createAsyncThunk(
  'cod/cancelPayment',
  async (params: { orderId: string; reason: string }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/payment/cod/cancel/${params.orderId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ reason: params.reason })
      });

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue(data.error || 'Failed to cancel COD payment');
      }

      return { orderId: params.orderId, reason: params.reason };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Network error'
      );
    }
  }
);

// Slice
const codSlice = createSlice({
  name: 'cod',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setCurrentPayment: (state, action: PayloadAction<CODCreatePaymentResponse | null>) => {
      state.currentPayment = action.payload;
    },
    addPaymentToHistory: (state, action: PayloadAction<CODPaymentStatus>) => {
      const existingIndex = state.paymentHistory.findIndex(
        payment => payment.orderId === action.payload.orderId
      );
      
      if (existingIndex >= 0) {
        state.paymentHistory[existingIndex] = action.payload;
      } else {
        state.paymentHistory.unshift(action.payload);
      }
    },
    updatePaymentStatus: (state, action: PayloadAction<{ orderId: string; status: CODPaymentStatus }>) => {
      const payment = state.paymentHistory.find(p => p.orderId === action.payload.orderId);
      if (payment) {
        Object.assign(payment, action.payload.status);
      }
    },
    addDeliveryUpdate: (state, action: PayloadAction<{ trackingNumber: string; update: CODDeliveryUpdate }>) => {
      const { trackingNumber, update } = action.payload;
      if (!state.deliveryUpdates[trackingNumber]) {
        state.deliveryUpdates[trackingNumber] = [];
      }
      state.deliveryUpdates[trackingNumber].push(update);
      
      // Sort by timestamp (newest first)
      state.deliveryUpdates[trackingNumber].sort(
        (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setConfig: (state, action: PayloadAction<CODConfig>) => {
      state.config = action.payload;
    },
    clearPaymentData: (state) => {
      state.currentPayment = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    removePaymentFromHistory: (state, action: PayloadAction<string>) => {
      state.paymentHistory = state.paymentHistory.filter(
        payment => payment.orderId !== action.payload
      );
    }
  },
  extraReducers: (builder) => {
    // Create Payment
    builder
      .addCase(createCODPayment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createCODPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPayment = action.payload;
        state.error = null;
      })
      .addCase(createCODPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.currentPayment = null;
      });

    // Fetch Payment History
    builder
      .addCase(fetchCODPaymentHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCODPaymentHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.paymentHistory = action.payload;
        state.error = null;
      })
      .addCase(fetchCODPaymentHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Check Payment Status
    builder
      .addCase(checkCODPaymentStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkCODPaymentStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        
        // Update payment in history
        const existingIndex = state.paymentHistory.findIndex(
          payment => payment.orderId === action.payload.orderId
        );
        
        if (existingIndex >= 0) {
          state.paymentHistory[existingIndex] = action.payload;
        } else {
          state.paymentHistory.unshift(action.payload);
        }
        
        state.error = null;
      })
      .addCase(checkCODPaymentStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update Payment Status
    builder
      .addCase(updateCODPaymentStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateCODPaymentStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        
        // Update payment status in history
        const payment = state.paymentHistory.find(p => p.orderId === action.payload.orderId);
        if (payment) {
          payment.status = action.payload.status;
          payment.updatedAt = new Date();
          if (action.payload.notes) {
            payment.deliveryNotes = action.payload.notes;
          }
        }
        
        state.error = null;
      })
      .addCase(updateCODPaymentStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Tracking Info
    builder
      .addCase(fetchCODTrackingInfo.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCODTrackingInfo.fulfilled, (state, action) => {
        state.isLoading = false;
        state.deliveryUpdates[action.payload.trackingNumber] = action.payload.updates;
        state.error = null;
      })
      .addCase(fetchCODTrackingInfo.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Cancel Payment
    builder
      .addCase(cancelCODPayment.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(cancelCODPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        
        // Update payment status to cancelled
        const payment = state.paymentHistory.find(p => p.orderId === action.payload.orderId);
        if (payment) {
          payment.status = 'cancelled';
          payment.failureReason = action.payload.reason;
          payment.updatedAt = new Date();
        }
        
        state.error = null;
      })
      .addCase(cancelCODPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }
});

// Actions
export const {
  setLoading,
  setCurrentPayment,
  addPaymentToHistory,
  updatePaymentStatus,
  addDeliveryUpdate,
  setError,
  setConfig,
  clearPaymentData,
  clearError,
  removePaymentFromHistory
} = codSlice.actions;

// Selectors
export const selectCODState = (state: { cod: CODState }) => state.cod;
export const selectCODLoading = (state: { cod: CODState }) => state.cod.isLoading;
export const selectCODCurrentPayment = (state: { cod: CODState }) => state.cod.currentPayment;
export const selectCODPaymentHistory = (state: { cod: CODState }) => state.cod.paymentHistory;
export const selectCODDeliveryUpdates = (state: { cod: CODState }) => state.cod.deliveryUpdates;
export const selectCODError = (state: { cod: CODState }) => state.cod.error;
export const selectCODConfig = (state: { cod: CODState }) => state.cod.config;

// Reducer
export default codSlice.reducer;
