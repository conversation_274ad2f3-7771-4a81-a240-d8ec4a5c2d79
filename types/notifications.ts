// types/notifications.ts
import { Types } from 'mongoose';

// Notification types
export type NotificationType = 
  | 'order_confirmation'
  | 'order_status_update'
  | 'payment_confirmation'
  | 'payment_failed'
  | 'group_milestone'
  | 'group_invitation'
  | 'group_order_finalized'
  | 'shipping_update'
  | 'delivery_confirmation'
  | 'discount_available'
  | 'cart_reminder'
  | 'system_announcement'
  | 'account_update'
  | 'security_alert';

// Notification priority levels
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

// Notification delivery channels
export type DeliveryChannel = 'in_app' | 'email' | 'sms' | 'push' | 'webhook';

// Notification status
export type NotificationStatus = 'pending' | 'sent' | 'delivered' | 'read' | 'failed' | 'expired';

// Base notification interface
export interface Notification {
  _id: string;
  userId: string;
  type: NotificationType;
  priority: NotificationPriority;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels: DeliveryChannel[];
  status: NotificationStatus;
  readAt?: Date;
  deliveredAt?: Date;
  expiresAt?: Date;
  actionUrl?: string;
  actionText?: string;
  imageUrl?: string;
  groupId?: string;
  orderId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Database model interface
export interface INotification extends Notification {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  groupId?: Types.ObjectId;
  orderId?: Types.ObjectId;
}

// Notification template interface
export interface NotificationTemplate {
  id: string;
  type: NotificationType;
  name: string;
  description: string;
  title: string;
  message: string;
  defaultChannels: DeliveryChannel[];
  defaultPriority: NotificationPriority;
  variables: Array<{
    name: string;
    description: string;
    required: boolean;
    type: 'string' | 'number' | 'date' | 'boolean';
  }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// User notification preferences
export interface NotificationPreferences {
  userId: string;
  preferences: Record<NotificationType, {
    enabled: boolean;
    channels: DeliveryChannel[];
    priority: NotificationPriority;
    quietHours?: {
      enabled: boolean;
      start: string; // HH:mm format
      end: string; // HH:mm format
      timezone: string;
    };
  }>;
  globalSettings: {
    enabled: boolean;
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
      timezone: string;
    };
    emailDigest: {
      enabled: boolean;
      frequency: 'daily' | 'weekly' | 'monthly';
      time: string; // HH:mm format
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

// Email notification data
export interface EmailNotificationData {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  templateId?: string;
  variables?: Record<string, any>;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
}

// SMS notification data
export interface SMSNotificationData {
  to: string;
  message: string;
  templateId?: string;
  variables?: Record<string, any>;
}

// Push notification data
export interface PushNotificationData {
  title: string;
  body: string;
  icon?: string;
  image?: string;
  badge?: number;
  sound?: string;
  clickAction?: string;
  data?: Record<string, any>;
  deviceTokens: string[];
}

// Webhook notification data
export interface WebhookNotificationData {
  url: string;
  method: 'POST' | 'PUT' | 'PATCH';
  headers?: Record<string, string>;
  payload: Record<string, any>;
  retryAttempts: number;
  timeout: number;
}

// Notification delivery result
export interface DeliveryResult {
  channel: DeliveryChannel;
  status: 'success' | 'failed' | 'pending';
  deliveredAt?: Date;
  error?: string;
  externalId?: string;
  metadata?: Record<string, any>;
}

// Bulk notification request
export interface BulkNotificationRequest {
  userIds: string[];
  template: string;
  variables?: Record<string, any>;
  channels?: DeliveryChannel[];
  priority?: NotificationPriority;
  scheduleAt?: Date;
  expiresAt?: Date;
}

// Notification analytics
export interface NotificationAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalRead: number;
  totalFailed: number;
  deliveryRate: number;
  readRate: number;
  failureRate: number;
  channelBreakdown: Record<DeliveryChannel, {
    sent: number;
    delivered: number;
    failed: number;
    deliveryRate: number;
  }>;
  typeBreakdown: Record<NotificationType, {
    sent: number;
    delivered: number;
    read: number;
    readRate: number;
  }>;
  hourlyTrends: Array<{
    hour: number;
    sent: number;
    delivered: number;
    read: number;
  }>;
  topPerformingTemplates: Array<{
    templateId: string;
    name: string;
    sent: number;
    readRate: number;
  }>;
}

// Notification queue item
export interface NotificationQueueItem {
  id: string;
  notificationId: string;
  channel: DeliveryChannel;
  priority: NotificationPriority;
  scheduledAt: Date;
  attempts: number;
  maxAttempts: number;
  lastAttemptAt?: Date;
  nextAttemptAt?: Date;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

// API request/response types
export interface CreateNotificationRequest {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: DeliveryChannel[];
  priority?: NotificationPriority;
  actionUrl?: string;
  actionText?: string;
  imageUrl?: string;
  groupId?: string;
  orderId?: string;
  scheduleAt?: Date;
  expiresAt?: Date;
}

export interface CreateNotificationResponse {
  success: boolean;
  notification?: Notification;
  error?: string;
}

export interface GetNotificationsRequest {
  userId: string;
  type?: NotificationType;
  status?: NotificationStatus;
  limit?: number;
  offset?: number;
  unreadOnly?: boolean;
}

export interface GetNotificationsResponse {
  notifications: Notification[];
  totalCount: number;
  unreadCount: number;
  hasMore: boolean;
}

export interface MarkAsReadRequest {
  notificationIds: string[];
}

export interface MarkAsReadResponse {
  success: boolean;
  updatedCount: number;
  error?: string;
}

export interface UpdatePreferencesRequest {
  userId: string;
  preferences: Partial<NotificationPreferences['preferences']>;
  globalSettings?: Partial<NotificationPreferences['globalSettings']>;
}

export interface UpdatePreferencesResponse {
  success: boolean;
  preferences?: NotificationPreferences;
  error?: string;
}

export interface SendBulkNotificationRequest extends BulkNotificationRequest {}

export interface SendBulkNotificationResponse {
  success: boolean;
  batchId?: string;
  queuedCount?: number;
  error?: string;
}

// Notification event for real-time updates
export interface NotificationEvent {
  type: 'notification_created' | 'notification_read' | 'notification_delivered' | 'notification_failed';
  notification: Notification;
  userId: string;
  timestamp: Date;
}

// Device token for push notifications
export interface DeviceToken {
  userId: string;
  token: string;
  platform: 'ios' | 'android' | 'web';
  deviceId: string;
  isActive: boolean;
  lastUsed: Date;
  createdAt: Date;
}

// Email template data
export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Notification scheduler
export interface ScheduledNotification {
  id: string;
  userId: string;
  templateId: string;
  variables: Record<string, any>;
  channels: DeliveryChannel[];
  priority: NotificationPriority;
  scheduledAt: Date;
  status: 'scheduled' | 'sent' | 'cancelled' | 'failed';
  createdAt: Date;
  updatedAt: Date;
}
