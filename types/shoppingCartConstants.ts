// lib/shoppingCartConstants.ts

// Bulk discount tiers (same in both environments)
export const BULK_DISCOUNT_TIERS: { threshold: number; discountPercentage: number }[] = [
    { threshold: 5000, discountPercentage: 10 },
    { threshold: 10000, discountPercentage: 15 },
    { threshold: 20000, discountPercentage: 20 },
  ];
  
  // Define an interface for group order milestones (plain TS interface)
  export interface IGroupOrderMilestone {
    name: string;
    targetAmount: number;
    isReached: boolean;
    reachedAt?: Date;
    currentAmount: number;
  }
  
  // Default milestones for group orders
  export const DEFAULT_MILESTONES: IGroupOrderMilestone[] = [
    { name: "Initial Order", targetAmount: 5000, isReached: false, reachedAt: undefined, currentAmount: 0 },
    { name: "Group Bulk Purchase", targetAmount: 10000, isReached: false, reachedAt: undefined, currentAmount: 0 },
    { name: "Maximum Group Discount", targetAmount: 20000, isReached: false, reachedAt: undefined, currentAmount: 0 },
  ];
  
  // Shared enum for order status that you can use on both server and client
  export enum GroupOrderStatus {
    DRAFT = 'draft',
    PENDING = 'pending',
    PROCESSING = 'processing',
    READY_FOR_DELIVERY = 'ready_for_delivery',
    SHIPPED = 'shipped',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled'
  }
  