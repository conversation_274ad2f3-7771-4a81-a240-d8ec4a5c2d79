// types/analytics.ts
import { Types } from 'mongoose';

// Time period types
export type TimePeriod = 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';
export type DateRange = {
  start: Date;
  end: Date;
};

// Metric types
export type MetricType = 
  | 'revenue'
  | 'orders'
  | 'users'
  | 'conversion'
  | 'retention'
  | 'engagement'
  | 'performance'
  | 'inventory'
  | 'shipping'
  | 'satisfaction';

// Chart types
export type ChartType = 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'heatmap';

// Base analytics interface
export interface AnalyticsMetric {
  id: string;
  name: string;
  type: MetricType;
  value: number;
  previousValue?: number;
  change?: number;
  changePercentage?: number;
  trend: 'up' | 'down' | 'stable';
  unit: string;
  format: 'number' | 'currency' | 'percentage' | 'duration';
  description?: string;
  lastUpdated: Date;
}

// Time series data point
export interface TimeSeriesDataPoint {
  timestamp: Date;
  value: number;
  label?: string;
  metadata?: Record<string, any>;
}

// Chart data interface
export interface ChartData {
  id: string;
  title: string;
  type: ChartType;
  data: TimeSeriesDataPoint[] | any[];
  labels?: string[];
  datasets?: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
  }>;
  options?: Record<string, any>;
  lastUpdated: Date;
}

// Dashboard widget interface
export interface DashboardWidget {
  id: string;
  title: string;
  type: 'metric' | 'chart' | 'table' | 'list' | 'custom';
  size: 'small' | 'medium' | 'large' | 'full';
  position: { x: number; y: number; w: number; h: number };
  data: AnalyticsMetric | ChartData | any;
  refreshInterval?: number; // in seconds
  isVisible: boolean;
  permissions?: string[];
}

// Revenue analytics
export interface RevenueAnalytics {
  totalRevenue: number;
  revenueGrowth: number;
  averageOrderValue: number;
  revenueByPeriod: TimeSeriesDataPoint[];
  revenueByCategory: Array<{
    category: string;
    revenue: number;
    percentage: number;
  }>;
  revenueByGroup: Array<{
    groupId: string;
    groupName: string;
    revenue: number;
    orderCount: number;
    averageOrderValue: number;
  }>;
  topProducts: Array<{
    productId: string;
    productName: string;
    revenue: number;
    quantity: number;
    margin: number;
  }>;
  monthlyRecurring: number;
  seasonalTrends: Array<{
    month: string;
    revenue: number;
    growth: number;
  }>;
}

// User analytics
export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  userGrowthRate: number;
  userRetentionRate: number;
  usersByPeriod: TimeSeriesDataPoint[];
  userDemographics: {
    ageGroups: Array<{ range: string; count: number; percentage: number }>;
    locations: Array<{ city: string; count: number; percentage: number }>;
    deviceTypes: Array<{ type: string; count: number; percentage: number }>;
  };
  userEngagement: {
    averageSessionDuration: number;
    averagePageViews: number;
    bounceRate: number;
    returnVisitorRate: number;
  };
  cohortAnalysis: Array<{
    cohort: string;
    period0: number;
    period1: number;
    period2: number;
    period3: number;
    period4: number;
    period5: number;
  }>;
}

// Order analytics
export interface OrderAnalytics {
  totalOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  averageOrderValue: number;
  orderCompletionRate: number;
  ordersByPeriod: TimeSeriesDataPoint[];
  ordersByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  ordersByPaymentMethod: Array<{
    method: string;
    count: number;
    revenue: number;
    percentage: number;
  }>;
  groupOrderMetrics: {
    totalGroupOrders: number;
    averageGroupSize: number;
    groupOrderValue: number;
    individualOrderValue: number;
    groupDiscountSavings: number;
  };
  fulfillmentMetrics: {
    averageFulfillmentTime: number;
    onTimeDeliveryRate: number;
    shippingCosts: number;
    returnRate: number;
  };
}

// Product analytics
export interface ProductAnalytics {
  totalProducts: number;
  activeProducts: number;
  outOfStockProducts: number;
  lowStockProducts: number;
  topSellingProducts: Array<{
    productId: string;
    name: string;
    sales: number;
    revenue: number;
    views: number;
    conversionRate: number;
  }>;
  categoryPerformance: Array<{
    category: string;
    sales: number;
    revenue: number;
    margin: number;
    growth: number;
  }>;
  inventoryTurnover: number;
  stockoutRate: number;
  priceOptimization: Array<{
    productId: string;
    currentPrice: number;
    suggestedPrice: number;
    potentialRevenue: number;
  }>;
}

// Group analytics
export interface GroupAnalytics {
  totalGroups: number;
  activeGroups: number;
  averageGroupSize: number;
  groupGrowthRate: number;
  groupsBySize: Array<{
    sizeRange: string;
    count: number;
    percentage: number;
  }>;
  groupEngagement: {
    averageOrdersPerGroup: number;
    averageOrderValuePerGroup: number;
    groupRetentionRate: number;
    memberInvitationRate: number;
  };
  discountUtilization: {
    totalDiscountsApplied: number;
    averageDiscountPercentage: number;
    discountSavings: number;
    milestoneAchievementRate: number;
  };
  topPerformingGroups: Array<{
    groupId: string;
    groupName: string;
    memberCount: number;
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
  }>;
}

// Performance analytics
export interface PerformanceAnalytics {
  apiResponseTimes: {
    average: number;
    p95: number;
    p99: number;
    slowestEndpoints: Array<{
      endpoint: string;
      averageTime: number;
      requestCount: number;
    }>;
  };
  databasePerformance: {
    queryTime: number;
    connectionPool: number;
    slowQueries: Array<{
      query: string;
      executionTime: number;
      frequency: number;
    }>;
  };
  cachePerformance: {
    hitRate: number;
    missRate: number;
    evictionRate: number;
    memoryUsage: number;
  };
  errorRates: {
    totalErrors: number;
    errorRate: number;
    errorsByType: Array<{
      type: string;
      count: number;
      percentage: number;
    }>;
  };
  uptime: number;
  throughput: number;
}

// Real-time analytics
export interface RealTimeAnalytics {
  currentActiveUsers: number;
  currentActiveGroups: number;
  realtimeOrders: number;
  realtimeRevenue: number;
  liveEvents: Array<{
    type: string;
    timestamp: Date;
    data: any;
  }>;
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical';
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  alertsAndNotifications: Array<{
    id: string;
    type: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    timestamp: Date;
    acknowledged: boolean;
  }>;
}

// Analytics dashboard configuration
export interface AnalyticsDashboard {
  id: string;
  name: string;
  description?: string;
  userId: string;
  isDefault: boolean;
  isPublic: boolean;
  widgets: DashboardWidget[];
  layout: Array<{
    widgetId: string;
    x: number;
    y: number;
    w: number;
    h: number;
  }>;
  filters: {
    dateRange: DateRange;
    groupIds?: string[];
    productIds?: string[];
    userIds?: string[];
    categories?: string[];
  };
  refreshInterval: number;
  permissions: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Analytics report
export interface AnalyticsReport {
  id: string;
  name: string;
  type: 'scheduled' | 'on_demand';
  format: 'pdf' | 'excel' | 'csv' | 'json';
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    time: string;
    timezone: string;
    recipients: string[];
  };
  sections: Array<{
    title: string;
    type: 'metrics' | 'chart' | 'table';
    data: any;
  }>;
  filters: Record<string, any>;
  generatedAt?: Date;
  fileUrl?: string;
  status: 'pending' | 'generating' | 'completed' | 'failed';
}

// API request/response types
export interface GetAnalyticsRequest {
  type: MetricType;
  period: TimePeriod;
  dateRange?: DateRange;
  groupBy?: string;
  filters?: Record<string, any>;
}

export interface GetAnalyticsResponse {
  metrics: AnalyticsMetric[];
  charts: ChartData[];
  summary: Record<string, any>;
  lastUpdated: Date;
}

export interface CreateDashboardRequest {
  name: string;
  description?: string;
  widgets: Omit<DashboardWidget, 'id'>[];
  isDefault?: boolean;
  isPublic?: boolean;
}

export interface CreateDashboardResponse {
  success: boolean;
  dashboard?: AnalyticsDashboard;
  error?: string;
}

export interface GenerateReportRequest {
  reportId?: string;
  name: string;
  type: 'scheduled' | 'on_demand';
  format: 'pdf' | 'excel' | 'csv' | 'json';
  sections: string[];
  filters?: Record<string, any>;
  recipients?: string[];
}

export interface GenerateReportResponse {
  success: boolean;
  reportId?: string;
  downloadUrl?: string;
  estimatedTime?: number;
  error?: string;
}
