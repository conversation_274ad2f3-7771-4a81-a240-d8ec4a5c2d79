// // types/product.ts
// export interface Product {
//     _id: string
//     name: string
//     description: string
//     price: number
//     category: string
//     stock: number
//     image: string
//     createdAt: Date
//     updatedAt: Date
//   }



  


// export interface Product {
//   _id: string
//   name: string
//   description: string
//   price: number
//   category: string
//   stock: number
//   image: string
//   createdAt: Date
//   updatedAt: Date
//   subcategory?: string
//   color?: string
//   size?: string
//   brand?: string
// }




// export interface ProductCategory {
//   _id: string
//   name: string
//   subcategories: string[]
//   attributes: {
//     name: string
//     values: string[]
//   }[]
// }

// export interface Product {
//   _id: string
//   name: string
//   description: string
//   price: number
//   category: string
//   subcategory?: string
//   stock: number
//   image: string
//   createdAt: Date
//   updatedAt: Date
//   [key: string]: any // This allows for dynamic attributes
// }



// export interface ProductCategory {
//   _id: string
//   name: string
//   subcategories?: string[] // Make this optional
//   attributes?: {
//     name: string
//     values: string[]
//   }[] // Make this optional
// }

// export interface Product {
//   _id: string
//   name: string
//   description: string
//   price: number
//   category: string
//   subcategory?: string
//   stock: number
//   image: string
//   createdAt: Date
//   updatedAt: Date
//   [key: string]: any // This allows for dynamic attributes
// }



// export interface ProductCategory {
//   _id: string;
//   name: string;
//   subcategories?: string[]; // Optional subcategories
//   attributes?: {
//     name: string;
//     values: string[];
//   }[]; // Optional attributes
// }

// export interface Product {
//   _id: string;
//   name: string;
//   description: string;
//   price: number;
//   category: ProductCategory; // Populated category object with name and _id
//   subcategory?: string; // Optional subcategory for more refined product classification
//   stock: number;
//   image: string;
//   createdAt: Date;
//   updatedAt: Date;
//   [key: string]: any; // Dynamic attributes for extensibility
// }


// // types/product.ts

// import { ProductCategory } from "./productCategory";
// export interface Product {
//   _id: string;
//   name: string;
//   description: string;
//   price: number;
//   category: ProductCategory;  // Populated category object (instead of just the ID)
//   subcategory?: string;       // Optional subcategory for refined classification
//   stock: number;
//   image: string;
//   createdAt: Date;
//   updatedAt: Date;
//   [key: string]: any;         // Dynamic attributes for extensibility
// }


// types/product.ts
import { ProductCategory } from "./productCategory";

export interface Product {
  _id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;     // For sale/discount pricing
  category: ProductCategory;  // Populated category object (instead of just the ID)
  subcategory?: string;       // Optional subcategory for refined classification
  stock: number;
  image: string;
  averageRating?: number;     // Product rating (0-5)
  reviewCount?: number;       // Number of reviews
  brand?: string;             // Product brand
  createdAt: Date;
  updatedAt: Date;
  [key: string]: unknown;     // Using unknown instead of any for dynamic properties
}
