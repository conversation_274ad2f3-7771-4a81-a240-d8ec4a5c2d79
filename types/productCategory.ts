export interface ProductCategory {
    _id: string;
    name: string;
    description?: string; // Optional description
    product_count?: number; // Optional product count
    is_active?: boolean; // Optional active status
    parent_category?: string; // Optional parent category
    subcategories?: string[];  // Optional subcategories
    attributes?: {
      name: string;
      values: string[];
    }[]; // Optional attribute key-value pairs
    createdAt?: Date;
    updatedAt?: Date;
  }
