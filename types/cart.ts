// types/cart.ts
import { Product } from "./product";

export interface CartItem {
  _id?: string;
  product: Product;
  quantity: number;
  price?: number;
}

export interface ShoppingCart {
  _id?: string;
  userId: string;
  groupId: string;
  items: CartItem[];
  total: number;
  isFinalized: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface AddToCartInput {
  userId: string;
  productId: string;
  quantity: number;
  groupId: string;
}

export interface UpdateCartItemInput {
  userId: string;
  productId: string;
  quantity: number;
  groupId: string;
}

// For frontend display purposes
export interface DisplayCartItem {
  _id: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  subtotal: number;
}
