// export interface CreateGroupInput {
//     name: string;
//     description: string;
//     admin: string;
//     geolocation: string;
//     members?: string[];
//     totalSales?: number;
//     avgOrderValue?: number;
//     activeOrders?: number;
//   }
  
export interface CreateGroupInput {
  name: string;
  description: string;
  admin: string;              // Admin user ID
  geolocation: string;
  members?: string[];         // Optional member IDs
  totalSales?: number;        // Defaults to 0 if not provided
  avgOrderValue?: number;     // Defaults to 0 if not provided
  activeOrders?: number;      // Defaults to 0 if not provided
}
