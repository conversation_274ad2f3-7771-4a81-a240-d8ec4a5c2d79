export interface FrontendGroupOrder {
    _id: string;
    groupId: string;
    totalOrderValue: number;
    status: string;
    orderPlacedAt: Date;
    lastUpdatedAt: Date;
    paymentStatus: string;
    userContributions: {
        userId: string;
        userName: string;
        totalSpent: number;
        contributionPercentage: number;
    }[];
    orderItems: {
        product: {
            _id: string;
            name: string;
            price: number;
            image?: string;
        } | string;
        quantity: number;
        userId: string;
        unitPrice: number;
        subtotal: number;
    }[];
    milestones: {
        name: string;
        targetAmount: number;
        isReached: boolean;
        currentAmount: number;
    }[];
    statusHistory?: {
        status: string;
        timestamp: Date;
    }[];
    bulkDiscountTiers: {
        threshold: number;
        discountPercentage: number;
    }[];
    appliedDiscountTier?: {
        threshold: number;
        discountPercentage: number;
    };
}
