// types/payment.ts
import { Types } from 'mongoose';

// Payment method types
export type PaymentMethodType = 'credit_card' | 'bank_transfer' | 'eft' | 'paypal' | 'apple_pay' | 'google_pay';

// Payment status types
export type PaymentStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled' 
  | 'refunded' 
  | 'partially_refunded';

// Payment provider types
export type PaymentProvider = 'stripe' | 'paypal' | 'payfast' | 'ozow' | 'yoco' | 'peach_payments';

// Credit card information
export interface CreditCardInfo {
  cardNumber: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  cardholderName: string;
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

// Bank transfer information
export interface BankTransferInfo {
  bankName: string;
  accountNumber: string;
  routingNumber?: string;
  accountHolderName: string;
  reference?: string;
}

// EFT information
export interface EFTInfo {
  bankName: string;
  accountNumber: string;
  branchCode: string;
  accountHolderName: string;
  reference?: string;
}

// Payment method interface
export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  provider: PaymentProvider;
  name: string;
  description?: string;
  icon?: string;
  isActive: boolean;
  processingFee?: number;
  processingFeeType?: 'fixed' | 'percentage';
  minAmount?: number;
  maxAmount?: number;
  supportedCurrencies: string[];
  estimatedProcessingTime?: string;
}

// Payment data for processing
export interface PaymentData {
  orderId: string;
  userId: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethodType;
  provider: PaymentProvider;
  
  // Payment method specific data
  creditCard?: CreditCardInfo;
  bankTransfer?: BankTransferInfo;
  eft?: EFTInfo;
  
  // Additional metadata
  description?: string;
  metadata?: Record<string, any>;
  returnUrl?: string;
  cancelUrl?: string;
}

// Payment result from processing
export interface PaymentResult {
  success: boolean;
  paymentId: string;
  transactionId?: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  processingFee?: number;
  message?: string;
  error?: string;
  redirectUrl?: string;
  requiresAction?: boolean;
  actionType?: 'redirect' | '3ds' | 'otp';
  actionData?: any;
  estimatedSettlement?: Date;
}

// Payment record for database
export interface Payment {
  _id: string;
  orderId: string;
  userId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethodType;
  provider: PaymentProvider;
  transactionId?: string;
  providerPaymentId?: string;
  processingFee?: number;
  netAmount?: number;
  description?: string;
  metadata?: Record<string, any>;
  failureReason?: string;
  refundAmount?: number;
  refundReason?: string;
  processedAt?: Date;
  settledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Database model interface
export interface IPayment extends Payment {
  _id: Types.ObjectId;
  orderId: Types.ObjectId;
  userId: Types.ObjectId;
}

// Payment validation result
export interface PaymentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// Payment processing options
export interface PaymentProcessingOptions {
  savePaymentMethod?: boolean;
  sendReceipt?: boolean;
  notifyUser?: boolean;
  autoCapture?: boolean;
  description?: string;
  metadata?: Record<string, any>;
}

// Payment webhook data
export interface PaymentWebhookData {
  eventType: string;
  paymentId: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  timestamp: Date;
  signature?: string;
  rawData: any;
}

// Payment analytics data
export interface PaymentAnalytics {
  totalPayments: number;
  totalAmount: number;
  averageAmount: number;
  successRate: number;
  failureRate: number;
  paymentMethodBreakdown: Record<PaymentMethodType, number>;
  statusBreakdown: Record<PaymentStatus, number>;
  monthlyTrends: Array<{
    month: string;
    totalPayments: number;
    totalAmount: number;
    successRate: number;
  }>;
}

// API request/response types
export interface ProcessPaymentRequest {
  paymentData: PaymentData;
  options?: PaymentProcessingOptions;
}

export interface ProcessPaymentResponse {
  success: boolean;
  payment?: Payment;
  result?: PaymentResult;
  error?: string;
}

export interface ValidatePaymentRequest {
  paymentData: Partial<PaymentData>;
}

export interface ValidatePaymentResponse {
  validation: PaymentValidationResult;
}

export interface GetPaymentStatusRequest {
  paymentId: string;
}

export interface GetPaymentStatusResponse {
  payment: Payment;
  status: PaymentStatus;
  lastUpdated: Date;
}

// Frontend payment form data
export interface PaymentFormData {
  paymentMethod: PaymentMethodType;
  savePaymentInfo: boolean;
  
  // Credit card fields
  cardNumber?: string;
  expiryMonth?: string;
  expiryYear?: string;
  cvv?: string;
  cardholderName?: string;
  
  // Bank transfer fields
  bankName?: string;
  accountNumber?: string;
  accountHolderName?: string;
  
  // Billing address
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

// Payment configuration
export interface PaymentConfig {
  providers: Record<PaymentProvider, {
    apiKey: string;
    secretKey: string;
    webhookSecret?: string;
    sandboxMode: boolean;
    supportedMethods: PaymentMethodType[];
  }>;
  defaultCurrency: string;
  supportedCurrencies: string[];
  minimumAmount: number;
  maximumAmount: number;
  processingFees: Record<PaymentMethodType, {
    type: 'fixed' | 'percentage';
    amount: number;
  }>;
}
