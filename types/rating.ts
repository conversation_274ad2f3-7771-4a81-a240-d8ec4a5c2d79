// types/rating.ts

import { IProductRating } from '@/models/ProductRating';

export type { IProductRating };

export interface ProductRating {
  _id: string;
  userId: string | {
    _id: string;
    name: string;
  };
  productId: string;
  rating: number;
  review?: string;
  title?: string;
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  reportedCount: number;
  status: 'active' | 'hidden' | 'pending';
  createdAt: Date;
  updatedAt: Date;
}

export interface RatingSummary {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  recentRatings: ProductRating[];
}

export interface CreateRatingRequest {
  userId: string;
  productId: string;
  rating: number;
  review?: string;
  title?: string;
}

export interface UpdateRatingRequest {
  ratingId: string;
  userId: string;
  rating?: number;
  review?: string;
  title?: string;
}

export interface RatingActionRequest {
  ratingId: string;
  userId: string;
  action: 'helpful' | 'report';
}

// Rating display configurations
export const RATING_LABELS = {
  1: 'Poor',
  2: 'Fair',
  3: 'Good',
  4: 'Very Good',
  5: 'Excellent'
} as const;

export const RATING_COLORS = {
  1: 'red',
  2: 'orange',
  3: 'yellow',
  4: 'blue',
  5: 'green'
} as const;

// Helper functions
export const getRatingLabel = (rating: number): string => {
  const roundedRating = Math.round(rating) as keyof typeof RATING_LABELS;
  return RATING_LABELS[roundedRating] || 'Unknown';
};

export const getRatingColor = (rating: number): string => {
  const roundedRating = Math.round(rating) as keyof typeof RATING_COLORS;
  return RATING_COLORS[roundedRating] || 'gray';
};

export const formatRatingValue = (rating: number): string => {
  return rating.toFixed(1);
};

export const validateRating = (rating: number): boolean => {
  return rating >= 1 && rating <= 5 && Number.isInteger(rating);
};

export const validateReview = (review: string): boolean => {
  return review.trim().length <= 1000;
};

export const validateTitle = (title: string): boolean => {
  return title.trim().length <= 100;
};