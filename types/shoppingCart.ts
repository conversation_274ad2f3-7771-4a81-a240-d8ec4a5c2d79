import { Product } from "./product";

export interface CartItem {
  _id: string;
  product: Product;  // Reference to the product object
  quantity: number;
  price: number; // Price of the item
  buyerName: string; // Name of the buyer
  image: string; // URL of the product image
}

export interface ShoppingCart {
    _id: string;
    user: string;             // User ID (or populated user object)
    items: CartItem[];
    total: number;
    groupOrderId?: string;    // Optional reference to a GroupOrder
    isFinalized: boolean;
    createdAt: Date;
    updatedAt: Date;
  }