// Common types

export type SubscriptionStatus = "active" | "inactive" | "pending"
export type BundleSize = "basic" | "standard" | "premium"
export type DeliveryFrequency = "monthly" | "quarterly" | "annually"

export interface YearEndBundleType {
  id?: string
  userId: string
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: SubscriptionStatus
  startDate?: Date | null
  endDate?: Date | null
  bundleSize: BundleSize
  itemList: string[]
  deliveryMonth: number
  totalSavingsGoal: number
  createdAt?: Date
  updatedAt?: Date
}

export interface MonthlyGroceriesType {
  id?: string
  userId: string
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: SubscriptionStatus
  startDate?: Date | null
  endDate?: Date | null
  groceryList: string[]
  deliveryDate: number
  preferredStores: string[]
  createdAt?: Date
  updatedAt?: Date
}

export interface GrocerySchoolBundleType {
  id?: string
  userId: string
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: SubscriptionStatus
  startDate?: Date | null
  endDate?: Date | null
  bundleSize: BundleSize
  groceryItems: string[]
  schoolItems: string[]
  deliveryFrequency: DeliveryFrequency
  groceryAllocation: number
  schoolSuppliesAllocation: number
  createdAt?: Date
  updatedAt?: Date
}

export interface FuneralBenefitsType {
  id?: string
  userId: string
  name: string
  description: string
  price: number
  monthlyContribution: number
  status: SubscriptionStatus
  startDate?: Date | null
  endDate?: Date | null
  coverageAmount: number
  beneficiaries: {
    name: string
    relationship: string
    contactNumber: string
  }[]
  waitingPeriod: number
  createdAt?: Date
  updatedAt?: Date
}

export type SubscriptionType = YearEndBundleType | MonthlyGroceriesType | GrocerySchoolBundleType | FuneralBenefitsType

export type CreateSubscriptionData<T extends SubscriptionType> = Omit<T, "id" | "createdAt" | "updatedAt">
export type UpdateSubscriptionData<T extends SubscriptionType> = Partial<T>

// Type guard functions
export function isYearEndBundle(subscription: SubscriptionType): subscription is YearEndBundleType {
  return "deliveryMonth" in subscription && "totalSavingsGoal" in subscription
}

export function isMonthlyGroceries(subscription: SubscriptionType): subscription is MonthlyGroceriesType {
  return "groceryList" in subscription && "deliveryDate" in subscription
}

export function isGrocerySchoolBundle(subscription: SubscriptionType): subscription is GrocerySchoolBundleType {
  return "groceryItems" in subscription && "schoolItems" in subscription && "groceryAllocation" in subscription
}

export function isFuneralBenefits(subscription: SubscriptionType): subscription is FuneralBenefitsType {
  return "coverageAmount" in subscription && "beneficiaries" in subscription && "waitingPeriod" in subscription
}

