export interface GroupOrder {
    _id: string;
    groupId: string;          // Reference to StokvelGroup ID
    orderItems: {
      product: string;        // Reference to Product ID
      quantity: number;
      userId: string;         // Reference to User ID
    }[];
    totalOrderValue: number;
    isBulkDiscountApplied: boolean;
    orderStatus: 'pending' | 'confirmed' | 'shipped' | 'delivered';
    createdAt: Date;
    updatedAt: Date;
  }
  