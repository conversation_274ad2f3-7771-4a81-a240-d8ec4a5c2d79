// types/unifiedCart.ts - Unified type system for shopping cart

import { Types } from 'mongoose';
import { GroupOrderStatus } from './shoppingCartConstants';

// Base types for both frontend and backend
export interface BaseCartItem {
  _id: string;
  productId: string;
  quantity: number;
  price: number;
}

// Frontend-specific cart item with product details
export interface DisplayCartItem extends BaseCartItem {
  name: string;
  image: string;
  description?: string;
  subtotal: number;
}

// Backend-specific cart item with Mongoose references
export interface DbCartItem extends BaseCartItem {
  product: Types.ObjectId | string;
}

// Shopping cart types
export interface BaseShoppingCart {
  _id: string;
  userId: string;
  groupId: string;
  total: number;
  isFinalized: boolean;
  createdAt?: string | Date;
  updatedAt?: string | Date;
}

export interface DisplayShoppingCart extends BaseShoppingCart {
  items: DisplayCartItem[];
}

export interface DbShoppingCart extends BaseShoppingCart {
  items: DbCartItem[];
  user: Types.ObjectId | string;
  groupOrderId?: Types.ObjectId | string;
}

// Group order types
export interface GroupOrderItem {
  productId: string;
  product?: {
    _id: string;
    name: string;
    price: number;
    image?: string;
  };
  quantity: number;
  userId: string;
  unitPrice: number;
  subtotal: number;
}

export interface UserContribution {
  userId: string;
  userName: string;
  totalSpent: number;
  contributionPercentage: number;
  items?: GroupOrderItem[];
}

export interface GroupOrderMilestone {
  name: string;
  targetAmount: number;
  currentAmount: number;
  isReached: boolean;
  reachedAt?: Date;
}

export interface DiscountTier {
  threshold: number;
  discountPercentage: number;
}

export interface GroupOrder {
  _id: string;
  groupId: string;
  orderItems: GroupOrderItem[];
  userContributions: UserContribution[];
  totalOrderValue: number;
  status: GroupOrderStatus;
  statusHistory?: {
    status: GroupOrderStatus;
    timestamp: Date;
    updatedBy?: string;
  }[];
  milestones: GroupOrderMilestone[];
  bulkDiscountTiers: DiscountTier[];
  appliedDiscountTier?: DiscountTier;
  discountAmount?: number;
  finalOrderValue?: number;
  orderPlacedAt: Date;
  lastUpdatedAt: Date;
  paymentStatus: 'unpaid' | 'partially_paid' | 'fully_paid';
  groupOrderNotes?: string;
}

// API Input types
export interface AddToCartInput {
  userId: string;
  productId: string;
  quantity: number;
  groupId: string;
}

export interface UpdateCartItemInput {
  userId: string;
  productId: string;
  quantity: number;
  groupId: string;
}

export interface RemoveFromCartInput {
  userId: string;
  productId: string;
  groupId: string;
}

export interface ClearCartInput {
  userId: string;
  groupId: string;
}

export interface CreateGroupOrderInput {
  userId: string;
  groupId: string;
  customerInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    country: string;
    postalCode: string;
  };
  paymentMethod: string;
}

export interface UpdateGroupOrderStatusInput {
  orderId: string;
  newStatus: GroupOrderStatus;
  userId: string;
}

// Response types
export interface DiscountCalculationResult {
  discountPercentage: number;
  discountAmount: number;
  finalOrderValue: number;
  appliedTier?: DiscountTier;
}

export interface GroupOrderSummary {
  totalOrders: number;
  averageOrderValue: number;
  totalRevenue: number;
  popularProducts: {
    productId: string;
    name: string;
    quantity: number;
  }[];
  ordersByStatus: {
    [key: string]: number;
  };
  monthlySales?: {
    month: string;
    revenue: number;
  }[];
  memberContributions?: {
    userName: string;
    totalSpent: number;
  }[];
}
