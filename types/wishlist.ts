// types/wishlist.ts

import { IWishlist, IWishlistItem } from '@/models/Wishlist';

export type { IWishlist, IWishlistItem };

export interface WishlistItem {
  product: string | {
    _id: string;
    name: string;
    price: number;
    image: string;
    description?: string;
    stock: number;
    category?: {
      _id: string;
      name: string;
    };
  };
  addedAt: Date;
  notes?: string;
  priority: 'low' | 'medium' | 'high';
}

export interface Wishlist {
  _id: string;
  userId: string;
  items: WishlistItem[];
  name: string;
  description?: string;
  isPublic: boolean;
  shareCode?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  itemCount?: number;
  totalValue?: number;
}

export interface CreateWishlistRequest {
  userId: string;
  name?: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface AddToWishlistRequest {
  userId: string;
  productId: string;
  wishlistId?: string;
  notes?: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface UpdateWishlistRequest {
  wishlistId: string;
  userId: string;
  name?: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface RemoveFromWishlistRequest {
  wishlistId: string;
  userId: string;
  productId: string;
}

export interface WishlistSummary {
  totalWishlists: number;
  totalItems: number;
  totalValue: number;
  recentlyAdded: WishlistItem[];
  topPriority: WishlistItem[];
}

// Priority configurations
export const PRIORITY_CONFIG = {
  low: {
    label: 'Low Priority',
    color: 'gray',
    icon: '⬇️',
    description: 'Nice to have'
  },
  medium: {
    label: 'Medium Priority',
    color: 'blue',
    icon: '➡️',
    description: 'Want to buy'
  },
  high: {
    label: 'High Priority',
    color: 'red',
    icon: '⬆️',
    description: 'Must have'
  }
} as const;

// Helper functions
export const getPriorityConfig = (priority: 'low' | 'medium' | 'high') => {
  return PRIORITY_CONFIG[priority] || PRIORITY_CONFIG.medium;
};

export const getPriorityColor = (priority: 'low' | 'medium' | 'high'): string => {
  return getPriorityConfig(priority).color;
};

export const getPriorityLabel = (priority: 'low' | 'medium' | 'high'): string => {
  return getPriorityConfig(priority).label;
};

export const getPriorityIcon = (priority: 'low' | 'medium' | 'high'): string => {
  return getPriorityConfig(priority).icon;
};

export const formatWishlistValue = (value: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const formatWishlistDate = (date: Date | string): string => {
  return new Date(date).toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export const sortWishlistItems = (items: WishlistItem[], sortBy: 'date' | 'priority' | 'name' | 'price') => {
  return [...items].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime();
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      case 'name':
        const nameA = typeof a.product === 'object' ? a.product.name : '';
        const nameB = typeof b.product === 'object' ? b.product.name : '';
        return nameA.localeCompare(nameB);
      case 'price':
        const priceA = typeof a.product === 'object' ? a.product.price : 0;
        const priceB = typeof b.product === 'object' ? b.product.price : 0;
        return priceB - priceA;
      default:
        return 0;
    }
  });
};

export const filterWishlistItems = (items: WishlistItem[], filters: {
  priority?: 'low' | 'medium' | 'high';
  inStock?: boolean;
  priceRange?: { min: number; max: number };
  search?: string;
}) => {
  return items.filter(item => {
    // Priority filter
    if (filters.priority && item.priority !== filters.priority) {
      return false;
    }

    // Stock filter
    if (filters.inStock && typeof item.product === 'object' && item.product.stock <= 0) {
      return false;
    }

    // Price range filter
    if (filters.priceRange && typeof item.product === 'object') {
      const price = item.product.price;
      if (price < filters.priceRange.min || price > filters.priceRange.max) {
        return false;
      }
    }

    // Search filter
    if (filters.search && typeof item.product === 'object') {
      const searchTerm = filters.search.toLowerCase();
      const nameMatch = item.product.name.toLowerCase().includes(searchTerm);
      const notesMatch = item.notes?.toLowerCase().includes(searchTerm);
      if (!nameMatch && !notesMatch) {
        return false;
      }
    }

    return true;
  });
};

export const getWishlistStats = (wishlist: Wishlist) => {
  const items = wishlist.items;
  const totalItems = items.length;
  
  let totalValue = 0;
  let inStockItems = 0;
  let outOfStockItems = 0;
  const priorityCounts = { low: 0, medium: 0, high: 0 };

  items.forEach(item => {
    if (typeof item.product === 'object') {
      totalValue += item.product.price;
      if (item.product.stock > 0) {
        inStockItems++;
      } else {
        outOfStockItems++;
      }
    }
    priorityCounts[item.priority]++;
  });

  return {
    totalItems,
    totalValue,
    inStockItems,
    outOfStockItems,
    priorityCounts,
    averageItemValue: totalItems > 0 ? totalValue / totalItems : 0
  };
};

// Wishlist sharing utilities
export const generateShareableLink = (shareCode: string): string => {
  if (typeof window !== 'undefined') {
    return `${window.location.origin}/wishlist/shared/${shareCode}`;
  }
  return `https://your-domain.com/wishlist/shared/${shareCode}`;
};

export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};
