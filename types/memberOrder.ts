// types/memberOrder.ts

import { IMemberOrder, MemberOrderStatus } from '@/models/MemberOrder';

export type { IMemberOrder, MemberOrderStatus };

export interface MemberOrderItem {
  product: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
}

export interface CustomerInfo {
  name: string;
  email: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  phone?: string;
}

export interface PaymentInfo {
  method: string;
  status: 'pending' | 'paid' | 'failed' | 'refunded';
  transactionId?: string;
  paidAt?: Date;
}

export interface ShippingInfo {
  trackingNumber?: string;
  carrier?: string;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  shippingAddress?: string;
}

export interface DiscountApplied {
  type: 'bulk' | 'coupon' | 'loyalty';
  amount: number;
  percentage: number;
}

export interface StatusHistory {
  status: MemberOrderStatus;
  timestamp: Date;
  notes?: string;
}

export interface MemberOrder {
  _id: string;
  userId: string;
  groupId: string;
  groupOrderId: string;
  orderNumber: string;
  items: MemberOrderItem[];
  totalAmount: number;
  status: MemberOrderStatus;
  statusHistory: StatusHistory[];
  customerInfo: CustomerInfo;
  paymentInfo: PaymentInfo;
  shippingInfo?: ShippingInfo;
  discountApplied?: DiscountApplied;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateMemberOrderRequest {
  userId: string;
  groupId: string;
  customerInfo: CustomerInfo;
  paymentMethod: string;
}

export interface UpdateMemberOrderStatusRequest {
  orderId: string;
  status: MemberOrderStatus;
  notes?: string;
}

export interface CancelMemberOrderRequest {
  orderId: string;
  reason?: string;
}

export interface MemberOrderSummary {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  ordersByStatus: Record<string, number>;
  recentOrders: MemberOrder[];
}

// Order status display configurations
export const ORDER_STATUS_CONFIG = {
  [MemberOrderStatus.PENDING]: {
    label: 'Pending',
    color: 'yellow',
    description: 'Order is being processed'
  },
  [MemberOrderStatus.CONFIRMED]: {
    label: 'Confirmed',
    color: 'blue',
    description: 'Order has been confirmed'
  },
  [MemberOrderStatus.PROCESSING]: {
    label: 'Processing',
    color: 'indigo',
    description: 'Order is being prepared'
  },
  [MemberOrderStatus.PACKED]: {
    label: 'Packed',
    color: 'purple',
    description: 'Order has been packed'
  },
  [MemberOrderStatus.SHIPPED]: {
    label: 'Shipped',
    color: 'blue',
    description: 'Order is on its way'
  },
  [MemberOrderStatus.DELIVERED]: {
    label: 'Delivered',
    color: 'green',
    description: 'Order has been delivered'
  },
  [MemberOrderStatus.CANCELLED]: {
    label: 'Cancelled',
    color: 'red',
    description: 'Order has been cancelled'
  },
  [MemberOrderStatus.REFUNDED]: {
    label: 'Refunded',
    color: 'gray',
    description: 'Order has been refunded'
  }
} as const;

// Helper functions
export const getOrderStatusConfig = (status: MemberOrderStatus) => {
  return ORDER_STATUS_CONFIG[status] || {
    label: status,
    color: 'gray',
    description: 'Unknown status'
  };
};

export const getOrderStatusColor = (status: MemberOrderStatus): string => {
  return getOrderStatusConfig(status).color;
};

export const getOrderStatusLabel = (status: MemberOrderStatus): string => {
  return getOrderStatusConfig(status).label;
};

export const isOrderCancellable = (status: MemberOrderStatus): boolean => {
  return [MemberOrderStatus.PENDING, MemberOrderStatus.CONFIRMED].includes(status);
};

export const isOrderRefundable = (status: MemberOrderStatus): boolean => {
  return [
    MemberOrderStatus.CONFIRMED,
    MemberOrderStatus.PROCESSING,
    MemberOrderStatus.PACKED,
    MemberOrderStatus.SHIPPED,
    MemberOrderStatus.DELIVERED
  ].includes(status);
};

export const canUpdateOrderStatus = (currentStatus: MemberOrderStatus, newStatus: MemberOrderStatus): boolean => {
  const statusFlow = {
    [MemberOrderStatus.PENDING]: [MemberOrderStatus.CONFIRMED, MemberOrderStatus.CANCELLED],
    [MemberOrderStatus.CONFIRMED]: [MemberOrderStatus.PROCESSING, MemberOrderStatus.CANCELLED],
    [MemberOrderStatus.PROCESSING]: [MemberOrderStatus.PACKED, MemberOrderStatus.CANCELLED],
    [MemberOrderStatus.PACKED]: [MemberOrderStatus.SHIPPED],
    [MemberOrderStatus.SHIPPED]: [MemberOrderStatus.DELIVERED],
    [MemberOrderStatus.DELIVERED]: [MemberOrderStatus.REFUNDED],
    [MemberOrderStatus.CANCELLED]: [],
    [MemberOrderStatus.REFUNDED]: []
  };

  return statusFlow[currentStatus]?.includes(newStatus) || false;
};
