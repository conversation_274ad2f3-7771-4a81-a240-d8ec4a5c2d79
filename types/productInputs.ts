import { Product } from "./product";

export interface CreateProductInput {
    name: string;
    description: string;
    price: number;
    category: string;          // Category ID reference
    stock: number;
    image: string;
  }

  
  export interface UpdateProductInput {
    id: string;                // Product ID
    updateData: Partial<Omit<Product, '_id' | 'createdAt' | 'updatedAt'>>;
  }
  

  export interface AddToCartInput {
    productId: string;         // Product ID
    quantity: number;
  }
  