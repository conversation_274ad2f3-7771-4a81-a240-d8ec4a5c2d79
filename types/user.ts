// import { Types } from 'mongoose';
// import { UserRole } from '@/models/User'; 

// export interface User {
//   _id: string;
//   email: string;
//   name: string;
//   password: string;
//   membership: Types.ObjectId | null;
//   role: UserRole; // Added role field
//   token?: string; // Optional token property
  
// }



// types/user.ts
export type UserRole = 'admin' | 'member' | 'customer' | 'guest';

export interface BankDetails {
  accountName?: string;
  accountNumber?: string;
  bankName?: string;
}

/**
 * A front-end representation of the user object, 
 * matching (or partially matching) your Mongoose IUser fields,
 * but using simple string/Date types and no server libs like Mongoose Types.
 */
export interface User {
  _id: string;                    // Mongoose ObjectId as a string on the front end
  email: string;
  name: string;
  phone?: string;
  role: UserRole;
  referralCode: string;
  bankDetails?: BankDetails;
  stokvelGroups?: string[];       // store group IDs or a typed array if needed
  createdAt?: string;             // or Date, if you parse them on the client
  updatedAt?: string;             // or Date
  /**
   * If your client code expects to store an auth token
   * or membership info, define them as needed:
   */
  token?: string;
  membership?: string | null;
  /**
   * Typically, we do NOT store the password in front-end objects. 
   * But if you do have a reason, you can define it as optional.
   */
  password?: string;
}
