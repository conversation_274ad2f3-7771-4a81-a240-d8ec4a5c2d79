// types/realTimeUpdates.ts
import { Types } from 'mongoose';
import { FulfillmentStatus } from './orderFulfillment';
import { PaymentStatus } from './payment';

// Real-time event types
export type RealTimeEventType = 
  | 'group_order_updated'
  | 'member_joined'
  | 'member_left'
  | 'cart_updated'
  | 'payment_processed'
  | 'order_status_changed'
  | 'discount_milestone_reached'
  | 'group_order_finalized'
  | 'notification_sent'
  | 'user_activity'
  | 'system_announcement';

// WebSocket connection status
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';

// Real-time event interface
export interface RealTimeEvent {
  id: string;
  type: RealTimeEventType;
  timestamp: Date;
  userId?: string;
  groupId?: string;
  orderId?: string;
  data: any;
  metadata?: {
    source: string;
    version: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
  };
}

// Group order real-time update
export interface GroupOrderUpdate {
  groupOrderId: string;
  groupId: string;
  updateType: 'member_added' | 'member_removed' | 'item_added' | 'item_removed' | 'quantity_changed' | 'status_changed';
  memberId?: string;
  memberName?: string;
  itemId?: string;
  itemName?: string;
  previousQuantity?: number;
  newQuantity?: number;
  previousStatus?: string;
  newStatus?: string;
  totalValue: number;
  memberCount: number;
  discountInfo?: {
    currentDiscount: number;
    nextMilestone?: {
      threshold: number;
      discount: number;
      remaining: number;
    };
  };
  timestamp: Date;
}

// Cart update event
export interface CartUpdateEvent {
  userId: string;
  groupId?: string;
  cartId: string;
  updateType: 'item_added' | 'item_removed' | 'quantity_changed' | 'cart_cleared';
  productId?: string;
  productName?: string;
  quantity?: number;
  price?: number;
  totalItems: number;
  totalValue: number;
  timestamp: Date;
}

// Payment update event
export interface PaymentUpdateEvent {
  paymentId: string;
  orderId: string;
  userId: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  paymentMethod: string;
  timestamp: Date;
}

// Order status update event
export interface OrderStatusUpdateEvent {
  orderId: string;
  groupOrderId?: string;
  previousStatus: FulfillmentStatus;
  newStatus: FulfillmentStatus;
  estimatedCompletion?: Date;
  notes?: string;
  assignedTo?: string;
  timestamp: Date;
}

// Discount milestone event
export interface DiscountMilestoneEvent {
  groupId: string;
  groupOrderId: string;
  milestoneType: 'threshold_reached' | 'new_tier_unlocked' | 'bonus_applied';
  previousDiscount: number;
  newDiscount: number;
  threshold: number;
  totalValue: number;
  memberCount: number;
  savings: number;
  timestamp: Date;
}

// User activity event
export interface UserActivityEvent {
  userId: string;
  userName: string;
  groupId?: string;
  activityType: 'joined_group' | 'left_group' | 'added_to_cart' | 'placed_order' | 'viewed_product' | 'shared_product';
  details?: any;
  timestamp: Date;
}

// Notification event
export interface NotificationEvent {
  id: string;
  userId: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  persistent: boolean;
  timestamp: Date;
}

// WebSocket message interface
export interface WebSocketMessage {
  type: 'event' | 'subscription' | 'unsubscription' | 'heartbeat' | 'error';
  event?: RealTimeEvent;
  subscription?: {
    type: 'group' | 'user' | 'order' | 'global';
    id: string;
  };
  error?: {
    code: string;
    message: string;
  };
  timestamp: Date;
}

// Subscription management
export interface Subscription {
  id: string;
  type: 'group' | 'user' | 'order' | 'global';
  targetId: string;
  userId: string;
  events: RealTimeEventType[];
  createdAt: Date;
  lastActivity: Date;
}

// Real-time connection interface
export interface RealTimeConnection {
  id: string;
  userId: string;
  socketId: string;
  subscriptions: Subscription[];
  status: ConnectionStatus;
  connectedAt: Date;
  lastActivity: Date;
  userAgent?: string;
  ipAddress?: string;
}

// Group activity summary
export interface GroupActivitySummary {
  groupId: string;
  activeMembers: number;
  totalMembers: number;
  recentActivity: UserActivityEvent[];
  currentOrders: {
    active: number;
    pending: number;
    completed: number;
  };
  discountProgress: {
    currentValue: number;
    nextThreshold: number;
    currentDiscount: number;
    nextDiscount: number;
    progress: number; // percentage
  };
  lastUpdated: Date;
}

// Real-time analytics
export interface RealTimeAnalytics {
  activeConnections: number;
  activeGroups: number;
  eventsPerMinute: number;
  topEvents: Array<{
    type: RealTimeEventType;
    count: number;
  }>;
  connectionsByGroup: Array<{
    groupId: string;
    connections: number;
  }>;
  systemHealth: {
    status: 'healthy' | 'degraded' | 'down';
    latency: number;
    errorRate: number;
  };
}

// Event handlers
export interface EventHandlers {
  onGroupOrderUpdate?: (update: GroupOrderUpdate) => void;
  onCartUpdate?: (update: CartUpdateEvent) => void;
  onPaymentUpdate?: (update: PaymentUpdateEvent) => void;
  onOrderStatusUpdate?: (update: OrderStatusUpdateEvent) => void;
  onDiscountMilestone?: (milestone: DiscountMilestoneEvent) => void;
  onUserActivity?: (activity: UserActivityEvent) => void;
  onNotification?: (notification: NotificationEvent) => void;
  onConnectionStatusChange?: (status: ConnectionStatus) => void;
  onError?: (error: Error) => void;
}

// Real-time service configuration
export interface RealTimeConfig {
  serverUrl: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  subscriptionTimeout: number;
  enableLogging: boolean;
  enableAnalytics: boolean;
}

// Event broadcasting options
export interface BroadcastOptions {
  targetUsers?: string[];
  targetGroups?: string[];
  excludeUsers?: string[];
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  persistent?: boolean;
  ttl?: number; // time to live in seconds
}

// Event filtering options
export interface EventFilter {
  types?: RealTimeEventType[];
  userId?: string;
  groupId?: string;
  orderId?: string;
  startDate?: Date;
  endDate?: Date;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

// API request/response types
export interface SubscribeRequest {
  type: 'group' | 'user' | 'order' | 'global';
  targetId: string;
  events?: RealTimeEventType[];
}

export interface SubscribeResponse {
  success: boolean;
  subscriptionId?: string;
  error?: string;
}

export interface BroadcastEventRequest {
  event: Omit<RealTimeEvent, 'id' | 'timestamp'>;
  options?: BroadcastOptions;
}

export interface BroadcastEventResponse {
  success: boolean;
  eventId?: string;
  recipientCount?: number;
  error?: string;
}

export interface GetActiveConnectionsResponse {
  connections: RealTimeConnection[];
  totalCount: number;
  analytics: RealTimeAnalytics;
}
