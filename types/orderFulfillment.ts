// types/orderFulfillment.ts
import { Types } from 'mongoose';

// Order fulfillment status types
export type FulfillmentStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'packed'
  | 'shipped'
  | 'out_for_delivery'
  | 'delivered'
  | 'failed'
  | 'cancelled'
  | 'returned';

// Shipping provider types
export type ShippingProvider = 'courier_guy' | 'fastway' | 'dawn_wing' | 'aramex' | 'pudo' | 'self_collection';

// Order priority levels
export type OrderPriority = 'low' | 'normal' | 'high' | 'urgent';

// Fulfillment step interface
export interface FulfillmentStep {
  id: string;
  name: string;
  status: FulfillmentStatus;
  description: string;
  completedAt?: Date;
  estimatedCompletion?: Date;
  notes?: string;
  assignedTo?: string;
  duration?: number; // in minutes
}

// Shipping information
export interface ShippingInfo {
  provider: ShippingProvider;
  trackingNumber?: string;
  trackingUrl?: string;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  shippingCost: number;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    instructions?: string;
  };
  packageDimensions?: {
    length: number;
    width: number;
    height: number;
    weight: number;
  };
}

// Inventory item interface
export interface InventoryItem {
  productId: string;
  sku: string;
  name: string;
  quantityAvailable: number;
  quantityReserved: number;
  quantityAllocated: number;
  location?: string;
  batchNumber?: string;
  expiryDate?: Date;
  lastUpdated: Date;
}

// Order fulfillment interface
export interface OrderFulfillment {
  _id: string;
  orderId: string;
  groupOrderId?: string;
  status: FulfillmentStatus;
  priority: OrderPriority;
  steps: FulfillmentStep[];
  shippingInfo?: ShippingInfo;
  inventoryAllocations: Array<{
    productId: string;
    quantity: number;
    allocatedFrom: string; // warehouse/location
    reservedAt: Date;
    expiresAt: Date;
  }>;
  fulfillmentNotes: string[];
  estimatedFulfillmentDate?: Date;
  actualFulfillmentDate?: Date;
  assignedWarehouse?: string;
  assignedStaff?: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Database model interface
export interface IOrderFulfillment extends OrderFulfillment {
  _id: Types.ObjectId;
  orderId: Types.ObjectId;
  groupOrderId?: Types.ObjectId;
}

// Order status update request
export interface UpdateOrderStatusRequest {
  orderId: string;
  status: FulfillmentStatus;
  notes?: string;
  estimatedCompletion?: Date;
  assignedTo?: string;
}

// Order status update response
export interface UpdateOrderStatusResponse {
  success: boolean;
  fulfillment?: OrderFulfillment;
  error?: string;
}

// Shipping tracking update
export interface ShippingTrackingUpdate {
  trackingNumber: string;
  status: string;
  location: string;
  timestamp: Date;
  description: string;
  estimatedDelivery?: Date;
}

// Fulfillment analytics
export interface FulfillmentAnalytics {
  totalOrders: number;
  ordersByStatus: Record<FulfillmentStatus, number>;
  averageFulfillmentTime: number; // in hours
  onTimeDeliveryRate: number; // percentage
  topPerformingWarehouses: Array<{
    warehouse: string;
    ordersProcessed: number;
    averageTime: number;
  }>;
  shippingProviderPerformance: Array<{
    provider: ShippingProvider;
    ordersShipped: number;
    onTimeRate: number;
    averageDeliveryTime: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    ordersProcessed: number;
    averageFulfillmentTime: number;
    onTimeRate: number;
  }>;
}

// Warehouse capacity and performance
export interface WarehouseInfo {
  id: string;
  name: string;
  location: string;
  capacity: number;
  currentLoad: number;
  staffCount: number;
  operatingHours: {
    open: string;
    close: string;
    timezone: string;
  };
  supportedShippingProviders: ShippingProvider[];
  averageProcessingTime: number; // in hours
  performanceRating: number; // 1-5 stars
}

// Fulfillment workflow configuration
export interface FulfillmentWorkflow {
  id: string;
  name: string;
  description: string;
  steps: Array<{
    stepId: string;
    name: string;
    description: string;
    estimatedDuration: number; // in minutes
    requiredRole?: string;
    autoComplete?: boolean;
    dependencies?: string[]; // other step IDs that must complete first
  }>;
  triggers: Array<{
    event: string;
    condition?: string;
    action: string;
  }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Bulk order processing
export interface BulkOrderProcessing {
  batchId: string;
  orderIds: string[];
  action: 'confirm' | 'pack' | 'ship' | 'cancel';
  parameters?: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: {
    total: number;
    processed: number;
    successful: number;
    failed: number;
  };
  results: Array<{
    orderId: string;
    success: boolean;
    error?: string;
  }>;
  createdAt: Date;
  completedAt?: Date;
}

// Real-time fulfillment updates
export interface FulfillmentUpdate {
  type: 'status_change' | 'shipping_update' | 'inventory_allocation' | 'note_added';
  orderId: string;
  timestamp: Date;
  data: any;
  userId?: string;
  automated: boolean;
}

// API request/response types
export interface GetOrderFulfillmentRequest {
  orderId: string;
}

export interface GetOrderFulfillmentResponse {
  fulfillment: OrderFulfillment;
  timeline: FulfillmentStep[];
}

export interface CreateFulfillmentRequest {
  orderId: string;
  priority?: OrderPriority;
  assignedWarehouse?: string;
  notes?: string;
}

export interface CreateFulfillmentResponse {
  success: boolean;
  fulfillment?: OrderFulfillment;
  error?: string;
}

export interface AllocateInventoryRequest {
  orderId: string;
  allocations: Array<{
    productId: string;
    quantity: number;
    warehouseId: string;
  }>;
}

export interface AllocateInventoryResponse {
  success: boolean;
  allocations?: Array<{
    productId: string;
    allocated: number;
    available: number;
  }>;
  error?: string;
}

export interface ShipOrderRequest {
  orderId: string;
  shippingProvider: ShippingProvider;
  trackingNumber?: string;
  estimatedDelivery?: Date;
  shippingCost?: number;
}

export interface ShipOrderResponse {
  success: boolean;
  trackingInfo?: {
    trackingNumber: string;
    trackingUrl: string;
    estimatedDelivery: Date;
  };
  error?: string;
}
