# 🎯 Referral System Usage Guide

## **🔗 How to Access the Referral UI**

### **For Existing Users:**
1. **Log into your account** at `/login`
2. **Navigate to your profile** by clicking your profile icon
3. **Click "Referrals & Rewards"** in the left sidebar (Gift icon)
4. **Access the full dashboard** at `/profile/referrals`

### **Direct URL Access:**
```
https://your-domain.com/profile/referrals
```

## **👥 How Referral Codes Work with User Registration**

### **🔄 Complete Referral Flow:**

#### **Step 1: User Gets Referral Code**
- Every user automatically gets a unique referral code when they register
- Example: `ABC123XYZ` (8-character alphanumeric code)
- Users can find their code in the referral dashboard

#### **Step 2: Sharing the Referral**
Users can share their referral in multiple ways:

**Option A: Direct Link**
```
https://your-domain.com/signup?ref=ABC123XYZ
```

**Option B: Social Media Sharing**
- WhatsApp: Pre-formatted message with link
- Facebook: Social media post with link
- Twitter: Tweet with referral message
- Email: Email template with referral link

**Option C: Manual Code Sharing**
- Just share the code: `ABC123XYZ`
- Friend enters it during registration

#### **Step 3: Friend Registration Process**

**Scenario A: Using Referral Link**
1. Friend clicks referral link: `https://your-domain.com/signup?ref=ABC123XYZ`
2. Signup form automatically detects referral code from URL
3. Shows welcome message: "🎉 You're invited by [Referrer Name]!"
4. Friend completes registration normally
5. System automatically links them to the referrer

**Scenario B: Manual Code Entry**
1. Friend goes to `/signup` normally
2. Sees referral code field (if we add it to the form)
3. Enters code: `ABC123XYZ`
4. System validates and links them to referrer

## **🎁 Reward System Association**

### **Automatic User Association:**
```
User Registration → Referral Code Detection → User Linking → Reward Tracking
```

1. **New User Registration:**
   - Gets unique referral code: `DEF456GHI`
   - If referred: `referrerId` points to referrer's user ID
   - Creates entry in `Referral` model

2. **Referral Tracking:**
   - Referrer: User who shared the code
   - Referee: New user who used the code
   - Status: `pending` → `completed` → `rewarded`

3. **Points Allocation:**
   - **Referee (new user):** 100 points welcome bonus
   - **Referrer:** 200 points when referee makes first purchase
   - **Additional:** Tier bonuses and multipliers

## **📊 Database Structure**

### **User Model:**
```typescript
{
  _id: ObjectId,
  name: "John Doe",
  email: "<EMAIL>",
  referralCode: "ABC123XYZ",  // Their unique code
  referrerId: ObjectId,       // Who referred them (if any)
  // ... other fields
}
```

### **Referral Model:**
```typescript
{
  _id: ObjectId,
  referrerId: ObjectId,       // User who made the referral
  referredUserId: ObjectId,   // User who was referred
  referralCode: "ABC123XYZ",  // Code that was used
  createdAt: Date,
  earnings: [
    {
      purchaseAmount: 150.00,
      percentage: 5,
      earningAmount: 7.50
    }
  ]
}
```

### **UserLoyalty Model:**
```typescript
{
  _id: ObjectId,
  userId: ObjectId,
  totalPoints: 1500,
  availablePoints: 1200,
  currentTier: "Silver",
  pointsHistory: [
    {
      action: "referral",
      points: 200,
      description: "Successful referral bonus",
      createdAt: Date
    }
  ]
}
```

## **🚀 Testing the Referral System**

### **Test Scenario 1: Complete Referral Flow**

1. **Create User A (Referrer):**
   ```
   POST /api/users
   {
     "name": "Alice Smith",
     "email": "<EMAIL>",
     "phone": "0712345678",
     "password": "Password123!"
   }
   ```
   Response includes: `referralCode: "ABC123XYZ"`

2. **User A Accesses Referral Dashboard:**
   - Login as Alice
   - Go to `/profile/referrals`
   - Copy referral link or code

3. **User B (Referee) Signs Up:**
   - Visit: `https://your-domain.com/signup?ref=ABC123XYZ`
   - See welcome message with Alice's name
   - Complete registration
   - Automatically linked to Alice

4. **Verify Association:**
   - Check User B's `referrerId` field points to Alice's ID
   - Check `Referral` model has entry linking them
   - Alice sees User B in her referral dashboard

### **Test Scenario 2: Points and Rewards**

1. **User B Makes First Purchase:**
   - Complete an order for R100
   - System awards:
     - User B: 10 points (R100 ÷ 10)
     - Alice: 200 points (referral bonus)

2. **Check Loyalty Points:**
   - Alice: `/api/loyalty/user/[aliceId]`
   - User B: `/api/loyalty/user/[userBId]`

3. **Redeem Rewards:**
   - Alice can redeem 200 points for R20 discount
   - Or save for cash withdrawal (1000 points = R100)

## **🔧 API Endpoints for Testing**

### **Get User's Referral Data:**
```
GET /api/referrals/user/[userId]
Authorization: Bearer [token]
```

### **Get Referrer Info:**
```
GET /api/referrals/info?code=ABC123XYZ
```

### **Generate Referral Link:**
```
POST /api/referrals?action=generate
Authorization: Bearer [token]
{
  "userId": "user_id",
  "programId": "default_program_id"
}
```

### **Share Referral:**
```
POST /api/referrals?action=share
Authorization: Bearer [token]
{
  "platform": "whatsapp",
  "referralCode": "ABC123XYZ"
}
```

### **Get Loyalty Points:**
```
GET /api/loyalty/user/[userId]
Authorization: Bearer [token]
```

### **Redeem Points:**
```
POST /api/loyalty?action=redeem
Authorization: Bearer [token]
{
  "userId": "user_id",
  "rewardType": "discount",
  "pointsCost": 500
}
```

## **📱 Mobile/WhatsApp Integration**

### **WhatsApp Sharing:**
The system generates WhatsApp-specific links:
```
https://wa.me/?text=🎉%20Join%20me%20on%20Stokvel%20and%20get%2010%25%20off%20your%20first%20order!%20Use%20my%20code:%20ABC123XYZ%20https://your-domain.com/signup?ref=ABC123XYZ
```

### **Contact Integration:**
- Support WhatsApp: +2727658079493
- Users can get help with referral issues

## **🎯 Key Features Summary**

✅ **Automatic Code Generation:** Every user gets a unique referral code
✅ **URL Parameter Detection:** Referral codes captured from signup links
✅ **Social Media Integration:** One-click sharing to WhatsApp, Facebook, Twitter
✅ **Points System:** Earn points for referrals, purchases, reviews, sharing
✅ **Multiple Redemption Options:** Discounts, delivery credits, cash withdrawals
✅ **Tier System:** Bronze → Silver → Gold → Platinum progression
✅ **Real-time Tracking:** Live referral statistics and conversion rates
✅ **Mobile Optimized:** WhatsApp integration for South African market

The referral system is now fully functional and ready for production use!
