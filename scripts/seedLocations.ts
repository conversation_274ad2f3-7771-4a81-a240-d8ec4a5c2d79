// scripts/seedLocations.ts

import { connectToDatabase } from '@/lib/dbconnect';
import { Province } from '@/models/Province';
import { City } from '@/models/City';
import { Township } from '@/models/Township';
import { Location } from '@/models/Location';
import { SOUTH_AFRICAN_LOCATIONS } from '@/lib/seedData/southAfricanLocations';

/**
 * Comprehensive seeding script for South African location hierarchy
 * Run with: npx tsx scripts/seedLocations.ts
 */

export async function seedLocations() {
  console.log('🌱 Starting South African location seeding...');
  
  try {
    await connectToDatabase();
    console.log('✅ Connected to database');
    
    let totalCreated = {
      provinces: 0,
      cities: 0,
      townships: 0,
      locations: 0
    };
    
    // Seed provinces
    console.log('\n📍 Seeding provinces...');
    for (const provinceData of SOUTH_AFRICAN_LOCATIONS.provinces) {
      const existingProvince = await Province.findOne({ code: provinceData.code });
      
      if (!existingProvince) {
        const province = await Province.create(provinceData);
        console.log(`  ✅ Created province: ${province.name} (${province.code})`);
        totalCreated.provinces++;
      } else {
        console.log(`  ⏭️  Province already exists: ${existingProvince.name} (${existingProvince.code})`);
      }
    }
    
    // Seed cities
    console.log('\n🏙️  Seeding cities...');
    for (const [provinceCode, cities] of Object.entries(SOUTH_AFRICAN_LOCATIONS.cities)) {
      const province = await Province.findOne({ code: provinceCode });
      
      if (!province) {
        console.log(`  ❌ Province not found for code: ${provinceCode}`);
        continue;
      }
      
      for (const cityName of cities) {
        const existingCity = await City.findOne({ 
          name: cityName, 
          provinceId: province._id 
        });
        
        if (!existingCity) {
          const city = await City.create({
            name: cityName,
            provinceId: province._id
          });
          console.log(`    ✅ Created city: ${city.name} in ${province.name}`);
          totalCreated.cities++;
        } else {
          console.log(`    ⏭️  City already exists: ${cityName} in ${province.name}`);
        }
      }
    }
    
    // Seed townships
    console.log('\n🏘️  Seeding townships...');
    for (const [cityName, townships] of Object.entries(SOUTH_AFRICAN_LOCATIONS.townships)) {
      const city = await City.findOne({ name: cityName }).populate('provinceId');
      
      if (!city) {
        console.log(`  ❌ City not found: ${cityName}`);
        continue;
      }
      
      for (const townshipName of townships) {
        const existingTownship = await Township.findOne({
          name: townshipName,
          cityId: city._id
        });
        
        if (!existingTownship) {
          const township = await Township.create({
            name: townshipName,
            cityId: city._id
          });
          console.log(`      ✅ Created township: ${township.name} in ${city.name}`);
          totalCreated.townships++;
        } else {
          console.log(`      ⏭️  Township already exists: ${townshipName} in ${city.name}`);
        }
      }
    }
    
    // Seed locations
    console.log('\n📍 Seeding locations...');
    for (const [townshipName, locations] of Object.entries(SOUTH_AFRICAN_LOCATIONS.locations)) {
      const township = await Township.findOne({ name: townshipName })
        .populate({
          path: 'cityId',
          populate: {
            path: 'provinceId'
          }
        });
      
      if (!township) {
        console.log(`  ❌ Township not found: ${townshipName}`);
        continue;
      }
      
      for (const locationName of locations) {
        const existingLocation = await Location.findOne({
          name: locationName,
          townshipId: township._id
        });
        
        if (!existingLocation) {
          const location = await Location.create({
            name: locationName,
            townshipId: township._id,
            description: `Location in ${townshipName}`
          });
          console.log(`        ✅ Created location: ${location.name} in ${township.name}`);
          totalCreated.locations++;
        } else {
          console.log(`        ⏭️  Location already exists: ${locationName} in ${township.name}`);
        }
      }
    }
    
    // Summary
    console.log('\n🎉 Location seeding completed!');
    console.log('\n📊 Summary:');
    console.log(`  Provinces created: ${totalCreated.provinces}`);
    console.log(`  Cities created: ${totalCreated.cities}`);
    console.log(`  Townships created: ${totalCreated.townships}`);
    console.log(`  Locations created: ${totalCreated.locations}`);
    console.log(`  Total new records: ${Object.values(totalCreated).reduce((a, b) => a + b, 0)}`);
    
    // Final counts
    const finalCounts = {
      provinces: await Province.countDocuments({ isActive: true }),
      cities: await City.countDocuments({ isActive: true }),
      townships: await Township.countDocuments({ isActive: true }),
      locations: await Location.countDocuments({ isActive: true })
    };
    
    console.log('\n📈 Final database counts:');
    console.log(`  Total provinces: ${finalCounts.provinces}`);
    console.log(`  Total cities: ${finalCounts.cities}`);
    console.log(`  Total townships: ${finalCounts.townships}`);
    console.log(`  Total locations: ${finalCounts.locations}`);
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

/**
 * Clean up function to remove all location data (use with caution!)
 */
export async function cleanupLocations() {
  console.log('🧹 Cleaning up location data...');
  
  try {
    await connectToDatabase();
    
    const deletedCounts = {
      locations: await Location.deleteMany({}),
      townships: await Township.deleteMany({}),
      cities: await City.deleteMany({}),
      provinces: await Province.deleteMany({})
    };
    
    console.log('✅ Cleanup completed:');
    console.log(`  Deleted locations: ${deletedCounts.locations.deletedCount}`);
    console.log(`  Deleted townships: ${deletedCounts.townships.deletedCount}`);
    console.log(`  Deleted cities: ${deletedCounts.cities.deletedCount}`);
    console.log(`  Deleted provinces: ${deletedCounts.provinces.deletedCount}`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

// Run seeding if this script is executed directly
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'cleanup') {
    cleanupLocations()
      .then(() => {
        console.log('🎉 Cleanup completed successfully!');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Cleanup failed:', error);
        process.exit(1);
      });
  } else {
    seedLocations()
      .then(() => {
        console.log('🎉 Seeding completed successfully!');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Seeding failed:', error);
        process.exit(1);
      });
  }
}
