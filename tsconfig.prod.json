{"extends": "./tsconfig.json", "compilerOptions": {"strict": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "noImplicitUseStrict": false, "alwaysStrict": false, "strictNullChecks": false, "strictBindCallApply": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "useUnknownInCatchVariables": false, "allowUnreachableCode": true, "allowUnusedLabels": true, "noFallthroughCasesInSwitch": false, "noUnusedLocals": false, "noUnusedParameters": false}, "exclude": ["node_modules", "__tests__", "e2e", "test-results", "playwright-report", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}