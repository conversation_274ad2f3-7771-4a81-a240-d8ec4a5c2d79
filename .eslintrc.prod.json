{"extends": ["next/core-web-vitals"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/prefer-as-const": "off", "@typescript-eslint/no-inferrable-types": "off", "react-hooks/exhaustive-deps": "off", "@next/next/no-img-element": "off", "jsx-a11y/role-supports-aria-props": "off", "jsx-a11y/aria-props": "off", "jsx-a11y/alt-text": "off", "react/no-unescaped-entities": "off", "react/display-name": "off", "prefer-const": "off", "no-unused-vars": "off", "no-console": "off", "no-debugger": "off"}}