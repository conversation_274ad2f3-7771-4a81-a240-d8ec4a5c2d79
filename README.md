This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Testing

This project includes a comprehensive testing setup with Jest for unit and integration tests, and Playwright for end-to-end tests. The testing module is excluded from production builds to keep the application lightweight.

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific types of tests
npm run test:api
npm run test:components
npm run test:hooks
npm run test:redux

# Run end-to-end tests
npm run test:e2e

# Run end-to-end tests with UI
npm run test:e2e:ui
```

For more information about the testing setup, see the [TESTING.md](TESTING.md) file.

For information about deploying the application without test files, see the [DEPLOYMENT.md](DEPLOYMENT.md) file.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

### Deploying Without Test Files

This project is configured to exclude test files from production builds and deployments. This is achieved through several mechanisms:

1. **Custom Build Script**: The `build.js` script handles the build process and ensures that only production dependencies are installed.

2. **Production Package.json**: The `package.prod.json` file contains only the dependencies needed for production.

3. **Vercel Configuration**: The `vercel.json` file configures Vercel to use the custom build script and ignore test files.

4. **Ignore Files**: The `.vercelignore`, `.npmignore`, and `.dockerignore` files exclude test files from being uploaded to their respective platforms.

To deploy the application:

```bash
# For Vercel
vercel

# For manual deployment
./deploy.sh  # Linux/Mac
deploy.bat   # Windows
```

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
