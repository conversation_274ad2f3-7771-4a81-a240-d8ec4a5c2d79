# Test files and directories
__tests__/
e2e/
test-results/
playwright-report/
playwright/.cache/
jest.config.js
jest.setup.js
playwright.config.ts
TESTING.md
coverage/
tsconfig.test.json

# Development files
.github/
.vscode/
.idea/
*.log
*.tsbuildinfo

# Source control
.git/
.gitignore

# Environment variables
.env*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build artifacts
.next/
out/
build/
dist/
node_modules/

# Misc
.DS_Store
*.pem
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Docker files
Dockerfile
.dockerignore
