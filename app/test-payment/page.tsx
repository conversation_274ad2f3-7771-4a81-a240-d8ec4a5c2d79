'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PaymentMethodSelector } from '@/components/payments/PaymentMethodSelector';
import { PaymentForm } from '@/components/payments/PaymentForm';
import { PaymentConfirmation } from '@/components/payments/PaymentConfirmation';
import { 
  PaymentMethodType, 
  PaymentFormData, 
  PaymentResult,
  ProcessPaymentRequest,
  ProcessPaymentResponse 
} from '@/types/payment';
import { toast } from 'sonner';

export default function TestPaymentPage() {
  const [step, setStep] = useState<'method' | 'form' | 'confirmation'>('method');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethodType>('credit_card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentResult, setPaymentResult] = useState<PaymentResult | null>(null);
  
  const testAmount = 299.99;

  const handleMethodSelect = (method: PaymentMethodType) => {
    setSelectedMethod(method);
    setStep('form');
  };

  const handleFormSubmit = async (formData: PaymentFormData) => {
    setIsProcessing(true);
    
    try {
      // Simulate payment processing
      const paymentData = {
        orderId: 'test_order_' + Date.now(),
        userId: 'test_user_123',
        amount: testAmount,
        currency: 'ZAR',
        paymentMethod: formData.paymentMethod,
        provider: getPaymentProvider(formData.paymentMethod),
        description: 'Test payment for development',
        metadata: {
          test: true,
          timestamp: new Date().toISOString()
        }
      };

      // Add payment method specific data
      if (formData.paymentMethod === 'credit_card') {
        paymentData.creditCard = {
          cardNumber: formData.cardNumber || '',
          expiryMonth: formData.expiryMonth || '',
          expiryYear: formData.expiryYear || '',
          cvv: formData.cvv || '',
          cardholderName: formData.cardholderName || ''
        };
      } else if (formData.paymentMethod === 'bank_transfer') {
        paymentData.bankTransfer = {
          bankName: formData.bankName || '',
          accountNumber: formData.accountNumber || '',
          accountHolderName: formData.accountHolderName || ''
        };
      } else if (formData.paymentMethod === 'eft') {
        paymentData.eft = {
          bankName: formData.bankName || '',
          accountNumber: formData.accountNumber || '',
          branchCode: formData.branchCode || '',
          accountHolderName: formData.accountHolderName || ''
        };
      }

      const request: ProcessPaymentRequest = {
        paymentData,
        options: {
          savePaymentMethod: formData.savePaymentInfo,
          sendReceipt: true,
          notifyUser: true
        }
      };

      // Call our payment API
      const response = await fetch('/api/payments/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result: ProcessPaymentResponse = await response.json();

      if (result.success && result.result) {
        setPaymentResult(result.result);
        setStep('confirmation');
        toast.success('Payment processed successfully!');
      } else {
        toast.error(result.error || 'Payment processing failed');
        // Create a failed payment result for demonstration
        setPaymentResult({
          success: false,
          paymentId: 'failed_' + Date.now(),
          status: 'failed',
          amount: testAmount,
          currency: 'ZAR',
          error: result.error || 'Payment processing failed'
        });
        setStep('confirmation');
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('Payment processing failed');
      
      // Create a failed payment result for demonstration
      setPaymentResult({
        success: false,
        paymentId: 'error_' + Date.now(),
        status: 'failed',
        amount: testAmount,
        currency: 'ZAR',
        error: 'Network error occurred'
      });
      setStep('confirmation');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRetry = () => {
    setStep('method');
    setPaymentResult(null);
  };

  const handleContinue = () => {
    toast.success('Redirecting to order confirmation...');
    // In a real app, redirect to order confirmation page
  };

  const handleViewOrder = (orderId: string) => {
    toast.info(`Viewing order: ${orderId}`);
    // In a real app, redirect to order details page
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Payment System Test</h1>
        <p className="text-gray-600">
          Test the payment system with different payment methods
        </p>
      </div>

      {step === 'method' && (
        <Card>
          <CardHeader>
            <CardTitle>Select Payment Method</CardTitle>
            <CardDescription>
              Choose how you'd like to pay for your test order of R{testAmount.toFixed(2)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PaymentMethodSelector
              selectedMethod={selectedMethod}
              onMethodChange={handleMethodSelect}
              amount={testAmount}
              currency="ZAR"
            />
          </CardContent>
        </Card>
      )}

      {step === 'form' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Details</CardTitle>
              <CardDescription>
                Enter your payment information for {selectedMethod.replace('_', ' ')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">Test Mode</h4>
                <p className="text-sm text-blue-700">
                  This is a test environment. No real payments will be processed.
                  {selectedMethod === 'credit_card' && (
                    <span className="block mt-1">
                      Use test card: 4111 1111 1111 1111, any future date, any CVV
                    </span>
                  )}
                </p>
              </div>
            </CardContent>
          </Card>

          <PaymentForm
            paymentMethod={selectedMethod}
            onSubmit={handleFormSubmit}
            isLoading={isProcessing}
          />

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setStep('method')}
              disabled={isProcessing}
            >
              Back to Payment Methods
            </Button>
          </div>
        </div>
      )}

      {step === 'confirmation' && paymentResult && (
        <div className="space-y-6">
          <PaymentConfirmation
            paymentResult={paymentResult}
            onRetry={handleRetry}
            onContinue={handleContinue}
            onViewOrder={handleViewOrder}
          />
          
          <div className="text-center">
            <Button
              variant="outline"
              onClick={() => {
                setStep('method');
                setPaymentResult(null);
              }}
            >
              Test Another Payment
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to get payment provider
function getPaymentProvider(paymentMethod: PaymentMethodType): string {
  switch (paymentMethod) {
    case 'credit_card':
      return 'stripe';
    case 'bank_transfer':
      return 'payfast';
    case 'eft':
      return 'ozow';
    default:
      return 'stripe';
  }
}
