"use client"

import { Suspense } from "react"
import UserSignup from "@/components/auth/UserSignup"

function RegisterPageContent() {
  return <UserSignup />
}

export default function RegisterPage() {
  return (
    <div className="min-h-screen bg-background">
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      }>
        <RegisterPageContent />
      </Suspense>
    </div>
  )
}

