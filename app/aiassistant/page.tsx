// // Example React component consuming the streaming endpoint
// 'use client';

// import { useState } from 'react';

// export default function AIChat() {
//   const [query, setQuery] = useState('');
//   const [responseText, setResponseText] = useState('');

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setResponseText('');

//     const res = await fetch('/api/ai/ask', {
//       method: 'POST',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify({ query }),
//     });

//     if (!res.body) return;

//     const reader = res.body.getReader();
//     const decoder = new TextDecoder();
//     let done = false;
//     while (!done) {
//       const { value, done: doneReading } = await reader.read();
//       done = doneReading;
//       if (value) {
//         const chunk = decoder.decode(value);
//         setResponseText((prev) => prev + chunk);
//       }
//     }
//   };

//   return (
//     <div>
//       <h1>Ask our AI Assistant</h1>
//       <form onSubmit={handleSubmit}>
//         <input
//           type="text"
//           value={query}
//           onChange={(e) => setQuery(e.target.value)}
//           placeholder="Type your question..."
//         />
//         <button type="submit">Send</button>
//       </form>
//       <pre>{responseText}</pre>
//     </div>
//   );
// }



'use client';

import { useState } from 'react';

export default function ChatComponent() {
  const [query, setQuery] = useState('');
  const [responseText, setResponseText] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setResponseText('');
    const res = await fetch('/api/ai/ask', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query }),
    });

    if (!res.body) return;
    
    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    while (!done) {
      const { value, done: doneReading } = await reader.read();
      done = doneReading;
      if (value) {
        setResponseText((prev) => prev + decoder.decode(value));
      }
    }
  };

  return (
    <div>
      <h1>Ask our AI Assistant</h1>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Your question..."
        />
        <button type="submit">Send</button>
      </form>
      <pre>{responseText}</pre>
    </div>
  );
}
