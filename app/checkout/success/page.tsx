// import Link from 'next/link'
// import { CheckCircle } from 'lucide-react'
// import { But<PERSON> } from "@/components/ui/button"

// export default function CheckoutSuccessPage() {
//   return (
//     <div className="max-w-2xl mx-auto p-6 text-center">
//       <div className="mb-6">
//         <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
//       </div>
//       <h1 className="text-3xl font-bold mb-4">Order Confirmed!</h1>
//       <p className="text-gray-600 mb-8">
//         Thank you for your order. We&apos;ll send you a confirmation email with your order details.
//       </p>
//       <Button asChild>
//         <Link href="/store">Continue Shopping</Link>
//       </Button>
//     </div>
//   )
// }

import Link from 'next/link'
import { Button } from "@/components/ui/button"
import { CheckCircle } from 'lucide-react'

export default function CheckoutSuccessPage() {
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-md mx-auto text-center">
        <CheckCircle className="mx-auto h-16 w-16 text-[#2A7C6C] mb-4" />
        <h1 className="text-3xl font-bold text-[#2A7C6C] mb-4">Order Confirmed!</h1>
        <p className="text-gray-600 mb-8">
          Thank you for your order. We&apos;ve received your payment and will process your order shortly. You will receive an email confirmation with the order details.
        </p>
        <Button asChild className="bg-[#2A7C6C] hover:bg-[#236657] text-white">
          <Link href="/store">Continue Shopping</Link>
        </Button>
      </div>
    </div>
  )
}

