// app/(group)/group/[groupId]/page.tsx

"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/context/AuthContext"
import { useParams, useRouter } from "next/navigation"
import { LoadingScreen } from "@/components/ui/loading-screen"
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership"
import GroupDashboard from "@/components/groups/GroupDashboard"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import GroupOrdersReduxTable from "@/components/admin/tables/GroupOrdersReduxTable"
import { useGetGroupOrdersQuery } from "@/lib/redux/features/cart/cartApiSlice"

interface GroupOrder {
  _id: string;
  groupId: string;
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
  }>;
  status: string;
  createdAt: string;
}

// We don't need this interface anymore since we're using the RTK Query types

export default function GroupPage() {
  const params = useParams<{ groupId: string }>()
  // Using params.groupId directly instead of a separate variable
  const router = useRouter()
  const { user } = useAuth()
  const { userGroups, isLoading, error } = useGroupMembership(user?._id)
  // Removed unused cartItems
  // Using state only for the setter function
  const [, setGroupOrders] = useState<GroupOrder[]>([])
  const groupDetails = userGroups.find((group) => group._id === params.groupId)

  // Use RTK Query to fetch group orders
  const { data: groupOrdersData, isLoading: isLoadingOrders } = useGetGroupOrdersQuery(params.groupId, {
    skip: !params.groupId
  })

  // Update local state when data is fetched
  useEffect(() => {
    if (groupOrdersData) {
      setGroupOrders(groupOrdersData.map(order => ({
        _id: order._id,
        groupId: order.groupId,
        items: order.orderItems?.map(item => ({
          productId: typeof item.product === 'string'
            ? item.product
            : item.product?._id || 'unknown',
          quantity: item.quantity || 0,
          price: item.unitPrice || 0
        })) || [],
        status: order.status,
        createdAt: new Date(order.orderPlacedAt).toISOString()
      })))
    }
  }, [groupOrdersData])

  useEffect(() => {
    if (!isLoading && !groupDetails) {
      router.push("/profile")
    }
  }, [isLoading, groupDetails, router])

  if (isLoading || isLoadingOrders) {
    return <LoadingScreen />
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <Alert variant="destructive" className="max-w-md">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {typeof error === 'string'
              ? error
              : 'An error occurred while loading group data. Please try again.'}
          </AlertDescription>
          <Button className="mt-4" onClick={() => router.push("/profile")}>
            Return to Profile
          </Button>
        </Alert>
      </div>
    )
  }

  if (!groupDetails) {
    return null
  }

  return (
    <div>
      <h1>Group Cart</h1>
      <GroupDashboard groupDetails={groupDetails} />
      <GroupOrdersReduxTable groupId={params.groupId} />
    </div>
  )
}
