// app/(group)/group/[groupId]/layout.tsx
"use client";

import { Inter } from "next/font/google";
import "../../../globals.css";
import { GroupSidebar } from "@/components/navigation/GroupSidebar";
import { GroupTopNavigation } from "@/components/navigation/GroupTopNavigation";
import { ReduxProvider } from "@/app/providers/ReduxProvider";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import AuthProvider, { useAuth } from "@/context/AuthContext";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
// import { GroupOrderProvider } from "@/context/GroupOrderContext";
import type React from "react";
import { CartProvider } from "@/components/cart/CartProvider";

const inter = Inter({ subsets: ["latin"] });

interface GroupLayoutProps {
  children: React.ReactNode;
}

const GroupLayoutContent = ({ children }: GroupLayoutProps) => {
  const { user } = useAuth();
  const params = useParams<{ groupId: string }>();
  const groupId = params.groupId;
  const { userGroups, isLoading: isLoadingGroups } = useGroupMembership(user?._id);
  const [isLoading, setIsLoading] = useState(true);

  // Wait until the user, the groupId parameter, and the user groups are available.
  useEffect(() => {
    if (user && groupId && userGroups.length > 0 && !isLoadingGroups) {
      setIsLoading(false);
    }
  }, [user, groupId, userGroups, isLoadingGroups]);

  if (isLoading) {
    return <LoadingScreen projectName="Stokvel Group" />;
  }

  const groupDetails = userGroups.find((group) => group._id === params.groupId);

  if (!user || !groupDetails) {
    return null;
  }

  return (
    <CartProvider groupId={params.groupId as string}>
      <div className={`flex h-screen bg-gray-50 ${inter.className}`}>
        <GroupSidebar groupName={groupDetails.name} groupId={groupDetails._id} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <GroupTopNavigation
            groupProgress={{
              totalAmount: groupDetails.totalSales || 0,
              targetAmount: 100000,
              nextDeliveryDate: "To be scheduled",
              groupId: groupDetails._id,
            }}
          />
          <main className="flex-1 overflow-x-hidden overflow-y-auto p-8">
            {children}
          </main>
        </div>
      </div>
    </CartProvider>
  );
};

const ProviderWrapper = ({ children }: GroupLayoutProps) => {
  return (
    <ReduxProvider>
      {children}
    </ReduxProvider>
  );
};

export default function GroupLayout({ children }: GroupLayoutProps) {
  return (
    <AuthProvider>
      <ProviderWrapper>
        <GroupLayoutContent>{children}</GroupLayoutContent>
      </ProviderWrapper>
    </AuthProvider>
  );
}