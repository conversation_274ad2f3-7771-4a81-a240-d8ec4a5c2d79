// app/(group)/group/[groupId]/groupcart/page.tsx
"use client";

import React from 'react';
import { useParams } from 'next/navigation';
import GroupOrdersReduxTable from '@/components/admin/tables/GroupOrdersReduxTable';

export default function GroupCartPage() {
    const params = useParams();
    const groupId = params.groupId as string;

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-2xl font-bold mb-4">Group Cart</h1>
            <GroupOrdersReduxTable groupId={groupId} />
        </div>
    );
}
