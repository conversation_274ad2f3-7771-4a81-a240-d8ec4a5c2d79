// app/(group)/group/[groupId]/groupmembers/page.tsx
"use client";

import React from 'react';
import { useParams } from 'next/navigation';
import { useGroupMembership } from '@/lib/redux/hooks/useGroupMembership';
import GroupMembersTable from '@/components/admin/tables/GroupMembersTable';
import { LoadingScreen } from '@/components/ui/loading-screen';

export default function GroupMembersPage() {
    const { groupId } = useParams();
    const { isLoading } = useGroupMembership();

    // Ensure groupId is a string
    const groupIdString = Array.isArray(groupId) ? groupId[0] : groupId;

    if (isLoading) {
        return <LoadingScreen />;
    }

    return (
        <div className="container mx-auto p-4">
            <h1 className="text-2xl font-bold mb-4">Group Members</h1>
            {groupIdString && typeof groupIdString === 'string' && <GroupMembersTable groupId={groupIdString} />}
        </div>
    );
}