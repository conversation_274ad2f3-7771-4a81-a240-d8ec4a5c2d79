// app/(group)/group/[groupId]/products/page.tsx

"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useGetAllProductsQuery } from "@/lib/redux/features/products/productsApiSlice";
import { useGetCategoriesQuery } from "@/lib/redux/features/categories/categoriesApiSlice";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import { ProductSearch } from "@/components/products/ProductSearch";
import { ReduxProductGrid } from "@/components/products/ReduxProductGrid";
import { ProductCategoryTabs } from "@/components/products/ProductCategoryTabs";
import { LoadingScreen } from "@/components/ui/loading-screen";
import type { StokvelGroup } from "@/types/stokvelgroup";

export default function ProductsPage() {
  const { groupId } = useParams();
  const { data: products = [], isLoading: productsLoading } = useGetAllProductsQuery();
  const { data: categories = [], isLoading: categoriesLoading } = useGetCategoriesQuery();
  const { userGroups, isLoading: _isLoadingGroups, error: groupsError } = useGroupMembership();
  const [filteredProducts, setFilteredProducts] = useState<typeof products>([]);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [isInitialized, setIsInitialized] = useState(false);

  // Store the current group ID in localStorage for use in other components
  useEffect(() => {
    if (groupId) {
      localStorage.setItem('currentGroupId', groupId as string);
      console.log('ProductsPage - Set currentGroupId in localStorage:', groupId);
    }
  }, [groupId]);

  // Log any errors with groups
  useEffect(() => {
    if (groupsError) {
      console.error('ProductsPage - Error loading groups:', groupsError);
    }
  }, [groupsError]);

  const currentGroup = userGroups.find((group) => group._id === groupId) as StokvelGroup | undefined;

  useEffect(() => {
    // Only update filtered products if products array has items and we haven't initialized yet
    // or if products length has changed (indicating new data has loaded)
    if (products.length > 0 && (!isInitialized || filteredProducts.length === 0)) {
      setFilteredProducts(products);
      setIsInitialized(true);
    }
  }, [products, isInitialized, filteredProducts.length]);

  const handleSearch = (query: string, category: string) => {
    const filtered = products.filter((product) => {
      const matchesQuery = product.name.toLowerCase().includes(query.toLowerCase());
      const matchesCategory = category === "all" || product.category._id === category;
      return matchesQuery && matchesCategory;
    });
    setFilteredProducts(filtered);
    setSelectedCategory(category);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);

    // Filter products based on the selected category
    const filtered = products.filter((product) => {
      return category === "all" || product.category._id === category;
    });

    setFilteredProducts(filtered);
  };

  // Only show loading screen if products or categories are loading
  // Don't wait for groups to load as we can proceed without them
  if (productsLoading || categoriesLoading) {
    return <LoadingScreen />;
  }

  // If groups are still loading, we can proceed with products
  // This allows the products page to work even if group membership data is not available

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Products for {currentGroup?.name || "Group"}</h1>
      <ProductSearch onSearch={handleSearch} />
      <ProductCategoryTabs
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
      />
      <ReduxProductGrid products={filteredProducts} groupId={groupId as string} />
    </div>
  );
}
