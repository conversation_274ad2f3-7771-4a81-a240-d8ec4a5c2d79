"use client"

import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from '@/components/ui/skeleton'
import { useCalculateGroupDiscountQuery, useGetGroupOrdersQuery } from '@/lib/redux/features/cart/cartApiSlice'
// Removed formatCurrency import - using local implementation
import {
  TrendingUp,
  Target,
  Users,
  ShoppingBag,
  Calendar,
  CheckCircle2,
  Trophy,
  Star,
  ArrowUp,
  ArrowDown,
  Minus,
  ChevronRight,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'
// Using native JavaScript Date methods instead of date-fns

// Date utility functions
const formatCurrencyLocal = (amount: number) => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const getStartOfMonth = (date: Date) => {
  return new Date(date.getFullYear(), date.getMonth(), 1);
};

const getEndOfMonth = (date: Date) => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999);
};

const subtractDays = (date: Date, days: number) => {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
};

const formatDate = (date: Date) => {
  return date.toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

interface GroupProgressMetrics {
  totalOrders: number
  totalValue: number
  totalSavings: number
  averageOrderValue: number
  memberCount: number
  ordersThisMonth: number
  valueThisMonth: number
  savingsThisMonth: number
  discountTier: number
  nextTierProgress: number
  monthlyGrowth: number
  orderGrowth: number
}

export default function GroupProgressPage() {
  const params = useParams<{ groupId: string }>()
  const router = useRouter()

  // RTK Query hooks
  const { data: groupOrders = [], isLoading: ordersLoading } = useGetGroupOrdersQuery(params.groupId)
  const { data: discountInfo, isLoading: discountLoading } = useCalculateGroupDiscountQuery(params.groupId)

  const [groupDetails, setGroupDetails] = useState<{
    name: string;
    description?: string;
    members?: Array<{ id: string; name: string }>;
    createdAt?: string;
  } | null>(null)
  const [loadingGroup, setLoadingGroup] = useState(true)
  const [progressMetrics, setProgressMetrics] = useState<GroupProgressMetrics | null>(null)

  // Fetch group details
  useEffect(() => {
    const fetchGroupDetails = async () => {
      try {
        const response = await fetch(`/api/stokvel-groups/${params.groupId}`)
        if (!response.ok) throw new Error('Failed to fetch group details')
        const data = await response.json()
        setGroupDetails(data)
      } catch (error) {
        console.error('Error fetching group details:', error)
      } finally {
        setLoadingGroup(false)
      }
    }

    fetchGroupDetails()
  }, [params.groupId])

  // Calculate progress metrics
  useEffect(() => {
    if (groupOrders.length > 0 && groupDetails) {
      const now = new Date()
      const monthStart = getStartOfMonth(now)
      const monthEnd = getEndOfMonth(now)
      const lastMonthStart = getStartOfMonth(subtractDays(now, 30))
      const lastMonthEnd = getEndOfMonth(subtractDays(now, 30))

      // Current month orders
      const thisMonthOrders = groupOrders.filter(order => {
        const orderDate = new Date(order.createdAt)
        return orderDate >= monthStart && orderDate <= monthEnd
      })

      // Last month orders for comparison
      const lastMonthOrders = groupOrders.filter(order => {
        const orderDate = new Date(order.createdAt)
        return orderDate >= lastMonthStart && orderDate <= lastMonthEnd
      })

      // Calculate metrics
      const totalValue = groupOrders.reduce((sum, order) => sum + order.totalOrderValue, 0)
      const totalSavings = discountInfo?.discountAmount || 0
      const thisMonthValue = thisMonthOrders.reduce((sum, order) => sum + order.totalOrderValue, 0)
      const lastMonthValue = lastMonthOrders.reduce((sum, order) => sum + order.totalOrderValue, 0)
      const thisMonthSavings = thisMonthOrders.reduce((sum, order) => sum + (order.discountAmount || 0), 0)

      // Calculate growth rates
      const monthlyGrowth = lastMonthValue > 0 ? ((thisMonthValue - lastMonthValue) / lastMonthValue) * 100 : 0
      const orderGrowth = lastMonthOrders.length > 0 ? ((thisMonthOrders.length - lastMonthOrders.length) / lastMonthOrders.length) * 100 : 0

      // Determine discount tier
      let discountTier = 0
      let nextTierProgress = 0
      const tiers = [
        { threshold: 5000, percentage: 10 },
        { threshold: 10000, percentage: 15 },
        { threshold: 20000, percentage: 20 }
      ]

      for (let i = 0; i < tiers.length; i++) {
        if (totalValue >= tiers[i].threshold) {
          discountTier = tiers[i].percentage
        } else {
          const prevThreshold = i > 0 ? tiers[i - 1].threshold : 0
          nextTierProgress = ((totalValue - prevThreshold) / (tiers[i].threshold - prevThreshold)) * 100
          break
        }
      }

      if (discountTier === 20) {
        nextTierProgress = 100 // Max tier reached
      }

      setProgressMetrics({
        totalOrders: groupOrders.length,
        totalValue,
        totalSavings,
        averageOrderValue: groupOrders.length > 0 ? totalValue / groupOrders.length : 0,
        memberCount: groupDetails.members?.length || 0,
        ordersThisMonth: thisMonthOrders.length,
        valueThisMonth: thisMonthValue,
        savingsThisMonth: thisMonthSavings,
        discountTier,
        nextTierProgress,
        monthlyGrowth,
        orderGrowth
      })
    }
  }, [groupOrders, groupDetails, discountInfo])

  const isLoading = loadingGroup || ordersLoading || discountLoading

  if (isLoading) {
    return (
      <div className="container py-8 space-y-8">
        <Skeleton className="h-12 w-1/2" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (!groupDetails) {
    return (
      <div className="container py-12 text-center">
        <h1 className="text-2xl font-bold mb-4">Group Not Found</h1>
        <p className="text-muted-foreground mb-6">
          The group you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
        </p>
        <Button onClick={() => router.push('/')}>
          Back to Home
        </Button>
      </div>
    )
  }

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <ArrowUp className="h-4 w-4 text-green-500" />
    if (growth < 0) return <ArrowDown className="h-4 w-4 text-red-500" />
    return <Minus className="h-4 w-4 text-gray-500" />
  }

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return "text-green-600"
    if (growth < 0) return "text-red-600"
    return "text-gray-600"
  }

  return (
    <div className="container py-8 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[#2F4858]">Group Progress</h1>
          <p className="text-muted-foreground">{groupDetails.name} - Performance Overview</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" asChild>
            <Link href={`/group/${params.groupId}`}>
              <BarChart3 className="h-4 w-4 mr-2" />
              Dashboard
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/group/${params.groupId}/products`}>
              <ShoppingBag className="h-4 w-4 mr-2" />
              Shop Now
            </Link>
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <h3 className="text-2xl font-bold">{progressMetrics?.totalOrders || 0}</h3>
                <div className="flex items-center mt-1">
                  {getGrowthIcon(progressMetrics?.orderGrowth || 0)}
                  <span className={`text-sm ml-1 ${getGrowthColor(progressMetrics?.orderGrowth || 0)}`}>
                    {Math.abs(progressMetrics?.orderGrowth || 0).toFixed(1)}% this month
                  </span>
                </div>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                <h3 className="text-2xl font-bold">{formatCurrencyLocal(progressMetrics?.totalValue || 0)}</h3>
                <div className="flex items-center mt-1">
                  {getGrowthIcon(progressMetrics?.monthlyGrowth || 0)}
                  <span className={`text-sm ml-1 ${getGrowthColor(progressMetrics?.monthlyGrowth || 0)}`}>
                    {Math.abs(progressMetrics?.monthlyGrowth || 0).toFixed(1)}% this month
                  </span>
                </div>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Savings</p>
                <h3 className="text-2xl font-bold">{formatCurrencyLocal(progressMetrics?.totalSavings || 0)}</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {progressMetrics?.discountTier || 0}% discount tier
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-yellow-500/10 flex items-center justify-center">
                <Trophy className="h-6 w-6 text-yellow-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Members</p>
                <h3 className="text-2xl font-bold">{progressMetrics?.memberCount || 0}</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Avg: {formatCurrencyLocal(progressMetrics?.averageOrderValue || 0)}
                </p>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discount Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Discount Tier Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Current Tier</p>
              <div className="flex items-center">
                <Badge variant="secondary" className="mr-2">
                  {progressMetrics?.discountTier || 0}% Off
                </Badge>
                {progressMetrics?.discountTier === 20 && (
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                )}
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground mb-1">Next Tier Progress</p>
              <p className="text-2xl font-bold">
                {progressMetrics?.nextTierProgress.toFixed(0) || 0}%
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Progress value={progressMetrics?.nextTierProgress || 0} className="h-3" />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Current: {formatCurrencyLocal(progressMetrics?.totalValue || 0)}</span>
              <span>
                Next: {progressMetrics?.discountTier === 20 ? 'Max Tier Reached' :
                  progressMetrics?.discountTier === 15 ? formatCurrencyLocal(20000) :
                  progressMetrics?.discountTier === 10 ? formatCurrencyLocal(10000) :
                  formatCurrencyLocal(5000)
                }
              </span>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className={`p-4 rounded-lg border ${progressMetrics?.discountTier >= 10 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between mb-2">
                <Star className={`h-5 w-5 ${progressMetrics?.discountTier >= 10 ? 'text-green-500' : 'text-gray-400'}`} />
                {progressMetrics?.discountTier >= 10 && <CheckCircle2 className="h-4 w-4 text-green-500" />}
              </div>
              <p className="font-semibold">10% Off</p>
              <p className="text-sm text-muted-foreground">{formatCurrencyLocal(5000)} target</p>
            </div>

            <div className={`p-4 rounded-lg border ${progressMetrics?.discountTier >= 15 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between mb-2">
                <Star className={`h-5 w-5 ${progressMetrics?.discountTier >= 15 ? 'text-green-500' : 'text-gray-400'}`} />
                {progressMetrics?.discountTier >= 15 && <CheckCircle2 className="h-4 w-4 text-green-500" />}
              </div>
              <p className="font-semibold">15% Off</p>
              <p className="text-sm text-muted-foreground">{formatCurrencyLocal(10000)} target</p>
            </div>

            <div className={`p-4 rounded-lg border ${progressMetrics?.discountTier >= 20 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between mb-2">
                <Trophy className={`h-5 w-5 ${progressMetrics?.discountTier >= 20 ? 'text-yellow-500' : 'text-gray-400'}`} />
                {progressMetrics?.discountTier >= 20 && <CheckCircle2 className="h-4 w-4 text-green-500" />}
              </div>
              <p className="font-semibold">20% Off</p>
              <p className="text-sm text-muted-foreground">{formatCurrencyLocal(20000)} target</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Monthly Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            This Month&apos;s Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-1">Orders This Month</p>
              <p className="text-3xl font-bold">{progressMetrics?.ordersThisMonth || 0}</p>
              <div className="flex items-center justify-center mt-1">
                {getGrowthIcon(progressMetrics?.orderGrowth || 0)}
                <span className={`text-sm ml-1 ${getGrowthColor(progressMetrics?.orderGrowth || 0)}`}>
                  {Math.abs(progressMetrics?.orderGrowth || 0).toFixed(1)}% vs last month
                </span>
              </div>
            </div>

            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-1">Value This Month</p>
              <p className="text-3xl font-bold">{formatCurrencyLocal(progressMetrics?.valueThisMonth || 0)}</p>
              <div className="flex items-center justify-center mt-1">
                {getGrowthIcon(progressMetrics?.monthlyGrowth || 0)}
                <span className={`text-sm ml-1 ${getGrowthColor(progressMetrics?.monthlyGrowth || 0)}`}>
                  {Math.abs(progressMetrics?.monthlyGrowth || 0).toFixed(1)}% vs last month
                </span>
              </div>
            </div>

            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-1">Savings This Month</p>
              <p className="text-3xl font-bold">{formatCurrencyLocal(progressMetrics?.savingsThisMonth || 0)}</p>
              <p className="text-sm text-muted-foreground mt-1">
                From group discounts
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button asChild className="flex-1">
          <Link href={`/group/${params.groupId}/products`} className="flex items-center justify-center">
            <ShoppingBag className="h-4 w-4 mr-2" />
            Start Shopping
            <ChevronRight className="h-4 w-4 ml-2" />
          </Link>
        </Button>
        <Button variant="outline" asChild className="flex-1">
          <Link href={`/group/${params.groupId}/groupmembers`} className="flex items-center justify-center">
            <Users className="h-4 w-4 mr-2" />
            Invite Members
            <ChevronRight className="h-4 w-4 ml-2" />
          </Link>
        </Button>
      </div>
    </div>
  )
}
