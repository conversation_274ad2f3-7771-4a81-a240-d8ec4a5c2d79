// app/(group)/groups/[groupId]/layout.tsx
"use client";

import { Inter } from "next/font/google";
import "../../../globals.css";
import { GroupSidebar } from "@/components/navigation/GroupSidebar";
import { GroupTopNavigation } from "@/components/navigation/GroupTopNavigation";
import { ReduxProvider } from "@/app/providers/ReduxProvider";
import AuthProvider, { useAuth } from "@/context/AuthContext";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { useParams } from "next/navigation";
import type React from "react";
import { CartContextProvider } from "@/context/CartContext";
import { useGroupMembership } from "@/lib/redux/hooks/useGroupMembership";
import { useGetGroupByIdQuery } from "@/lib/redux/features/groupMembership/groupMembershipApiSlice";

const inter = Inter({ subsets: ["latin"] });

interface GroupLayoutProps {
  children: React.ReactNode;
}

const GroupLayoutContent = ({ children }: GroupLayoutProps) => {
  const { user } = useAuth();
  const params = useParams<{ groupId: string }>();
  const groupId = params.groupId;

  console.log('GroupLayoutContent - groupId:', groupId);
  console.log('GroupLayoutContent - user:', user?._id);

  // Use Redux hooks - we're not using userGroups here but keeping the hook call
  useGroupMembership(user?._id);

  // Fetch group details using RTK Query
  const {
    data: groupDetails,
    isLoading: isLoadingGroup,
    // Not using groupError but keeping it for future error handling
  } = useGetGroupByIdQuery(groupId, {
    skip: !groupId
  });

  // Loading state
  const isLoading = isLoadingGroup || !user;

  if (isLoading) {
    return <LoadingScreen projectName="Stokvel Group" />;
  }

  if (!user || !groupDetails) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Group Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The group you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
          </p>
        </div>
      </div>
    );
  }

  console.log('GroupLayoutContent - Rendering with groupId:', params.groupId);

  return (
    <CartContextProvider groupId={params.groupId as string}>
      <div className={`flex h-screen bg-gray-50 ${inter.className}`}>
        <GroupSidebar groupName={groupDetails.name} groupId={groupDetails._id} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <GroupTopNavigation
            groupProgress={{
              totalAmount: groupDetails.totalSales || 0,
              targetAmount: 100000,
              nextDeliveryDate: "To be scheduled",
              groupId: groupDetails._id,
            }}
          />
          <main className="flex-1 overflow-x-hidden overflow-y-auto p-8">
            {children}
          </main>
        </div>
      </div>
    </CartContextProvider>
  );
};

const ProviderWrapper = ({ children }: GroupLayoutProps) => {
  return (
    <ReduxProvider>
      {children}
    </ReduxProvider>
  );
};

export default function GroupLayout({ children }: GroupLayoutProps) {
  return (
    <AuthProvider>
      <ProviderWrapper>
        <GroupLayoutContent>{children}</GroupLayoutContent>
      </ProviderWrapper>
    </AuthProvider>
  );
}
