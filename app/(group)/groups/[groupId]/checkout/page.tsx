'use client'

import { useParams } from 'next/navigation'
import { StableCheckout } from '@/components/checkout/StableCheckout'
import { Card, CardContent } from '@/components/ui/card'
import { useCartContext } from '@/context/CartContext'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Skeleton } from '@/components/ui/skeleton'
import { DiscountProgressBar } from '@/components/cart/DiscountProgressBar'
import { CartLoadingIndicator } from '@/components/cart/CartLoadingIndicator'
import { ShoppingBag, ArrowLeft, ShoppingCart } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import Link from 'next/link'

export default function GroupCheckoutPage() {
  const params = useParams<{ groupId: string }>()
  const router = useRouter()

  // Use the new CartContext for stable cart data
  const { stableCartData, isLoading } = useCartContext()
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false)

  // Track when cart has initially loaded to distinguish between loading and empty states
  useEffect(() => {
    if (!isLoading && !hasInitiallyLoaded) {
      setHasInitiallyLoaded(true)
    }
  }, [isLoading, hasInitiallyLoaded])

  // Show loading state while cart is loading
  if (isLoading || !hasInitiallyLoaded) {
    return (
      <div className="container py-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-purple-100 p-2 rounded-full">
            <ShoppingBag className="h-6 w-6 text-purple-600" />
          </div>
          <h1 className="text-3xl font-bold">Checkout</h1>
        </div>
        <Skeleton className="h-12 w-1/3 mb-6" />
        <div className="grid gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    )
  }

  // Show empty cart message if cart is truly empty after loading
  if (hasInitiallyLoaded && stableCartData.totalItems === 0) {
    return (
      <div className="container py-8">
        {/* Back button */}
        <Button
          variant="ghost"
          className="mb-4 flex items-center gap-2 text-gray-600 hover:text-gray-900"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center py-12"
        >
          <div className="bg-gray-100 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
            <ShoppingCart className="h-10 w-10 text-gray-400" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h1>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            It looks like you haven't added any items to your cart yet.
            Browse our products and add some items to get started!
          </p>
          <div className="flex gap-4 justify-center">
            <Button variant="outline" onClick={() => router.back()}>
              Go Back
            </Button>
            <Button asChild>
              <Link href={`/groups/${params.groupId}/new-order`}>
                <ShoppingCart className="h-4 w-4 mr-2" />
                Start Shopping
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      {/* Loading indicator */}
      <CartLoadingIndicator />

      {/* Back button */}
      <Button
        variant="ghost"
        className="mb-4 flex items-center gap-2 text-gray-600 hover:text-gray-900"
        onClick={() => router.back()}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Cart
      </Button>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-purple-100 p-2 rounded-full">
            <ShoppingBag className="h-6 w-6 text-purple-600" />
          </div>
          <h1 className="text-3xl font-bold">Checkout</h1>
        </div>

        {/* Order summary */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="pt-6">
                {hasInitiallyLoaded && stableCartData.totalItems > 0 ? (
                  <StableCheckout groupId={params.groupId} />
                ) : (
                  <div className="animate-pulse space-y-4">
                    <div className="h-8 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-32 bg-gray-200 rounded"></div>
                    <div className="h-12 bg-gray-200 rounded w-1/4"></div>
                  </div>
                )}

                {/* Debug cart information - removed for production */}
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            {/* Discount progress */}
            <DiscountProgressBar groupId={params.groupId} />

            {/* Order summary card */}
            <Card>
              <CardContent className="pt-6">
                <h3 className="font-medium text-lg mb-4">Order Summary</h3>
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Items ({stableCartData.totalItems})</span>
                    <span>R {stableCartData.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Shipping</span>
                    <span className="text-green-600">Free</span>
                  </div>
                </div>
                <div className="border-t pt-4 flex justify-between font-medium">
                  <span>Total</span>
                  <span>R {stableCartData.subtotal.toFixed(2)}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
