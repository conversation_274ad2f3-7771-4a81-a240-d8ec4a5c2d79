"use client"

import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { GroupOrderDetails } from '@/components/group-orders/GroupOrderDetails'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/components/ui/use-toast'
import { ArrowLeft } from 'lucide-react'
import { GroupOrderStatus } from '@/types/shoppingCartConstants'
import { useGetGroupOrdersQuery, useUpdateGroupOrderStatusMutation } from '@/lib/redux/features/cart/cartApiSlice'
import { useAuth } from '@/context/AuthContext'

export default function GroupOrderDetailsPage() {
  const params = useParams<{ groupId: string; orderId: string }>()
  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()

  // Use RTK Query hooks
  const { data: groupOrders = [], isLoading: loading } = useGetGroupOrdersQuery(params.groupId)
  const [updateOrderStatus, { isLoading: isUpdating }] = useUpdateGroupOrderStatusMutation()

  // State for tracking status updates
  const [updatingStatus, setUpdatingStatus] = useState(false)

  // Find the specific order from the groupOrders array
  const order = groupOrders.find(order => order._id === params.orderId)

  // Get user ID from auth context
  const isAdmin = true // In a real app, this would be determined by user roles
  const currentUserId = user?._id || 'unknown-user'

  // Handle updating order status
  const handleUpdateStatus = async (newStatus: string) => {
    try {
      setUpdatingStatus(true)

      await updateOrderStatus({
        orderId: params.orderId,
        newStatus: newStatus as GroupOrderStatus,
        userId: user?._id || currentUserId
      }).unwrap()

      toast({
        title: "Order status updated",
        description: `The order status has been changed to ${newStatus.replace(/_/g, ' ')}.`,
      })

      setUpdatingStatus(false)
    } catch (error) {
      console.error('Error updating order status:', error)

      toast({
        title: "Update failed",
        description: "There was an error updating the order status. Please try again.",
        variant: "destructive",
      })

      setUpdatingStatus(false)
    }
  }

  // Handle cancelling the order
  const handleCancelOrder = async () => {
    try {
      setUpdatingStatus(true)

      await updateOrderStatus({
        orderId: params.orderId,
        newStatus: GroupOrderStatus.CANCELLED,
        userId: user?._id || currentUserId
      }).unwrap()

      toast({
        title: "Order cancelled",
        description: "The order has been cancelled successfully.",
      })

      setUpdatingStatus(false)
    } catch (error) {
      console.error('Error cancelling order:', error)

      toast({
        title: "Cancellation failed",
        description: "There was an error cancelling the order. Please try again.",
        variant: "destructive",
      })

      setUpdatingStatus(false)
    }
  }

  if (loading || isUpdating || updatingStatus) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center">
          <Button variant="ghost" className="p-0">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
        </div>

        <div className="space-y-6">
          <Skeleton className="h-[200px] w-full" />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Skeleton className="h-[300px] w-full" />
              <Skeleton className="h-[400px] w-full" />
            </div>
            <div className="space-y-6">
              <Skeleton className="h-[250px] w-full" />
              <Skeleton className="h-[200px] w-full" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="container py-8">
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <h2 className="text-2xl font-bold mb-2">Order Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The order you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
          </p>
          <Button onClick={() => router.push(`/groups/${params.groupId}/orders`)}>
            Back to Orders
          </Button>
        </div>
      </div>
    )
  }

  // Define the proper order type instead of using 'any'
  type OrderType = {
    _id: string;
    groupId: string;
    totalOrderValue: number;
    status: string;
    orderPlacedAt: Date;
    lastUpdatedAt: Date;
    paymentStatus: string;
    userContributions: Array<{
      userId: string;
      userName: string;
      totalSpent: number;
      contributionPercentage: number;
    }>;
    orderItems: Array<{
      product: {
        _id: string;
        name: string;
        price: number;
        image?: string;
      };
      quantity: number;
      userId: string;
      unitPrice: number;
      subtotal: number;
    }>;
    milestones: Array<{
      name: string;
      targetAmount: number;
      isReached: boolean;
      currentAmount: number;
    }>;
    statusHistory?: Array<{
      status: string;
      timestamp: Date;
    }>;
    bulkDiscountTiers: Array<{
      threshold: number;
      discountPercentage: number;
    }>;
    appliedDiscountTier?: {
      threshold: number;
      discountPercentage: number;
    };
  };

  return (
    <div className="container py-8">
      <GroupOrderDetails
        order={order as OrderType}
        currentUserId={currentUserId}
        isAdmin={isAdmin}
        onUpdateStatus={handleUpdateStatus}
        onCancel={handleCancelOrder}
      />
    </div>
  )
}
