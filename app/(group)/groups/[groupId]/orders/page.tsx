"use client"

import React from 'react'
import { useParams } from 'next/navigation'
import { GroupOrderList } from '@/components/group-orders/GroupOrderList'
import { Skeleton } from '@/components/ui/skeleton'
import { useEffect, useState, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { CheckCircle2 } from 'lucide-react'
import { useGetGroupOrdersQuery } from '@/lib/redux/features/cart/cartApiSlice'

function GroupOrdersContent() {
  const params = useParams<{ groupId: string }>()
  const groupId = params.groupId
  const { data: groupOrders = [], isLoading: loading } = useGetGroupOrdersQuery(groupId)
  const searchParams = useSearchParams()
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)

  // Check for success parameter in URL
  useEffect(() => {
    if (searchParams.get('success') === 'true') {
      setShowSuccessAlert(true)

      // Hide the alert after 5 seconds
      const timer = setTimeout(() => {
        setShowSuccessAlert(false)
      }, 5000)

      return () => clearTimeout(timer)
    }
  }, [searchParams])

  // This component uses RTK Query to fetch group orders from the API

  if (loading) {
    return (
      <div className="container py-8 space-y-6">
        <Skeleton className="h-10 w-1/3" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="container py-8">
      {showSuccessAlert && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <CheckCircle2 className="h-5 w-5 text-green-600" />
          <AlertTitle className="text-green-800">Order Successful!</AlertTitle>
          <AlertDescription className="text-green-700">
            Your group order has been successfully created and is now being processed.
          </AlertDescription>
        </Alert>
      )}

      <GroupOrderList
        orders={groupOrders}
        groupId={groupId}
      />
    </div>
  )
}

export default function GroupOrdersPage() {
  return (
    <Suspense fallback={
      <div className="container py-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      </div>
    }>
      <GroupOrdersContent />
    </Suspense>
  )
}
