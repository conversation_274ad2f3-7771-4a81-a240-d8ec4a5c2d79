"use client"

import React, { useEffect, useState } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { GroupOrderList } from '@/components/group-orders/GroupOrderList'
import { useCalculateGroupDiscountQuery, useGetGroupOrdersQuery } from '@/lib/redux/features/cart/cartApiSlice'
import { formatCurrency } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'
import {
  ShoppingBag,
  Users,
  TrendingUp,
  BarChart,
  ShoppingCart,
  ChevronRight,
  CheckCircle2
} from 'lucide-react'
import Link from 'next/link'

export default function GroupDashboardPage() {
  const params = useParams<{ groupId: string }>()
  const router = useRouter()

  // RTK Query hooks
  const { data: groupOrders = [] } = useGetGroupOrdersQuery(params.groupId)
  const { data: discountInfo } = useCalculateGroupDiscountQuery(params.groupId)

  const [groupDetails, setGroupDetails] = useState<{
    name: string;
    description?: string;
    members?: Array<{ id: string; name: string }>;
    totalSales?: number;
    avgOrderValue?: number;
  } | null>(null)
  const [loadingGroup, setLoadingGroup] = useState(true)

  // Fetch group details
  useEffect(() => {
    const fetchGroupDetails = async () => {
      try {
        const response = await fetch(`/api/stokvel-groups/${params.groupId}`)
        if (!response.ok) throw new Error('Failed to fetch group details')
        const data = await response.json()
        setGroupDetails(data)
      } catch (error) {
        console.error('Error fetching group details:', error)
      } finally {
        setLoadingGroup(false)
      }
    }

    fetchGroupDetails()
  }, [params.groupId])

  // Discount information is now fetched using RTK Query

  // Filter active orders (not completed or cancelled)
  const activeOrders = groupOrders.filter(
    order => order.status !== 'completed' && order.status !== 'cancelled'
  )

  // Calculate progress toward next discount tier
  const calculateDiscountProgress = () => {
    if (!discountInfo) return 0

    // Find the appropriate tier based on total value
    const tiers = [
      { threshold: 5000, percentage: 10 },
      { threshold: 10000, percentage: 15 },
      { threshold: 20000, percentage: 20 }
    ]

    const currentTotalValue = groupOrders.reduce(
      (sum, order) => sum + order.totalOrderValue,
      0
    )

    // Find the current tier and next tier
    let currentTier = tiers[0]
    let nextTier = tiers[1]

    for (let i = tiers.length - 1; i >= 0; i--) {
      if (currentTotalValue >= tiers[i].threshold) {
        currentTier = tiers[i]
        nextTier = tiers[i + 1]
        break
      }
    }

    // If we're already at the highest tier
    if (!nextTier) return 100

    // Calculate progress to next tier
    const progressToNextTier = ((currentTotalValue - currentTier.threshold) /
      (nextTier.threshold - currentTier.threshold)) * 100

    return Math.min(Math.max(0, progressToNextTier), 100)
  }

  if (loadingGroup) {
    return (
      <div className="container py-8 space-y-8">
        <Skeleton className="h-12 w-1/2" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-40 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  if (!groupDetails) {
    return (
      <div className="container py-12 text-center">
        <h1 className="text-2xl font-bold mb-4">Group Not Found</h1>
        <p className="text-muted-foreground mb-6">
          The group you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
        </p>
        <Button onClick={() => router.push('/')}>
          Back to Home
        </Button>
      </div>
    )
  }

  return (
    <div className="container py-8 space-y-8">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[#2F4858]">{groupDetails.name}</h1>
          <p className="text-muted-foreground">{groupDetails.description}</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" asChild>
            <Link href={`/groups/${params.groupId}/analytics`}>
              <BarChart className="h-4 w-4 mr-2" />
              Analytics
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/groups/${params.groupId}/new-order`}>
              <ShoppingCart className="h-4 w-4 mr-2" />
              New Order
            </Link>
          </Button>
        </div>
      </div>

      {/* Key stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Members</p>
                <h3 className="text-2xl font-bold">{groupDetails.members?.length || 0}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Orders</p>
                <h3 className="text-2xl font-bold">{activeOrders.length}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Saved</p>
                <h3 className="text-2xl font-bold">{formatCurrency(groupDetails.totalSales || 0)}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Order Value</p>
                <h3 className="text-2xl font-bold">{formatCurrency(groupDetails.avgOrderValue || 0)}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                <BarChart className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discount progress */}
      <Card>
        <CardHeader>
          <CardTitle>Group Discount Progress</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-end">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Current Discount Tier</p>
              <div className="text-2xl font-bold flex items-center">
                {discountInfo && discountInfo.discountPercentage > 0 ? (
                  <>
                    <span>{discountInfo.discountPercentage}% Off</span>
                    <CheckCircle2 className="ml-2 h-5 w-5 text-green-500" />
                  </>
                ) : (
                  <span>No Discount Yet</span>
                )}
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground mb-1">Saved so far</p>
              <p className="text-2xl font-bold text-green-600">
                {discountInfo ? formatCurrency(discountInfo.discountAmount) : formatCurrency(0)}
              </p>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>Progress to next tier</span>
              <span>{Math.round(calculateDiscountProgress())}%</span>
            </div>
            <Progress value={calculateDiscountProgress()} className="h-2" />
          </div>

          <div className="grid grid-cols-3 gap-2 mt-4">
            <Card className={`border ${discountInfo && discountInfo.discountPercentage >= 10 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
              <CardContent className="p-3">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm font-medium">10% Off</p>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(5000)}
                    </p>
                  </div>
                  {discountInfo && discountInfo.discountPercentage >= 10 && (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className={`border ${discountInfo && discountInfo.discountPercentage >= 15 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
              <CardContent className="p-3">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm font-medium">15% Off</p>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(10000)}
                    </p>
                  </div>
                  {discountInfo && discountInfo.discountPercentage >= 15 && (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className={`border ${discountInfo && discountInfo.discountPercentage >= 20 ? 'border-green-500 bg-green-50' : 'border-gray-200'}`}>
              <CardContent className="p-3">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="text-sm font-medium">20% Off</p>
                    <p className="text-xs text-muted-foreground">
                      {formatCurrency(20000)}
                    </p>
                  </div>
                  {discountInfo && discountInfo.discountPercentage >= 20 && (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-4 pt-4 border-t">
            <Button asChild variant="outline" className="w-full">
              <Link href={`/groups/${params.groupId}/new-order`} className="flex items-center justify-center">
                Start a New Order
                <ChevronRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Orders list */}
      <div>
        <GroupOrderList orders={groupOrders} groupId={params.groupId} />
      </div>
    </div>
  )
}
