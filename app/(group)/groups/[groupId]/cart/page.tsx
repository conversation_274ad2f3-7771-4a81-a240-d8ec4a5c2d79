// app/(group)/groups/[groupId]/cart/page.tsx
'use client'

import React from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { StableCartDisplay } from '@/components/cart/StableCartDisplay'
import { DiscountProgressBar } from '@/components/cart/DiscountProgressBar'
import { useCartContext } from '@/context/CartContext'

export default function GroupCartPage() {
  const params = useParams<{ groupId: string }>()
  const { stableCartData } = useCartContext()

  return (
    <div className="container mx-auto max-w-4xl">
      <div className="mb-6">
        <Link href={`/groups/${params.groupId}`}>
          <Button variant="ghost" className="pl-0">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Group Dashboard
          </Button>
        </Link>
      </div>

      <h1 className="text-3xl font-bold mb-6">Your Shopping Cart</h1>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardContent className="p-6">
            <StableCartDisplay groupId={params.groupId} />

            {stableCartData.totalItems > 0 && (
              <div className="mt-6 pt-6 border-t">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="font-semibold text-lg">Group Discount</h3>
                </div>

                <DiscountProgressBar
                  groupId={params.groupId}
                />

                <div className="mt-6 flex justify-end">
                  <Link href={`/groups/${params.groupId}/checkout`}>
                    <Button
                      size="lg"
                      className="bg-[#2A7C6C] hover:bg-[#236358]"
                      disabled={stableCartData.totalItems === 0}
                    >
                      Proceed to Checkout
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
