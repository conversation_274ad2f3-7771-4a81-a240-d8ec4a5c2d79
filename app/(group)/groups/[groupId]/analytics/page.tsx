"use client"

import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent} from "@/components/ui/card"
import { GroupOrderReduxAnalytics } from '@/components/group-orders/GroupOrderReduxAnalytics'
import { formatCurrency } from '@/lib/utils'
import { ArrowLeft, BarChart, ShoppingBag, Users, TrendingUp } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'

export default function GroupAnalyticsPage() {
  const params = useParams<{ groupId: string }>()
  const router = useRouter()
  // No need for useCombinedCart anymore
  const [groupDetails, setGroupDetails] = useState<{
    name: string;
    description?: string;
    members?: Array<{ id: string; name: string }>;
    activeOrders?: number;
    totalSales?: number;
    avgOrderValue?: number;
  } | null>(null)
  const [loadingGroup, setLoadingGroup] = useState(true)

  // Fetch group details
  useEffect(() => {
    const fetchGroupDetails = async () => {
      try {
        const response = await fetch(`/api/stokvel-groups/${params.groupId}`)
        if (!response.ok) throw new Error('Failed to fetch group details')
        const data = await response.json()
        setGroupDetails(data)
      } catch (error) {
        console.error('Error fetching group details:', error)
      } finally {
        setLoadingGroup(false)
      }
    }

    fetchGroupDetails()
  }, [params.groupId])

  if (loadingGroup) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center">
          <Button variant="ghost" className="p-0">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Group
          </Button>
        </div>

        <Skeleton className="h-12 w-1/2" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    )
  }

  if (!groupDetails) {
    return (
      <div className="container py-12 text-center">
        <h1 className="text-2xl font-bold mb-4">Group Not Found</h1>
        <p className="text-muted-foreground mb-6">
          The group you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
        </p>
        <Button onClick={() => router.push('/')}>
          Back to Home
        </Button>
      </div>
    )
  }

  return (
    <div className="container py-8 space-y-8">
      <div className="flex items-center">
        <Button variant="ghost" asChild className="pl-0">
          <Link href={`/groups/${params.groupId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Group
          </Link>
        </Button>
      </div>

      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[#2F4858]">
            {groupDetails.name} Analytics
          </h1>
          <p className="text-muted-foreground">
            Performance metrics and insights for your group
          </p>
        </div>
      </div>

      {/* Key stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Members</p>
                <h3 className="text-2xl font-bold">{groupDetails.members?.length || 0}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Orders</p>
                <h3 className="text-2xl font-bold">{groupDetails.activeOrders || 0}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-orange-500/10 flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Saved</p>
                <h3 className="text-2xl font-bold">{formatCurrency(groupDetails.totalSales || 0)}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Order Value</p>
                <h3 className="text-2xl font-bold">{formatCurrency(groupDetails.avgOrderValue || 0)}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                <BarChart className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Component */}
      <div>
        <GroupOrderReduxAnalytics
          groupId={params.groupId}
        />
      </div>
    </div>
  )
}
