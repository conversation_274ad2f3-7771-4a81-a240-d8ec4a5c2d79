"use client"

import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { GroupOrderReduxCheckout } from '@/components/group-orders/GroupOrderReduxCheckout'
import { OrdersDebugView } from '@/components/orders/OrdersDebugView'
import { useAppSelector } from '@/lib/redux/hooks'
import {
  selectCartItems,
  selectSubtotal
} from '@/lib/redux/features/cart/cartSlice'
import {
  useAddToCartMutation,
  useUpdateCartItemMutation,
  useRemoveFromCartMutation,
  useCalculateGroupDiscountQuery
} from '@/lib/redux/features/cart/cartApiSlice'
import { useAuth } from '@/context/AuthContext'
import { formatCurrency } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'
import {
  ShoppingBag,
  Search,
  Plus,
  Minus,
  Trash2,
  ArrowLeft,
  ShoppingCart,
  Filter,
  CheckCircle2
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export default function NewOrderPage() {
  const params = useParams<{ groupId: string }>()
  const { user } = useAuth()
  const userId = user?._id || ''

  // Redux state
  const cartItems = useAppSelector(selectCartItems)
  const subtotal = useAppSelector(selectSubtotal)

  // RTK Query hooks
  const [addToCart] = useAddToCartMutation()
  const [updateCartItem] = useUpdateCartItemMutation()
  const [removeFromCart] = useRemoveFromCartMutation()
  const { data: discountInfo } = useCalculateGroupDiscountQuery(params.groupId, {
    skip: subtotal <= 0
  })

  const [activeTab, setActiveTab] = useState('products')
  const [products, setProducts] = useState<{
    _id: string;
    name: string;
    description: string;
    price: number;
    category: string;
    image?: string;
  }[]>([])
  const [categories, setCategories] = useState<{
    _id: string;
    name: string;
  }[]>([])
  const [loadingProducts, setLoadingProducts] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch('/api/products')
        if (!response.ok) throw new Error('Failed to fetch products')
        const data = await response.json()
        setProducts(data)
      } catch (error) {
        console.error('Error fetching products:', error)
      } finally {
        setLoadingProducts(false)
      }
    }

    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/product-categories')
        if (!response.ok) throw new Error('Failed to fetch categories')
        const data = await response.json()
        setCategories(data)
      } catch (error) {
        console.error('Error fetching categories:', error)
      }
    }

    fetchProducts()
    fetchCategories()
  }, [])

  // Filter products based on search and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter

    return matchesSearch && matchesCategory
  })

  // Handle add to cart
  const handleAddToCart = async (productId: string) => {
    if (!userId) return;

    try {
      await addToCart({
        userId,
        productId,
        quantity: 1,
        groupId: params.groupId
      }).unwrap();

      // Show notification
      window.dispatchEvent(new CustomEvent('cart:notification:show'));
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  }

  // Handle updating cart item quantity
  const handleUpdateQuantity = async (productId: string, currentQuantity: number, increment: boolean) => {
    if (!userId) return;

    const newQuantity = increment ? currentQuantity + 1 : Math.max(1, currentQuantity - 1);

    try {
      await updateCartItem({
        userId,
        productId,
        quantity: newQuantity,
        groupId: params.groupId
      }).unwrap();
    } catch (error) {
      console.error('Error updating quantity:', error);
    }
  }

  // Handle removing item from cart
  const handleRemoveItem = async (productId: string) => {
    if (!userId) return;

    try {
      await removeFromCart({
        userId,
        productId,
        groupId: params.groupId
      }).unwrap();
    } catch (error) {
      console.error('Error removing item:', error);
    }
  }

  // Find quantity of product in cart
  const getQuantityInCart = (productId: string) => {
    const item = cartItems.find(item => item.productId === productId)
    return item?.quantity || 0
  }

  if (loadingProducts && activeTab === 'products') {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" asChild className="pl-0">
            <Link href={`/groups/${params.groupId}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Group
            </Link>
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="checkout">Checkout</TabsTrigger>
          </TabsList>

          <TabsContent value="products" className="mt-6">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  className="pl-8"
                  disabled
                />
              </div>

              <Select disabled>
                <SelectTrigger className="w-full md:w-[180px]">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    <SelectValue placeholder="All categories" />
                  </div>
                </SelectTrigger>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Skeleton key={i} className="h-72 w-full rounded-lg" />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    )
  }

  return (
    <div className="container py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" asChild className="pl-0">
          <Link href={`/groups/${params.groupId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Group
          </Link>
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="cart">
            Cart
            {cartItems.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {cartItems.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="checkout" disabled={cartItems.length === 0}>Checkout</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>

        <TabsContent value="products" className="mt-6">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <Select
              value={categoryFilter}
              onValueChange={setCategoryFilter}
            >
              <SelectTrigger className="w-full md:w-[180px]">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <SelectValue placeholder="All categories" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category._id} value={category._id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingBag className="mx-auto h-12 w-12 text-muted-foreground mb-3 opacity-50" />
              <h3 className="text-lg font-medium mb-1">No Products Found</h3>
              <p className="text-muted-foreground">
                {searchQuery || categoryFilter
                  ? "Try adjusting your search or filter criteria"
                  : "There are no products available at the moment"}
              </p>
              {(searchQuery || categoryFilter) && (
                <Button
                  variant="link"
                  className="mt-2"
                  onClick={() => {
                    setSearchQuery('')
                    setCategoryFilter('')
                  }}
                >
                  Clear filters
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {filteredProducts.map((product) => {
                const quantityInCart = getQuantityInCart(product._id)

                return (
                  <Card key={product._id} className="overflow-hidden">
                    <div className="aspect-video w-full bg-muted relative">
                      {product.image ? (
                        <Image
                          src={product.image}
                          alt={product.name}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gray-100">
                          <ShoppingBag className="h-10 w-10 text-gray-300" />
                        </div>
                      )}
                      {quantityInCart > 0 && (
                        <div className="absolute top-2 right-2 bg-primary text-white rounded-full h-6 w-6 flex items-center justify-center text-xs font-medium">
                          {quantityInCart}
                        </div>
                      )}
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold line-clamp-1">{product.name}</h3>
                      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                        {product.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="font-bold">{formatCurrency(product.price)}</span>

                        {quantityInCart === 0 ? (
                          <Button
                            size="sm"
                            onClick={() => handleAddToCart(product._id)}
                            className="bg-[#2A7C6C] hover:bg-[#236358]"
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Add
                          </Button>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <Button
                              size="icon"
                              variant="outline"
                              className="h-8 w-8 rounded-full"
                              onClick={() => handleUpdateQuantity(product._id, quantityInCart, false)}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-5 text-center">{quantityInCart}</span>
                            <Button
                              size="icon"
                              variant="outline"
                              className="h-8 w-8 rounded-full"
                              onClick={() => handleUpdateQuantity(product._id, quantityInCart, true)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                              onClick={() => handleRemoveItem(product._id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}

          {cartItems.length > 0 && (
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg p-4 md:hidden">
              <div className="container">
                <div className="flex justify-between items-center mb-1">
                  <div>
                    <span className="text-sm text-muted-foreground">Subtotal</span>
                    <p className="font-bold">{formatCurrency(subtotal)}</p>
                  </div>
                  <Button
                    onClick={() => setActiveTab('checkout')}
                    className="bg-[#2A7C6C] hover:bg-[#236358]"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Checkout ({cartItems.length})
                  </Button>
                </div>
                {discountInfo && discountInfo.discountPercentage > 0 && (
                  <div className="text-xs text-green-600 mt-1 flex items-center">
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    {discountInfo.discountPercentage}% discount applied! You save {formatCurrency(discountInfo.discountAmount)}
                  </div>
                )}
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="cart" className="mt-6">
          {cartItems.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="mx-auto h-12 w-12 text-muted-foreground mb-3 opacity-50" />
              <h3 className="text-lg font-medium mb-1">Your Cart is Empty</h3>
              <p className="text-muted-foreground mb-4">
                Add some products to your cart to get started
              </p>
              <Button
                onClick={() => setActiveTab('products')}
                className="bg-[#2A7C6C] hover:bg-[#236358]"
              >
                Browse Products
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">Your Cart</h2>
                <Badge variant="outline">
                  {cartItems.length} item{cartItems.length !== 1 ? 's' : ''}
                </Badge>
              </div>

              <div className="grid gap-4">
                {cartItems.map((item) => (
                  <Card key={item.productId} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-4">
                        <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center flex-shrink-0">
                          {item.product?.image ? (
                            <Image
                              src={item.product.image}
                              alt={item.product.name}
                              width={64}
                              height={64}
                              className="object-cover rounded-lg"
                            />
                          ) : (
                            <ShoppingBag className="h-6 w-6 text-gray-300" />
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold truncate">{item.product?.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {formatCurrency(item.product?.price || 0)} each
                          </p>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            size="icon"
                            variant="outline"
                            className="h-8 w-8 rounded-full"
                            onClick={() => handleUpdateQuantity(item.productId, item.quantity, false)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center font-medium">{item.quantity}</span>
                          <Button
                            size="icon"
                            variant="outline"
                            className="h-8 w-8 rounded-full"
                            onClick={() => handleUpdateQuantity(item.productId, item.quantity, true)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                            onClick={() => handleRemoveItem(item.productId)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="text-right">
                          <p className="font-bold">
                            {formatCurrency((item.product?.price || 0) * item.quantity)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Cart Summary */}
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-medium">Subtotal</span>
                      <span className="text-lg font-bold">{formatCurrency(subtotal)}</span>
                    </div>

                    {discountInfo && discountInfo.discountPercentage > 0 && (
                      <div className="flex justify-between items-center text-green-600">
                        <span>Group Discount ({discountInfo.discountPercentage}%)</span>
                        <span>-{formatCurrency(discountInfo.discountAmount)}</span>
                      </div>
                    )}

                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-xl font-bold">Total</span>
                        <span className="text-xl font-bold text-[#2A7C6C]">
                          {formatCurrency(discountInfo ? subtotal - discountInfo.discountAmount : subtotal)}
                        </span>
                      </div>
                    </div>

                    <Button
                      onClick={() => setActiveTab('checkout')}
                      className="w-full bg-[#2A7C6C] hover:bg-[#236358] py-6 text-lg"
                      size="lg"
                    >
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      Proceed to Checkout
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="checkout" className="mt-6">
          <GroupOrderReduxCheckout groupId={params.groupId} />
        </TabsContent>

        <TabsContent value="orders" className="mt-6">
          <OrdersDebugView groupId={params.groupId} />
        </TabsContent>
      </Tabs>

      {cartItems.length > 0 && activeTab === 'products' && (
        <div className="fixed bottom-6 right-6 hidden md:block">
          <Button
            onClick={() => setActiveTab('checkout')}
            size="lg"
            className="bg-[#2A7C6C] hover:bg-[#236358] shadow-lg rounded-full h-16 w-16 p-0"
          >
            <div className="flex flex-col items-center">
              <ShoppingCart className="h-5 w-5 mb-1" />
              <span className="text-xs">{cartItems.length}</span>
            </div>
          </Button>
        </div>
      )}
    </div>
  )
}
