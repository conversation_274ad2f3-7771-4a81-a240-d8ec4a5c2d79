"use client"

import { motion } from "framer-motion"
import { ContactInfo } from "@/components/contact/ContactInfo"
import { ContactForm } from "@/components/contact/ContactForm"
import { ContactMethods } from "@/components/contact/ContactMethods"
import { ContactMap } from "@/components/contact/ContactMap"
import { ContactFAQ } from "@/components/contact/ContactFAQ"
import {
  MessageCircle,
  Phone,
  Mail,
  MapPin,
  Clock,
  Sparkles,
  Users,
  Heart
} from "lucide-react"

export default function ContactPage() {
  return (
    <main className="relative bg-gradient-to-br from-slate-50 via-white to-slate-100 overflow-hidden">
      {/* Pattern Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232A7C6C' fill-opacity='0.4'%3E%3Cpath d='M50 50L25 75L75 75z'/%3E%3Cpath d='M50 50L75 25L75 75z'/%3E%3Cpath d='M50 50L25 25L75 25z'/%3E%3Cpath d='M50 50L25 25L25 75z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-[#2A7C6C]/10 rounded-full blur-2xl" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-[#7FDBCA]/20 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-[#2A7C6C]/5 rounded-full blur-xl" />

      {/* Hero Section */}
      <section className="relative py-32 px-4 md:px-6">
        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-block"
            >
              <div className="flex items-center justify-center mb-6">
                <div className="h-16 w-16 bg-gradient-to-br from-[#2A7C6C] to-[#1E5A4F] rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                  <MessageCircle className="h-8 w-8 text-white" />
                </div>
                <h1
                  className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-[#2A7C6C] to-[#1E5A4F] bg-clip-text text-transparent"
                  style={{
                    fontFamily: 'ClashDisplay-Variable, sans-serif',
                    letterSpacing: '-0.03em'
                  }}
                >
                  Let's Connect
                </h1>
              </div>
            </motion.div>

            <div className="w-32 h-1 bg-gradient-to-r from-[#2A7C6C] to-[#7FDBCA] mx-auto mb-8 rounded-full" />

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-gray-600 text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              We're here to help you succeed in your community shopping journey.
              Reach out anytime - we'd love to hear from you!
            </motion.p>
          </div>

          {/* Contact Methods Grid */}
          <ContactMethods />
        </div>
      </section>

      {/* Main Contact Section */}
      <section className="py-20 px-4 md:px-6">
        <div className="container mx-auto max-w-6xl">
          <div className="grid lg:grid-cols-2 gap-16 items-start">
            <ContactInfo />
            <ContactForm />
          </div>
        </div>
      </section>

      {/* Map Section */}
      <ContactMap />

      {/* Quick FAQ Section */}
      <ContactFAQ />

      {/* Support Hours & Additional Info */}
      <section className="py-20 px-4 md:px-6 bg-gradient-to-r from-[#2A7C6C]/5 to-[#7FDBCA]/5">
        <div className="container mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-center mb-6">
              <Sparkles className="h-8 w-8 text-[#2A7C6C] mr-3" />
              <h2
                className="text-3xl md:text-4xl font-bold text-[#2F4858]"
                style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
              >
                We're Here for You
              </h2>
            </div>

            <p
              className="text-gray-600 text-lg md:text-xl mb-8 leading-relaxed max-w-2xl mx-auto"
              style={{ fontFamily: 'Avenir, sans-serif' }}
            >
              Our dedicated support team is committed to helping you make the most of your
              Stokvel experience. Every question matters, every concern is heard.
            </p>

            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg"
              >
                <Clock className="h-8 w-8 text-[#2A7C6C] mx-auto mb-4" />
                <h3
                  className="text-lg font-bold text-[#2F4858] mb-2"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  Support Hours
                </h3>
                <p className="text-gray-600" style={{ fontFamily: 'Avenir, sans-serif' }}>
                  Monday - Friday<br />
                  8:00 AM - 6:00 PM SAST
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg"
              >
                <Users className="h-8 w-8 text-[#2A7C6C] mx-auto mb-4" />
                <h3
                  className="text-lg font-bold text-[#2F4858] mb-2"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  Community First
                </h3>
                <p className="text-gray-600" style={{ fontFamily: 'Avenir, sans-serif' }}>
                  Built by the community,<br />
                  for the community
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg"
              >
                <Heart className="h-8 w-8 text-[#2A7C6C] mx-auto mb-4" />
                <h3
                  className="text-lg font-bold text-[#2F4858] mb-2"
                  style={{ fontFamily: 'ClashDisplay-Variable, sans-serif' }}
                >
                  Ubuntu Spirit
                </h3>
                <p className="text-gray-600" style={{ fontFamily: 'Avenir, sans-serif' }}>
                  We care because<br />
                  your success is ours
                </p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}

