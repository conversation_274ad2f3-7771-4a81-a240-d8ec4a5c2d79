// app/page.tsx
"use client"


import { AboutSection } from "@/components/about/AboutSection"
import { FullWidthVideo } from "@/components/home/<USER>"
import { HeroSlider } from "@/components/home/<USER>"
import { MembershipSection } from "@/components/home/<USER>"
// import { PricingSection } from "@/components/home/<USER>"
import { ProductListing } from "@/components/home/<USER>"
import { ValueProposition } from "@/components/about/ValueProposition"
export default function Home() {

  return (
    <main>
      <HeroSlider/>
      <MembershipSection />
      <AboutSection />
      <ValueProposition />
      <ProductListing/>
      <FullWidthVideo />
      {/* <PricingSection/> */}
    </main>
  )
}
