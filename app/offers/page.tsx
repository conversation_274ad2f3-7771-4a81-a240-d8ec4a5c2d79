import { OffersHero } from "@/components/offers/OffersHero"
import { CategoryOffers } from "@/components/offers/CategoryOffers"
import { FeaturedDeal } from "@/components/offers/FeaturedDeal"
import { LimitedTimeOffers } from "@/components/offers/LimitedTimeOffers"
import { PromotionalBanner } from "@/components/offers/PromotionalBanner"

export default function OffersPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero section with carousel */}
      <OffersHero />

      {/* Featured deal of the day */}
      <FeaturedDeal
        title="Premium Wireless Earbuds"
        subtitle="Noise Cancellation & 24hr Battery"
        description="Experience crystal-clear sound with our premium wireless earbuds featuring active noise cancellation, water resistance, and all-day battery life."
        imageUrl="https://images.unsplash.com/photo-1590658268037-6bf216165a8d?q=80&w=2069&auto=format&fit=crop"
        expiryDate="2023-08-10T23:59:59"
        discount="40% OFF"
        code="AUDIO40"
        originalPrice={2499}
        discountedPrice={1499}
      />

      {/* Limited time offers carousel */}
      <LimitedTimeOffers />

      {/* Mid-page promotional banner */}
      <PromotionalBanner
        title="Join Our Loyalty Program"
        description="Sign up today and get exclusive access to member-only deals, early access to sales, and earn points with every purchase."
        buttonText="Join Now"
        buttonLink="/loyalty"
        bgColor="bg-green-600"
        textColor="text-white"
      />

      {/* Category-based offers with tabs */}
      <CategoryOffers />
    </div>
  )
}

