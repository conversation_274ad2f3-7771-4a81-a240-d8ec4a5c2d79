// app/api/location-requests/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose, { Schema, model } from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import '@/models'; // Ensure all models are loaded
/** Minimal model for location requests */
interface ILocationRequest extends mongoose.Document {
  location: string;
  createdAt: Date;
}
const LocationRequestSchema = new Schema<ILocationRequest>(
  { location: { type: String, required: true } },
  { timestamps: true }
);
const LocationRequest =
  mongoose.models.LocationRequest ||
  model<ILocationRequest>("LocationRequest", LocationRequestSchema);

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    const body = await req.json();
    const { location } = body;
    if (!location) {
      return NextResponse.json(
        { error: "Location is required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    // 1) Create a location request doc
    const newReq = await LocationRequest.create({ location });

    return NextResponse.json(
      { locationRequest: newReq },
      { headers: corsHeaders, status: 201 }
    );
  } catch (error) {
    console.error("Error creating location request:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
