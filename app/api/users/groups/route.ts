// app/api/users/groups/route.ts
import { type NextRequest, NextResponse } from "next/server"
import { connectToDatabase } from "@/lib/dbconnect"
import { User, type IUser } from "@/models/User"
import { StokvelGroup} from "@/models/StokvelGroup"
import type { StokvelGroup as StokvelGroupType } from "@/types/stokvelgroup"
import { getCorsHeaders } from "@/lib/cors"
import { ObjectId } from "mongodb"
import '@/models'; // Ensure all models are loaded

// Define types for lean documents
type LeanUser = Pick<IUser, "_id" | "stokvelGroups">

// Define a more specific type for the lean StokvelGroup document
type LeanStokvelGroup = {
  _id: ObjectId
  name: string
  description: string
  members: ObjectId[]
  admin: ObjectId
  geolocation: string
  totalSales: number
  avgOrderValue: number
  activeOrders: number
  createdAt: Date
  updatedAt: Date
}

export async function GET(request: NextRequest) {
  await connectToDatabase()
  const origin = request.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    const userId = request.nextUrl.searchParams.get("userId")
    console.log(`GET /api/users/groups - Received userId: ${userId}`)

    if (!userId || !ObjectId.isValid(userId)) {
      console.log(`GET /api/users/groups - Invalid userId: ${userId}`)
      return NextResponse.json({ error: "Invalid or missing user ID" }, { status: 400, headers: corsHeaders })
    }

    console.log(`GET /api/users/groups - Finding user with ID: ${userId}`)
    const user = (await User.findById(userId).select("stokvelGroups").lean()) as LeanUser | null

    if (!user) {
      console.log(`GET /api/users/groups - User not found with ID: ${userId}`)
      return NextResponse.json({ error: "User not found" }, { status: 404, headers: corsHeaders })
    }

    console.log(`GET /api/users/groups - User found, stokvelGroups: ${user.stokvelGroups?.length || 0}`)

    if (!user.stokvelGroups || user.stokvelGroups.length === 0) {
      console.log(`GET /api/users/groups - User has no groups, returning empty array`)
      return NextResponse.json([], { headers: corsHeaders })
    }

    console.log(`GET /api/users/groups - Finding groups for user: ${user.stokvelGroups.map(id => id.toString())}`)
    const userGroups = (await StokvelGroup.find({
      _id: { $in: user.stokvelGroups },
    })
      .select(
        "_id name description members admin geolocation totalSales avgOrderValue activeOrders createdAt updatedAt"
      )
      .lean()) as unknown as LeanStokvelGroup[]

    console.log(`GET /api/users/groups - Found ${userGroups.length} groups`)

    const groupsData: StokvelGroupType[] = userGroups.map((group) => ({
      _id: group._id.toString(),
      name: group.name,
      description: group.description,
      geolocation: group.geolocation,
      members: group.members.map((id) => id.toString()),
      admin: group.admin.toString(),
      totalSales: group.totalSales,
      avgOrderValue: group.avgOrderValue,
      activeOrders: group.activeOrders,
      createdAt: group.createdAt.toISOString(),
      updatedAt: group.updatedAt.toISOString(),
    }))

    console.log(`GET /api/users/groups - Returning ${groupsData.length} groups`)
    return NextResponse.json(groupsData, { headers: corsHeaders })
  } catch (error) {
    console.error("Error listing user groups:", {
      error,
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : 'No stack trace',
      userId: request.nextUrl.searchParams.get("userId")
    });

    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500, headers: corsHeaders });
  }
}

