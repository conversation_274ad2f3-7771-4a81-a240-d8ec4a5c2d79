// app/api/users/check-any-group-membership/route.ts
import { type NextRequest, NextResponse } from "next/server"
import { connectToDatabase } from "@/lib/dbconnect"
import { User, type IUser } from "@/models/User"
import { getCorsHeaders } from "@/lib/cors"
import { ObjectId } from "mongodb"
import '@/models'; // Ensure all models are loaded

interface GroupMembershipResponse {
  isMemberOfAnyGroup: boolean;
  groupIds?: string[];
}

export async function GET(request: NextRequest): Promise<NextResponse<{ error: string } | GroupMembershipResponse>> {
  await connectToDatabase()
  const origin = request.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    const userId = request.nextUrl.searchParams.get("userId")
    const email = request.nextUrl.searchParams.get("email")

    // Check if either userId or email is provided
    if (!userId && !email) {
      return NextResponse.json(
        { error: "Either userId or email parameter is required" },
        { status: 400, headers: corsHeaders }
      )
    }

    let user: Pick<IUser, "_id" | "stokvelGroups"> | null = null;

    // Find user by ID if provided and valid
    if (userId && ObjectId.isValid(userId)) {
      user = await User.findById(userId).select("stokvelGroups").lean() as Pick<IUser, "_id" | "stokvelGroups"> | null;
    }
    // Otherwise find by email if provided
    else if (email) {
      user = await User.findOne({ email }).select("stokvelGroups").lean() as Pick<IUser, "_id" | "stokvelGroups"> | null;
    }

    if (!user) {
      return NextResponse.json(
        { isMemberOfAnyGroup: false },
        { headers: corsHeaders }
      )
    }

    const isMemberOfAnyGroup = user.stokvelGroups && user.stokvelGroups.length > 0
    const groupIds = isMemberOfAnyGroup ? user.stokvelGroups.map(id => id.toString()) : undefined;

    return NextResponse.json(
      { isMemberOfAnyGroup, groupIds },
      { headers: corsHeaders }
    )
  } catch (error) {
    console.error("Error checking any group membership:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers: corsHeaders }
    )
  }
}

