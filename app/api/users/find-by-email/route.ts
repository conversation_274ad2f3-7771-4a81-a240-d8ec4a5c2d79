// app/api/users/find-by-email/route.ts
import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { User } from "@/models/User";
import '@/models'; // Ensure all models are loaded

export async function GET(req: NextRequest) {
  // 1) Grab the origin header & generate CORS
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // 2) Parse the query param from URL
    const { searchParams } = new URL(req.url);
    const email = searchParams.get("email");
    if (!email) {
      return NextResponse.json(
        { error: "Email query param required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    // 3) Find user by email
    const user = await User.findOne({ email }).exec();
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Convert your Mongoose doc to JSON & remove password
    const userObj = user.toObject();
    delete userObj.password;
    // optional: delete other sensitive fields

    return NextResponse.json({ user: userObj }, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error("Error finding user by email:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
