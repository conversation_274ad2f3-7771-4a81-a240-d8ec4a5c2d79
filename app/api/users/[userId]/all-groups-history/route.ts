// app/api/users/[userId]/all-groups-history/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { User } from "@/models/User";
import { StokvelGroup } from "@/models/StokvelGroup";
import { ShoppingCart } from "@/models/ShoppingCart";
import { getCorsHeaders } from "@/lib/cors";
import mongoose from "mongoose";
import '@/models'; // Ensure all models are loaded

export async function GET(request: NextRequest) {
  const origin = request.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  // Extract userId from URL path
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const userId = pathParts[pathParts.length - 2]; // The userId is the second-to-last part of the path

  try {
    await connectToDatabase();

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          message: "User ID is required."
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: "User not found."
        },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Get the user's current groups
    const currentGroups = user.stokvelGroups || [];

    // Get the user's previous groups from shopping cart history
    const previousGroupsFromCarts = await ShoppingCart.distinct("groupId", {
      user: userId,
      groupId: { $nin: currentGroups }
    });

    // Combine current and previous groups
    const allGroupIds = [...currentGroups, ...previousGroupsFromCarts];

    // Get group details
    const groupsData = await StokvelGroup.find({
      _id: { $in: allGroupIds }
    });

    // Check for undelivered items in each group
    const groupsWithUndeliveredItems = await Promise.all(
      groupsData.map(async (group) => {
        // Find unfinalized carts for this user in this group
        const unfinalizedCarts = await ShoppingCart.find({
          user: userId,
          groupId: group._id,
          isFinalized: false
        });

        // Count items in all unfinalized carts
        let totalItems = 0;
        for (const cart of unfinalizedCarts) {
          totalItems += cart.items.reduce((sum, item) => sum + item.quantity, 0);
        }

        // Check if this is a current group
        const isCurrentGroup = currentGroups.some(
          (id: mongoose.Types.ObjectId) => id.toString() === group._id.toString()
        );

        return {
          ...group.toObject(),
          hasUndeliveredItems: totalItems > 0,
          undeliveredItemsCount: totalItems,
          isCurrentGroup
        };
      })
    );

    return NextResponse.json(
      {
        success: true,
        groups: groupsWithUndeliveredItems
      },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error("Error getting user groups history:", error);
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while getting user groups history.",
        error: "Internal Server Error"
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}

