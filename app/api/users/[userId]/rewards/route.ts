// User Rewards API
// Handles user reward management and claiming

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { gamificationService } from '@/lib/services/gamificationService';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if user can access this data (self or admin)
    if (payload.userId !== params.userId && payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    // Get user rewards
    const allRewards = await gamificationService.getUserRewards(params.userId);
    
    // Filter rewards based on query parameters
    let filteredRewards = allRewards;
    
    if (status) {
      filteredRewards = filteredRewards.filter(reward => reward.status === status);
    }
    
    if (type) {
      filteredRewards = filteredRewards.filter(reward => reward.type === type);
    }

    // Calculate summary statistics
    const summary = {
      totalRewards: allRewards.length,
      pendingRewards: allRewards.filter(r => r.status === 'pending').length,
      claimedRewards: allRewards.filter(r => r.status === 'claimed').length,
      expiredRewards: allRewards.filter(r => r.status === 'expired').length,
      usedRewards: allRewards.filter(r => r.status === 'used').length,
      byType: {
        discount: allRewards.filter(r => r.type === 'discount').length,
        points: allRewards.filter(r => r.type === 'points').length,
        cash: allRewards.filter(r => r.type === 'cash').length,
        voucher: allRewards.filter(r => r.type === 'voucher').length,
        feature_access: allRewards.filter(r => r.type === 'feature_access').length
      },
      totalValue: {
        points: allRewards.filter(r => r.type === 'points').reduce((sum, r) => sum + r.value, 0),
        cash: allRewards.filter(r => r.type === 'cash').reduce((sum, r) => sum + r.value, 0),
        voucher: allRewards.filter(r => r.type === 'voucher').reduce((sum, r) => sum + r.value, 0)
      }
    };

    return NextResponse.json({
      success: true,
      data: filteredRewards,
      summary,
      metadata: {
        userId: params.userId,
        filters: { status, type },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('User rewards GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user rewards',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
