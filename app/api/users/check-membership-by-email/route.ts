// app/api/users/check-membership-by-email/route.ts
import { type NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { User, type IUser } from "@/models/User";
import { StokvelGroup } from "@/models/StokvelGroup";
import { getCorsHeaders } from "@/lib/cors";
import { Types } from "mongoose";
import '@/models'; // Ensure all models are loaded

interface MembershipResponse {
  isMember: boolean;
  group: {
    _id: string;
    groupName: string;
    geolocation: string;
  } | null;
}

// Add type for lean group document
type LeanStokvelGroup = {
  _id: Types.ObjectId;
  name: string;
  geolocation: string;
}

export async function GET(request: NextRequest): Promise<NextResponse<{ error: string } | MembershipResponse>> {
  await connectToDatabase();
  const origin = request.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    const email = request.nextUrl.searchParams.get("email");

    if (!email) {
      return NextResponse.json(
        { error: "Email parameter is required" },
        { status: 400, headers: corsHeaders }
      );
    }

    const user = await User.findOne({ email }).select("_id stokvelGroups").lean() as Pick<IUser, "_id" | "stokvelGroups"> | null;

    if (!user) {
      return NextResponse.json(
        { isMember: false, group: null },
        { headers: corsHeaders }
      );
    }

    const isMember = user.stokvelGroups && user.stokvelGroups.length > 0;

    if (!isMember) {
      return NextResponse.json(
        { isMember: false, group: null },
        { headers: corsHeaders }
      );
    }

    const groupId = user.stokvelGroups[0];
    const group = await StokvelGroup.findById(groupId).select("name geolocation").lean() as LeanStokvelGroup | null;

    if (!group) {
      return NextResponse.json(
        { isMember: true, group: null },
        { headers: corsHeaders }
      );
    }

    return NextResponse.json(
      {
        isMember: true,
        group: {
          _id: group._id.toString(),
          groupName: group.name,
          geolocation: group.geolocation
        }
      },
      { headers: corsHeaders }
    );
  } catch (error) {
    console.error("Error checking membership by email:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500, headers: corsHeaders }
    );
  }
}


