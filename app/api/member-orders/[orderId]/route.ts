// app/api/member-orders/[orderId]/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { updateMemberOrderStatus, cancelMemberOrder } from '@/lib/memberOrderUtilities';
import { MemberOrder } from '@/models/MemberOrder';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: Request,
  { params }: { params: { orderId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { orderId } = params;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const order = await MemberOrder.findById(orderId)
      .populate('items.product', 'name price image description')
      .populate('userId', 'name email')
      .populate('groupId', 'name');

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(order, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch member order:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: { orderId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { orderId } = params;
    const { status, notes, action } = await req.json();

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    let updatedOrder;

    if (action === 'cancel') {
      updatedOrder = await cancelMemberOrder(orderId, notes);
    } else if (status) {
      updatedOrder = await updateMemberOrderStatus(orderId, status, notes);
    } else {
      return NextResponse.json(
        { error: 'Status or action is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!updatedOrder) {
      return NextResponse.json(
        { error: 'Failed to update order or order not found.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(updatedOrder, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to update member order:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
