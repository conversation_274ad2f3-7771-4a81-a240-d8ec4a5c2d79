// app/api/member-orders/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getMemberOrdersByUser, getMemberOrderSummary } from '@/lib/memberOrderUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const userId = req.nextUrl.searchParams.get('userId');
    const summary = req.nextUrl.searchParams.get('summary');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (summary === 'true') {
      // Return order summary
      const orderSummary = await getMemberOrderSummary(userId);
      if (!orderSummary) {
        return NextResponse.json(
          { error: 'Failed to generate order summary.' },
          { headers: corsHeaders, status: 500 }
        );
      }
      return NextResponse.json(orderSummary, { headers: corsHeaders, status: 200 });
    } else {
      // Return full order list
      const orders = await getMemberOrdersByUser(userId);
      return NextResponse.json(orders, { headers: corsHeaders, status: 200 });
    }
  } catch (error) {
    console.error('Failed to fetch member orders:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
