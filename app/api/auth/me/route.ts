// app/api/auth/me/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { User } from '@/models/User';
import { verify } from 'jsonwebtoken';
import '@/models'; // Ensure all models are loaded
// Reuse the same secrets used in login/refresh
const ACCESS_TOKEN_SECRET = process.env.ACCESS_TOKEN_SECRET!;
// const REFRESH_TOKEN_SECRET = process.env.REFRESH_TOKEN_SECRET!;

// Shape of JWT payload
interface DecodedToken {
  id: string;   // User ID
  role: string; // User role
  iat?: number;
  exp?: number;
}

export async function GET(req: Request) {
  // Convert to NextRequest so we can access headers, cookies, etc.
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  // 1. Connect to DB
  await connectToDatabase();

  // 2. Determine if the request is from web or mobile
  const clientType = nextReq.headers.get('x-client-type') || 'mobile';

  let accessToken: string | undefined;

  if (clientType === 'web') {
    // For "web", read from the accessToken cookie
    accessToken = nextReq.cookies.get('accessToken')?.value;
  } else {
    // For "mobile", read from the Authorization header as Bearer token
    const authHeader = nextReq.headers.get('authorization') || '';
    if (authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    }
  }

  if (!accessToken) {
    return NextResponse.json(
      { error: 'No access token found. User not logged in.' },
      { headers: corsHeaders, status: 401 }
    );
  }

  try {
    // 3. Verify the token
    const decoded = verify(accessToken, ACCESS_TOKEN_SECRET) as DecodedToken;

    // 4. Find the user in the DB
    const user = await User.findById(decoded.id).select('-password');
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // 5. Return user data in the response
    // Make sure to exclude or sanitize any sensitive fields
    return NextResponse.json(
      { user },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Token verification failed:', error);
    return NextResponse.json(
      { error: 'Invalid or expired token' },
      { headers: corsHeaders, status: 401 }
    );
  }
}

export async function OPTIONS() {
  // Handle preflight
  return NextResponse.json({}, { status: 200 });
}
