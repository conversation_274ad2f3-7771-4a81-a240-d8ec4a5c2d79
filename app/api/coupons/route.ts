// app/api/coupons/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { promotionService } from '@/lib/services/promotionService';
import { Coupon } from '@/models/Coupon';
import { 
  CreateCouponRequest, 
  CreateCouponResponse,
  BulkCouponGenerationRequest,
  BulkCouponGenerationResponse
} from '@/types/promotions';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body = await req.json();
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    // Get user ID from headers or auth token (simplified for demo)
    const userId = req.headers.get('x-user-id') || 'demo-user-id';

    switch (action) {
      case 'bulk':
        return handleBulkGeneration(body, userId, corsHeaders);
      default:
        return handleCreateCoupon(body, userId, corsHeaders);
    }
  } catch (error) {
    console.error('Error in coupons API:', error);
    
    const response: CreateCouponResponse = {
      success: false,
      error: 'Internal server error'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  
  const userId = req.headers.get('x-user-id');
  const type = searchParams.get('type');
  const status = searchParams.get('status');
  const isPublic = searchParams.get('public');
  const limit = parseInt(searchParams.get('limit') || '20');
  const offset = parseInt(searchParams.get('offset') || '0');

  try {
    if (type === 'available' && userId) {
      // Get available coupons for user
      const coupons = await promotionService.getAvailableCoupons(userId, {
        status: status || 'active',
        isPublic: isPublic !== 'false'
      });

      return NextResponse.json({
        coupons: coupons.slice(offset, offset + limit),
        totalCount: coupons.length,
        hasMore: offset + limit < coupons.length
      }, {
        headers: corsHeaders,
        status: 200
      });
    }

    // Get all coupons with filters
    const filters: Record<string, unknown> = {};
    if (status) filters.status = status;
    if (isPublic !== null) filters.isPublic = isPublic === 'true';
    if (type) filters.type = type;

    const coupons = await Coupon.find(filters)
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset);

    const totalCount = await Coupon.countDocuments(filters);

    return NextResponse.json({
      coupons,
      totalCount,
      hasMore: offset + limit < totalCount
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching coupons:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  const couponId = searchParams.get('id');

  if (!couponId) {
    return NextResponse.json(
      { error: 'Coupon ID is required' },
      { headers: corsHeaders, status: 400 }
    );
  }

  try {
    const updates = await req.json();
    
    // Remove fields that shouldn't be updated directly
    delete updates._id;
    delete updates.code;
    delete updates.usageCount;
    delete updates.createdBy;
    delete updates.createdAt;

    const coupon = await Coupon.findByIdAndUpdate(
      couponId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate('createdBy', 'name email');

    if (!coupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      coupon
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error updating coupon:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  const couponId = searchParams.get('id');

  if (!couponId) {
    return NextResponse.json(
      { error: 'Coupon ID is required' },
      { headers: corsHeaders, status: 400 }
    );
  }

  try {
    const coupon = await Coupon.findById(couponId);
    
    if (!coupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check if coupon has been used
    if (coupon.usageCount > 0) {
      // Don't delete used coupons, just deactivate them
      coupon.status = 'inactive';
      await coupon.save();
      
      return NextResponse.json({
        success: true,
        message: 'Coupon deactivated (cannot delete used coupons)'
      }, {
        headers: corsHeaders,
        status: 200
      });
    }

    await Coupon.findByIdAndDelete(couponId);

    return NextResponse.json({
      success: true,
      message: 'Coupon deleted successfully'
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error deleting coupon:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Helper functions
async function handleCreateCoupon(
  body: CreateCouponRequest, 
  userId: string, 
  corsHeaders: HeadersInit
) {
  // Validate required fields
  if (!body.name || !body.type || !body.discountTarget || !body.discountValue) {
    const response: CreateCouponResponse = {
      success: false,
      error: 'Missing required fields: name, type, discountTarget, discountValue'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Validate dates
  const validFrom = new Date(body.validFrom);
  const validUntil = new Date(body.validUntil);

  if (isNaN(validFrom.getTime()) || isNaN(validUntil.getTime())) {
    const response: CreateCouponResponse = {
      success: false,
      error: 'Invalid date format'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  if (validFrom >= validUntil) {
    const response: CreateCouponResponse = {
      success: false,
      error: 'Valid from date must be before valid until date'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Create coupon
  const result = await promotionService.createCoupon(body, userId);

  return NextResponse.json(result, {
    headers: corsHeaders,
    status: result.success ? 201 : 400
  });
}

async function handleBulkGeneration(
  body: BulkCouponGenerationRequest,
  userId: string,
  corsHeaders: HeadersInit
) {
  // Validate bulk generation request
  if (!body.template || !body.quantity || body.quantity <= 0 || body.quantity > 1000) {
    const response: BulkCouponGenerationResponse = {
      success: false,
      error: 'Invalid bulk generation request. Quantity must be between 1 and 1000.'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Validate template
  if (!body.template.name || !body.template.type || !body.template.discountTarget || !body.template.discountValue) {
    const response: BulkCouponGenerationResponse = {
      success: false,
      error: 'Invalid template. Missing required fields.'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Generate bulk coupons
  const result = await promotionService.generateBulkCoupons(body, userId);

  return NextResponse.json(result, {
    headers: corsHeaders,
    status: result.success ? 201 : 400
  });
}
