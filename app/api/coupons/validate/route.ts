// app/api/coupons/validate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { promotionService } from '@/lib/services/promotionService';
import { 
  ValidateCouponRequest, 
  ValidateCouponResponse,
  ApplyCouponRequest,
  ApplyCouponResponse
} from '@/types/promotions';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  const action = searchParams.get('action');

  try {
    const body = await req.json();

    switch (action) {
      case 'apply':
        return handleApplyCoupon(body, corsHeaders);
      default:
        return handleValidateCoupon(body, corsHeaders);
    }
  } catch (error) {
    console.error('Error in coupon validation API:', error);
    
    const response: ValidateCouponResponse = {
      valid: false,
      error: 'Internal server error'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

async function handleValidateCoupon(
  body: ValidateCouponRequest,
  corsHeaders: HeadersInit
) {
  // Validate request
  if (!body.code || !body.userId || !body.cartItems || !body.orderTotal) {
    const response: ValidateCouponResponse = {
      valid: false,
      error: 'Missing required fields: code, userId, cartItems, orderTotal'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Validate cart items structure
  if (!Array.isArray(body.cartItems) || body.cartItems.length === 0) {
    const response: ValidateCouponResponse = {
      valid: false,
      error: 'Cart items must be a non-empty array'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Validate each cart item
  for (const item of body.cartItems) {
    if (!item.productId || !item.quantity || !item.price || !item.category) {
      const response: ValidateCouponResponse = {
        valid: false,
        error: 'Each cart item must have productId, quantity, price, and category'
      };

      return NextResponse.json(response, {
        headers: corsHeaders,
        status: 400
      });
    }

    if (item.quantity <= 0 || item.price <= 0) {
      const response: ValidateCouponResponse = {
        valid: false,
        error: 'Cart item quantity and price must be positive numbers'
      };

      return NextResponse.json(response, {
        headers: corsHeaders,
        status: 400
      });
    }
  }

  // Validate order total
  if (body.orderTotal <= 0) {
    const response: ValidateCouponResponse = {
      valid: false,
      error: 'Order total must be a positive number'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Validate coupon
  const result = await promotionService.validateCoupon(body);

  const response: ValidateCouponResponse = {
    valid: result.valid,
    coupon: result.coupon,
    discountAmount: result.discountAmount,
    error: result.error,
    warnings: result.warnings
  };

  return NextResponse.json(response, {
    headers: corsHeaders,
    status: 200
  });
}

async function handleApplyCoupon(
  body: ApplyCouponRequest,
  corsHeaders: HeadersInit
) {
  // Validate request (same as validate, plus orderId)
  if (!body.code || !body.userId || !body.cartItems || !body.orderTotal) {
    const response: ApplyCouponResponse = {
      success: false,
      error: 'Missing required fields: code, userId, cartItems, orderTotal'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Validate cart items structure
  if (!Array.isArray(body.cartItems) || body.cartItems.length === 0) {
    const response: ApplyCouponResponse = {
      success: false,
      error: 'Cart items must be a non-empty array'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Validate order total
  if (body.orderTotal <= 0) {
    const response: ApplyCouponResponse = {
      success: false,
      error: 'Order total must be a positive number'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 400
    });
  }

  // Apply coupon
  const result = await promotionService.applyCoupon(body);

  const response: ApplyCouponResponse = {
    success: result.success,
    discountAmount: result.discountAmount,
    finalTotal: result.finalTotal,
    error: result.error
  };

  return NextResponse.json(response, {
    headers: corsHeaders,
    status: result.success ? 200 : 400
  });
}

// GET endpoint for coupon details by code
export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  const code = searchParams.get('code');

  if (!code) {
    return NextResponse.json(
      { error: 'Coupon code is required' },
      { headers: corsHeaders, status: 400 }
    );
  }

  try {
    const { Coupon } = await import('@/models/Coupon');
    
    const coupon = await Coupon.findOne({ 
      code: code.toUpperCase(),
      status: 'active'
    }).populate('applicableProducts', 'name category')
      .populate('applicableCategories', 'name')
      .populate('excludedProducts', 'name category')
      .populate('excludedCategories', 'name');

    if (!coupon) {
      return NextResponse.json(
        { error: 'Coupon not found or inactive' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Return basic coupon info (without sensitive data)
    const couponInfo = {
      code: coupon.code,
      name: coupon.name,
      description: coupon.description,
      type: coupon.type,
      discountTarget: coupon.discountTarget,
      discountValue: coupon.discountValue,
      minimumOrderValue: coupon.minimumOrderValue,
      maximumDiscountAmount: coupon.maximumDiscountAmount,
      validFrom: coupon.validFrom,
      validUntil: coupon.validUntil,
      groupOrdersOnly: coupon.groupOrdersOnly,
      minimumGroupSize: coupon.minimumGroupSize,
      stackable: coupon.stackable,
      isCurrentlyValid: coupon.isCurrentlyValid,
      usagePercentage: coupon.usagePercentage,
      applicableProducts: coupon.applicableProducts,
      applicableCategories: coupon.applicableCategories,
      excludedProducts: coupon.excludedProducts,
      excludedCategories: coupon.excludedCategories
    };

    return NextResponse.json({
      coupon: couponInfo
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching coupon details:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
