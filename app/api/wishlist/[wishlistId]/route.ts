// app/api/wishlist/[wishlistId]/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { 
  getWishlistById, 
  updateWishlist, 
  deleteWishlist,
  removeFromWishlist
} from '@/lib/wishlistUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: Request,
  { params }: { params: { wishlistId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { wishlistId } = params;
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');

    if (!wishlistId) {
      return NextResponse.json(
        { error: 'Wishlist ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const wishlist = await getWishlistById(wishlistId, userId || undefined);

    if (!wishlist) {
      return NextResponse.json(
        { error: 'Wishlist not found or access denied.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(wishlist, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch wishlist:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PATCH(
  req: Request,
  { params }: { params: { wishlistId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { wishlistId } = params;
    const body = await req.json();
    const { userId, action, productId, ...updates } = body;

    if (!wishlistId || !userId) {
      return NextResponse.json(
        { error: 'Wishlist ID and User ID are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (action === 'remove-item') {
      // Remove item from wishlist
      if (!productId) {
        return NextResponse.json(
          { error: 'Product ID is required for removing item.' },
          { headers: corsHeaders, status: 400 }
        );
      }

      const wishlist = await removeFromWishlist(userId, productId, wishlistId);
      if (!wishlist) {
        return NextResponse.json(
          { error: 'Failed to remove item from wishlist.' },
          { headers: corsHeaders, status: 500 }
        );
      }

      return NextResponse.json(
        { 
          message: 'Item removed from wishlist successfully',
          wishlist
        },
        { headers: corsHeaders, status: 200 }
      );
    } else {
      // Update wishlist details
      const wishlist = await updateWishlist({ wishlistId, ...updates }, userId);
      if (!wishlist) {
        return NextResponse.json(
          { error: 'Failed to update wishlist or wishlist not found.' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json(
        { 
          message: 'Wishlist updated successfully',
          wishlist
        },
        { headers: corsHeaders, status: 200 }
      );
    }
  } catch (error) {
    console.error('Failed to update wishlist:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { wishlistId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { wishlistId } = params;
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');

    if (!wishlistId || !userId) {
      return NextResponse.json(
        { error: 'Wishlist ID and User ID are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const success = await deleteWishlist(wishlistId, userId);
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete wishlist or wishlist not found.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Wishlist deleted successfully' },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Failed to delete wishlist:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
