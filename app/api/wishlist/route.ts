// app/api/wishlist/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { 
  getUserWishlists, 
  createWishlist, 
  addToWishlist,
  getWishlistSummary,
  isProductInWishlist
} from '@/lib/wishlistUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const userId = req.nextUrl.searchParams.get('userId');
    const summary = req.nextUrl.searchParams.get('summary');
    const productId = req.nextUrl.searchParams.get('productId');
    const checkProduct = req.nextUrl.searchParams.get('checkProduct');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Check if specific product is in wishlist
    if (checkProduct === 'true' && productId) {
      const isInWishlist = await isProductInWishlist(userId, productId);
      return NextResponse.json({ isInWishlist }, { headers: corsHeaders, status: 200 });
    }

    // Return wishlist summary
    if (summary === 'true') {
      const wishlistSummary = await getWishlistSummary(userId);
      if (!wishlistSummary) {
        return NextResponse.json(
          { error: 'Failed to generate wishlist summary.' },
          { headers: corsHeaders, status: 500 }
        );
      }
      return NextResponse.json(wishlistSummary, { headers: corsHeaders, status: 200 });
    }

    // Return user's wishlists
    const wishlists = await getUserWishlists(userId);
    return NextResponse.json(wishlists, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch wishlists:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const body = await req.json();
    const { action, userId, productId, wishlistId, notes, priority, name, description, isPublic, tags } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (action === 'add-to-wishlist') {
      // Add product to wishlist
      if (!productId) {
        return NextResponse.json(
          { error: 'Product ID is required for adding to wishlist.' },
          { headers: corsHeaders, status: 400 }
        );
      }

      const wishlist = await addToWishlist({
        userId,
        productId,
        wishlistId,
        notes,
        priority
      });

      if (!wishlist) {
        return NextResponse.json(
          { error: 'Failed to add product to wishlist.' },
          { headers: corsHeaders, status: 500 }
        );
      }

      return NextResponse.json(
        { 
          message: 'Product added to wishlist successfully',
          wishlist,
          wishlistId: wishlist._id
        },
        { headers: corsHeaders, status: 201 }
      );
    } else if (action === 'create-wishlist') {
      // Create new wishlist
      const wishlist = await createWishlist({
        userId,
        name,
        description,
        isPublic,
        tags
      });

      if (!wishlist) {
        return NextResponse.json(
          { error: 'Failed to create wishlist.' },
          { headers: corsHeaders, status: 500 }
        );
      }

      return NextResponse.json(
        { 
          message: 'Wishlist created successfully',
          wishlist,
          wishlistId: wishlist._id
        },
        { headers: corsHeaders, status: 201 }
      );
    } else {
      return NextResponse.json(
        { error: 'Invalid action. Use "add-to-wishlist" or "create-wishlist".' },
        { headers: corsHeaders, status: 400 }
      );
    }
  } catch (error) {
    console.error('Failed to process wishlist request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
