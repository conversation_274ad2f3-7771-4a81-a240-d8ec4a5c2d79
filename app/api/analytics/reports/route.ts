// app/api/analytics/reports/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { analyticsService } from '@/lib/services/analyticsService';
import { 
  GenerateReportRequest, 
  GenerateReportResponse,
  AnalyticsReport 
} from '@/types/analytics';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body: GenerateReportRequest = await req.json();

    // Validate request
    if (!body.name) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Report name is required' 
        } as GenerateReportResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!body.format || !['pdf', 'excel', 'csv', 'json'].includes(body.format)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Valid format is required (pdf, excel, csv, json)' 
        } as GenerateReportResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!body.sections || body.sections.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'At least one report section is required' 
        } as GenerateReportResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Generate report ID
    const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create report record
    const report: AnalyticsReport = {
      id: reportId,
      name: body.name,
      type: body.type || 'on_demand',
      format: body.format,
      sections: body.sections.map(section => ({
        title: section,
        type: 'metrics',
        data: {}
      })),
      filters: body.filters || {},
      status: 'pending',
      generatedAt: new Date()
    };

    // Start report generation (in a real implementation, this would be queued)
    const reportData = await generateReportData(body.sections, body.filters);
    
    // Generate file based on format
    const fileUrl = await generateReportFile(report, reportData);
    
    // Update report status
    report.status = 'completed';
    report.fileUrl = fileUrl;

    const response: GenerateReportResponse = {
      success: true,
      reportId: reportId,
      downloadUrl: fileUrl,
      estimatedTime: 0 // Immediate for demo
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error generating report:', error);
    
    const response: GenerateReportResponse = {
      success: false,
      error: 'Internal server error during report generation'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  
  const reportId = searchParams.get('reportId');
  const status = searchParams.get('status');

  try {
    if (reportId) {
      // Get specific report status
      const report = await getReportById(reportId);
      
      if (!report) {
        return NextResponse.json(
          { error: 'Report not found' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json(report, {
        headers: corsHeaders,
        status: 200
      });
    }

    // Get all reports (with optional status filter)
    const reports = await getReports(status);

    return NextResponse.json({
      reports,
      totalCount: reports.length
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching reports:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Helper functions
async function generateReportData(sections: string[], _filters: Record<string, unknown> = {}) {
  const data: Record<string, unknown> = {};

  // Default date range (last 30 days)
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
  const dateRange = { start: startDate, end: endDate };

  for (const section of sections) {
    switch (section) {
      case 'revenue':
        data.revenue = await analyticsService.getRevenueAnalytics(dateRange);
        break;
      case 'users':
        data.users = await analyticsService.getUserAnalytics(dateRange);
        break;
      case 'orders':
        data.orders = await analyticsService.getOrderAnalytics(dateRange);
        break;
      case 'overview':
        const [revenue, users, orders] = await Promise.all([
          analyticsService.getRevenueAnalytics(dateRange),
          analyticsService.getUserAnalytics(dateRange),
          analyticsService.getOrderAnalytics(dateRange)
        ]);
        data.overview = { revenue, users, orders };
        break;
      default:
        console.warn(`Unknown report section: ${section}`);
    }
  }

  return data;
}

async function generateReportFile(report: AnalyticsReport, data: Record<string, unknown>): Promise<string> {
  // In a real implementation, this would generate actual files
  // For demo purposes, we'll return a mock URL
  
  switch (report.format) {
    case 'json':
      return generateJsonReport(report, data);
    case 'csv':
      return generateCsvReport(report, data);
    case 'excel':
      return generateExcelReport(report, data);
    case 'pdf':
      return generatePdfReport(report, data);
    default:
      throw new Error(`Unsupported format: ${report.format}`);
  }
}

function generateJsonReport(report: AnalyticsReport, data: Record<string, unknown>): string {
  // Generate JSON report
  const _jsonData = {
    report: {
      id: report.id,
      name: report.name,
      generatedAt: report.generatedAt,
      format: report.format
    },
    data
  };

  // In a real implementation, save to file storage and return URL
  const mockUrl = `/api/analytics/reports/download/${report.id}.json`;
  
  // Store the data temporarily (in production, use proper file storage)
  // This is just for demo purposes
  
  return mockUrl;
}

function generateCsvReport(report: AnalyticsReport, data: Record<string, unknown>): string {
  // Generate CSV report
  let _csvContent = `Report: ${report.name}\nGenerated: ${report.generatedAt}\n\n`;

  // Add data sections
  for (const [section, sectionData] of Object.entries(data)) {
    _csvContent += `\n${section.toUpperCase()}\n`;
    _csvContent += generateCsvSection(sectionData);
    _csvContent += '\n';
  }

  // In a real implementation, save to file storage and return URL
  return `/api/analytics/reports/download/${report.id}.csv`;
}

function generateExcelReport(_report: AnalyticsReport, _data: Record<string, unknown>): string {
  // Generate Excel report (would use a library like xlsx)
  // For demo, return mock URL
  return `/api/analytics/reports/download/${report.id}.xlsx`;
}

function generatePdfReport(_report: AnalyticsReport, _data: Record<string, unknown>): string {
  // Generate PDF report (would use a library like puppeteer or jsPDF)
  // For demo, return mock URL
  return `/api/analytics/reports/download/${report.id}.pdf`;
}

function generateCsvSection(data: unknown): string {
  if (!data || typeof data !== 'object') {
    return '';
  }

  let csv = '';
  
  // Handle different data structures
  if (Array.isArray(data)) {
    // Array of objects
    if (data.length > 0 && typeof data[0] === 'object') {
      const headers = Object.keys(data[0]);
      csv += headers.join(',') + '\n';
      
      for (const item of data) {
        const values = headers.map(header => item[header] || '');
        csv += values.join(',') + '\n';
      }
    }
  } else {
    // Object with key-value pairs
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'object' && value !== null) {
        csv += `${key},${JSON.stringify(value)}\n`;
      } else {
        csv += `${key},${value}\n`;
      }
    }
  }

  return csv;
}

async function getReportById(reportId: string): Promise<AnalyticsReport | null> {
  // In a real implementation, fetch from database
  // For demo, return mock data
  return {
    id: reportId,
    name: 'Sample Report',
    type: 'on_demand',
    format: 'json',
    sections: [{ title: 'Overview', type: 'metrics', data: {} }],
    filters: {},
    status: 'completed',
    generatedAt: new Date(),
    fileUrl: `/api/analytics/reports/download/${reportId}.json`
  };
}

async function getReports(status?: string | null): Promise<AnalyticsReport[]> {
  // In a real implementation, fetch from database
  // For demo, return mock data
  const mockReports: AnalyticsReport[] = [
    {
      id: 'report_1',
      name: 'Monthly Revenue Report',
      type: 'scheduled',
      format: 'pdf',
      sections: [{ title: 'Revenue', type: 'metrics', data: {} }],
      filters: {},
      status: 'completed',
      generatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      fileUrl: '/api/analytics/reports/download/report_1.pdf'
    },
    {
      id: 'report_2',
      name: 'User Analytics Report',
      type: 'on_demand',
      format: 'excel',
      sections: [{ title: 'Users', type: 'metrics', data: {} }],
      filters: {},
      status: 'completed',
      generatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      fileUrl: '/api/analytics/reports/download/report_2.xlsx'
    }
  ];

  if (status) {
    return mockReports.filter(report => report.status === status);
  }

  return mockReports;
}
