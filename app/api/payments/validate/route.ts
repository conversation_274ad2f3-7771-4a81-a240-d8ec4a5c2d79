// app/api/payments/validate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { PaymentProcessor } from '@/lib/services/paymentProcessor';
import { 
  ValidatePaymentRequest, 
  ValidatePaymentResponse,
  PaymentData 
} from '@/types/payment';
import '@/models'; // Ensure all models are loaded

// Initialize payment processor
const paymentProcessor = new PaymentProcessor({});

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body: ValidatePaymentRequest = await req.json();
    const { paymentData } = body;

    // Validate that payment data is provided
    if (!paymentData) {
      return NextResponse.json(
        { 
          validation: {
            isValid: false,
            errors: ['Payment data is required']
          }
        } as ValidatePaymentResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Perform validation using the payment processor
    const validation = await paymentProcessor.validatePaymentData(paymentData as PaymentData);

    const response: ValidatePaymentResponse = {
      validation
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Payment validation error:', error);
    
    const response: ValidatePaymentResponse = {
      validation: {
        isValid: false,
        errors: ['Internal server error during validation']
      }
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

// Additional validation endpoint for specific payment methods
export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  
  const paymentMethod = searchParams.get('method');
  const amount = searchParams.get('amount');
  const currency = searchParams.get('currency') || 'ZAR';

  try {
    // Validate query parameters
    if (!paymentMethod) {
      return NextResponse.json(
        { error: 'Payment method is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!amount || isNaN(Number(amount))) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const amountNum = Number(amount);

    // Get payment method requirements and limits
    const methodInfo = getPaymentMethodInfo(paymentMethod, amountNum, currency);

    return NextResponse.json(methodInfo, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error getting payment method info:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Helper function to get payment method information
function getPaymentMethodInfo(paymentMethod: string, amount: number, currency: string) {
  const baseInfo = {
    method: paymentMethod,
    amount,
    currency,
    isAvailable: false,
    requirements: [] as string[],
    limits: {} as Record<string, unknown>,
    processingTime: '',
    fees: {} as Record<string, unknown>
  };

  switch (paymentMethod) {
    case 'credit_card':
      return {
        ...baseInfo,
        isAvailable: amount >= 10 && amount <= 50000 && ['ZAR', 'USD', 'EUR'].includes(currency),
        requirements: [
          'Valid credit or debit card',
          'Card number (13-19 digits)',
          'Expiry date (MM/YYYY)',
          'CVV (3-4 digits)',
          'Cardholder name'
        ],
        limits: {
          minimum: 10,
          maximum: 50000,
          supportedCurrencies: ['ZAR', 'USD', 'EUR']
        },
        processingTime: 'Instant',
        fees: {
          type: 'percentage',
          amount: 2.9,
          description: '2.9% processing fee'
        }
      };

    case 'bank_transfer':
      return {
        ...baseInfo,
        isAvailable: amount >= 50 && amount <= 100000 && currency === 'ZAR',
        requirements: [
          'Valid bank account',
          'Bank name',
          'Account number',
          'Account holder name'
        ],
        limits: {
          minimum: 50,
          maximum: 100000,
          supportedCurrencies: ['ZAR']
        },
        processingTime: '1-3 business days',
        fees: {
          type: 'fixed',
          amount: 0,
          description: 'No processing fees'
        }
      };

    case 'eft':
      return {
        ...baseInfo,
        isAvailable: amount >= 20 && amount <= 75000 && currency === 'ZAR',
        requirements: [
          'Valid bank account',
          'Bank name',
          'Account number',
          'Branch code (6 digits)',
          'Account holder name'
        ],
        limits: {
          minimum: 20,
          maximum: 75000,
          supportedCurrencies: ['ZAR']
        },
        processingTime: 'Within 24 hours',
        fees: {
          type: 'percentage',
          amount: 1.5,
          description: '1.5% processing fee'
        }
      };

    default:
      return {
        ...baseInfo,
        error: 'Unsupported payment method'
      };
  }
}

// Helper function to validate card number using Luhn algorithm
function _validateCardNumber(cardNumber: string): boolean {
  // Remove spaces and non-digits
  const number = cardNumber.replace(/\D/g, '');
  
  // Check length
  if (number.length < 13 || number.length > 19) {
    return false;
  }

  // Luhn algorithm
  let sum = 0;
  let isEven = false;

  for (let i = number.length - 1; i >= 0; i--) {
    let digit = parseInt(number.charAt(i), 10);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
}

// Helper function to get card type
function _getCardType(cardNumber: string): string {
  const number = cardNumber.replace(/\D/g, '');
  
  if (/^4/.test(number)) return 'visa';
  if (/^5[1-5]/.test(number)) return 'mastercard';
  if (/^3[47]/.test(number)) return 'amex';
  if (/^6/.test(number)) return 'discover';
  
  return 'unknown';
}

// Helper function to validate expiry date
function _validateExpiryDate(month: string, year: string): boolean {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  
  const expYear = parseInt(year, 10);
  const expMonth = parseInt(month, 10);
  
  if (expMonth < 1 || expMonth > 12) return false;
  if (expYear < currentYear) return false;
  if (expYear === currentYear && expMonth < currentMonth) return false;
  
  return true;
}
