// app/api/payments/status/[paymentId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { PaymentProcessor } from '@/lib/services/paymentProcessor';
import { GetPaymentStatusResponse } from '@/types/payment';
import '@/models'; // Ensure all models are loaded

// Initialize payment processor
const paymentProcessor = new PaymentProcessor({});

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: NextRequest,
  { params }: { params: { paymentId: string } }
) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const { paymentId } = params;

    // Validate payment ID
    if (!paymentId) {
      return NextResponse.json(
        { error: 'Payment ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate payment ID format (MongoDB ObjectId)
    if (!/^[0-9a-fA-F]{24}$/.test(paymentId)) {
      return NextResponse.json(
        { error: 'Invalid payment ID format' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get payment status
    const payment = await paymentProcessor.getPaymentStatus(paymentId);

    if (!payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Transform payment data for response
    const response: GetPaymentStatusResponse = {
      payment: {
        _id: payment._id.toString(),
        orderId: payment.orderId.toString(),
        userId: payment.userId.toString(),
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        paymentMethod: payment.paymentMethod,
        provider: payment.provider,
        transactionId: payment.transactionId,
        providerPaymentId: payment.providerPaymentId,
        processingFee: payment.processingFee,
        netAmount: payment.netAmount,
        description: payment.description,
        metadata: payment.metadata,
        failureReason: payment.failureReason,
        refundAmount: payment.refundAmount,
        refundReason: payment.refundReason,
        processedAt: payment.processedAt,
        settledAt: payment.settledAt,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt
      },
      status: payment.status,
      lastUpdated: payment.updatedAt
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching payment status:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: { paymentId: string } }
) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const { paymentId } = params;
    const body = await req.json();

    // Validate payment ID
    if (!paymentId) {
      return NextResponse.json(
        { error: 'Payment ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate payment ID format
    if (!/^[0-9a-fA-F]{24}$/.test(paymentId)) {
      return NextResponse.json(
        { error: 'Invalid payment ID format' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get current payment
    const payment = await paymentProcessor.getPaymentStatus(paymentId);
    if (!payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Handle different update operations
    const { action, ...updateData } = body;

    switch (action) {
      case 'verify':
        // Verify payment with provider
        const isVerified = await paymentProcessor.verifyPayment(paymentId);
        
        return NextResponse.json(
          { 
            verified: isVerified,
            status: payment.status,
            message: isVerified ? 'Payment verified successfully' : 'Payment verification failed'
          },
          { headers: corsHeaders, status: 200 }
        );

      case 'refund':
        // Process refund
        const { amount, reason } = updateData;
        const refundResult = await paymentProcessor.processRefund(paymentId, amount, reason);
        
        return NextResponse.json(refundResult, {
          headers: corsHeaders,
          status: refundResult.success ? 200 : 400
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { headers: corsHeaders, status: 400 }
        );
    }

  } catch (error) {
    console.error('Error updating payment:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
