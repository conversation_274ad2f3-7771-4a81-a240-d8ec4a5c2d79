// app/api/payments/process/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { PaymentProcessor } from '@/lib/services/paymentProcessor';
import { 
  PaymentData, 
  ProcessPaymentRequest, 
  ProcessPaymentResponse 
} from '@/types/payment';
import '@/models'; // Ensure all models are loaded

// Initialize payment processor with configuration
const paymentConfig = {
  stripe: {
    publicKey: process.env.STRIPE_PUBLIC_KEY || '',
    secretKey: process.env.STRIPE_SECRET_KEY || '',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || ''
  },
  payfast: {
    merchantId: process.env.PAYFAST_MERCHANT_ID || '',
    merchantKey: process.env.PAYFAST_MERCHANT_KEY || '',
    passphrase: process.env.PAYFAST_PASSPHRASE || '',
    sandbox: process.env.NODE_ENV !== 'production'
  },
  yoco: {
    secretKey: process.env.YOCO_SECRET_KEY || '',
    publicKey: process.env.YOCO_PUBLIC_KEY || ''
  }
};

const paymentProcessor = new PaymentProcessor(paymentConfig);

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body: ProcessPaymentRequest = await req.json();
    const { paymentData, options: _options } = body;

    // Validate required fields
    if (!paymentData) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Payment data is required' 
        } as ProcessPaymentResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate payment data structure
    const requiredFields = ['orderId', 'userId', 'amount', 'currency', 'paymentMethod', 'provider'];
    const missingFields = requiredFields.filter(field => !paymentData[field as keyof PaymentData]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Missing required fields: ${missingFields.join(', ')}` 
        } as ProcessPaymentResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate amount
    if (paymentData.amount <= 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Payment amount must be greater than 0' 
        } as ProcessPaymentResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate payment method specific data
    const validationError = validatePaymentMethodData(paymentData);
    if (validationError) {
      return NextResponse.json(
        { 
          success: false, 
          error: validationError 
        } as ProcessPaymentResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Process the payment
    const result = await paymentProcessor.processPayment(paymentData);

    // Return the result
    const response: ProcessPaymentResponse = {
      success: result.success,
      result,
      error: result.error
    };

    const statusCode = result.success ? 200 : 400;

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: statusCode
    });

  } catch (error) {
    console.error('Payment processing error:', error);
    
    const response: ProcessPaymentResponse = {
      success: false,
      error: 'Internal server error during payment processing'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

// Validate payment method specific data
function validatePaymentMethodData(paymentData: PaymentData): string | null {
  switch (paymentData.paymentMethod) {
    case 'credit_card':
      if (!paymentData.creditCard) {
        return 'Credit card information is required';
      }
      
      const { cardNumber, expiryMonth, expiryYear, cvv, cardholderName } = paymentData.creditCard;
      
      if (!cardNumber || cardNumber.length < 13 || cardNumber.length > 19) {
        return 'Valid card number is required';
      }
      
      if (!expiryMonth || !expiryYear) {
        return 'Card expiry date is required';
      }
      
      if (!cvv || cvv.length < 3 || cvv.length > 4) {
        return 'Valid CVV is required';
      }
      
      if (!cardholderName || cardholderName.trim().length < 2) {
        return 'Cardholder name is required';
      }
      
      // Validate expiry date
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const expYear = parseInt(expiryYear);
      const expMonth = parseInt(expiryMonth);
      
      if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
        return 'Card has expired';
      }
      
      break;

    case 'bank_transfer':
      if (!paymentData.bankTransfer) {
        return 'Bank transfer information is required';
      }
      
      const { bankName, accountNumber, accountHolderName } = paymentData.bankTransfer;
      
      if (!bankName || bankName.trim().length < 2) {
        return 'Bank name is required';
      }
      
      if (!accountNumber || accountNumber.length < 8) {
        return 'Valid account number is required';
      }
      
      if (!accountHolderName || accountHolderName.trim().length < 2) {
        return 'Account holder name is required';
      }
      
      break;

    case 'eft':
      if (!paymentData.eft) {
        return 'EFT information is required';
      }
      
      const { bankName: eftBankName, accountNumber: eftAccountNumber, branchCode, accountHolderName: eftAccountHolderName } = paymentData.eft;
      
      if (!eftBankName || eftBankName.trim().length < 2) {
        return 'Bank name is required';
      }
      
      if (!eftAccountNumber || eftAccountNumber.length < 8) {
        return 'Valid account number is required';
      }
      
      if (!branchCode || branchCode.length !== 6) {
        return 'Valid 6-digit branch code is required';
      }
      
      if (!eftAccountHolderName || eftAccountHolderName.trim().length < 2) {
        return 'Account holder name is required';
      }
      
      break;

    default:
      return `Unsupported payment method: ${paymentData.paymentMethod}`;
  }

  return null;
}

// Rate limiting helper (basic implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

function _checkRateLimit(identifier: string, maxRequests: number = 5, windowMs: number = 60000): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(identifier);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (userLimit.count >= maxRequests) {
    return false;
  }

  userLimit.count++;
  return true;
}

// Clean up rate limit map periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of rateLimitMap.entries()) {
    if (now > value.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}, 60000); // Clean up every minute
