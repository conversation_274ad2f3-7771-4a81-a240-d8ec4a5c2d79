// app/api/referrals/user/[userId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Referral } from '@/models/Referral';
import { getCorsHeaders } from '@/lib/cors';
import { ReferralStatsResponse } from '@/types/promotions';
import mongoose from 'mongoose';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    // Check if user can access this data (self or admin)
    if (payload.userId !== params.userId && payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { headers: corsHeaders, status: 403 }
      );
    }

    await connectToDatabase();

    // Get user data
    const user = await User.findById(params.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Get referral statistics
    const referrals = await Referral.find({ referrerId: params.userId })
      .populate('referredUserId', 'name email createdAt')
      .sort({ createdAt: -1 });

    // Calculate statistics
    const totalReferrals = referrals.length;
    const successfulReferrals = referrals.filter(r => r.earnings && r.earnings.length > 0).length;
    const pendingReferrals = totalReferrals - successfulReferrals;
    
    const totalEarnings = referrals.reduce((sum, referral) => {
      return sum + (referral.earnings?.reduce((earnSum, earning) => earnSum + earning.earningAmount, 0) || 0);
    }, 0);

    // Convert earnings to points (assuming 1 ZAR = 10 points)
    const totalPoints = Math.floor(totalEarnings * 10);
    
    const conversionRate = totalReferrals > 0 ? (successfulReferrals / totalReferrals) * 100 : 0;

    // Format referral history
    const referralHistory = referrals.map(referral => {
      const referee = referral.referredUserId as any;
      const totalEarned = referral.earnings?.reduce((sum, earning) => sum + earning.earningAmount, 0) || 0;
      
      return {
        refereeId: referee._id.toString(),
        refereeName: referee.name,
        status: referral.earnings && referral.earnings.length > 0 ? 'completed' : 'pending',
        joinedAt: referee.createdAt,
        firstPurchaseAt: referral.earnings && referral.earnings.length > 0 ? 
          new Date(referral.createdAt.getTime() + 24 * 60 * 60 * 1000) : undefined, // Mock first purchase date
        earnedPoints: Math.floor(totalEarned * 10),
        earnedAmount: totalEarned
      };
    });

    const response: ReferralStatsResponse = {
      success: true,
      stats: {
        totalReferrals,
        successfulReferrals,
        pendingReferrals,
        totalEarnings,
        totalPoints,
        conversionRate,
        referralHistory
      }
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching referral stats:', error);
    const response: ReferralStatsResponse = {
      success: false,
      error: 'Failed to fetch referral statistics'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}
