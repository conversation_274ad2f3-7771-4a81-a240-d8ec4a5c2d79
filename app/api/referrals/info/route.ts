// app/api/referrals/info/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { getCorsHeaders } from '@/lib/cors';

export async function GET(request: NextRequest) {
  const corsHeaders = getCorsHeaders(request.headers.get('origin'));

  try {
    const { searchParams } = new URL(request.url);
    const referralCode = searchParams.get('code');

    if (!referralCode) {
      return NextResponse.json(
        { error: 'Referral code is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    await connectToDatabase();

    // Find the user with this referral code
    const referrer = await User.findOne({ referralCode }).select('name');

    if (!referrer) {
      return NextResponse.json(
        { error: 'Invalid referral code' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      referrerName: referrer.name,
      referralCode
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching referrer info:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch referrer information' 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
