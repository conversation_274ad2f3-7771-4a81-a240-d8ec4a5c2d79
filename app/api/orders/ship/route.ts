// app/api/orders/ship/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { orderFulfillmentService } from '@/lib/services/orderFulfillmentService';
import { 
  ShipOrderRequest, 
  ShipOrderResponse,
  ShippingProvider 
} from '@/types/orderFulfillment';
import '@/models'; // Ensure all models are loaded

// Valid shipping providers
const VALID_PROVIDERS: ShippingProvider[] = [
  'courier_guy',
  'fastway',
  'dawn_wing',
  'aramex',
  'pudo',
  'self_collection'
];

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body: ShipOrderRequest = await req.json();

    // Validate required fields
    if (!body.orderId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Order ID is required' 
        } as ShipOrderResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!body.shippingProvider) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Shipping provider is required' 
        } as ShipOrderResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate order ID format
    if (!/^[0-9a-fA-F]{24}$/.test(body.orderId)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid order ID format' 
        } as ShipOrderResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate shipping provider
    if (!VALID_PROVIDERS.includes(body.shippingProvider)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid shipping provider. Valid providers are: ${VALID_PROVIDERS.join(', ')}` 
        } as ShipOrderResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate tracking number format if provided
    if (body.trackingNumber && body.trackingNumber.length < 5) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Tracking number must be at least 5 characters long' 
        } as ShipOrderResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate estimated delivery date if provided
    if (body.estimatedDelivery) {
      const deliveryDate = new Date(body.estimatedDelivery);
      if (isNaN(deliveryDate.getTime())) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid estimated delivery date format' 
          } as ShipOrderResponse,
          { headers: corsHeaders, status: 400 }
        );
      }
      
      // Ensure estimated delivery is in the future
      if (deliveryDate <= new Date()) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Estimated delivery date must be in the future' 
          } as ShipOrderResponse,
          { headers: corsHeaders, status: 400 }
        );
      }
    }

    // Validate shipping cost if provided
    if (body.shippingCost !== undefined && body.shippingCost < 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Shipping cost cannot be negative' 
        } as ShipOrderResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Ship the order
    const result = await orderFulfillmentService.shipOrder(body);

    const statusCode = result.success ? 200 : 400;

    return NextResponse.json(result, {
      headers: corsHeaders,
      status: statusCode
    });

  } catch (error) {
    console.error('Order shipping error:', error);
    
    const response: ShipOrderResponse = {
      success: false,
      error: 'Internal server error during order shipping'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  
  try {
    // Return available shipping providers and their information
    const providerInfo = VALID_PROVIDERS.map(provider => ({
      provider,
      name: getProviderName(provider),
      description: getProviderDescription(provider),
      estimatedDeliveryTime: getEstimatedDeliveryTime(provider),
      trackingSupported: provider !== 'self_collection'
    }));

    return NextResponse.json({
      availableProviders: providerInfo,
      currentTime: new Date().toISOString()
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching shipping providers:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Helper functions
function getProviderName(provider: ShippingProvider): string {
  const names = {
    courier_guy: 'The Courier Guy',
    fastway: 'Fastway Couriers',
    dawn_wing: 'Dawn Wing',
    aramex: 'Aramex',
    pudo: 'PUDO Locker',
    self_collection: 'Self Collection'
  };
  
  return names[provider] || provider;
}

function getProviderDescription(provider: ShippingProvider): string {
  const descriptions = {
    courier_guy: 'Reliable door-to-door delivery service',
    fastway: 'Fast and affordable courier service',
    dawn_wing: 'Express delivery and logistics',
    aramex: 'International and domestic courier service',
    pudo: 'Convenient locker pickup locations',
    self_collection: 'Collect from our warehouse or store'
  };
  
  return descriptions[provider] || 'Shipping service';
}

function getEstimatedDeliveryTime(provider: ShippingProvider): string {
  const deliveryTimes = {
    courier_guy: '2-3 business days',
    fastway: '1-2 business days',
    dawn_wing: '1-3 business days',
    aramex: '2-5 business days',
    pudo: '1-2 business days',
    self_collection: 'Immediate'
  };
  
  return deliveryTimes[provider] || '2-3 business days';
}
