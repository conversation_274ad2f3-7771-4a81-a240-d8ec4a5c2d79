// app/api/orders/update-status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { orderFulfillmentService } from '@/lib/services/orderFulfillmentService';
import { 
  UpdateOrderStatusRequest, 
  UpdateOrderStatusResponse,
  FulfillmentStatus 
} from '@/types/orderFulfillment';
import '@/models'; // Ensure all models are loaded

// Valid fulfillment statuses
const VALID_STATUSES: FulfillmentStatus[] = [
  'pending',
  'confirmed',
  'processing',
  'packed',
  'shipped',
  'out_for_delivery',
  'delivered',
  'failed',
  'cancelled',
  'returned'
];

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function PATCH(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body: UpdateOrderStatusRequest = await req.json();

    // Validate required fields
    if (!body.orderId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Order ID is required' 
        } as UpdateOrderStatusResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!body.status) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Status is required' 
        } as UpdateOrderStatusResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate order ID format
    if (!/^[0-9a-fA-F]{24}$/.test(body.orderId)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid order ID format' 
        } as UpdateOrderStatusResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate status
    if (!VALID_STATUSES.includes(body.status)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid status. Valid statuses are: ${VALID_STATUSES.join(', ')}` 
        } as UpdateOrderStatusResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate estimated completion date if provided
    if (body.estimatedCompletion) {
      const estimatedDate = new Date(body.estimatedCompletion);
      if (isNaN(estimatedDate.getTime())) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid estimated completion date format' 
          } as UpdateOrderStatusResponse,
          { headers: corsHeaders, status: 400 }
        );
      }
      
      // Ensure estimated completion is in the future
      if (estimatedDate <= new Date()) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Estimated completion date must be in the future' 
          } as UpdateOrderStatusResponse,
          { headers: corsHeaders, status: 400 }
        );
      }
    }

    // Update order status
    const result = await orderFulfillmentService.updateOrderStatus(body);

    const statusCode = result.success ? 200 : 400;

    return NextResponse.json(result, {
      headers: corsHeaders,
      status: statusCode
    });

  } catch (error) {
    console.error('Order status update error:', error);
    
    const response: UpdateOrderStatusResponse = {
      success: false,
      error: 'Internal server error during status update'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  
  try {
    // Return valid statuses and their descriptions
    const statusInfo = VALID_STATUSES.map(status => ({
      status,
      description: getStatusDescription(status)
    }));

    return NextResponse.json({
      validStatuses: statusInfo,
      currentTime: new Date().toISOString()
    }, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching status info:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Helper function to get status descriptions
function getStatusDescription(status: FulfillmentStatus): string {
  const descriptions = {
    pending: 'Order has been received and is awaiting confirmation',
    confirmed: 'Order has been confirmed and payment verified',
    processing: 'Order is being processed and items are being prepared',
    packed: 'Items have been packed and are ready for shipping',
    shipped: 'Order has been shipped and is in transit',
    out_for_delivery: 'Order is out for delivery and will arrive soon',
    delivered: 'Order has been successfully delivered',
    failed: 'Order fulfillment has failed',
    cancelled: 'Order has been cancelled',
    returned: 'Order has been returned'
  };
  
  return descriptions[status] || 'Unknown status';
}
