// app/api/orders/fulfillment/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { orderFulfillmentService } from '@/lib/services/orderFulfillmentService';
import { 
  CreateFulfillmentRequest, 
  CreateFulfillmentResponse 
} from '@/types/orderFulfillment';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const body: CreateFulfillmentRequest = await req.json();

    // Validate required fields
    if (!body.orderId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Order ID is required' 
        } as CreateFulfillmentResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate order ID format
    if (!/^[0-9a-fA-F]{24}$/.test(body.orderId)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid order ID format' 
        } as CreateFulfillmentResponse,
        { headers: corsHeaders, status: 400 }
      );
    }

    // Create fulfillment
    const result = await orderFulfillmentService.createFulfillment(body);

    const statusCode = result.success ? 201 : 400;

    return NextResponse.json(result, {
      headers: corsHeaders,
      status: statusCode
    });

  } catch (error) {
    console.error('Fulfillment creation error:', error);
    
    const response: CreateFulfillmentResponse = {
      success: false,
      error: 'Internal server error during fulfillment creation'
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 500
    });
  }
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  
  const orderId = searchParams.get('orderId');
  const status = searchParams.get('status');
  const warehouse = searchParams.get('warehouse');

  try {
    let fulfillments;

    if (orderId) {
      // Get specific order fulfillment
      if (!/^[0-9a-fA-F]{24}$/.test(orderId)) {
        return NextResponse.json(
          { error: 'Invalid order ID format' },
          { headers: corsHeaders, status: 400 }
        );
      }
      
      const fulfillment = await orderFulfillmentService.getFulfillmentByOrderId(orderId);
      if (!fulfillment) {
        return NextResponse.json(
          { error: 'Fulfillment not found' },
          { headers: corsHeaders, status: 404 }
        );
      }
      
      return NextResponse.json(fulfillment, {
        headers: corsHeaders,
        status: 200
      });
    } else if (status) {
      // Get orders by status
      fulfillments = await orderFulfillmentService.getOrdersByStatus(status as string);
    } else if (warehouse) {
      // Get orders by warehouse
      fulfillments = await orderFulfillmentService.getOrdersByWarehouse(warehouse);
    } else {
      return NextResponse.json(
        { error: 'Please specify orderId, status, or warehouse parameter' },
        { headers: corsHeaders, status: 400 }
      );
    }

    return NextResponse.json(fulfillments, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching fulfillments:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
