// app/api/orders/track/[orderId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { orderFulfillmentService } from '@/lib/services/orderFulfillmentService';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: NextRequest,
  { params }: { params: { orderId: string } }
) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const { orderId } = params;

    // Validate order ID
    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate order ID format
    if (!/^[0-9a-fA-F]{24}$/.test(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID format' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get fulfillment information
    const fulfillment = await orderFulfillmentService.getFulfillmentByOrderId(orderId);

    if (!fulfillment) {
      return NextResponse.json(
        { error: 'Order fulfillment not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Build tracking response
    const trackingInfo = {
      orderId: fulfillment.orderId.toString(),
      status: fulfillment.status,
      priority: fulfillment.priority,
      estimatedFulfillmentDate: fulfillment.estimatedFulfillmentDate,
      actualFulfillmentDate: fulfillment.actualFulfillmentDate,
      timeline: fulfillment.steps.map(step => ({
        id: step.id,
        name: step.name,
        status: step.status,
        description: step.description,
        completedAt: step.completedAt,
        estimatedCompletion: step.estimatedCompletion,
        notes: step.notes,
        assignedTo: step.assignedTo,
        isCompleted: !!step.completedAt,
        isCurrent: step.status === fulfillment.status
      })),
      shipping: fulfillment.shippingInfo ? {
        provider: fulfillment.shippingInfo.provider,
        trackingNumber: fulfillment.shippingInfo.trackingNumber,
        trackingUrl: fulfillment.shippingInfo.trackingUrl,
        estimatedDelivery: fulfillment.shippingInfo.estimatedDelivery,
        actualDelivery: fulfillment.shippingInfo.actualDelivery,
        shippingCost: fulfillment.shippingInfo.shippingCost,
        shippingAddress: fulfillment.shippingInfo.shippingAddress
      } : null,
      warehouse: fulfillment.assignedWarehouse,
      notes: fulfillment.fulfillmentNotes,
      lastUpdated: fulfillment.updatedAt
    };

    // Add progress percentage
    const completedSteps = fulfillment.steps.filter(step => step.completedAt).length;
    const totalSteps = fulfillment.steps.length;
    trackingInfo.progressPercentage = Math.round((completedSteps / totalSteps) * 100);

    // Add estimated completion for current step
    const currentStep = fulfillment.steps.find(step => step.status === fulfillment.status);
    if (currentStep && currentStep.estimatedCompletion) {
      trackingInfo.nextMilestone = {
        name: currentStep.name,
        estimatedCompletion: currentStep.estimatedCompletion,
        description: currentStep.description
      };
    }

    return NextResponse.json(trackingInfo, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching order tracking:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: { orderId: string } }
) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const { orderId } = params;
    const body = await req.json();

    // Validate order ID
    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate order ID format
    if (!/^[0-9a-fA-F]{24}$/.test(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID format' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const { action, ...updateData } = body;

    switch (action) {
      case 'update_tracking':
        // Update tracking information (for shipping providers)
        const fulfillment = await orderFulfillmentService.getFulfillmentByOrderId(orderId);
        if (!fulfillment) {
          return NextResponse.json(
            { error: 'Order fulfillment not found' },
            { headers: corsHeaders, status: 404 }
          );
        }

        // Update shipping info if provided
        if (updateData.trackingUpdate && fulfillment.shippingInfo) {
          const { location, status, description, estimatedDelivery } = updateData.trackingUpdate;
          
          // Add tracking update to notes
          const trackingNote = `Tracking Update: ${status} - ${description} (${location})`;
          fulfillment.fulfillmentNotes.push(trackingNote);
          
          // Update estimated delivery if provided
          if (estimatedDelivery) {
            fulfillment.shippingInfo.estimatedDelivery = new Date(estimatedDelivery);
          }
          
          await fulfillment.save();
        }

        return NextResponse.json(
          { 
            success: true,
            message: 'Tracking information updated successfully'
          },
          { headers: corsHeaders, status: 200 }
        );

      case 'add_note':
        // Add a note to the fulfillment
        if (!updateData.note) {
          return NextResponse.json(
            { error: 'Note content is required' },
            { headers: corsHeaders, status: 400 }
          );
        }

        const fulfillmentForNote = await orderFulfillmentService.getFulfillmentByOrderId(orderId);
        if (!fulfillmentForNote) {
          return NextResponse.json(
            { error: 'Order fulfillment not found' },
            { headers: corsHeaders, status: 404 }
          );
        }

        fulfillmentForNote.fulfillmentNotes.push(updateData.note);
        await fulfillmentForNote.save();

        return NextResponse.json(
          { 
            success: true,
            message: 'Note added successfully'
          },
          { headers: corsHeaders, status: 200 }
        );

      default:
        return NextResponse.json(
          { error: 'Invalid action specified' },
          { headers: corsHeaders, status: 400 }
        );
    }

  } catch (error) {
    console.error('Error updating order tracking:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
