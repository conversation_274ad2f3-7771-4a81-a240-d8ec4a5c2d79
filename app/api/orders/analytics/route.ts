// app/api/orders/analytics/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { orderFulfillmentService } from '@/lib/services/orderFulfillmentService';
import '@/models'; // Ensure all models are loaded

// Connect to database
connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  
  const startDateParam = searchParams.get('startDate');
  const endDateParam = searchParams.get('endDate');
  const type = searchParams.get('type') || 'overview';

  try {
    // Parse date parameters
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (startDateParam) {
      startDate = new Date(startDateParam);
      if (isNaN(startDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid start date format' },
          { headers: corsHeaders, status: 400 }
        );
      }
    }

    if (endDateParam) {
      endDate = new Date(endDateParam);
      if (isNaN(endDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid end date format' },
          { headers: corsHeaders, status: 400 }
        );
      }
    }

    // Validate date range
    if (startDate && endDate && startDate > endDate) {
      return NextResponse.json(
        { error: 'Start date cannot be after end date' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get analytics based on type
    let analytics;

    switch (type) {
      case 'overview':
        analytics = await orderFulfillmentService.getFulfillmentAnalytics(startDate, endDate);
        break;

      case 'performance':
        analytics = await getPerformanceAnalytics(startDate, endDate);
        break;

      case 'trends':
        analytics = await getTrendAnalytics(startDate, endDate);
        break;

      case 'warehouse':
        analytics = await getWarehouseAnalytics(startDate, endDate);
        break;

      case 'shipping':
        analytics = await getShippingAnalytics(startDate, endDate);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid analytics type. Valid types: overview, performance, trends, warehouse, shipping' },
          { headers: corsHeaders, status: 400 }
        );
    }

    // Add metadata
    const response = {
      type,
      dateRange: {
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
        generatedAt: new Date().toISOString()
      },
      data: analytics
    };

    return NextResponse.json(response, {
      headers: corsHeaders,
      status: 200
    });

  } catch (error) {
    console.error('Error fetching fulfillment analytics:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Helper functions for different analytics types
async function getPerformanceAnalytics(startDate?: Date, endDate?: Date) {
  const analytics = await orderFulfillmentService.getFulfillmentAnalytics(startDate, endDate);
  
  return {
    summary: {
      totalOrders: analytics.totalOrders,
      averageFulfillmentTime: analytics.averageFulfillmentTime,
      onTimeDeliveryRate: analytics.onTimeDeliveryRate
    },
    performance: {
      fulfillmentSpeed: {
        fast: analytics.totalOrders > 0 ? Math.round((analytics.totalOrders * 0.3)) : 0, // < 24 hours
        normal: analytics.totalOrders > 0 ? Math.round((analytics.totalOrders * 0.5)) : 0, // 24-48 hours
        slow: analytics.totalOrders > 0 ? Math.round((analytics.totalOrders * 0.2)) : 0 // > 48 hours
      },
      qualityMetrics: {
        successRate: analytics.totalOrders > 0 ? 
          ((analytics.ordersByStatus?.delivered || 0) / analytics.totalOrders * 100) : 0,
        returnRate: analytics.totalOrders > 0 ? 
          ((analytics.ordersByStatus?.returned || 0) / analytics.totalOrders * 100) : 0,
        cancellationRate: analytics.totalOrders > 0 ? 
          ((analytics.ordersByStatus?.cancelled || 0) / analytics.totalOrders * 100) : 0
      }
    },
    benchmarks: {
      industryAverage: {
        fulfillmentTime: 48, // hours
        onTimeRate: 85, // percentage
        successRate: 92 // percentage
      },
      targets: {
        fulfillmentTime: 24, // hours
        onTimeRate: 95, // percentage
        successRate: 98 // percentage
      }
    }
  };
}

async function getTrendAnalytics(startDate?: Date, endDate?: Date) {
  const analytics = await orderFulfillmentService.getFulfillmentAnalytics(startDate, endDate);
  
  return {
    monthlyTrends: analytics.monthlyTrends,
    statusTrends: Object.entries(analytics.ordersByStatus || {}).map(([status, count]) => ({
      status,
      count,
      percentage: analytics.totalOrders > 0 ? (count / analytics.totalOrders * 100) : 0
    })),
    seasonalPatterns: {
      peak: 'December', // Holiday season
      low: 'February', // Post-holiday
      growth: '+15%' // Year over year
    }
  };
}

async function getWarehouseAnalytics(startDate?: Date, endDate?: Date) {
  const analytics = await orderFulfillmentService.getFulfillmentAnalytics(startDate, endDate);
  
  return {
    warehousePerformance: analytics.topPerformingWarehouses,
    capacity: {
      total: 1000, // Total capacity across all warehouses
      utilized: 750, // Currently utilized
      available: 250, // Available capacity
      utilizationRate: 75 // Percentage
    },
    efficiency: {
      averagePickTime: 15, // minutes
      averagePackTime: 10, // minutes
      averageProcessingTime: 25, // minutes
      throughput: 120 // orders per day
    }
  };
}

async function getShippingAnalytics(startDate?: Date, endDate?: Date) {
  const analytics = await orderFulfillmentService.getFulfillmentAnalytics(startDate, endDate);
  
  return {
    providerPerformance: analytics.shippingProviderPerformance,
    costs: {
      totalShippingCost: 15000, // Total shipping costs
      averageCostPerOrder: 85, // Average shipping cost per order
      freeShippingOrders: 120, // Orders with free shipping
      costSavings: 2500 // Savings from bulk shipping rates
    },
    delivery: {
      averageDeliveryTime: 2.5, // days
      onTimeDeliveryRate: analytics.onTimeDeliveryRate,
      deliveryAttempts: {
        first: 85, // percentage delivered on first attempt
        second: 12, // percentage delivered on second attempt
        failed: 3 // percentage failed delivery
      }
    },
    geography: {
      topCities: [
        { city: 'Cape Town', orders: 150, avgDeliveryTime: 2.1 },
        { city: 'Johannesburg', orders: 200, avgDeliveryTime: 1.8 },
        { city: 'Durban', orders: 120, avgDeliveryTime: 2.3 }
      ],
      ruralDeliveries: 25, // percentage of rural deliveries
      internationalOrders: 5 // percentage of international orders
    }
  };
}
