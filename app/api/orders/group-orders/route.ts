import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from '@/lib/cors';
import mongoose from 'mongoose';
import '@/models'; // Ensure all models are loaded

// Define interfaces for type safety
interface UserContribution {
  userId: string;
  userName: string;
  totalSpent: number;
}

// Interface for the document as stored in MongoDB
interface GroupOrderDocument extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  groupId: string;
  totalOrderValue: number;
  status: string;
  paymentStatus: string;
  orderPlacedAt: Date;
  userContributions: UserContribution[];
}

// Interface for the lean document returned by Mongoose
interface GroupOrderLean {
  _id: mongoose.Types.ObjectId;
  groupId: string;
  totalOrderValue: number;
  status: string;
  paymentStatus: string;
  orderPlacedAt: Date;
  userContributions: UserContributionLean[];
}

// Interface for user contributions in lean documents
interface UserContributionLean {
  userId: string;
  userName: string;
  totalSpent: number;
}

// Interface for the transformed order sent to the frontend
interface TransformedGroupOrder {
  _id: string;
  groupId: string;
  totalOrderValue: number;
  status: string;
  paymentStatus: string;
  orderPlacedAt: Date;
  userContributions: TransformedUserContribution[];
}

// Interface for transformed user contributions
interface TransformedUserContribution {
  userId: string;
  userName: string;
  totalSpent: number;
}

// Define a GroupOrder model if it doesn't exist
const GroupOrderSchema = new mongoose.Schema({
  groupId: { type: String, required: true },
  totalOrderValue: { type: Number, default: 0 },
  status: { type: String, default: 'Pending' },
  paymentStatus: { type: String, default: 'Unpaid' },
  orderPlacedAt: { type: Date, default: Date.now },
  userContributions: [{
    userId: String,
    userName: String,
    totalSpent: Number
  }]
});

// Create or get the model
const GroupOrder = mongoose.models.GroupOrder || mongoose.model<GroupOrderDocument>('GroupOrder', GroupOrderSchema);

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(request: NextRequest) {
  const origin = request.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Connect to the database
    await connectToDatabase();

    // Get all group orders using Mongoose
    const groupOrders = await GroupOrder.find({}).lean() as unknown as GroupOrderLean[];

    // Transform the data for the frontend
    const transformedOrders: TransformedGroupOrder[] = groupOrders.map((order) => ({
      _id: order._id ? order._id.toString() : 'unknown',
      groupId: order.groupId || '',
      totalOrderValue: order.totalOrderValue || 0,
      status: order.status || 'Pending',
      paymentStatus: order.paymentStatus || 'Unpaid',
      orderPlacedAt: order.orderPlacedAt || new Date(),
      userContributions: Array.isArray(order.userContributions)
        ? order.userContributions.map((contribution: UserContributionLean) => ({
            userId: contribution.userId || '',
            userName: contribution.userName || 'Unknown User',
            totalSpent: contribution.totalSpent || 0
          }))
        : []
    }));

    return NextResponse.json(transformedOrders, { headers: corsHeaders });
  } catch (error) {
    console.error('Error fetching group orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch group orders' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
