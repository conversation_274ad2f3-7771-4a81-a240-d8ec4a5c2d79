import { NextRequest, NextResponse } from 'next/server';
import { PayFastService } from '@/modules/payments/payfast/server';

// Lazy initialization of PayFast service
function getPayFastService() {
  return new PayFastService({
    merchantId: process.env.PAYFAST_MERCHANT_ID || 'test',
    merchantKey: process.env.PAYFAST_MERCHANT_KEY || 'test',
    passphrase: process.env.PAYFAST_PASSPHRASE,
    sandbox: process.env.PAYFAST_SANDBOX !== 'false',
    returnUrl: process.env.PAYFAST_RETURN_URL || 'http://localhost:3000/payment/success',
    cancelUrl: process.env.PAYFAST_CANCEL_URL || 'http://localhost:3000/payment/cancel',
    notifyUrl: process.env.PAYFAST_NOTIFY_URL || 'http://localhost:3000/api/payment/payfast/notify'
  });
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get payment status
    const payFastService = getPayFastService();
    const status = await payFastService.getPaymentStatus(orderId);

    if (!status) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ status });

  } catch (error) {
    console.error('PayFast status check error:', error);
    return NextResponse.json(
      { error: 'Failed to check payment status' },
      { status: 500 }
    );
  }
}
