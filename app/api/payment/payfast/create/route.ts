import { NextRequest, NextResponse } from 'next/server';
import { PayFastService } from '@/modules/payments/payfast/server';

// Lazy initialization of PayFast service
function getPayFastService() {
  return new PayFastService({
    merchantId: process.env.PAYFAST_MERCHANT_ID || 'test',
    merchantKey: process.env.PAYFAST_MERCHANT_KEY || 'test',
    passphrase: process.env.PAYFAST_PASSPHRASE,
    sandbox: process.env.PAYFAST_SANDBOX !== 'false',
    returnUrl: process.env.PAYFAST_RETURN_URL || 'http://localhost:3000/payment/success',
    cancelUrl: process.env.PAYFAST_CANCEL_URL || 'http://localhost:3000/payment/cancel',
    notifyUrl: process.env.PAYFAST_NOTIFY_URL || 'http://localhost:3000/api/payment/payfast/notify'
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      orderId,
      amount,
      description,
      customerEmail,
      customerName,
      customData
    } = body;

    // Validate required fields
    if (!orderId || !amount || !description || !customerEmail || !customerName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create PayFast payment
    const payFastService = getPayFastService();
    const result = await payFastService.createPayment({
      orderId,
      amount,
      description,
      customerEmail,
      customerName,
      customData
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('PayFast payment creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create PayFast payment' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
