import { NextRequest, NextResponse } from 'next/server';
import { CODService } from '@/modules/payments/cod/server';

// Lazy initialization of COD service
function getCODService() {
  return new CODService({
    enabled: true,
    maxAmount: parseInt(process.env.COD_MAX_AMOUNT || '5000'),
    minAmount: parseInt(process.env.COD_MIN_AMOUNT || '50'),
    deliveryFee: parseInt(process.env.COD_DELIVERY_FEE || '50'),
    deliveryFeeType: (process.env.COD_DELIVERY_FEE_TYPE as 'fixed' | 'percentage') || 'fixed',
    supportedAreas: process.env.COD_SUPPORTED_AREAS?.split(',') || ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'],
    estimatedDeliveryDays: parseInt(process.env.COD_ESTIMATED_DELIVERY_DAYS || '3'),
    requiresPhoneVerification: true,
    requiresAddressVerification: true
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get COD payment status
    const codService = getCODService();
    const status = await codService.getPaymentStatus(orderId);

    if (!status) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ status });

  } catch (error) {
    console.error('COD status check error:', error);
    return NextResponse.json(
      { error: 'Failed to check payment status' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params;
    const body = await request.json();
    const { status, notes } = body;

    if (!orderId || !status) {
      return NextResponse.json(
        { error: 'Order ID and status are required' },
        { status: 400 }
      );
    }

    // Update COD payment status
    const codService = getCODService();
    const result = await codService.updatePaymentStatus(orderId, status, notes);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to update status' },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('COD status update error:', error);
    return NextResponse.json(
      { error: 'Failed to update payment status' },
      { status: 500 }
    );
  }
}
