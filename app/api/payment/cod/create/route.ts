import { NextRequest, NextResponse } from 'next/server';
import { CODService } from '@/modules/payments/cod/server';

// Lazy initialization of COD service
function getCODService() {
  return new CODService({
    enabled: true,
    maxAmount: parseInt(process.env.COD_MAX_AMOUNT || '5000'),
    minAmount: parseInt(process.env.COD_MIN_AMOUNT || '50'),
    deliveryFee: parseInt(process.env.COD_DELIVERY_FEE || '50'),
    deliveryFeeType: (process.env.COD_DELIVERY_FEE_TYPE as 'fixed' | 'percentage') || 'fixed',
    supportedAreas: process.env.COD_SUPPORTED_AREAS?.split(',') || ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'],
    estimatedDeliveryDays: parseInt(process.env.COD_ESTIMATED_DELIVERY_DAYS || '3'),
    requiresPhoneVerification: true,
    requiresAddressVerification: true
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      orderId,
      amount,
      currency = 'ZAR',
      description,
      customerName,
      customerEmail,
      customerPhone,
      deliveryAddress,
      specialInstructions,
      preferredDeliveryTime,
      customData
    } = body;

    // Validate required fields
    if (!orderId || !amount || !description || !customerName || !customerEmail || !customerPhone || !deliveryAddress) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create COD payment
    const codService = getCODService();
    const result = await codService.createPayment({
      orderId,
      amount,
      currency,
      description,
      customerName,
      customerEmail,
      customerPhone,
      deliveryAddress,
      specialInstructions,
      preferredDeliveryTime,
      customData
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('COD payment creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create COD payment' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
