import { NextRequest, NextResponse } from 'next/server';
import { PeachService } from '@/modules/payments/peach/server';

// Lazy initialization of Peach service
function getPeachService() {
  return new PeachService({
    entityId: process.env.PEACH_ENTITY_ID || 'test',
    username: process.env.PEACH_USERNAME || 'test',
    password: process.env.PEACH_PASSWORD || 'test',
    sandbox: process.env.PEACH_SANDBOX !== 'false',
    baseUrl: process.env.PEACH_BASE_URL || 'https://testapi-v2.peachpayments.com',
    successUrl: process.env.PEACH_SUCCESS_URL || 'http://localhost:3000/payment/success',
    cancelUrl: process.env.PEACH_CANCEL_URL || 'http://localhost:3000/payment/cancel',
    webhookUrl: process.env.PEACH_WEBHOOK_URL
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      orderId,
      amount,
      currency = 'ZAR',
      description,
      customerEmail,
      customerName,
      paymentBrand = 'CARD',
      customData
    } = body;

    // Validate required fields
    if (!orderId || !amount || !description || !customerEmail || !customerName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create Peach payment
    const peachService = getPeachService();
    const result = await peachService.createPayment({
      orderId,
      amount,
      currency,
      description,
      customerEmail,
      customerName,
      paymentBrand,
      customData
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('Peach payment creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create Peach payment' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
