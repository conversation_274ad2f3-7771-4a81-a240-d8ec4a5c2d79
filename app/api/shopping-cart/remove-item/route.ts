// app/shopping-cart/remove-item/route.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { removeItemFromCart } from '@/lib/shoppingCartBackendUtilities';
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Extract userId, productId, and groupId from the request body.
    const { userId, productId, groupId } = await req.json();

    console.log('Remove item request received:', { userId, productId, groupId });

    if (!userId || !productId || !groupId) {
      console.error('Missing required fields:', { userId, productId, groupId });
      return NextResponse.json(
        { error: 'User ID, Product ID, and Group ID are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Pass all three parameters to removeItemFromCart.
    console.log('Calling removeItemFromCart with:', { userId, productId, groupId });

    let updatedCart;
    try {
      updatedCart = await removeItemFromCart(userId, productId, groupId);

      if (!updatedCart) {
        console.error('removeItemFromCart returned null');
        return NextResponse.json(
          { error: 'Failed to remove item from cart. Item may not exist or cart not found.' },
          { headers: corsHeaders, status: 404 }
        );
      }

      // Log the updated cart structure for debugging
      console.log('Updated cart after removal:', JSON.stringify(updatedCart, null, 2));
    } catch (removeError) {
      console.error('Error in removeItemFromCart:', removeError);
      return NextResponse.json(
        {
          error: 'Error removing item from cart',
          details: removeError instanceof Error ? removeError.message : String(removeError)
        },
        { headers: corsHeaders, status: 500 }
      );
    }

    console.log('Item successfully removed, returning updated cart');
    return NextResponse.json(updatedCart, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to remove item from ShoppingCart:', error);
    return NextResponse.json(
      {
        error: 'Failed to remove item from ShoppingCart',
        details: error instanceof Error ? error.message : String(error)
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
