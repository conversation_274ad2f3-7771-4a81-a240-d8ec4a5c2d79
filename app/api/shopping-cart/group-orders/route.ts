import { NextResponse } from 'next/server';
import { getGroupOrders } from '../../../../lib/shoppingCartBackendUtilities';
import { getCorsHeaders } from '@/lib/cors';

export async function OPTIONS(req: Request) {
    const origin = req.headers.get('origin');
    const corsHeaders = getCorsHeaders(origin);
    return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: Request) {
    const origin = req.headers.get('origin');
    const corsHeaders = getCorsHeaders(origin);

    const url = new URL(req.url);
    const groupId = url.pathname.split('/')[4]; // Adjust index based on your route structure

    if (!groupId) {
        return NextResponse.json(
            { error: 'Group ID is required.' },
            { headers: corsHeaders, status: 400 }
        );
    }

    try {
        const orders = await getGroupOrders(groupId);
        if (!orders) {
            return NextResponse.json(
                { error: 'No orders found for this group.' },
                { headers: corsHeaders, status: 404 }
            );
        }
        return NextResponse.json(orders, { headers: corsHeaders, status: 200 });
    } catch (error) {
        console.error('Failed to fetch group orders:', error);
        return NextResponse.json(
            { error: 'Failed to fetch group orders.' },
            { headers: corsHeaders, status: 500 }
        );
    }
}
