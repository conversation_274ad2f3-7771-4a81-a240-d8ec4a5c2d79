// app/api/shopping-cart/transfer-items/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { ShoppingCart } from "@/models/ShoppingCart";
import { User } from "@/models/User";
import { getCorsHeaders } from "@/lib/cors";
import mongoose, { Types } from "mongoose";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    const body = await req.json();
    const { userId, sourceGroupId, targetGroupId } = body;

    if (!userId || !sourceGroupId || !targetGroupId) {
      return NextResponse.json(
        {
          success: false,
          message: "userId, sourceGroupId, and targetGroupId are required."
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: "User not found."
        },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Verify the user is a member of the target group
    const isUserInTargetGroup = user.stokvelGroups.some(
      (groupId: Types.ObjectId) => groupId.toString() === targetGroupId
    );

    if (!isUserInTargetGroup) {
      return NextResponse.json(
        {
          success: false,
          message: "User is not a member of the target group."
        },
        { headers: corsHeaders, status: 403 }
      );
    }

    // Find the source cart
    const sourceCart = await ShoppingCart.findOne({
      user: userId,
      groupId: sourceGroupId,
      isFinalized: false
    }).populate('items.product');

    if (!sourceCart || sourceCart.items.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: "No items found in the source group cart."
        },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Find or create the target cart
    let targetCart = await ShoppingCart.findOne({
      user: userId,
      groupId: targetGroupId,
      isFinalized: false
    });

    if (!targetCart) {
      targetCart = new ShoppingCart({
        user: userId,
        groupId: targetGroupId,
        items: [],
        total: 0,
        isFinalized: false
      });
    }

    // Transfer items from source cart to target cart
    for (const item of sourceCart.items) {
      // Get the product ID safely, handling both populated and non-populated cases
      const productId = typeof item.product === 'object' && item.product._id
        ? item.product._id
        : item.product;

      // Check if the item already exists in the target cart
      const existingItemIndex = targetCart.items.findIndex(
        (i) => i.product.toString() === productId.toString()
      );

      if (existingItemIndex !== -1) {
        // Update quantity if item already exists
        targetCart.items[existingItemIndex].quantity += item.quantity;
      } else {
        // Get product details - these are available from the populated product
        // Use type assertion to handle the populated product case
        const populatedProduct = item.product as unknown as { _id: Types.ObjectId; price?: number };
        const productPrice = typeof item.product === 'object' && 'price' in populatedProduct
          ? populatedProduct.price || 0
          : (item.price || 0);

        // Add new item if it doesn't exist
        targetCart.items.push({
          _id: new mongoose.Types.ObjectId().toString(), // Generate a new ID for the cart item
          product: productId,
          quantity: item.quantity,
          price: productPrice
        });
      }
    }

    // Recalculate the total
    targetCart.total = targetCart.items.reduce(
      (sum, item) => sum + (item.price || 0) * item.quantity,
      0
    );

    // Save the target cart
    await targetCart.save();

    // Clear the source cart
    sourceCart.items = [];
    sourceCart.total = 0;
    await sourceCart.save();

    return NextResponse.json(
      {
        success: true,
        message: "Items transferred successfully.",
        targetCart
      },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error("Error transferring cart items:", error);
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while transferring cart items.",
        error: "Internal Server Error"
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
