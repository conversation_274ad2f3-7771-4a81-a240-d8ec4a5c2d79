// app/api/shopping-cart/check-undelivered/route.ts

import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { ShoppingCart } from "@/models/ShoppingCart";
import { getCorsHeaders } from "@/lib/cors";
import '@/models'; // Ensure all models are loaded

export async function GET(request: NextRequest) {
  const origin = request.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Get userId and groupId from query parameters
    const userId = request.nextUrl.searchParams.get("userId");
    const groupId = request.nextUrl.searchParams.get("groupId");

    if (!userId || !groupId) {
      return NextResponse.json(
        { 
          success: false,
          message: "userId and groupId are required query parameters." 
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Find unfinalized carts for this user in this group
    const unfinalizedCarts = await ShoppingCart.find({
      user: userId,
      groupId: groupId,
      isFinalized: false
    });

    // Count items in all unfinalized carts
    let totalItems = 0;
    for (const cart of unfinalizedCarts) {
      totalItems += cart.items.reduce((sum, item) => sum + item.quantity, 0);
    }

    return NextResponse.json(
      { 
        success: true,
        hasUndeliveredItems: totalItems > 0,
        undeliveredItemsCount: totalItems
      },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error("Error checking undelivered items:", error);
    return NextResponse.json(
      { 
        success: false,
        message: "An error occurred while checking undelivered items.",
        error: "Internal Server Error" 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
