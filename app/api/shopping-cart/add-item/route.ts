// app/api/shopping-cart/add-item/route.ts
import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { addItemToCart } from '@/lib/shoppingCartBackendUtilities';
import { Product } from '@/models/Product';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: Request) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));

  try {
    const { userId, productId, quantity, groupId } = await req.json();

    // Validate required fields
    if (!userId || !productId || !groupId) {
      return NextResponse.json(
        { error: 'User ID, Product ID, and Group ID are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate quantity is a positive number
    const parsedQuantity = parseInt(quantity.toString(), 10);
    if (isNaN(parsedQuantity) || parsedQuantity <= 0) {
      return NextResponse.json(
        { error: 'Quantity must be a positive number.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Verify product exists
    const product = await Product.findById(productId);
    if (!product) {
      return NextResponse.json(
        { error: 'Product not found.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Add item to cart
    const updatedCart = await addItemToCart(userId, productId, parsedQuantity, groupId);
    if (!updatedCart) {
      return NextResponse.json(
        { error: 'Failed to add item to cart.' },
        { headers: corsHeaders, status: 500 }
      );
    }

    // Return the updated cart
    return NextResponse.json(updatedCart, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to add item to ShoppingCart:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

