// app/shopping-cart/create/route.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { createShoppingCart } from '@/lib/shoppingCartBackendUtilities';
import mongoose from 'mongoose';
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const userObjId = new mongoose.Types.ObjectId(userId);

    const newCart = await createShoppingCart(userObjId);

    return NextResponse.json(newCart, { headers: corsHeaders, status: 201 });
  } catch (error) {
    console.error('Error creating ShoppingCart:', error);
    return NextResponse.json(
      { error: 'Failed to create ShoppingCart' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

