// app/api/shopping-cart/clear/route.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { clearShoppingCart } from '@/lib/shoppingCartBackendUtilities';
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: Request) {
  // Cast to NextRequest for consistency
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Extract both userId and groupId from the request body
    const { userId, groupId } = await req.json();

    if (!userId || !groupId) {
      return NextResponse.json(
        { error: 'User ID and Group ID are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Call clearShoppingCart with both parameters
    const clearedCart = await clearShoppingCart(userId, groupId);
    if (!clearedCart) {
      return NextResponse.json(
        { error: 'Failed to clear shopping cart.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(clearedCart, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to clear ShoppingCart:', error);
    return NextResponse.json(
      { error: 'Failed to clear ShoppingCart' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
