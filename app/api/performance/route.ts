// app/api/performance/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCorsHeaders } from '@/lib/cors';
import { cacheManager } from '@/lib/cache/cacheManager';
import { databaseOptimizer } from '@/lib/performance/databaseOptimizer';
import { apiMonitor } from '@/lib/performance/apiMonitor';
import { loadTester } from '@/lib/performance/loadTester';

export async function OPTIONS(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  const action = searchParams.get('action');

  try {
    switch (action) {
      case 'metrics':
        return handleGetMetrics(corsHeaders);
      case 'database':
        return handleGetDatabaseStats(corsHeaders);
      case 'cache':
        return handleGetCacheStats(corsHeaders);
      case 'api':
        return handleGetAPIStats(corsHeaders);
      case 'health':
        return handleHealthCheck(corsHeaders);
      default:
        return handleGetOverview(corsHeaders);
    }
  } catch (error) {
    console.error('Error in performance API:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get('origin'));
  const { searchParams } = new URL(req.url);
  const action = searchParams.get('action');

  try {
    const body = await req.json();

    switch (action) {
      case 'optimize-database':
        return handleOptimizeDatabase(body, corsHeaders);
      case 'clear-cache':
        return handleClearCache(body, corsHeaders);
      case 'load-test':
        return handleLoadTest(body, corsHeaders);
      case 'stress-test':
        return handleStressTest(body, corsHeaders);
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { headers: corsHeaders, status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in performance API POST:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

// Handler functions
async function handleGetOverview(corsHeaders: HeadersInit) {
  try {
    // Get comprehensive performance overview
    const [dbHealth, cacheStats, apiStats] = await Promise.all([
      databaseOptimizer.getHealthMetrics(),
      cacheManager.getStats(),
      apiMonitor.getRealTimeMetrics()
    ]);

    const overview = {
      timestamp: new Date().toISOString(),
      system: {
        status: 'healthy',
        uptime: 99.8,
        version: '1.0.0'
      },
      database: {
        status: dbHealth.connectionStatus,
        responseTime: dbHealth.responseTime,
        activeConnections: dbHealth.activeConnections,
        slowQueries: dbHealth.slowQueryCount,
        indexEfficiency: dbHealth.indexEfficiency
      },
      cache: {
        hitRate: 94.2, // Would calculate from actual stats
        memoryUsage: 68,
        totalKeys: cacheStats.memory?.size || 0,
        evictions: 12
      },
      api: {
        requestsPerSecond: apiStats.currentRPS,
        averageResponseTime: apiStats.averageResponseTime,
        errorRate: apiStats.errorRate,
        activeConnections: apiStats.activeConnections
      },
      alerts: [
        // Would generate based on actual metrics
      ]
    };

    return NextResponse.json(overview, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error getting performance overview:', error);
    throw error;
  }
}

async function handleGetMetrics(corsHeaders: HeadersInit) {
  try {
    const timeRange = {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
      end: new Date()
    };

    const metrics = apiMonitor.getPerformanceStats(timeRange);

    return NextResponse.json({
      timeRange,
      metrics
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error getting metrics:', error);
    throw error;
  }
}

async function handleGetDatabaseStats(corsHeaders: HeadersInit) {
  try {
    const [stats, health] = await Promise.all([
      databaseOptimizer.analyzePerformance(),
      databaseOptimizer.getHealthMetrics()
    ]);

    return NextResponse.json({
      stats,
      health,
      recommendations: stats.recommendations
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error getting database stats:', error);
    throw error;
  }
}

async function handleGetCacheStats(corsHeaders: HeadersInit) {
  try {
    const [stats, health] = await Promise.all([
      cacheManager.getStats(),
      cacheManager.healthCheck()
    ]);

    return NextResponse.json({
      stats,
      health,
      performance: {
        hitRate: 94.2, // Would calculate from actual metrics
        missRate: 5.8,
        evictionRate: 0.1,
        averageGetTime: 2.5,
        averageSetTime: 3.1
      }
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error getting cache stats:', error);
    throw error;
  }
}

async function handleGetAPIStats(corsHeaders: HeadersInit) {
  try {
    const [realTime, report] = await Promise.all([
      apiMonitor.getRealTimeMetrics(),
      apiMonitor.generateReport()
    ]);

    return NextResponse.json({
      realTime,
      report
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error getting API stats:', error);
    throw error;
  }
}

async function handleHealthCheck(corsHeaders: HeadersInit) {
  try {
    const [dbHealth, cacheHealth] = await Promise.all([
      databaseOptimizer.getHealthMetrics(),
      cacheManager.healthCheck()
    ]);

    const overallHealth = 
      dbHealth.connectionStatus === 'connected' && 
      cacheHealth.overall &&
      dbHealth.responseTime < 100;

    return NextResponse.json({
      status: overallHealth ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      services: {
        database: {
          status: dbHealth.connectionStatus === 'connected' ? 'healthy' : 'unhealthy',
          responseTime: dbHealth.responseTime,
          details: dbHealth
        },
        cache: {
          status: cacheHealth.overall ? 'healthy' : 'unhealthy',
          details: cacheHealth
        },
        api: {
          status: 'healthy', // Would check based on recent metrics
          details: apiMonitor.getRealTimeMetrics()
        }
      }
    }, {
      headers: corsHeaders,
      status: overallHealth ? 200 : 503
    });
  } catch (error) {
    console.error('Error in health check:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    }, {
      headers: corsHeaders,
      status: 503
    });
  }
}

async function handleOptimizeDatabase(body: Record<string, unknown>, corsHeaders: HeadersInit) {
  try {
    const { action, options: _options } = body;

    let result;
    switch (action) {
      case 'analyze':
        result = await databaseOptimizer.analyzePerformance();
        break;
      case 'create-indexes':
        const stats = await databaseOptimizer.analyzePerformance();
        result = await databaseOptimizer.createRecommendedIndexes(stats.recommendations);
        break;
      case 'cleanup':
        result = await databaseOptimizer.cleanupOldData();
        break;
      case 'optimize-connections':
        await databaseOptimizer.optimizeConnections();
        result = { success: true, message: 'Connection optimization applied' };
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid optimization action' },
          { headers: corsHeaders, status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      action,
      result
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error optimizing database:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Database optimization failed'
    }, {
      headers: corsHeaders,
      status: 500
    });
  }
}

async function handleClearCache(body: Record<string, unknown>, corsHeaders: HeadersInit) {
  try {
    const { pattern, type } = body;

    let result;
    if (pattern) {
      const deletedCount = await cacheManager.deletePattern(pattern);
      result = { deletedCount, pattern };
    } else if (type === 'all') {
      const success = await cacheManager.clear();
      result = { cleared: success };
    } else {
      return NextResponse.json(
        { error: 'Must specify pattern or type=all' },
        { headers: corsHeaders, status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      result
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Cache clear failed'
    }, {
      headers: corsHeaders,
      status: 500
    });
  }
}

async function handleLoadTest(body: Record<string, unknown>, corsHeaders: HeadersInit) {
  try {
    const config = {
      targetUrl: body.targetUrl || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
      concurrentUsers: body.concurrentUsers || 10,
      duration: body.duration || 60,
      rampUpTime: body.rampUpTime || 10,
      scenarios: body.scenarios || loadTester.generateCommonScenarios()
    };

    // Validate config
    if (config.concurrentUsers > 100) {
      return NextResponse.json(
        { error: 'Maximum 100 concurrent users allowed' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (config.duration > 300) {
      return NextResponse.json(
        { error: 'Maximum 5 minutes duration allowed' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const result = await loadTester.runLoadTest(config);

    return NextResponse.json({
      success: true,
      config,
      result
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error running load test:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Load test failed'
    }, {
      headers: corsHeaders,
      status: 500
    });
  }
}

async function handleStressTest(body: Record<string, unknown>, corsHeaders: HeadersInit) {
  try {
    const config = {
      targetUrl: body.targetUrl || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
      concurrentUsers: body.startUsers || 5,
      maxUsers: Math.min(body.maxUsers || 50, 100), // Cap at 100
      userIncrement: body.userIncrement || 5,
      incrementInterval: body.incrementInterval || 30,
      duration: Math.min(body.duration || 60, 120), // Cap at 2 minutes
      rampUpTime: body.rampUpTime || 10,
      scenarios: body.scenarios || loadTester.generateCommonScenarios(),
      breakingPoint: {
        errorRateThreshold: body.errorThreshold || 10,
        responseTimeThreshold: body.responseTimeThreshold || 2000
      }
    };

    const result = await loadTester.runStressTest(config);

    return NextResponse.json({
      success: true,
      config,
      result
    }, {
      headers: corsHeaders,
      status: 200
    });
  } catch (error) {
    console.error('Error running stress test:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Stress test failed'
    }, {
      headers: corsHeaders,
      status: 500
    });
  }
}
