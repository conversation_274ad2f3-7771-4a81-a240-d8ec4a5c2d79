import { type NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { connectToDatabase } from "@/lib/dbconnect"
import { getCorsHeaders } from "@/lib/cors"
import {
  createYearEndBundle,
  getYearEndBundleById,
  updateYearEndBundle,
  deleteYearEndBundle,
} from "@/lib/backendSubscriptionUtilities"
import type { YearEndBundleType } from "@/types/subscriptionTypes"
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)
  return NextResponse.json({}, { headers: corsHeaders, status: 200 })
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const body = await req.json()
    const { 
      userId, 
      name,
      description,
      price,
      monthlyContribution,
      status,
      startDate, 
      endDate,
      bundleSize,
      itemList,
      deliveryMonth,
      totalSavingsGoal 
    } = body as YearEndBundleType

    // Validate required fields
    if (!userId || !name || !description || price === undefined || 
        !monthlyContribution || !status || !startDate || !endDate || 
        !bundleSize || !deliveryMonth || totalSavingsGoal === undefined) {
      return NextResponse.json(
        { error: "All fields are required.", missingFields: { 
          userId: !userId,
          name: !name,
          description: !description,
          price: price === undefined,
          monthlyContribution: !monthlyContribution,
          status: !status,
          startDate: !startDate,
          endDate: !endDate,
          bundleSize: !bundleSize,
          deliveryMonth: !deliveryMonth,
          totalSavingsGoal: totalSavingsGoal === undefined
        }}, 
        { headers: corsHeaders, status: 400 }
      )
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json({ error: "Invalid userId format" }, { headers: corsHeaders, status: 400 })
    }

    const newBundle = await createYearEndBundle(
      new mongoose.Types.ObjectId(userId),
      new Date(startDate),
      new Date(endDate),
      Number(monthlyContribution),
      Number(totalSavingsGoal),
      name,
      description,
      Number(price),
      bundleSize,
      Array.isArray(itemList) ? itemList : [],
      Number(deliveryMonth)
    )

    return NextResponse.json(newBundle, { headers: corsHeaders, status: 201 })
  } catch (error) {
    console.error("Error creating YearEndBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const { searchParams } = new URL(req.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { headers: corsHeaders, status: 400 })
    }

    const bundle = await getYearEndBundleById(id)
    if (!bundle) {
      return NextResponse.json({ error: "Bundle not found" }, { headers: corsHeaders, status: 404 })
    }

    return NextResponse.json(bundle, { headers: corsHeaders })
  } catch (error) {
    console.error("Error fetching YearEndBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const { searchParams } = new URL(req.url)
    const id = searchParams.get("id")
    const body = await req.json()

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { headers: corsHeaders, status: 400 })
    }

    const updatedBundle = await updateYearEndBundle(id, body)
    if (!updatedBundle) {
      return NextResponse.json({ error: "Bundle not found" }, { headers: corsHeaders, status: 404 })
    }

    return NextResponse.json(updatedBundle, { headers: corsHeaders })
  } catch (error) {
    console.error("Error updating YearEndBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const { searchParams } = new URL(req.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { headers: corsHeaders, status: 400 })
    }

    const deletedBundle = await deleteYearEndBundle(id)
    if (!deletedBundle) {
      return NextResponse.json({ error: "Bundle not found" }, { headers: corsHeaders, status: 404 })
    }

    return NextResponse.json(deletedBundle, { headers: corsHeaders })
  } catch (error) {
    console.error("Error deleting YearEndBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}
