import { type NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { connectToDatabase } from "@/lib/dbconnect"
import { getCorsHeaders } from "@/lib/cors"
import {
  createFuneralBenefits,
  getFuneralBenefitsById,
  updateFuneralBenefits,
  deleteFuneralBenefits,
} from "@/lib/backendSubscriptionUtilities"
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)
  return NextResponse.json({}, { headers: corsHeaders, status: 200 })
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const body = await req.json()
    const { userId, startDate, endDate, monthlyContribution, coverageAmount, beneficiaries } = body

    if (!userId || !startDate || !endDate || !monthlyContribution || !coverageAmount || !beneficiaries) {
      return NextResponse.json({ error: "All fields are required." }, { headers: corsHeaders, status: 400 })
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json({ error: "Invalid userId format" }, { headers: corsHeaders, status: 400 })
    }

    const newSubscription = await createFuneralBenefits(
      new mongoose.Types.ObjectId(userId),
      new Date(startDate),
      new Date(endDate),
      monthlyContribution,
      coverageAmount,
      beneficiaries,
    )

    return NextResponse.json(newSubscription, { headers: corsHeaders, status: 201 })
  } catch (error) {
    console.error("Error creating FuneralBenefits:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const url = new URL(req.url)
    const id = url.searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "id is required." }, { headers: corsHeaders, status: 400 })
    }

    const subscription = await getFuneralBenefitsById(id)

    if (!subscription) {
      return NextResponse.json(
        { error: "FuneralBenefits subscription not found" },
        { headers: corsHeaders, status: 404 },
      )
    }

    return NextResponse.json(subscription, { headers: corsHeaders, status: 200 })
  } catch (error) {
    console.error("Error fetching FuneralBenefits:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const url = new URL(req.url)
    const id = url.searchParams.get("id")
    const body = await req.json()

    if (!id) {
      return NextResponse.json({ error: "id is required." }, { headers: corsHeaders, status: 400 })
    }

    const updatedSubscription = await updateFuneralBenefits(id, body)

    if (!updatedSubscription) {
      return NextResponse.json(
        { error: "FuneralBenefits subscription not found" },
        { headers: corsHeaders, status: 404 },
      )
    }

    return NextResponse.json(updatedSubscription, { headers: corsHeaders, status: 200 })
  } catch (error) {
    console.error("Error updating FuneralBenefits:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const url = new URL(req.url)
    const id = url.searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "id is required." }, { headers: corsHeaders, status: 400 })
    }

    const deletedSubscription = await deleteFuneralBenefits(id)

    if (!deletedSubscription) {
      return NextResponse.json(
        { error: "FuneralBenefits subscription not found" },
        { headers: corsHeaders, status: 404 },
      )
    }

    return NextResponse.json(
      { message: "FuneralBenefits subscription deleted successfully" },
      { headers: corsHeaders, status: 200 },
    )
  } catch (error) {
    console.error("Error deleting FuneralBenefits:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

