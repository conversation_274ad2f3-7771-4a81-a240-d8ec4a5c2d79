import { type NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { connectToDatabase } from "@/lib/dbconnect"
import { getCorsHeaders } from "@/lib/cors"
import {
  createGrocerySchoolBundle,
  getGrocerySchoolBundleById,
  updateGrocerySchoolBundle,
  deleteGrocerySchoolBundle,
} from "@/lib/backendSubscriptionUtilities"

import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)
  return NextResponse.json({}, { headers: corsHeaders, status: 200 })
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const body = await req.json()
    const { userId, startDate, endDate, monthlyContribution, groceryAllocation, schoolSuppliesAllocation } = body

    if (!userId || !startDate || !endDate || !monthlyContribution || !groceryAllocation || !schoolSuppliesAllocation) {
      return NextResponse.json({ error: "All fields are required." }, { headers: corsHeaders, status: 400 })
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json({ error: "Invalid userId format" }, { headers: corsHeaders, status: 400 })
    }

    const newBundle = await createGrocerySchoolBundle(
      new mongoose.Types.ObjectId(userId),
      new Date(startDate),
      new Date(endDate),
      monthlyContribution,
      groceryAllocation,
      schoolSuppliesAllocation,
    )

    return NextResponse.json(newBundle, { headers: corsHeaders, status: 201 })
  } catch (error) {
    console.error("Error creating GrocerySchoolBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const url = new URL(req.url)
    const id = url.searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "id is required." }, { headers: corsHeaders, status: 400 })
    }

    const bundle = await getGrocerySchoolBundleById(id)

    if (!bundle) {
      return NextResponse.json({ error: "GrocerySchoolBundle not found" }, { headers: corsHeaders, status: 404 })
    }

    return NextResponse.json(bundle, { headers: corsHeaders, status: 200 })
  } catch (error) {
    console.error("Error fetching GrocerySchoolBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const url = new URL(req.url)
    const id = url.searchParams.get("id")
    const body = await req.json()

    if (!id) {
      return NextResponse.json({ error: "id is required." }, { headers: corsHeaders, status: 400 })
    }

    const updatedBundle = await updateGrocerySchoolBundle(id, body)

    if (!updatedBundle) {
      return NextResponse.json({ error: "GrocerySchoolBundle not found" }, { headers: corsHeaders, status: 404 })
    }

    return NextResponse.json(updatedBundle, { headers: corsHeaders, status: 200 })
  } catch (error) {
    console.error("Error updating GrocerySchoolBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const url = new URL(req.url)
    const id = url.searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "id is required." }, { headers: corsHeaders, status: 400 })
    }

    const deletedBundle = await deleteGrocerySchoolBundle(id)

    if (!deletedBundle) {
      return NextResponse.json({ error: "GrocerySchoolBundle not found" }, { headers: corsHeaders, status: 404 })
    }

    return NextResponse.json(
      { message: "GrocerySchoolBundle deleted successfully" },
      { headers: corsHeaders, status: 200 },
    )
  } catch (error) {
    console.error("Error deleting GrocerySchoolBundle:", error)
    return NextResponse.json({ error: "Internal Server Error" }, { headers: corsHeaders, status: 500 })
  }
}

