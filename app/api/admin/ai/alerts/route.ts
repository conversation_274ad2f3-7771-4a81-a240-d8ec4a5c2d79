// Smart Alerts API
// Handles AI-powered smart alerts and notifications

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { aiRecommendationService } from '@/lib/services/aiRecommendationService';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const severity = searchParams.get('severity');
    const type = searchParams.get('type');
    const resolved = searchParams.get('resolved');
    const limit = parseInt(searchParams.get('limit') || '50');

    // Get smart alerts
    const allAlerts = await aiRecommendationService.getSmartAlerts();
    
    // Filter alerts based on query parameters
    let filteredAlerts = allAlerts;
    
    if (severity) {
      filteredAlerts = filteredAlerts.filter(alert => alert.severity === severity);
    }
    
    if (type) {
      filteredAlerts = filteredAlerts.filter(alert => alert.type === type);
    }
    
    if (resolved !== null) {
      const resolvedFilter = resolved === 'true';
      filteredAlerts = filteredAlerts.filter(alert => !!alert.resolvedAt === resolvedFilter);
    }

    // Sort by creation date (newest first) and limit results
    filteredAlerts = filteredAlerts
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);

    // Calculate summary statistics
    const summary = {
      totalAlerts: allAlerts.length,
      unresolvedAlerts: allAlerts.filter(a => !a.resolvedAt).length,
      bySeverity: {
        critical: allAlerts.filter(a => a.severity === 'critical').length,
        warning: allAlerts.filter(a => a.severity === 'warning').length,
        info: allAlerts.filter(a => a.severity === 'info').length
      },
      byType: {
        performance: allAlerts.filter(a => a.type === 'performance').length,
        security: allAlerts.filter(a => a.type === 'security').length,
        business: allAlerts.filter(a => a.type === 'business').length,
        system: allAlerts.filter(a => a.type === 'system').length
      },
      averageResolutionTime: calculateAverageResolutionTime(allAlerts.filter(a => a.resolvedAt))
    };

    return NextResponse.json({
      success: true,
      data: filteredAlerts,
      summary,
      metadata: {
        totalCount: filteredAlerts.length,
        filters: { severity, type, resolved, limit },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Smart alerts GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch smart alerts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'create':
        // Create new smart alert
        const { type, severity, title, message, context } = data;
        
        if (!type || !severity || !title || !message) {
          return NextResponse.json(
            { error: 'Missing required fields: type, severity, title, message' },
            { status: 400 }
          );
        }

        const alert = await aiRecommendationService.generateSmartAlert(
          type,
          severity,
          title,
          message,
          context || {}
        );

        return NextResponse.json({
          success: true,
          data: alert,
          message: 'Smart alert created successfully'
        });

      case 'resolve':
        // Resolve alert
        const { alertId, resolution } = data;
        
        if (!alertId) {
          return NextResponse.json(
            { error: 'Alert ID is required' },
            { status: 400 }
          );
        }

        const resolved = await resolveAlert(alertId, resolution, payload.userId);
        
        if (!resolved) {
          return NextResponse.json(
            { error: 'Alert not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          message: 'Alert resolved successfully'
        });

      case 'bulk_resolve':
        // Resolve multiple alerts
        const { alertIds, bulkResolution } = data;
        
        if (!alertIds || !Array.isArray(alertIds)) {
          return NextResponse.json(
            { error: 'Alert IDs array is required' },
            { status: 400 }
          );
        }

        const results = await Promise.all(
          alertIds.map(id => resolveAlert(id, bulkResolution, payload.userId))
        );

        const resolvedCount = results.filter(Boolean).length;

        return NextResponse.json({
          success: true,
          data: { resolved: resolvedCount, total: alertIds.length },
          message: `${resolvedCount} alerts resolved successfully`
        });

      case 'escalate':
        // Escalate alert
        const { alertId: escalateId, escalationLevel } = data;
        
        if (!escalateId) {
          return NextResponse.json(
            { error: 'Alert ID is required' },
            { status: 400 }
          );
        }

        const escalated = await escalateAlert(escalateId, escalationLevel, payload.userId);
        
        return NextResponse.json({
          success: true,
          data: escalated,
          message: 'Alert escalated successfully'
        });

      case 'auto_resolve':
        // Trigger auto-resolution for eligible alerts
        const autoResolved = await triggerAutoResolution();
        
        return NextResponse.json({
          success: true,
          data: { autoResolved },
          message: `${autoResolved} alerts auto-resolved`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Smart alerts POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process smart alerts request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { id, resolution, escalationLevel } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Alert ID is required' },
        { status: 400 }
      );
    }

    if (resolution) {
      // Resolve alert
      const resolved = await resolveAlert(id, resolution, payload.userId);
      
      if (!resolved) {
        return NextResponse.json(
          { error: 'Alert not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Alert resolved successfully'
      });
    }

    if (escalationLevel) {
      // Escalate alert
      const escalated = await escalateAlert(id, escalationLevel, payload.userId);
      
      return NextResponse.json({
        success: true,
        data: escalated,
        message: 'Alert escalated successfully'
      });
    }

    return NextResponse.json(
      { error: 'No valid action specified' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Smart alerts PUT error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update smart alert',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper functions
function calculateAverageResolutionTime(resolvedAlerts: any[]): number {
  if (resolvedAlerts.length === 0) return 0;
  
  const totalTime = resolvedAlerts.reduce((sum, alert) => {
    const created = new Date(alert.createdAt).getTime();
    const resolved = new Date(alert.resolvedAt).getTime();
    return sum + (resolved - created);
  }, 0);
  
  return totalTime / resolvedAlerts.length / (1000 * 60); // Return in minutes
}

async function resolveAlert(alertId: string, resolution: string, userId: string): Promise<boolean> {
  // Mock implementation - in production, this would update the database
  console.log(`Resolving alert ${alertId} with resolution: ${resolution} by user: ${userId}`);
  return true;
}

async function escalateAlert(alertId: string, escalationLevel: number, userId: string): Promise<any> {
  // Mock implementation - in production, this would trigger escalation workflow
  const escalation = {
    alertId,
    escalationLevel,
    escalatedBy: userId,
    escalatedAt: new Date().toISOString(),
    notificationsSent: ['<EMAIL>', '<EMAIL>']
  };
  
  console.log('Alert escalated:', escalation);
  return escalation;
}

async function triggerAutoResolution(): Promise<number> {
  // Mock implementation - in production, this would check for auto-resolvable alerts
  const autoResolvableCount = Math.floor(Math.random() * 5);
  console.log(`Auto-resolved ${autoResolvableCount} alerts`);
  return autoResolvableCount;
}
