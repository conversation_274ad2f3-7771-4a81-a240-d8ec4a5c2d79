// AI Predictive Insights API
// Handles AI-powered predictive analytics and insights

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { aiRecommendationService } from '@/lib/services/aiRecommendationService';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const timeHorizon = searchParams.get('timeHorizon') || '30d';
    const insightTypes = searchParams.get('types')?.split(',') || [];

    // Get predictive insights
    const insights = await aiRecommendationService.generatePredictiveInsights(timeHorizon);

    // Filter by types if specified
    const filteredInsights = insightTypes.length > 0 
      ? insights.filter(insight => insightTypes.includes(insight.type))
      : insights;

    // Calculate summary statistics
    const summary = {
      totalInsights: filteredInsights.length,
      byType: {
        trend: filteredInsights.filter(i => i.type === 'trend').length,
        anomaly: filteredInsights.filter(i => i.type === 'anomaly').length,
        opportunity: filteredInsights.filter(i => i.type === 'opportunity').length,
        risk: filteredInsights.filter(i => i.type === 'risk').length
      },
      averageConfidence: filteredInsights.reduce((sum, i) => sum + i.confidence, 0) / filteredInsights.length || 0,
      highConfidenceCount: filteredInsights.filter(i => i.confidence > 80).length
    };

    return NextResponse.json({
      success: true,
      data: filteredInsights,
      summary,
      metadata: {
        timeHorizon,
        requestedTypes: insightTypes,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('AI insights error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch AI insights',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'refresh':
        // Refresh predictive insights
        const timeHorizon = data?.timeHorizon || '30d';
        const refreshedInsights = await aiRecommendationService.generatePredictiveInsights(timeHorizon);
        
        return NextResponse.json({
          success: true,
          data: refreshedInsights,
          message: 'Insights refreshed successfully'
        });

      case 'analyze_metric':
        // Analyze specific metric for insights
        const { metric, period } = data;
        
        if (!metric) {
          return NextResponse.json(
            { error: 'Metric is required for analysis' },
            { status: 400 }
          );
        }

        // Generate insights for specific metric
        const metricInsights = await generateMetricInsights(metric, period || '30d');
        
        return NextResponse.json({
          success: true,
          data: metricInsights,
          message: 'Metric analysis completed'
        });

      case 'export_insights':
        // Export insights data
        const { format, filters } = data;
        const exportData = await exportInsightsData(format || 'json', filters);
        
        return NextResponse.json({
          success: true,
          data: exportData,
          message: 'Insights export generated'
        });

      case 'create_alert':
        // Create alert based on insight
        const { insightId, alertConfig } = data;
        
        if (!insightId || !alertConfig) {
          return NextResponse.json(
            { error: 'Insight ID and alert configuration are required' },
            { status: 400 }
          );
        }

        const alert = await createInsightAlert(insightId, alertConfig, payload.userId);
        
        return NextResponse.json({
          success: true,
          data: alert,
          message: 'Alert created successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('AI insights POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process AI insights request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Generate insights for specific metric
async function generateMetricInsights(metric: string, period: string) {
  // Mock implementation - in production, this would use actual ML models
  const insights = [];

  switch (metric) {
    case 'revenue':
      insights.push({
        id: `insight_revenue_${Date.now()}`,
        type: 'trend',
        title: 'Revenue Trend Analysis',
        description: `Revenue analysis for ${period} period shows positive growth trajectory`,
        prediction: {
          metric: 'Revenue',
          currentValue: 125000,
          predictedValue: 147500,
          changePercentage: 18
        },
        confidence: 85,
        timeHorizon: period,
        affectedMetrics: ['Revenue', 'Profit Margin', 'Customer Acquisition'],
        recommendedActions: [
          'Maintain current marketing strategies',
          'Consider scaling successful campaigns',
          'Monitor for seasonal variations'
        ]
      });
      break;

    case 'customer_churn':
      insights.push({
        id: `insight_churn_${Date.now()}`,
        type: 'risk',
        title: 'Customer Churn Risk Analysis',
        description: 'AI models detect elevated churn risk in specific customer segments',
        prediction: {
          metric: 'Churn Rate',
          currentValue: 5.2,
          predictedValue: 7.8,
          changePercentage: 50
        },
        confidence: 88,
        timeHorizon: period,
        affectedMetrics: ['Customer Retention', 'Lifetime Value', 'Revenue'],
        recommendedActions: [
          'Launch targeted retention campaigns',
          'Improve customer support touchpoints',
          'Analyze feedback from churned customers'
        ]
      });
      break;

    case 'inventory':
      insights.push({
        id: `insight_inventory_${Date.now()}`,
        type: 'opportunity',
        title: 'Inventory Optimization Opportunity',
        description: 'AI analysis identifies potential inventory optimization opportunities',
        prediction: {
          metric: 'Inventory Turnover',
          currentValue: 6.2,
          predictedValue: 8.1,
          changePercentage: 31
        },
        confidence: 76,
        timeHorizon: period,
        affectedMetrics: ['Inventory Costs', 'Cash Flow', 'Storage Efficiency'],
        recommendedActions: [
          'Optimize slow-moving inventory',
          'Adjust reorder points based on demand patterns',
          'Consider just-in-time ordering for high-turnover items'
        ]
      });
      break;

    default:
      insights.push({
        id: `insight_general_${Date.now()}`,
        type: 'trend',
        title: `${metric} Analysis`,
        description: `General analysis for ${metric} metric`,
        prediction: {
          metric: metric,
          currentValue: 100,
          predictedValue: 110,
          changePercentage: 10
        },
        confidence: 70,
        timeHorizon: period,
        affectedMetrics: [metric],
        recommendedActions: [
          'Monitor metric trends closely',
          'Investigate underlying factors',
          'Consider optimization strategies'
        ]
      });
  }

  return insights;
}

// Export insights data
async function exportInsightsData(format: string, filters: any) {
  const exportId = `insights_export_${Date.now()}`;
  const fileName = `ai_insights_${new Date().toISOString().split('T')[0]}.${format}`;
  
  // Mock export data
  const exportData = {
    exportId,
    fileName,
    format,
    status: 'completed',
    downloadUrl: `/api/admin/exports/${exportId}`,
    createdAt: new Date().toISOString(),
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    recordCount: 25,
    fileSize: 1.2 * 1024 * 1024, // 1.2 MB
    filters
  };

  return exportData;
}

// Create alert based on insight
async function createInsightAlert(insightId: string, alertConfig: any, userId: string) {
  const alert = {
    id: `alert_${Date.now()}`,
    insightId,
    type: alertConfig.type || 'insight',
    severity: alertConfig.severity || 'info',
    title: alertConfig.title || 'AI Insight Alert',
    message: alertConfig.message || 'AI has detected an important insight',
    threshold: alertConfig.threshold,
    recipients: alertConfig.recipients || [],
    isActive: true,
    createdAt: new Date().toISOString(),
    createdBy: userId
  };

  // In production, this would save to database and set up monitoring
  console.log('Created insight alert:', alert);

  return alert;
}
