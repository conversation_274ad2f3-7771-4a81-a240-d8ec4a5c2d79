// User Analytics API
// Provides comprehensive user analytics and insights

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { userManagementService } from '@/lib/services/userManagementService';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const includeChurnPrediction = searchParams.get('includeChurnPrediction') === 'true';
    const includeCLV = searchParams.get('includeCLV') === 'true';

    // Get user analytics
    const userAnalytics = await userManagementService.getUserAnalytics(userId || undefined);

    // Enhance with additional analytics if requested
    const enhancedAnalytics = await Promise.all(
      userAnalytics.map(async (user) => {
        const enhanced: any = { ...user };

        if (includeChurnPrediction) {
          try {
            enhanced.churnPrediction = await userManagementService.predictCustomerChurn(user.userId);
          } catch (error) {
            console.warn(`Failed to get churn prediction for user ${user.userId}:`, error);
          }
        }

        if (includeCLV) {
          try {
            enhanced.lifetimeValueAnalysis = await userManagementService.calculateCustomerLifetimeValue(user.userId);
          } catch (error) {
            console.warn(`Failed to get CLV for user ${user.userId}:`, error);
          }
        }

        return enhanced;
      })
    );

    // Calculate summary statistics
    const summary = calculateUserSummary(userAnalytics);

    return NextResponse.json({
      success: true,
      data: enhancedAnalytics,
      summary,
      metadata: {
        totalUsers: userAnalytics.length,
        timestamp: new Date().toISOString(),
        includeChurnPrediction,
        includeCLV
      }
    });

  } catch (error) {
    console.error('User analytics error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'bulk_churn_prediction':
        // Generate churn predictions for multiple users
        const { userIds } = data;
        const churnPredictions = await Promise.all(
          userIds.map(async (userId: string) => {
            try {
              return await userManagementService.predictCustomerChurn(userId);
            } catch (error) {
              return { userId, error: 'Failed to predict churn' };
            }
          })
        );

        return NextResponse.json({
          success: true,
          data: churnPredictions,
          message: 'Bulk churn predictions generated'
        });

      case 'bulk_clv_calculation':
        // Calculate CLV for multiple users
        const { userIds: clvUserIds } = data;
        const clvCalculations = await Promise.all(
          clvUserIds.map(async (userId: string) => {
            try {
              return await userManagementService.calculateCustomerLifetimeValue(userId);
            } catch (error) {
              return { userId, error: 'Failed to calculate CLV' };
            }
          })
        );

        return NextResponse.json({
          success: true,
          data: clvCalculations,
          message: 'Bulk CLV calculations completed'
        });

      case 'export_analytics':
        // Export user analytics data
        const { format, filters } = data;
        const exportData = await exportUserAnalytics(format, filters);

        return NextResponse.json({
          success: true,
          data: exportData,
          message: 'User analytics export generated'
        });

      case 'refresh_analytics':
        // Refresh analytics cache
        // Implementation would clear cache and recalculate
        return NextResponse.json({
          success: true,
          message: 'Analytics cache refreshed'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('User analytics POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process user analytics request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Calculate user summary statistics
function calculateUserSummary(users: any[]) {
  if (users.length === 0) {
    return {
      totalUsers: 0,
      activeUsers: 0,
      highValueUsers: 0,
      atRiskUsers: 0,
      averageLifetimeValue: 0,
      averageActivityScore: 0,
      engagementDistribution: {},
      segmentDistribution: {},
      acquisitionChannelDistribution: {}
    };
  }

  const totalUsers = users.length;
  const activeUsers = users.filter(u => u.engagementLevel !== 'inactive').length;
  const highValueUsers = users.filter(u => u.lifetimeValue > 10000).length;
  const atRiskUsers = users.filter(u => u.churnProbability > 0.7).length;
  
  const averageLifetimeValue = users.reduce((sum, u) => sum + u.lifetimeValue, 0) / totalUsers;
  const averageActivityScore = users.reduce((sum, u) => sum + u.activityScore, 0) / totalUsers;

  // Engagement distribution
  const engagementDistribution = users.reduce((acc, user) => {
    acc[user.engagementLevel] = (acc[user.engagementLevel] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Segment distribution
  const segmentDistribution = users.reduce((acc, user) => {
    acc[user.behaviorSegment] = (acc[user.behaviorSegment] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Acquisition channel distribution
  const acquisitionChannelDistribution = users.reduce((acc, user) => {
    acc[user.acquisitionChannel] = (acc[user.acquisitionChannel] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    totalUsers,
    activeUsers,
    highValueUsers,
    atRiskUsers,
    averageLifetimeValue,
    averageActivityScore,
    engagementDistribution,
    segmentDistribution,
    acquisitionChannelDistribution,
    metrics: {
      activeUserPercentage: (activeUsers / totalUsers) * 100,
      highValueUserPercentage: (highValueUsers / totalUsers) * 100,
      atRiskUserPercentage: (atRiskUsers / totalUsers) * 100
    }
  };
}

// Export user analytics data
async function exportUserAnalytics(format: string, filters: any) {
  // Mock export functionality - in production, this would generate actual files
  const exportId = `export_${Date.now()}`;
  const fileName = `user_analytics_${new Date().toISOString().split('T')[0]}.${format}`;
  
  // Simulate export processing
  const exportData = {
    exportId,
    fileName,
    format,
    status: 'completed',
    downloadUrl: `/api/admin/exports/${exportId}`,
    createdAt: new Date().toISOString(),
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    recordCount: 1000, // Mock count
    fileSize: 2.5 * 1024 * 1024, // 2.5 MB
    filters
  };

  return exportData;
}
