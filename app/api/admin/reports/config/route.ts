// Report Configuration API
// Handles CRUD operations for report configurations

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { reportingService } from '@/lib/services/reportingService';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get all report configurations
    const reportConfigs = reportingService.getReportConfigs();

    return NextResponse.json({
      success: true,
      data: reportConfigs
    });

  } catch (error) {
    console.error('Report config GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch report configurations',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.type || !body.format) {
      return NextResponse.json(
        { error: 'Missing required fields: name, type, format' },
        { status: 400 }
      );
    }

    // Create report configuration
    const reportConfig = await reportingService.createReportConfig({
      name: body.name,
      description: body.description || '',
      type: body.type,
      format: body.format,
      schedule: body.schedule,
      filters: body.filters || {
        dateRange: {
          from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          to: new Date(),
          type: 'rolling',
          rollingPeriod: 30
        },
        categories: [],
        products: [],
        customers: [],
        groups: [],
        status: []
      },
      metrics: body.metrics || [],
      visualizations: body.visualizations || [],
      recipients: body.recipients || [],
      createdBy: payload.userId,
      isActive: body.isActive !== false
    });

    return NextResponse.json({
      success: true,
      data: reportConfig,
      message: 'Report configuration created successfully'
    });

  } catch (error) {
    console.error('Report config POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create report configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { id, ...updates } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Report configuration ID is required' },
        { status: 400 }
      );
    }

    // Update report configuration
    const updatedConfig = await reportingService.updateReportConfig(id, updates);

    if (!updatedConfig) {
      return NextResponse.json(
        { error: 'Report configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedConfig,
      message: 'Report configuration updated successfully'
    });

  } catch (error) {
    console.error('Report config PUT error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update report configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Report configuration ID is required' },
        { status: 400 }
      );
    }

    // Delete report configuration
    const deleted = await reportingService.deleteReportConfig(id);

    if (!deleted) {
      return NextResponse.json(
        { error: 'Report configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Report configuration deleted successfully'
    });

  } catch (error) {
    console.error('Report config DELETE error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete report configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
