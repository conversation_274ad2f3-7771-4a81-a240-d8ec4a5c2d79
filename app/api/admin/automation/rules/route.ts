// Automation Rules API
// Handles CRUD operations for automation rules

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { aiRecommendationService } from '@/lib/services/aiRecommendationService';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get('active');
    const type = searchParams.get('type');

    // Get automation rules
    const allRules = await aiRecommendationService.getAutomationRules();
    
    // Filter rules based on query parameters
    let filteredRules = allRules;
    
    if (isActive !== null) {
      const activeFilter = isActive === 'true';
      filteredRules = filteredRules.filter(rule => rule.isActive === activeFilter);
    }
    
    if (type) {
      filteredRules = filteredRules.filter(rule => rule.trigger.type === type);
    }

    // Calculate summary statistics
    const summary = {
      totalRules: allRules.length,
      activeRules: allRules.filter(r => r.isActive).length,
      inactiveRules: allRules.filter(r => !r.isActive).length,
      byTriggerType: {
        schedule: allRules.filter(r => r.trigger.type === 'schedule').length,
        event: allRules.filter(r => r.trigger.type === 'event').length,
        threshold: allRules.filter(r => r.trigger.type === 'threshold').length,
        manual: allRules.filter(r => r.trigger.type === 'manual').length
      },
      averageSuccessRate: allRules.reduce((sum, r) => sum + r.successRate, 0) / allRules.length || 0,
      totalExecutions: allRules.reduce((sum, r) => sum + r.executionCount, 0)
    };

    return NextResponse.json({
      success: true,
      data: filteredRules,
      summary,
      metadata: {
        totalCount: filteredRules.length,
        filters: { isActive, type },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Automation rules GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch automation rules',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.trigger || !body.actions) {
      return NextResponse.json(
        { error: 'Missing required fields: name, trigger, actions' },
        { status: 400 }
      );
    }

    // Create automation rule
    const automationRule = await aiRecommendationService.createAutomationRule({
      name: body.name,
      description: body.description || '',
      trigger: body.trigger,
      conditions: body.conditions || [],
      actions: body.actions,
      isActive: body.isActive !== false,
      priority: body.priority || 1,
      createdBy: payload.userId
    });

    return NextResponse.json({
      success: true,
      data: automationRule,
      message: 'Automation rule created successfully'
    });

  } catch (error) {
    console.error('Automation rules POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create automation rule',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { id, action, ...updates } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Automation rule ID is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'toggle':
        // Toggle rule active status
        const rules = await aiRecommendationService.getAutomationRules();
        const rule = rules.find(r => r.id === id);
        
        if (!rule) {
          return NextResponse.json(
            { error: 'Automation rule not found' },
            { status: 404 }
          );
        }

        // Update rule status (mock implementation)
        rule.isActive = !rule.isActive;
        
        return NextResponse.json({
          success: true,
          data: rule,
          message: `Automation rule ${rule.isActive ? 'activated' : 'deactivated'}`
        });

      case 'execute':
        // Execute rule manually
        const executed = await aiRecommendationService.executeAutomationRule(id);
        
        return NextResponse.json({
          success: true,
          data: { executed },
          message: executed ? 'Rule executed successfully' : 'Rule execution failed'
        });

      case 'update':
        // Update rule configuration
        // Mock implementation - in production, this would update the actual rule
        return NextResponse.json({
          success: true,
          message: 'Automation rule updated successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Automation rules PUT error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update automation rule',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Automation rule ID is required' },
        { status: 400 }
      );
    }

    // Delete automation rule (mock implementation)
    // In production, this would remove the rule from database and stop any scheduled executions
    
    return NextResponse.json({
      success: true,
      message: 'Automation rule deleted successfully'
    });

  } catch (error) {
    console.error('Automation rules DELETE error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete automation rule',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
