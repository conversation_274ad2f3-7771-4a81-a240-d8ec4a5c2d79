// app/api/products/get/[productId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Product } from '@/models/Product';
import { getCorsHeaders } from '@/lib/cors';
import '@/models'; // Ensure all models are loaded

export async function GET(request: NextRequest) {
  const origin = request.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Extract productId from the URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const productId = pathParts[pathParts.length - 1]; // The productId is the last part of the path

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400, headers: corsHeaders }
      );
    }

    // Find the product by ID and populate the category
    const product = await Product.findById(productId)
      .populate('category')
      .lean();

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404, headers: corsHeaders }
      );
    }

    // Convert dates to ISO strings for JSON serialization
    const serializedProduct = {
      ...product,
      createdAt: product.createdAt ? product.createdAt.toISOString() : null,
      updatedAt: product.updatedAt ? product.updatedAt.toISOString() : null,
    };

    return NextResponse.json(serializedProduct, { headers: corsHeaders });
  } catch (error) {
    console.error('Error fetching product by ID:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    );
  }
}
