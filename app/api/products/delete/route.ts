import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { Product } from '@/models/Product';
import { ProductArchive } from '@/models/ProductArchive';
import '@/models'; // Ensure all models are loaded

const JWT_SECRET = process.env.JWT_SECRET!;

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function DELETE(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Ensure database connection
    await connectToDatabase();

    // Get the product ID from the request
    const { searchParams } = new URL(req.url);
    const productId = searchParams.get('id');

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' }, 
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get access token from cookies
    const accessToken = req.cookies.get('accessToken')?.value;
    
    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    // Verify the JWT token
    let payload: { 
      userId: string; 
      role: string; 
      email: string 
    };
    
    try {
      const { payload: verifiedPayload } = await jwtVerify(
        accessToken, 
        new TextEncoder().encode(JWT_SECRET)
      );
      
      payload = verifiedPayload as typeof payload;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Failed to verify authentication token';
      
      console.error('Token verification error:', errorMessage);
      
      return NextResponse.json(
        { error: errorMessage }, 
        { headers: corsHeaders, status: 401 }
      );
    }

    // Check admin role
    if (payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized: Admin access required' }, 
        { headers: corsHeaders, status: 403 }
      );
    }

    // Find the product to be deleted
    const productToDelete = await Product.findById(productId);

    if (!productToDelete) {
      return NextResponse.json(
        { error: 'Product not found' }, 
        { headers: corsHeaders, status: 404 }
      );
    }

    // Create an archive record
    const archivedProduct = new ProductArchive({
      originalProductId: productToDelete._id,
      name: productToDelete.name,
      description: productToDelete.description,
      price: productToDelete.price,
      category: productToDelete.category,
      stock: productToDelete.stock,
      image: productToDelete.image,
      originalCreatedAt: productToDelete.createdAt,
      originalUpdatedAt: productToDelete.updatedAt,
      deletedBy: payload.userId  // Use the current user's ID from JWT
    });

    // Save the archived product
    await archivedProduct.save();

    // Delete the original product
    await Product.findByIdAndDelete(productId);

    return NextResponse.json(
      { 
        message: 'Product successfully archived', 
        archivedProduct 
      }, 
      { headers: corsHeaders, status: 200 }
    );

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Product deletion error:', errorMessage);
    return NextResponse.json(
      { error: errorMessage }, 
      { headers: corsHeaders, status: 500 }
    );
  }
}
