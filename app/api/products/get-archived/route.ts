import { NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/dbconnect'
import { ProductArchive } from '@/models/ProductArchive'
import { cookies } from 'next/headers'
import { verifyAccessToken } from '@/lib/auth'
import { getCorsHeaders } from '@/lib/cors'
import '@/models'; // Ensure all models are loaded

export async function OPTIONS() {
  const corsHeaders = getCorsHeaders(null);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET() {
  const corsHeaders = getCorsHeaders(null);

  try {
    // Ensure database connection
    await connectToDatabase()

    // Get access token from cookies
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('accessToken')?.value;

    // Verify token and extract user information
    if (!accessToken) {
      return NextResponse.json({ 
        error: 'Unauthorized: No access token', 
        details: 'Authentication is required to retrieve archived products' 
      }, { status: 401 });
    }

    let verifiedToken;
    try {
      verifiedToken = await verifyAccessToken(accessToken);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Failed to verify authentication token';
      
      console.error('Token verification error:', errorMessage);
      
      return NextResponse.json(
        { 
          error: 'Invalid authentication token', 
          details: errorMessage 
        },
        { status: 401, headers: corsHeaders }
      );
    }

    // Check if user is an admin
    if (verifiedToken.role !== 'admin') {
      return NextResponse.json({ 
        error: 'Forbidden', 
        details: 'Only administrators can retrieve archived products' 
      }, { status: 403 });
    }

    // Fetch archived products
    const archivedProducts = await ProductArchive.find()
      .sort({ archivedAt: -1 })
      .populate('category')

    return NextResponse.json(
      archivedProducts, 
      { 
        status: 200, 
        headers: corsHeaders 
      }
    )
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error fetching archived products:', errorMessage);
    return NextResponse.json(
      { error: errorMessage },
      { 
        status: 500, 
        headers: corsHeaders 
      }
    );
  }
}
