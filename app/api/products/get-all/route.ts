// app/api/products/get-all/rote.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getAllProducts } from '@/lib/productBackendUtilities';
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}



export async function GET(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Fetch products with populated categories
    const products = await getAllProducts();
    return NextResponse.json(products, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch Products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Products' },
      { headers: corsHeaders, status: 500 }
    );
  }
}


