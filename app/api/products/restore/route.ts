import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Product } from '@/models/Product';
import { ProductArchive } from '@/models/ProductArchive';
import { cookies } from 'next/headers';
import { verifyAccessToken } from '@/lib/auth';
import { getCorsHeaders } from '@/lib/cors';
import '@/models'; // Ensure all models are loaded

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(null);

  try {
    // Ensure database connection
    await connectToDatabase();

    // Get access token from cookies
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('accessToken')?.value;

    // Verify token and extract user information
    if (!accessToken) {
      return NextResponse.json({ 
        error: 'Unauthorized: No access token', 
        details: 'Authentication is required to restore products' 
      }, { status: 401 });
    }

    let verifiedToken;
    try {
      verifiedToken = await verifyAccessToken(accessToken);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Failed to verify authentication token';
      
      console.error('Token verification error:', errorMessage);
      
      return NextResponse.json(
        { 
          error: 'Invalid authentication token', 
          details: errorMessage 
        },
        { 
          status: 401, 
          headers: corsHeaders 
        }
      );
    }

    // Check if user is an admin
    if (verifiedToken.role !== 'admin') {
      return NextResponse.json({ 
        error: 'Forbidden', 
        details: 'Only administrators can restore archived products' 
      }, { status: 403 });
    }

    // Parse request body
    const body = await req.json();
    const { productId } = body;

    // Validate product ID
    if (!productId) {
      return NextResponse.json({ 
        error: 'Invalid Request', 
        details: 'Product ID is required' 
      }, { status: 400 });
    }

    // Find the archived product
    const archivedProduct = await ProductArchive.findById(productId);

    if (!archivedProduct) {
      return NextResponse.json({ 
        error: 'Product Not Found', 
        details: 'No archived product found with the given ID' 
      }, { status: 404 });
    }

    // Create a new product document from the archived product
    const restoredProduct = new Product({
      ...archivedProduct.toObject(),
      _id: undefined, // Let MongoDB generate a new ID
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Save the restored product
    await restoredProduct.save();

    // Remove from archive
    await ProductArchive.findByIdAndDelete(productId);

    return NextResponse.json({ 
      message: 'Product successfully restored', 
      product: restoredProduct 
    }, { status: 200 });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Product restoration error:', errorMessage);
    return NextResponse.json(
      { error: errorMessage },
      { 
        status: 500, 
        headers: corsHeaders 
      }
    );
  }
}
