import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { updateProduct } from '@/lib/productBackendUtilities';
import { UpdateProductInput } from '@/lib/frontendProductUtilities';
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function PATCH(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { id, updateData }: UpdateProductInput = await req.json();
    if (!id || !updateData) {
      return NextResponse.json(
        { error: 'id and updateData are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const updatedProduct = await updateProduct(id, updateData);
    if (!updatedProduct) {
      return NextResponse.json(
        { error: 'Product not found or update failed.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(updatedProduct, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to update Product:', error);
    return NextResponse.json(
      { error: 'Failed to update Product' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
