

// // app/api/products/create/route.ts

import { NextResponse } from "next/server"
import { connectToDatabase } from "@/lib/dbconnect"
import { getCorsHeaders } from "@/lib/cors"
import { createProduct } from "@/lib/productBackendUtilities"
import { uploadImageToGridFSFromBuffer } from "@/lib/imageUpload"
import mongoose from "mongoose"
import '@/models'; // Ensure all models are loaded

export async function POST(req: Request) {
  const origin = req.headers.get("origin")
  const corsHeaders = getCorsHeaders(origin)

  try {
    await connectToDatabase()

    const formData = await req.formData()
    const name = formData.get("name") as string
    const description = formData.get("description") as string
    const price = Number.parseFloat(formData.get("price") as string)
    const category = formData.get("category") as string
    const stock = Number.parseInt(formData.get("stock") as string)
    const image = formData.get("image") as File

    if (!name || !description || isNaN(price) || !category || isNaN(stock) || !image) {
      return NextResponse.json({ error: "All fields are required." }, { headers: corsHeaders, status: 400 })
    }

    const imageBuffer = await image.arrayBuffer()
    const imageId = await uploadImageToGridFSFromBuffer({
      buffer: Buffer.from(imageBuffer),
      filename: image.name,
      mimeType: image.type,
    })

    const categoryObjectId = new mongoose.Types.ObjectId(category)

    const newProduct = await createProduct(name, description, price, categoryObjectId, stock, imageId)

    console.log("[DEBUG] New product created:", newProduct)

    return NextResponse.json(newProduct, { headers: corsHeaders, status: 201 })
  } catch (error) {
    console.error("Error creating Product:", error)
    return NextResponse.json({ error: "Failed to create Product" }, { headers: corsHeaders, status: 500 })
  }
}


