
// app/api/groups/[groupId]/check-membership/route.ts
import { type NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { StokvelGroup } from "@/models/StokvelGroup";
import { User } from "@/models/User";
import { getCorsHeaders } from "@/lib/cors";
// import { Redis } from 'ioredis';
import { memoryCache } from "@/lib/memoryCache";
import { ObjectId } from "mongodb";
import '@/models'; // Ensure all models are loaded
// Define types for lean documents
interface LeanStokvelGroup {
  _id: ObjectId;
  groupName: string;
  members: ObjectId[];
  admin: ObjectId;
  geolocation: string;
  totalSales: number;
  avgOrderValue: number;
  activeOrders: number;
  createdAt: Date;
  updatedAt: Date;
}

interface LeanUser {
  _id: ObjectId;
  stokvelGroups: ObjectId[];
}

// const redis = new Redis(); // Use default local in-memory Redis configuration

export async function POST(request: NextRequest) {
  await connectToDatabase();
  const origin = request.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Extract groupId from URL
    const url = new URL(request.url);
    const segments = url.pathname.split("/");
    const groupId = segments[segments.length - 2]; // [api, groups, :groupId, check-membership]

    // Get userId from request body
    const { userId } = await request.json();

    // Check cache first
    const cacheKey = `membership:${userId}:${groupId}`;
    const cachedResult = memoryCache.get(cacheKey);
    
    if (cachedResult !== null) {
      return NextResponse.json({ isMember: cachedResult }, { headers: corsHeaders });
    }

    // Validate ObjectIds
    if (!ObjectId.isValid(groupId) || !ObjectId.isValid(userId)) {
      return NextResponse.json(
        { error: "Invalid ID format" },
        { status: 400, headers: corsHeaders }
      );
    }

    const [group, user] = await Promise.all([
      StokvelGroup.findById(groupId)
        .select("groupName members admin geolocation totalSales avgOrderValue activeOrders createdAt updatedAt")
        .lean()
        .exec() as Promise<LeanStokvelGroup | null>,
      User.findById(userId)
        .select("stokvelGroups")
        .lean()
        .exec() as Promise<LeanUser | null>,
    ]);

    if (!group || !user) {
      memoryCache.set(cacheKey, false, 300); // Cache non-membership for 5 minutes
      return NextResponse.json(
        { isMember: false },
        { status: 404, headers: corsHeaders }
      );
    }

    // Check if user is a member
    const isMember = group.members.some((memberId) => 
      memberId.toString() === userId.toString()
    );

    // Cache the result for 5 minutes
    memoryCache.set(cacheKey, isMember, 300);

    return NextResponse.json({ isMember }, { headers: corsHeaders });
  } catch (error) {
    console.error('Error checking membership:', error);
    return NextResponse.json(
      { error: 'Failed to check membership status' },
      { status: 500, headers: corsHeaders }
    );
  }
}
