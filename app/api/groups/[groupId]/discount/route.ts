// app/api/groups/[groupId]/discount/route.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { StokvelGroup } from '@/models/StokvelGroup';
import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';
import '@/models'; // Ensure all models are loaded

// Define a type for the group document
type GroupDocument = {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  members: mongoose.Types.ObjectId[];
  admin: mongoose.Types.ObjectId;
  totalSales: number;
  avgOrderValue: number;
  activeOrders: number;
  createdAt: Date;
  updatedAt: Date;
};

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(request: NextRequest) {
  await connectToDatabase();
  const origin = request.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Extract groupId from the URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const groupId = pathParts[pathParts.length - 2]; // The groupId is the second-to-last part of the path
    console.log(`GET /api/groups/[groupId]/discount - Calculating discount for group: ${groupId}`);

    if (!groupId || !ObjectId.isValid(groupId)) {
      console.log(`GET /api/groups/[groupId]/discount - Invalid groupId: ${groupId}`);
      return NextResponse.json(
        { error: 'Invalid group ID' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // First convert to unknown, then to our specific type to avoid TypeScript errors
    const groupDoc = await StokvelGroup.findById(groupId).lean();

    if (!groupDoc) {
      console.log(`GET /api/groups/[groupId]/discount - Group not found with ID: ${groupId}`);
      return NextResponse.json(
        { error: 'Group not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Convert to unknown first, then to our specific type
    const group = groupDoc as unknown as GroupDocument;

    // Calculate discount based on group's total sales or other factors
    // This is a simple example - you can implement your own discount logic
    const totalSales = group.totalSales || 0;
    let discountPercentage = 0;

    if (totalSales > 50000) {
      discountPercentage = 20;
    } else if (totalSales > 20000) {
      discountPercentage = 15;
    } else if (totalSales > 10000) {
      discountPercentage = 10;
    } else if (totalSales > 5000) {
      discountPercentage = 5;
    }

    const discountInfo = {
      discountPercentage,
      discountAmount: 0, // This would be calculated based on the current order value
      finalOrderValue: 0, // This would be calculated based on the current order value
      totalSales,
    };

    console.log(`GET /api/groups/[groupId]/discount - Calculated discount: ${discountPercentage}%`);

    return NextResponse.json(discountInfo, { headers: corsHeaders });
  } catch (error) {
    console.error('Error calculating discount:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
