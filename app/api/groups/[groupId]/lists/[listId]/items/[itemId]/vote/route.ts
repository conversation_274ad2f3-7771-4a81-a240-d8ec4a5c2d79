// List Item Voting API
// <PERSON>les voting on collaborative shopping list items

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { realTimeGroupService } from '@/lib/services/realTimeGroupService';

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string; listId: string; itemId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { vote, reason } = body;

    if (!vote || !['up', 'down'].includes(vote)) {
      return NextResponse.json(
        { error: 'Valid vote (up or down) is required' },
        { status: 400 }
      );
    }

    // Vote on list item
    const success = await realTimeGroupService.voteOnListItem(
      params.listId,
      params.itemId,
      payload.userId,
      vote,
      reason
    );

    if (!success) {
      return NextResponse.json(
        { error: 'List or item not found' },
        { status: 404 }
      );
    }

    // Create activity for the vote
    await realTimeGroupService.createActivity(
      params.groupId,
      payload.userId,
      'product_voted',
      `Voted ${vote} on product`,
      `Voted ${vote} on a product in collaborative list`,
      {
        listId: params.listId,
        itemId: params.itemId,
        vote
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Vote recorded successfully'
    });

  } catch (error) {
    console.error('List item vote error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to record vote',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
