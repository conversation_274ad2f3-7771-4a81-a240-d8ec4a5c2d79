// Group Chat Messages API
// Handles real-time group messaging functionality

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { realTimeGroupService } from '@/lib/services/realTimeGroupService';

export async function GET(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get group messages
    const messages = await realTimeGroupService.getGroupMessages(params.groupId, limit, offset);

    return NextResponse.json({
      success: true,
      data: messages,
      metadata: {
        groupId: params.groupId,
        limit,
        offset,
        count: messages.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Group messages GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch group messages',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { groupId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { content, type = 'text', metadata, replyTo } = body;

    if (!content || !content.trim()) {
      return NextResponse.json(
        { error: 'Message content is required' },
        { status: 400 }
      );
    }

    // Send message
    const message = await realTimeGroupService.sendMessage(
      params.groupId,
      payload.userId,
      content,
      type,
      metadata
    );

    // If this is a reply, update the message
    if (replyTo) {
      message.replyTo = replyTo;
    }

    return NextResponse.json({
      success: true,
      data: message,
      message: 'Message sent successfully'
    });

  } catch (error) {
    console.error('Group messages POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send message',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
