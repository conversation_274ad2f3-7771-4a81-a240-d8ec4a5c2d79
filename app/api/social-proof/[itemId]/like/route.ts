// Social Proof Like API
// <PERSON>les liking social proof items

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';

export async function POST(
  request: NextRequest,
  { params }: { params: { itemId: string } }
) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Mock implementation - in production, this would:
    // 1. Check if the social proof item exists
    // 2. Check if the user has already liked this item
    // 3. Add or toggle the like
    // 4. Update engagement metrics
    // 5. Possibly notify the item creator

    return NextResponse.json({
      success: true,
      message: 'Social proof item liked successfully',
      data: {
        itemId: params.itemId,
        likedBy: payload.userId,
        likedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Social proof like error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to like social proof item',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
