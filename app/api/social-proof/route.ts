// Social Proof API
// Handles social proof items and community highlights

import { NextRequest, NextResponse } from 'next/server';
import { verifyAccessToken } from '@/lib/auth';
import { gamificationService } from '@/lib/services/gamificationService';

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type');

    // Get social proof items
    const allItems = await gamificationService.getSocialProofItems(limit);
    
    // Filter by type if specified
    const filteredItems = type 
      ? allItems.filter(item => item.type === type)
      : allItems;

    // Calculate summary statistics
    const summary = {
      totalItems: filteredItems.length,
      byType: {
        group_success: allItems.filter(i => i.type === 'group_success').length,
        member_achievement: allItems.filter(i => i.type === 'member_achievement').length,
        savings_milestone: allItems.filter(i => i.type === 'savings_milestone').length,
        product_review: allItems.filter(i => i.type === 'product_review').length,
        challenge_completion: allItems.filter(i => i.type === 'challenge_completion').length
      },
      totalEngagement: {
        views: filteredItems.reduce((sum, i) => sum + i.engagement.views, 0),
        likes: filteredItems.reduce((sum, i) => sum + i.engagement.likes, 0),
        shares: filteredItems.reduce((sum, i) => sum + i.engagement.shares, 0),
        comments: filteredItems.reduce((sum, i) => sum + i.engagement.comments, 0)
      }
    };

    return NextResponse.json({
      success: true,
      data: filteredItems,
      summary,
      metadata: {
        limit,
        filters: { type },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Social proof GET error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch social proof items',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const payload = await verifyAccessToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      type, 
      title, 
      description, 
      groupId, 
      metrics = [], 
      tags = [], 
      isPublic = true 
    } = body;

    // Validate required fields
    if (!type || !title || !description) {
      return NextResponse.json(
        { error: 'Missing required fields: type, title, description' },
        { status: 400 }
      );
    }

    // Create social proof item
    const socialProofItem = await gamificationService.createSocialProof({
      type,
      title,
      description,
      groupId,
      userId: payload.userId,
      metrics,
      tags,
      isPublic
    });

    return NextResponse.json({
      success: true,
      data: socialProofItem,
      message: 'Social proof item created successfully'
    });

  } catch (error) {
    console.error('Social proof POST error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create social proof item',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
