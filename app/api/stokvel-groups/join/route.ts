// app/api/stokvel-groups/join/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { User } from "@/models/User";
import { StokvelGroup } from "@/models/StokvelGroup";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    const body = await req.json();
    const { userId, groupId } = body;
    if (!userId || !groupId) {
      return NextResponse.json(
        { error: "userId and groupId are required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(groupId)) {
      return NextResponse.json(
        { error: "Invalid userId or groupId format" },
        { headers: corsHeaders, status: 400 }
      );
    }

    // 1) find user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { headers: corsHeaders, status: 404 }
      );
    }

    // 2) find group
    const group = await StokvelGroup.findById(groupId);
    if (!group) {
      return NextResponse.json(
        { error: "Group not found" },
        { headers: corsHeaders, status: 404 }
      );
    }

    // 3) push user into group.members if not included
    if (!group.members.includes(user._id)) {
      group.members.push(user._id);
      await group.save();
    }

    // 4) push group into user.stokvelGroups if not included
    if (!user.stokvelGroups.includes(group._id)) {
      user.stokvelGroups.push(group._id);
      await user.save();
    }

    return NextResponse.json({
      success: true,
      message: "Successfully joined group",
      group
    }, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error("Error joining group:", error);
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while processing your request.",
        error: "Internal Server Error"
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
