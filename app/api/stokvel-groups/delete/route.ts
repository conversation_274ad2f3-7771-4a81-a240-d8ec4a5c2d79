import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { deleteStokvelGroup } from '@/lib/backendGroupUtilities';
import '@/models'; // Ensure all models are loaded
// import rateLimiter from '@/lib/rateLimit';
// import { getClientIp } from '@/lib/getClientIp';

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function DELETE(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { id } = await req.json();
    if (!id) {
      return NextResponse.json(
        { error: 'ID is required for deletion.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const deletedGroup = await deleteStokvelGroup(id);
    if (!deletedGroup) {
      return NextResponse.json(
        { error: 'Group not found or already deleted.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'StokvelGroup deleted successfully.' },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Failed to delete StokvelGroup:', error);
    return NextResponse.json(
      { error: 'Failed to delete StokvelGroup' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
