// app/api/stokvel-groups/[groupId]/route.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { StokvelGroup } from '@/models/StokvelGroup';
import mongoose from 'mongoose';
import { ObjectId } from 'mongodb';
import '@/models'; // Ensure all models are loaded

// Define a type for the group document
type GroupDocument = {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  members: mongoose.Types.ObjectId[];
  admin: mongoose.Types.ObjectId;
  totalSales: number;
  avgOrderValue: number;
  activeOrders: number;
  createdAt: Date;
  updatedAt: Date;
};

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(request: NextRequest) {
  await connectToDatabase();
  const origin = request.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Extract groupId from the URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const groupId = pathParts[pathParts.length - 1]; // The groupId is the last part of the path
    console.log(`GET /api/stokvel-groups/[groupId] - Fetching group with ID: ${groupId}`);

    if (!groupId || !ObjectId.isValid(groupId)) {
      console.log(`GET /api/stokvel-groups/[groupId] - Invalid groupId: ${groupId}`);
      return NextResponse.json(
        { error: 'Invalid group ID' },
        { headers: corsHeaders, status: 400 }
      );
    }

    // First convert to unknown, then to our specific type to avoid TypeScript errors
    const groupDoc = await StokvelGroup.findById(groupId).lean();

    if (!groupDoc) {
      console.log(`GET /api/stokvel-groups/[groupId] - Group not found with ID: ${groupId}`);
      return NextResponse.json(
        { error: 'Group not found' },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Convert to unknown first, then to our specific type
    const group = groupDoc as unknown as GroupDocument;

    console.log(`GET /api/stokvel-groups/[groupId] - Successfully fetched group: ${group.name}`);

    // Convert ObjectId to string for all fields that are ObjectId
    const formattedGroup = {
      ...group,
      _id: group._id.toString(),
      admin: group.admin.toString(),
      members: group.members.map((id: ObjectId) => id.toString()),
      createdAt: group.createdAt.toISOString(),
      updatedAt: group.updatedAt.toISOString(),
    };

    return NextResponse.json(formattedGroup, { headers: corsHeaders });
  } catch (error) {
    console.error('Error fetching group:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
