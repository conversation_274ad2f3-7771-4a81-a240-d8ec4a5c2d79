import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { updateStokvelGroup } from '@/lib/backendGroupUtilities';
import '@/models'; // Ensure all models are loaded
// import rateLimiter from '@/lib/rateLimit';
// import { getClientIp } from '@/lib/getClientIp';

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function PATCH(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { id, updateData } = await req.json();
    if (!id || !updateData) {
      return NextResponse.json(
        { error: 'id and updateData are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const updatedGroup = await updateStokvelGroup(id, updateData);
    if (!updatedGroup) {
      return NextResponse.json(
        { error: 'Group not found or update failed.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(updatedGroup, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to update StokvelGroup:', error);
    return NextResponse.json(
      { error: 'Failed to update StokvelGroup' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
