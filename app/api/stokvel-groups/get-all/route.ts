// app/api/stokvel-groups/get-all/rote.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getAllStokvelGroups } from '@/lib/backendGroupUtilities';
import '@/models'; // Ensure all models are loaded
// import rateLimiter from '@/lib/rateLimit';
// import { getClientIp } from '@/lib/getClientIp';

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  // Example rate limiting (optional)
  // const clientIp = getClientIp(nextReq);
  // if (!rateLimiter.allowRequest(clientIp)) {
  //   return NextResponse.json(
  //     { error: 'Too many requests. Please try again later.' },
  //     { headers: corsHeaders, status: 429 }
  //   );
  // }

  try {
    const groups = await getAllStokvelGroups();
    return NextResponse.json(groups, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch StokvelGroups:', error);
    return NextResponse.json(
      { error: 'Failed to fetch StokvelGroups' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
