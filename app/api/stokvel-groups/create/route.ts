// app/api/stockvel-groups/create/route.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { createStokvelGroup } from '@/lib/backendGroupUtilities';
import mongoose from 'mongoose';
import '@/models'; // Ensure all models are loaded
// import rateLimiter from '@/lib/rateLimit';
// import { getClientIp } from '@/lib/getClientIp';

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: Request) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  // Example rate limiting (optional)
  // const clientIp = getClientIp(nextReq);
  // if (!rateLimiter.allowRequest(clientIp)) {
  //   return NextResponse.json(
  //     { error: 'Too many requests. Please try again later.' },
  //     { headers: corsHeaders, status: 429 }
  //   );
  // }

  try {
    const { name, description, admin, geolocation, members } = await req.json();

    // Basic validation
    if (!name || !description || !admin || !geolocation) {
      return NextResponse.json(
        { error: 'name, description, admin, and geolocation are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const adminObjId = new mongoose.Types.ObjectId(admin);
    const memberObjIds = (Array.isArray(members) ? members : []).map(
      (m: string) => new mongoose.Types.ObjectId(m)
    );

    const newGroup = await createStokvelGroup(
      name,
      description,
      adminObjId,
      geolocation,
      memberObjIds
    );

    return NextResponse.json(newGroup, { headers: corsHeaders, status: 201 });
  } catch (error) {
    console.error('Error creating StokvelGroup:', error);
    return NextResponse.json(
      { error: 'Failed to create StokvelGroup' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
