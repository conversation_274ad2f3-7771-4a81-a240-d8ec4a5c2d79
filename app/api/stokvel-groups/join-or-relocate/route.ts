// app/api/stokvel-groups/join-or-relocate/route.ts

import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { User } from "@/models/User";
import { StokvelGroup } from "@/models/StokvelGroup";
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get("origin");
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    const body = await req.json();
    const { userId, groupId, isRelocation = false } = body;

    if (!userId || !groupId) {
      return NextResponse.json(
        { error: "userId and groupId are required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(groupId)) {
      return NextResponse.json(
        { error: "Invalid userId or groupId format" },
        { headers: corsHeaders, status: 400 }
      );
    }

    // 1) find user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { headers: corsHeaders, status: 404 }
      );
    }

    // 2) find group
    const group = await StokvelGroup.findById(groupId);
    if (!group) {
      return NextResponse.json(
        {
          success: false,
          message: "Group not found",
          error: "Group not found"
        },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Check if user is trying to join the same group they're already in
    if (isRelocation && user.stokvelGroups.includes(group._id)) {
      return NextResponse.json(
        {
          success: false,
          message: "You are already a member of this group."
        },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Handle relocation case - remove user from all current groups first
    if (isRelocation && user.stokvelGroups.length > 0) {
      // Get all groups the user is currently a member of
      const currentGroups = await StokvelGroup.find({
        _id: { $in: user.stokvelGroups }
      });

      // Remove user from each group's members array
      for (const currentGroup of currentGroups) {
        currentGroup.members = currentGroup.members.filter(
          (memberId: mongoose.Types.ObjectId) => memberId.toString() !== userId
        );
        await currentGroup.save();
      }

      // Clear user's stokvelGroups array
      user.stokvelGroups = [];
      await user.save();
    }

    // 3) push user into group.members if not included
    if (!group.members.includes(user._id)) {
      group.members.push(user._id);
      await group.save();
    }

    // 4) push group into user.stokvelGroups if not included
    if (!user.stokvelGroups.includes(group._id)) {
      user.stokvelGroups.push(group._id);
      await user.save();
    }

    return NextResponse.json(
      {
        success: true,
        message: isRelocation ? "Successfully relocated to new group" : "Successfully joined group",
        group
      },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error("Error joining or relocating group:", error);
    return NextResponse.json(
      {
        success: false,
        message: "An error occurred while processing your request.",
        error: "Internal Server Error"
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
