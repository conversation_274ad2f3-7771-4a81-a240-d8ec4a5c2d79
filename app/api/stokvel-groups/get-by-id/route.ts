import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getStokvelGroupById } from '@/lib/backendGroupUtilities';
import '@/models'; // Ensure all models are loaded
// import rateLimiter from '@/lib/rateLimit';
// import { getClientIp } from '@/lib/getClientIp';

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: Request) {
  // This example expects an ID in the body, but you might prefer a dynamic route with [id]/route.ts
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { id } = await req.json();
    if (!id) {
      return NextResponse.json(
        { error: 'ID is required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const group = await getStokvelGroupById(id);
    if (!group) {
      return NextResponse.json(
        { error: 'Group not found.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(group, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch group by ID:', error);
    return NextResponse.json(
      { error: 'Failed to fetch group' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
