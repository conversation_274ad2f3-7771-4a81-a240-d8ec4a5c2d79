import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { updateRating } from '@/lib/productRatingBackendUtilities';
import { verifyAccessToken } from '@/lib/auth';
import { cookies } from 'next/headers';
import '@/models';

export async function PATCH(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication
    const cookieStore = cookies();
    const accessToken = (await cookieStore).get('accessToken')?.value;

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(accessToken);
    if (!payload?.userId) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const { id, rating, comment } = await req.json();

    if (!id || (!rating && !comment)) {
      return NextResponse.json(
        { error: 'Rating ID and update data are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (rating && (rating < 1 || rating > 5)) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const updatedRating = await updateRating(id, payload.userId, { rating, comment });

    if (!updatedRating) {
      return NextResponse.json(
        { error: 'Rating not found or not authorized to update' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(updatedRating, { headers: corsHeaders });
  } catch (error: unknown) {
    console.error('Error updating rating:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update rating';
    return NextResponse.json(
      { error: errorMessage },
      { headers: corsHeaders, status: 500 }
    );
  }
}