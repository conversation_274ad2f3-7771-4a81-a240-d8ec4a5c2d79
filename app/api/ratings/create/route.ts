import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { createRating } from '@/lib/productRatingBackendUtilities';
import { verifyAccessToken } from '@/lib/auth';
import { cookies } from 'next/headers';
import '@/models';

export async function POST(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Get access token from cookies
    const cookieStore = cookies();
    const accessToken = (await cookieStore).get('accessToken')?.value;

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    // Verify token and get user ID
    const payload = await verifyAccessToken(accessToken);
    if (!payload?.userId) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const { productId, rating, comment } = await req.json();

    if (!productId || !rating || rating < 1 || rating > 5) {
      return NextResponse.json(
        { error: 'Product ID and valid rating (1-5) are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const newRating = await createRating(payload.userId, productId, rating, comment);

    return NextResponse.json(newRating, { headers: corsHeaders, status: 201 });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to create rating';
    console.error('Error creating rating:', error);
    return NextResponse.json(
      { error: errorMessage },
      { headers: corsHeaders, status: 500 }
    );
  }
}