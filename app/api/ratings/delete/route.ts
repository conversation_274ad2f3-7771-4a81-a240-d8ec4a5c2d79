import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { deleteRating } from '@/lib/productRatingBackendUtilities';
import { verifyAccessToken } from '@/lib/auth';
import { cookies } from 'next/headers';
import '@/models';

export async function DELETE(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    // Verify authentication
    const cookieStore = cookies();
    const accessToken = (await cookieStore).get('accessToken')?.value;

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const payload = await verifyAccessToken(accessToken);
    if (!payload?.userId) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { headers: corsHeaders, status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Rating ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const deletedRating = await deleteRating(id, payload.userId);

    if (!deletedRating) {
      return NextResponse.json(
        { error: 'Rating not found or not authorized to delete' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Rating deleted successfully' },
      { headers: corsHeaders }
    );
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to delete rating';
    console.error('Error deleting rating:', error);
    return NextResponse.json(
      { error: errorMessage },
      { headers: corsHeaders, status: 500 }
    );
  }
}