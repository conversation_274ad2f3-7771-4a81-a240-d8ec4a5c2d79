// app/api/ratings/[ratingId]/route.ts

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { 
  updateProductRating,
  deleteProductRating,
  markRatingHelpful,
  reportRating
} from '@/lib/ratingUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function PATCH(
  req: Request,
  { params }: { params: { ratingId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { ratingId } = params;
    const body = await req.json();
    const { userId, action, rating, review, title } = body;

    if (!ratingId || !userId) {
      return NextResponse.json(
        { error: 'Rating ID and User ID are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (action === 'helpful') {
      // Mark rating as helpful
      const updatedRating = await markRatingHelpful(ratingId);
      if (!updatedRating) {
        return NextResponse.json(
          { error: 'Failed to mark rating as helpful.' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json(
        { 
          message: 'Rating marked as helpful',
          rating: updatedRating
        },
        { headers: corsHeaders, status: 200 }
      );
    } else if (action === 'report') {
      // Report rating
      const success = await reportRating(ratingId);
      if (!success) {
        return NextResponse.json(
          { error: 'Failed to report rating.' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json(
        { message: 'Rating reported successfully' },
        { headers: corsHeaders, status: 200 }
      );
    } else {
      // Update rating
      const updatedRating = await updateProductRating(
        { ratingId, rating, review, title },
        userId
      );

      if (!updatedRating) {
        return NextResponse.json(
          { error: 'Failed to update rating or rating not found.' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json(
        { 
          message: 'Rating updated successfully',
          rating: updatedRating
        },
        { headers: corsHeaders, status: 200 }
      );
    }
  } catch (error) {
    console.error('Failed to update rating:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { ratingId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    const { ratingId } = params;
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');

    if (!ratingId || !userId) {
      return NextResponse.json(
        { error: 'Rating ID and User ID are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const success = await deleteProductRating(ratingId, userId);
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete rating or rating not found.' },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Rating deleted successfully' },
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Failed to delete rating:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
