import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getProductRatings } from '@/lib/productRatingBackendUtilities';
import '@/models';

export async function GET(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();

    const { searchParams } = new URL(req.url);
    const productId = searchParams.get('productId');

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const ratings = await getProductRatings(productId);

    return NextResponse.json(ratings, { headers: corsHeaders });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch ratings';
    console.error('Error fetching ratings:', error);
    return NextResponse.json(
      { error: errorMessage },
      { headers: corsHeaders, status: 500 }
    );
  }
}