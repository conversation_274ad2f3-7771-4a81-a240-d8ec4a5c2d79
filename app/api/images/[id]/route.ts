
// app/api/images/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/dbconnect";

// In‑memory cache: key = fileId, value = { buffer, expiration }
const imageCache = new Map<string, { buffer: Buffer; expiration: number }>();

// Helper function to convert a NodeJS stream into a Buffer.
async function streamToBuffer(stream: NodeJS.ReadableStream): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    stream.on("data", (chunk) =>
      chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk))
    );
    stream.on("end", () => resolve(Buffer.concat(chunks)));
    stream.on("error", (err) => reject(err));
  });
}

export async function GET(request: NextRequest) {
  // 1) Extract the "id" segment from the URL.
  const url = new URL(request.url);
  const segments = url.pathname.split("/");
  const fileId = segments[segments.length - 1];

  // 2) Check if the image is in the in-memory cache.
  const cached = imageCache.get(fileId);
  if (cached) {
    if (Date.now() < cached.expiration) {
      // If found and not expired, return the cached image.
      return new NextResponse(cached.buffer, {
        headers: {
          "Content-Type": "image/jpeg", // Adjust if needed (or store the content type with the image)
          "Cache-Control": "max-age=31536000, immutable",
        },
      });
    } else {
      // Remove expired entry.
      imageCache.delete(fileId);
    }
  }

  // 3) Connect to the database.
  await connectToDatabase();
  const db = mongoose.connection.db;
  if (!db) {
    return NextResponse.json(
      { error: "Database connection not established" },
      { status: 500 }
    );
  }

  // 4) Validate and convert fileId to a Mongo ObjectId.
  let objectId: mongoose.Types.ObjectId;
  try {
    objectId = new mongoose.Types.ObjectId(fileId);
  } catch (err) {
    console.error("[GET /api/images/[id]] Invalid file ID:", err);
    return NextResponse.json({ error: "Invalid file ID" }, { status: 400 });
  }

  // 5) Retrieve the file from GridFS.
  const bucket = new mongoose.mongo.GridFSBucket(db, { bucketName: "uploads" });
  const files = await bucket.find({ _id: objectId }).toArray();
  if (!files || files.length === 0) {
    return NextResponse.json({ error: "File not found" }, { status: 404 });
  }
  const fileDoc = files[0];
  const contentType = fileDoc.contentType || "application/octet-stream";

  // 6) Open a download stream from GridFS.
  const downloadStream = bucket.openDownloadStream(objectId);

  // 7) Convert the stream into a Buffer.
  let imageBuffer: Buffer;
  try {
    imageBuffer = await streamToBuffer(downloadStream);
  } catch (err) {
    console.error("Error converting stream to buffer:", err);
    return NextResponse.json({ error: "Error processing image" }, { status: 500 });
  }

  // 8) Store the image in the in-memory cache with an expiration (1 year).
  const oneYearMs = 31536000000; // 1 year in milliseconds
  imageCache.set(fileId, { buffer: imageBuffer, expiration: Date.now() + oneYearMs });

  // 9) Return the image.
  return new NextResponse(imageBuffer, {
    headers: {
      "Content-Type": contentType,
      "Cache-Control": "max-age=31536000, immutable",
    },
  });
}

