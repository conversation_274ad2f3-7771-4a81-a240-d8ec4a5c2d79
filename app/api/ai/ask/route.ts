// app/api/ai/ask/route.ts
import { NextResponse } from 'next/server';
import { createChatMemory } from '@/lib/langchain/memory';
import { createChat<PERSON>hain } from '@/lib/langchain/chain';

export async function POST(request: Request) {
  try {
    const { query, sessionId } = await request.json();
    
    if (!query || !sessionId) {
      return NextResponse.json(
        { error: 'Query and sessionId are required' },
        { status: 400 }
      );
    }

    // Create or retrieve chat memory
    const memory = await createChatMemory(sessionId);
    
    // Create chat chain
    const chain = await createChatChain(memory);

    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          await chain.call({
            question: query,
          }, {
            callbacks: [{
              handleLLMNewToken(token: string) {
                controller.enqueue(encoder.encode(token));
              },
            }],
          });
          
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    });

    return new NextResponse(stream, {
      headers: {
        "Content-Type": "text/plain",
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    console.error("Error generating AI response:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
