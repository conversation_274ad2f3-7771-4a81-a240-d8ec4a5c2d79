import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getArchivedProductCategories } from '@/lib/productCategoriesBackendUtilities';
import '@/models'; // Ensure all models are loaded

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Ensure database connection
    await connectToDatabase();

    // Fetch archived categories
    const archivedCategories = await getArchivedProductCategories();

    // Return successful response
    return NextResponse.json(
      { 
        message: 'Archived categories fetched successfully',
        archivedCategories 
      }, 
      { 
        status: 200, 
        headers: corsHeaders 
      }
    );
  } catch (error) {
    console.error('Error fetching archived categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch archived categories' },
      { 
        status: 500, 
        headers: corsHeaders 
      }
    );
  }
}

// Optional: Add OPTIONS handler for CORS preflight
export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}
