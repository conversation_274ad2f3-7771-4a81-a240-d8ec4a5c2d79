import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { archiveProductCategory } from '@/lib/productCategoriesBackendUtilities';
import '@/models'; // Ensure all models are loaded

export async function DELETE(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Ensure database connection
    await connectToDatabase();

    // Parse request query parameters
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    // Validate input
    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { 
          status: 400, 
          headers: corsHeaders 
        }
      );
    }

    // Archive the category
    const archivedCategory = await archiveProductCategory(id);

    // Check if archiving was successful
    if (!archivedCategory) {
      return NextResponse.json(
        { error: 'Category not found or archiving failed' },
        { 
          status: 404, 
          headers: corsHeaders 
        }
      );
    }

    // Return successful response
    return NextResponse.json(
      { 
        message: 'Category archived successfully', 
        archivedCategory 
      }, 
      { 
        status: 200, 
        headers: corsHeaders 
      }
    );
  } catch (error) {
    console.error('Error archiving category:', error);
    return NextResponse.json(
      { error: 'Failed to archive category' },
      { 
        status: 500, 
        headers: corsHeaders 
      }
    );
  }
}

// Optional: Add OPTIONS handler for CORS preflight
export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}
