// app/api/product-categories/get-all/route.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getAllProductCategories } from '@/lib/productCategoriesBackendUtilities';
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Ensure database connection
    await connectToDatabase();

    const categories = await getAllProductCategories();
    return NextResponse.json(categories, { headers: corsHeaders, status: 200 });
  } catch (error) {
    console.error('Failed to fetch ProductCategories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ProductCategories' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
