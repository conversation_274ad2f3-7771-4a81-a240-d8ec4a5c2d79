// app/api/product-categories/create/route.ts
import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { createProductCategory } from '@/lib/productCategoriesBackendUtilities';
import '@/models'; // Ensure all models are loaded

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Ensure database connection
    await connectToDatabase();

    const { name, description } = await req.json();

    // Basic validation
    if (!name || !description) {
      return NextResponse.json(
        { error: 'name and description are required.' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const newCategory = await createProductCategory(name, description);

    return NextResponse.json(newCategory, { headers: corsHeaders, status: 201 });
  } catch (error) {
    console.error('Error creating ProductCategory:', error);
    return NextResponse.json(
      { error: 'Failed to create ProductCategory' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
