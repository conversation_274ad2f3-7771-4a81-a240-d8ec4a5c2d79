import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { restoreProductCategory } from '@/lib/productCategoriesBackendUtilities';
import '@/models'; // Ensure all models are loaded

export async function POST(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Ensure database connection
    await connectToDatabase();

    // Parse request body
    const body = await req.json();
    const { archivedCategoryId } = body;

    // Validate input
    if (!archivedCategoryId) {
      return NextResponse.json(
        { error: 'Archived Category ID is required' },
        { 
          status: 400, 
          headers: corsHeaders 
        }
      );
    }

    // Restore the category
    const restoredCategory = await restoreProductCategory(archivedCategoryId);

    // Check if restoration was successful
    if (!restoredCategory) {
      return NextResponse.json(
        { error: 'Category not found or restoration failed' },
        { 
          status: 404, 
          headers: corsHeaders 
        }
      );
    }

    // Return successful response
    return NextResponse.json(
      { 
        message: 'Category restored successfully', 
        restoredCategory 
      }, 
      { 
        status: 200, 
        headers: corsHeaders 
      }
    );
  } catch (error) {
    console.error('Error restoring category:', error);
    return NextResponse.json(
      { error: 'Failed to restore category' },
      { 
        status: 500, 
        headers: corsHeaders 
      }
    );
  }
}

// Optional: Add OPTIONS handler for CORS preflight
export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}
