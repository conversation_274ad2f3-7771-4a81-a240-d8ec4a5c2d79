import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { updateProductCategory } from '@/lib/productCategoriesBackendUtilities';
import mongoose from 'mongoose';

import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function PATCH(req: NextRequest) {
  const nextReq = req as unknown as NextRequest;
  const origin = nextReq.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    // Ensure database connection
    await connectToDatabase();

    // Try to parse the request body, handling different input formats
    let requestData;
    try {
      requestData = await req.json();
    } catch (parseError) {
      // If JSON parsing fails, try to get the body as text
      const bodyText = await req.text();
      console.log('Raw request body:', bodyText);
      
      // If it looks like a direct ID string, try to use it
      if (typeof bodyText === 'string' && bodyText.trim()) {
        requestData = { id: bodyText.trim() };
      } else {
        console.error('Failed to parse request body:', parseError);
        return NextResponse.json(
          { error: 'Invalid request body' },
          { 
            status: 400, 
            headers: corsHeaders 
          }
        );
      }
    }

    console.log('Parsed request data:', JSON.stringify(requestData, null, 2));
    
    // Handle both direct update and object with id/updateData
    let id: string, updateData: {
      name?: string;
      description?: string;
      isArchived?: boolean;
      [key: string]: unknown;
    };
    
    if (typeof requestData === 'string') {
      id = requestData;
      updateData = {};
    } else if (requestData && typeof requestData === 'object') {
      if ('id' in requestData) {
        id = requestData.id;
        updateData = requestData.updateData || {};
      } else {
        const { _id, ...rest } = requestData;
        id = _id;
        updateData = rest;
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { 
          status: 400, 
          headers: corsHeaders 
        }
      );
    }

    // Validate ID
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { error: 'Valid category ID is required' },
        { 
          status: 400, 
          headers: corsHeaders 
        }
      );
    }

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid category ID format' },
        { 
          status: 400, 
          headers: corsHeaders 
        }
      );
    }

    // Update the category
    const updatedCategory = await updateProductCategory(id, {
      ...updateData,
      updatedAt: new Date()
    });

    // Check if update was successful
    if (!updatedCategory) {
      return NextResponse.json(
        { error: 'Category not found or update failed' },
        { 
          status: 404, 
          headers: corsHeaders 
        }
      );
    }

    // Return successful response
    return NextResponse.json(
      { 
        message: 'Category updated successfully', 
        updatedCategory 
      }, 
      { 
        status: 200, 
        headers: corsHeaders 
      }
    );
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { error: 'Failed to update category' },
      { 
        status: 500, 
        headers: corsHeaders 
      }
    );
  }
}
