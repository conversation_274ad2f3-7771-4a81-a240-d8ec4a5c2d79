// app/api/locations/stats/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { getLocationStats } from '@/lib/locationUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    
    const stats = await getLocationStats();
    
    return NextResponse.json({ 
      stats,
      timestamp: new Date().toISOString()
    }, { 
      headers: corsHeaders, 
      status: 200 
    });
  } catch (error) {
    console.error('Error fetching location stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch location statistics' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
