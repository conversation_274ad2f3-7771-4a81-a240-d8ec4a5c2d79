// app/api/locations/provinces/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { 
  getAllProvinces, 
  createProvince, 
  getProvinceById,
  updateProvince,
  deleteProvince 
} from '@/lib/locationUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(req.url);
    const provinceId = searchParams.get('id');
    
    if (provinceId) {
      // Get specific province
      const province = await getProvinceById(provinceId);
      
      if (!province) {
        return NextResponse.json(
          { error: 'Province not found' },
          { headers: corsHeaders, status: 404 }
        );
      }
      
      return NextResponse.json({ province }, { 
        headers: corsHeaders, 
        status: 200 
      });
    } else {
      // Get all provinces
      const provinces = await getAllProvinces();
      
      return NextResponse.json({ provinces }, { 
        headers: corsHeaders, 
        status: 200 
      });
    }
  } catch (error) {
    console.error('Error fetching provinces:', error);
    return NextResponse.json(
      { error: 'Failed to fetch provinces' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { name, code } = await req.json();
    
    // Validation
    if (!name || !code) {
      return NextResponse.json(
        { error: 'Name and code are required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    if (name.length < 2 || name.length > 100) {
      return NextResponse.json(
        { error: 'Name must be between 2 and 100 characters' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    if (code.length < 2 || code.length > 10) {
      return NextResponse.json(
        { error: 'Code must be between 2 and 10 characters' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const province = await createProvince(name.trim(), code.trim().toUpperCase());
    
    return NextResponse.json({ province }, { 
      headers: corsHeaders, 
      status: 201 
    });
  } catch (error: any) {
    console.error('Error creating province:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return NextResponse.json(
        { error: `Province ${field} already exists` },
        { headers: corsHeaders, status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create province' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { id, name, code, isActive } = await req.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'Province ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (code !== undefined) updateData.code = code.trim().toUpperCase();
    if (isActive !== undefined) updateData.isActive = isActive;
    
    const province = await updateProvince(id, updateData);
    
    if (!province) {
      return NextResponse.json(
        { error: 'Province not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json({ province }, { 
      headers: corsHeaders, 
      status: 200 
    });
  } catch (error: any) {
    console.error('Error updating province:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return NextResponse.json(
        { error: `Province ${field} already exists` },
        { headers: corsHeaders, status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update province' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Province ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const province = await deleteProvince(id);
    
    if (!province) {
      return NextResponse.json(
        { error: 'Province not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json(
      { message: 'Province deleted successfully', province }, 
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Error deleting province:', error);
    return NextResponse.json(
      { error: 'Failed to delete province' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
