// app/api/locations/search/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { searchLocationsByName } from '@/lib/locationUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('q');
    const limitParam = searchParams.get('limit');
    
    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    if (query.length < 2) {
      return NextResponse.json(
        { error: 'Search query must be at least 2 characters' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const limit = limitParam ? parseInt(limitParam, 10) : 10;
    
    if (limit < 1 || limit > 50) {
      return NextResponse.json(
        { error: 'Limit must be between 1 and 50' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const results = await searchLocationsByName(query, limit);
    
    return NextResponse.json({ 
      query,
      results,
      totalResults: {
        provinces: results.provinces.length,
        cities: results.cities.length,
        townships: results.townships.length,
        locations: results.locations.length
      }
    }, { 
      headers: corsHeaders, 
      status: 200 
    });
  } catch (error) {
    console.error('Error searching locations:', error);
    return NextResponse.json(
      { error: 'Failed to search locations' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
