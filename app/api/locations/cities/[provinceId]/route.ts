// app/api/locations/cities/[provinceId]/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { 
  getCitiesByProvince, 
  createCity,
  getCityById,
  updateCity,
  deleteCity 
} from '@/lib/locationUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ provinceId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();
    const { provinceId } = await params;

    const { searchParams } = new URL(req.url);
    const cityId = searchParams.get('id');

    if (cityId) {
      // Get specific city
      const city = await getCityById(cityId);

      if (!city) {
        return NextResponse.json(
          { error: 'City not found' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json({ city }, {
        headers: corsHeaders,
        status: 200
      });
    } else {
      // Get all cities for the province
      const cities = await getCitiesByProvince(provinceId);
      
      return NextResponse.json({ cities }, { 
        headers: corsHeaders, 
        status: 200 
      });
    }
  } catch (error) {
    console.error('Error fetching cities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cities' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ provinceId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();
    const { provinceId } = await params;
    const { name } = await req.json();

    // Validation
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (name.length < 2 || name.length > 100) {
      return NextResponse.json(
        { error: 'Name must be between 2 and 100 characters' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const city = await createCity(name.trim(), provinceId);
    
    return NextResponse.json({ city }, { 
      headers: corsHeaders, 
      status: 201 
    });
  } catch (error: any) {
    console.error('Error creating city:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'City name already exists in this province' },
        { headers: corsHeaders, status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create city' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ provinceId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { id, name, isActive } = await req.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'City ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (isActive !== undefined) updateData.isActive = isActive;
    
    const city = await updateCity(id, updateData);
    
    if (!city) {
      return NextResponse.json(
        { error: 'City not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json({ city }, { 
      headers: corsHeaders, 
      status: 200 
    });
  } catch (error: any) {
    console.error('Error updating city:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'City name already exists in this province' },
        { headers: corsHeaders, status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update city' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ provinceId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'City ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const city = await deleteCity(id);
    
    if (!city) {
      return NextResponse.json(
        { error: 'City not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json(
      { message: 'City deleted successfully', city }, 
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Error deleting city:', error);
    return NextResponse.json(
      { error: 'Failed to delete city' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
