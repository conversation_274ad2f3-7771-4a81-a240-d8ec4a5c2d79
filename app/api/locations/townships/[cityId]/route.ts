// app/api/locations/townships/[cityId]/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { 
  getTownshipsByCity, 
  createTownship,
  getTownshipById,
  updateTownship,
  deleteTownship 
} from '@/lib/locationUtilities';
import '@/models'; // Ensure all models are loaded

// Connect to database when the module is loaded
connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ cityId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();
    const { cityId } = await params;

    const { searchParams } = new URL(req.url);
    const townshipId = searchParams.get('id');

    if (townshipId) {
      // Get specific township
      const township = await getTownshipById(townshipId);

      if (!township) {
        return NextResponse.json(
          { error: 'Township not found' },
          { headers: corsHeaders, status: 404 }
        );
      }

      return NextResponse.json({ township }, {
        headers: corsHeaders,
        status: 200
      });
    } else {
      // Get all townships for the city
      const townships = await getTownshipsByCity(cityId);
      
      return NextResponse.json({ townships }, { 
        headers: corsHeaders, 
        status: 200 
      });
    }
  } catch (error) {
    console.error('Error fetching townships:', error);
    return NextResponse.json(
      { error: 'Failed to fetch townships' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ cityId: string }> }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  try {
    await connectToDatabase();
    const { cityId } = await params;
    const { name } = await req.json();

    // Validation
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { headers: corsHeaders, status: 400 }
      );
    }

    if (name.length < 2 || name.length > 100) {
      return NextResponse.json(
        { error: 'Name must be between 2 and 100 characters' },
        { headers: corsHeaders, status: 400 }
      );
    }

    const township = await createTownship(name.trim(), cityId);
    
    return NextResponse.json({ township }, { 
      headers: corsHeaders, 
      status: 201 
    });
  } catch (error: any) {
    console.error('Error creating township:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Township name already exists in this city' },
        { headers: corsHeaders, status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create township' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { cityId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { id, name, isActive } = await req.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'Township ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (isActive !== undefined) updateData.isActive = isActive;
    
    const township = await updateTownship(id, updateData);
    
    if (!township) {
      return NextResponse.json(
        { error: 'Township not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json({ township }, { 
      headers: corsHeaders, 
      status: 200 
    });
  } catch (error: any) {
    console.error('Error updating township:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Township name already exists in this city' },
        { headers: corsHeaders, status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update township' },
      { headers: corsHeaders, status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { cityId: string } }
) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Township ID is required' },
        { headers: corsHeaders, status: 400 }
      );
    }
    
    const township = await deleteTownship(id);
    
    if (!township) {
      return NextResponse.json(
        { error: 'Township not found' },
        { headers: corsHeaders, status: 404 }
      );
    }
    
    return NextResponse.json(
      { message: 'Township deleted successfully', township }, 
      { headers: corsHeaders, status: 200 }
    );
  } catch (error) {
    console.error('Error deleting township:', error);
    return NextResponse.json(
      { error: 'Failed to delete township' },
      { headers: corsHeaders, status: 500 }
    );
  }
}
