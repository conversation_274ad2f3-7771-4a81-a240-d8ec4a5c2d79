// app/api/locations/seed/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { seedLocations } from '@/scripts/seedLocations';

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    
    console.log('🌱 Starting location seeding via API...');
    
    // Run the seeding function
    await seedLocations();
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'South African location data seeded successfully!' 
      },
      { headers: corsHeaders, status: 200 }
    );
    
  } catch (error: any) {
    console.error('Error seeding locations:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to seed location data',
        details: error.message 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
