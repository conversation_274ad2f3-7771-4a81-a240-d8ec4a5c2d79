// app/api/locations/migrate/route.ts

import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { getCorsHeaders } from '@/lib/cors';
import { StokvelGroup } from '@/models/StokvelGroup';
import { Location } from '@/models/Location';
import { Township } from '@/models/Township';
import { City } from '@/models/City';
import { Province } from '@/models/Province';

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

// Helper function to find location by geolocation string
async function findLocationByGeolocation(geolocation: string) {
  const searchTerm = geolocation.toLowerCase().trim();
  
  // Try to find exact matches first
  let location = await Location.findOne({
    name: { $regex: new RegExp(`^${searchTerm}$`, 'i') }
  });
  
  if (location) return location;
  
  // Try partial matches
  location = await Location.findOne({
    name: { $regex: new RegExp(searchTerm, 'i') }
  });
  
  if (location) return location;
  
  // Try township matches
  const township = await Township.findOne({
    name: { $regex: new RegExp(searchTerm, 'i') }
  });
  
  if (township) {
    // Find or create a default location in this township
    location = await Location.findOne({ townshipId: township._id });
    if (!location) {
      location = await Location.create({
        name: `${township.name} Central`,
        townshipId: township._id,
        description: `Default location for ${township.name}`
      });
    }
    return location;
  }
  
  return null;
}

// Helper function to create default location for unmatched groups
async function createDefaultLocation(geolocation: string) {
  // Find a default province (Gauteng) and city (Johannesburg)
  let province = await Province.findOne({ code: 'GP' });
  if (!province) {
    province = await Province.create({ name: 'Gauteng', code: 'GP' });
  }
  
  let city = await City.findOne({ name: 'Johannesburg', provinceId: province._id });
  if (!city) {
    city = await City.create({ name: 'Johannesburg', provinceId: province._id });
  }
  
  let township = await Township.findOne({ name: 'Other Areas', cityId: city._id });
  if (!township) {
    township = await Township.create({ name: 'Other Areas', cityId: city._id });
  }
  
  const location = await Location.create({
    name: geolocation || 'Unknown Location',
    townshipId: township._id,
    description: `Migrated from legacy geolocation: ${geolocation}`
  });
  
  return location;
}

export async function POST(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  
  try {
    await connectToDatabase();
    
    console.log('🔄 Starting group location migration via API...');
    
    // Get all groups with old geolocation field
    const groups = await StokvelGroup.find({
      geolocation: { $exists: true, $ne: null, $ne: '' },
      locationId: { $exists: false }
    });
    
    console.log(`Found ${groups.length} groups to migrate`);
    
    let migrated = 0;
    let created = 0;
    let errors = 0;
    
    for (const group of groups) {
      try {
        // Try to match geolocation string to new location hierarchy
        let locationMatch = await findLocationByGeolocation(group.geolocation);
        
        if (!locationMatch) {
          // Create a default location for unmatched groups
          locationMatch = await createDefaultLocation(group.geolocation);
          created++;
        } else {
          migrated++;
        }
        
        // Update group with new locationId
        await StokvelGroup.findByIdAndUpdate(group._id, {
          locationId: locationMatch._id,
          $unset: { geolocation: 1 } // Remove old field
        });
        
        console.log(`✅ Migrated group: ${group.name} -> ${locationMatch.name}`);
        
      } catch (error) {
        console.error(`❌ Error migrating group ${group.name}:`, error);
        errors++;
      }
    }
    
    const summary = {
      total: groups.length,
      migrated,
      created,
      errors
    };
    
    console.log('🎉 Migration completed!', summary);
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'Group location migration completed successfully!',
        summary
      },
      { headers: corsHeaders, status: 200 }
    );
    
  } catch (error: any) {
    console.error('Error migrating group locations:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to migrate group locations',
        details: error.message 
      },
      { headers: corsHeaders, status: 500 }
    );
  }
}
