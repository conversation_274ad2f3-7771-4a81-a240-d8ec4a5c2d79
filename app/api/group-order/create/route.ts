// app/api/group-order/create/route.ts
import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { createMemberOrderAndUpdateGroup } from "@/lib/shoppingCartBackendUtilities";
import { ShoppingCart } from "@/models/ShoppingCart";
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function OPTIONS(req: Request) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);
  return NextResponse.json({}, { headers: corsHeaders, status: 200 });
}

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get("origin"));

  try {
    const { userId, groupId, customerInfo, paymentMethod } = await req.json();

    // Validate required fields
    if (!userId || !groupId || !customerInfo) {
      return NextResponse.json(
        { error: "User ID, Group ID, and customer info are required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Get the user's cart
    const cart = await ShoppingCart.findOne({ user: userId, groupId })
      .populate({
        path: 'items.product',
        select: 'name price image'
      });

    if (!cart || !cart.items || cart.items.length === 0) {
      return NextResponse.json(
        { error: "Cart is empty or not found." },
        { headers: corsHeaders, status: 404 }
      );
    }

    // Create member order and update group order
    const { memberOrder, groupOrder } = await createMemberOrderAndUpdateGroup(
      userId,
      groupId,
      cart,
      customerInfo,
      paymentMethod
    );

    if (!groupOrder || !memberOrder) {
      return NextResponse.json(
        { error: "Failed to create member order and group order." },
        { headers: corsHeaders, status: 500 }
      );
    }

    return NextResponse.json({
      message: "Order created successfully",
      groupOrder,
      memberOrder,
      orderId: groupOrder._id,
      memberOrderId: memberOrder._id,
      memberOrderNumber: memberOrder.orderNumber
    }, {
      headers: corsHeaders,
      status: 201,
    });
  } catch (error) {
    console.error("Failed to create GroupOrder:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
