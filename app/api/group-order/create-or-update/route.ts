// app/api/group-order/create-or-update/route.ts
import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { createOrUpdateGroupOrder } from "@/lib/shoppingCartBackendUtilities";
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get("origin"));

  try {
    const { groupId, cartItems, customerInfo, paymentMethod } = await req.json();

    // Validate required fields
    if (
      !groupId ||
      !cartItems ||
      !Array.isArray(cartItems) ||
      cartItems.length === 0 ||
      !customerInfo ||
      !customerInfo.userId
    ) {
      return NextResponse.json(
        { error: "Group ID, cart items, and a valid user in customerInfo are required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Extract the userId from the customerInfo object.
    const userId = customerInfo.userId;

    const updatedGroupOrder = await createOrUpdateGroupOrder(
      userId,
      groupId,
      customerInfo,
      paymentMethod
    );

    if (!updatedGroupOrder) {
      return NextResponse.json(
        { error: "Failed to create or update group order." },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(updatedGroupOrder, {
      headers: corsHeaders,
      status: 200,
    });
  } catch (_error) {
    console.error("Failed to create or update GroupOrder:", _error);
    return NextResponse.json(
      { error: "Internal server error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}

