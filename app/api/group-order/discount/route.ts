// app/api/group-order/discount/route.ts
import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { calculateBulkDiscount } from "@/lib/shoppingCartBackendUtilities";
import { GroupOrder } from "@/models/GroupOrder";
import { BULK_DISCOUNT_TIERS } from "@/types/shoppingCartConstants";
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get("origin"));
  const groupId = req.nextUrl.searchParams.get("groupId");

  if (!groupId) {
    return NextResponse.json(
      { error: "Group ID is required" },
      { headers: corsHeaders, status: 400 }
    );
  }

  try {
    // Get the group order
    const groupOrder = await GroupOrder.findOne({ groupId });
    
    if (!groupOrder) {
      // If there's no group order yet, return zero discount
      return NextResponse.json(
        { 
          discountPercentage: 0,
          discountAmount: 0,
          finalOrderValue: 0
        },
        { headers: corsHeaders, status: 200 }
      );
    }

    // Calculate the bulk discount
    const discountResult = await calculateBulkDiscount(
      groupOrder.totalOrderValue,
      BULK_DISCOUNT_TIERS
    );

    return NextResponse.json(discountResult, {
      headers: corsHeaders,
      status: 200,
    });
  } catch (error) {
    console.error("Failed to calculate group discount:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
