// app/api/group-order/analytics/route.ts
import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { generateGroupOrderAnalytics } from "@/lib/shoppingCartBackendUtilities";
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get("origin"));
  const groupId = req.nextUrl.searchParams.get("groupId");
  const startDateStr = req.nextUrl.searchParams.get("startDate");
  const endDateStr = req.nextUrl.searchParams.get("endDate");

  try {
    // Parse dates if provided
    const startDate = startDateStr ? new Date(startDateStr) : undefined;
    const endDate = endDateStr ? new Date(endDateStr) : undefined;

    // Generate analytics
    const analytics = await generateGroupOrderAnalytics(
      groupId || undefined,
      startDate,
      endDate
    );

    return NextResponse.json(analytics, {
      headers: corsHeaders,
      status: 200,
    });
  } catch (error) {
    console.error("Failed to generate group order analytics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
