// app/api/group-order/list/route.ts
import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { getGroupOrders } from "@/lib/shoppingCartBackendUtilities";
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function GET(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get("origin"));
  const groupId = req.nextUrl.searchParams.get("groupId");
  const userId = req.nextUrl.searchParams.get("userId");

  // Either groupId or userId (or both) must be provided
  if (!groupId && !userId) {
    return NextResponse.json(
      { error: "Either group ID or user ID is required" },
      { headers: corsHeaders, status: 400 }
    );
  }

  try {
    const orders = await getGroupOrders(groupId, userId);

    return NextResponse.json(orders, {
      headers: corsHeaders,
      status: 200,
    });
  } catch (error) {
    console.error("Failed to get group orders:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
