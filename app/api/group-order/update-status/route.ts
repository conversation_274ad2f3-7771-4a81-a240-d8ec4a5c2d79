// app/api/group-order/update-status/route.ts
import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/dbconnect";
import { getCorsHeaders } from "@/lib/cors";
import { updateGroupOrderStatus } from "@/lib/shoppingCartBackendUtilities";
import { GroupOrderStatus } from "@/types/shoppingCartConstants";
import '@/models'; // Ensure all models are loaded

connectToDatabase();

export async function POST(req: NextRequest) {
  const corsHeaders = getCorsHeaders(req.headers.get("origin"));

  try {
    const { orderId, status, userId } = await req.json();

    // Validate required fields
    if (!orderId || !status || !userId) {
      return NextResponse.json(
        { error: "Order ID, status, and user ID are required." },
        { headers: corsHeaders, status: 400 }
      );
    }

    // Validate the status is a valid enum value
    if (!Object.values(GroupOrderStatus).includes(status as GroupOrderStatus)) {
      return NextResponse.json(
        { error: "Invalid status value." },
        { headers: corsHeaders, status: 400 }
      );
    }

    const updatedOrder = await updateGroupOrderStatus(orderId, status as GroupOrderStatus, userId);

    if (!updatedOrder) {
      return NextResponse.json(
        { error: "Failed to update order status or order not found." },
        { headers: corsHeaders, status: 404 }
      );
    }

    return NextResponse.json(updatedOrder, {
      headers: corsHeaders,
      status: 200,
    });
  } catch (error) {
    console.error("Failed to update group order status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { headers: corsHeaders, status: 500 }
    );
  }
}
