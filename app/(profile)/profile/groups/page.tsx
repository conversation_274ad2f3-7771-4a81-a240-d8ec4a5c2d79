// app/(profile)/profile/page.tsx
"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Users, Search, Plus, Settings } from 'lucide-react'
import { GroupDetailsDialog } from '@/components/groups/GroupDetailsDialog'
import { CreateGroupDialog } from '@/components/groups/CreateGroupDialog'

// Dummy data for demonstration
const groupStats = {
  totalGroups: 5,
  activeMembers: 37,
  pendingInvites: 3,
}

const groups = [
  {
    id: "GRP-001",
    name: "Soweto Savings Circle",
    members: 12,
    balance: 15000.00,
    nextMeeting: "2024-01-25",
    status: "Active",
  },
  {
    id: "GRP-002",
    name: "Johannesburg Bulk Buyers",
    members: 8,
    balance: 8500.50,
    nextMeeting: "2024-01-27",
    status: "Active",
  },
  {
    id: "GRP-003",
    name: "Pretoria Produce Co-op",
    members: 15,
    balance: 22000.00,
    nextMeeting: "2024-01-30",
    status: "Active",
  },
  {
    id: "GRP-004",
    name: "Cape Town Community Savers",
    members: 10,
    balance: 12500.75,
    nextMeeting: "2024-02-02",
    status: "Active",
  },
  {
    id: "GRP-005",
    name: "Durban Discount Shoppers",
    members: 7,
    balance: 6800.25,
    nextMeeting: "2024-02-05",
    status: "Active",
  },
]

export default function GroupsPage() {
  const [selectedGroup, setSelectedGroup] = useState<typeof groups[0] | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  
  const handleViewDetails = (group: typeof groups[0]) => {
    setSelectedGroup(group)
    setIsDetailsDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 
            className="text-3xl text-[#2F4858] font-bold tracking-tight"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            My Stokvel Groups
          </h2>
          <p className="text-muted-foreground">
            Manage your savings and buying circles
          </p>
        </div>
        <div className="flex flex-col sm:flex-row w-full md:w-auto gap-2">
          <Input 
            placeholder="Search groups..." 
            className="max-w-sm"
          />
          <Button>
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Group
          </Button>
        </div>
      </div>

      {/* Group Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Groups</p>
                <h3 className="text-2xl font-bold">{groupStats.totalGroups}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Members</p>
                <h3 className="text-2xl font-bold">{groupStats.activeMembers}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Invites</p>
                <h3 className="text-2xl font-bold">{groupStats.pendingInvites}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-yellow-500/10 flex items-center justify-center">
                <Users className="h-6 w-6 text-yellow-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Groups Table */}
      <Card>
        <CardHeader>
          <CardTitle>Your Stokvel Groups</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Group Name</TableHead>
                <TableHead>Members</TableHead>
                <TableHead>Balance</TableHead>
                <TableHead>Next Meeting</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell className="font-medium">{group.name}</TableCell>
                  <TableCell>{group.members}</TableCell>
                  <TableCell>R{group.balance.toFixed(2)}</TableCell>
                  <TableCell>{group.nextMeeting}</TableCell>
                  <TableCell>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {group.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm" onClick={() => handleViewDetails(group)}>
                      <Settings className="h-4 w-4 mr-2" />
                      Manage
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Group Details Dialog */}
      {selectedGroup && (
        <GroupDetailsDialog
          isOpen={isDetailsDialogOpen}
          onClose={() => setIsDetailsDialogOpen(false)}
          group={selectedGroup}
        />
      )}

      {/* Create Group Dialog */}
      <CreateGroupDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
      />
    </div>
  )
}




// "use client"

// import { useState, useEffect } from "react"
// import { useAuth } from "@/context/AuthContext"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { Button } from "@/components/ui/button"
// import { Users, ArrowRight } from "lucide-react"
// import Link from "next/link"

// type Group = {
//   _id: string
//   name: string
//   members: string[]
//   // Add other relevant group properties
// }

// export default function GroupsPage() {
//   const [groups, setGroups] = useState<Group[]>([])
//   const [isLoading, setIsLoading] = useState(true)
//   const [error, setError] = useState<string | null>(null)
//   const { user } = useAuth()

//   useEffect(() => {
//     const fetchGroups = async () => {
//       if (user?._id) {
//         try {
//           const response = await fetch(`/api/users/${user._id}/groups`)
//           if (!response.ok) {
//             throw new Error("Failed to fetch user groups")
//           }
//           const data = await response.json()
//           setGroups(data)
//         } catch (error) {
//           console.error("Error fetching groups:", error)
//           setError("Failed to fetch your groups. Please try again later.")
//         } finally {
//           setIsLoading(false)
//         }
//       }
//     }

//     fetchGroups()
//   }, [user])

//   if (isLoading) {
//     return <div>Loading...</div>
//   }

//   return (
//     <div className="container mx-auto px-4 py-8">
//       <h1 className="text-3xl font-bold mb-6">My Groups</h1>
//       {error ? (
//         <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
//           <strong className="font-bold">Error: </strong>
//           <span className="block sm:inline">{error}</span>
//         </div>
//       ) : groups.length === 0 ? (
//         <p>You are not a member of any groups yet.</p>
//       ) : (
//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//           {groups.map((group) => (
//             <Card key={group._id}>
//               <CardHeader>
//                 <CardTitle>{group.name}</CardTitle>
//               </CardHeader>
//               <CardContent>
//                 <p className="text-sm text-gray-500 mb-4">
//                   <Users className="inline mr-2" />
//                   {group.members.length} members
//                 </p>
//                 <Link href={`/group/${group._id}`}>
//                   <Button>
//                     View Group
//                     <ArrowRight className="ml-2 h-4 w-4" />
//                   </Button>
//                 </Link>
//               </CardContent>
//             </Card>
//           ))}
//         </div>
//       )}
//     </div>
//   )
// }

