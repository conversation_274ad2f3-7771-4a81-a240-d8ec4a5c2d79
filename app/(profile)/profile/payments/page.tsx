"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { DollarSign, PlusCircle, Search, ArrowUpDown, ArrowDownUp } from 'lucide-react'
import { PaymentMethodDialog } from '@/components/payments/PaymentMethodDialog'
import { TransactionDetailsDialog } from '@/components/payments/TransactionDetailsDialog'

// Dummy data for demonstration
const paymentStats = {
  totalBalance: 25000.00,
  pendingPayments: 1500.00,
  lastTransaction: 750.00,
}

const paymentMethods = [
  { id: 1, type: 'Credit Card', last4: '4242', expiryDate: '12/2025' },
  { id: 2, type: 'Bank Account', last4: '1234', bankName: 'Standard Bank' },
]

const transactions = [
  { id: 'TRX001', date: '2024-01-20', description: 'Group Contribution', amount: 1000.00, type: 'Credit' },
  { id: 'TRX002', date: '2024-01-18', description: 'Bulk Purchase - Groceries', amount: 2500.00, type: 'Debit' },
  { id: 'TRX003', date: '2024-01-15', description: 'Dividend Payout', amount: 750.00, type: 'Credit' },
  { id: 'TRX004', date: '2024-01-10', description: 'Monthly Savings', amount: 1500.00, type: 'Credit' },
  { id: 'TRX005', date: '2024-01-05', description: 'Group Purchase - Electronics', amount: 3000.00, type: 'Debit' },
]

export default function PaymentsPage() {
  const [isAddPaymentMethodOpen, setIsAddPaymentMethodOpen] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<typeof transactions[0] | null>(null)
  const [isTransactionDetailsOpen, setIsTransactionDetailsOpen] = useState(false)

  const handleViewTransactionDetails = (transaction: typeof transactions[0]) => {
    setSelectedTransaction(transaction)
    setIsTransactionDetailsOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 
            className="text-3xl text-[#2F4858] font-bold tracking-tight"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            Payments & Transactions
          </h2>
          <p className="text-muted-foreground">
            Manage your payment methods and view transaction history
          </p>
        </div>
        <div className="flex flex-col sm:flex-row w-full md:w-auto gap-2">
          <Input 
            placeholder="Search transactions..." 
            className="max-w-sm"
          />
          <Button>
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>
      </div>

      {/* Payment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Balance</p>
                <h3 className="text-2xl font-bold">R{paymentStats.totalBalance.toFixed(2)}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Payments</p>
                <h3 className="text-2xl font-bold">R{paymentStats.pendingPayments.toFixed(2)}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-yellow-500/10 flex items-center justify-center">
                <ArrowUpDown className="h-6 w-6 text-yellow-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-none">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Transaction</p>
                <h3 className="text-2xl font-bold">R{paymentStats.lastTransaction.toFixed(2)}</h3>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <ArrowDownUp className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods and Transactions */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Payment Information</CardTitle>
            <Button onClick={() => setIsAddPaymentMethodOpen(true)}>
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Payment Method
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="methods">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="methods">Payment Methods</TabsTrigger>
              <TabsTrigger value="transactions">Transaction History</TabsTrigger>
            </TabsList>
            <TabsContent value="methods">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paymentMethods.map((method) => (
                    <TableRow key={method.id}>
                      <TableCell>{method.type}</TableCell>
                      <TableCell>
                        {method.type === 'Credit Card' 
                          ? `**** **** **** ${method.last4} (Expires: ${method.expiryDate})`
                          : `${method.bankName} - **** ${method.last4}`
                        }
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">Edit</Button>
                        <Button variant="ghost" size="sm" className="text-red-500">Remove</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>
            <TabsContent value="transactions">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell>R{transaction.amount.toFixed(2)}</TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          transaction.type === 'Credit' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {transaction.type}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" onClick={() => handleViewTransactionDetails(transaction)}>
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Add Payment Method Dialog */}
      <PaymentMethodDialog
        isOpen={isAddPaymentMethodOpen}
        onClose={() => setIsAddPaymentMethodOpen(false)}
      />

      {/* Transaction Details Dialog */}
      {selectedTransaction && (
        <TransactionDetailsDialog
          isOpen={isTransactionDetailsOpen}
          onClose={() => setIsTransactionDetailsOpen(false)}
          transaction={selectedTransaction}
        />
      )}
    </div>
  )
}

