"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { PersonalInfoForm } from "@/components/settings/PersonalInfoForm"
import { ChangePasswordForm } from '@/components/settings/ChangePasswordForm' 
import { NotificationSettings } from '@/components/settings/NotificationSettings' 
import { PrivacySettings } from '@/components/settings/PrivacySettings' 
import { DeleteAccountDialog } from '@/components/settings/DeleteAccountDialog' 

export default function SettingsPage() {
  const [isDeleteAccountOpen, setIsDeleteAccountOpen] = useState(false)

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div>
        <h2 
          className="text-3xl text-[#2F4858] font-bold tracking-tight"
          style={{
            fontFamily: "ClashDisplay-Variable, sans-serif",
            letterSpacing: "-0.02em",
          }}
        >
          Account Settings
        </h2>
        <p className="text-muted-foreground">
          Manage your account preferences and personal information
        </p>
      </div>

      {/* Settings Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Settings</CardTitle>
          <CardDescription>
            Update your account settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="personal-info" className="w-full">
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-4">
              <TabsTrigger value="personal-info">Personal Info</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="privacy">Privacy</TabsTrigger>
            </TabsList>
            <TabsContent value="personal-info">
              <PersonalInfoForm />
            </TabsContent>
            <TabsContent value="security">
              <ChangePasswordForm />
            </TabsContent>
            <TabsContent value="notifications">
              <NotificationSettings />
            </TabsContent>
            <TabsContent value="privacy">
              <PrivacySettings />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600">Danger Zone</CardTitle>
          <CardDescription>
            Irreversible and destructive actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <button
            onClick={() => setIsDeleteAccountOpen(true)}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
          >
            Delete Account
          </button>
        </CardContent>
      </Card>

      {/* Delete Account Dialog */}
      <DeleteAccountDialog
        isOpen={isDeleteAccountOpen}
        onClose={() => setIsDeleteAccountOpen(false)}
      />
    </div>
  )
}

