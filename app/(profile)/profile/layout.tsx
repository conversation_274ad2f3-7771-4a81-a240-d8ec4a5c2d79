

// app/(profile)/profile/layout.tsx
"use client"

import "../../globals.css"
import { ProfileSidebar } from "@/components/navigation/ProfileSidebar"
import { ProfileTopNavigation } from "@/components/navigation/ProfileTopNavigation"
import { ReduxProvider } from "@/app/providers/ReduxProvider"
import { CartProvider } from "@/components/cart/CartProvider"
import { useAuth } from "@/context/AuthContext"
import { LoadingScreen } from "@/components/ui/loading-screen"

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user } = useAuth()

  if (!user) {
    return <LoadingScreen />
  }

  return (
    <div className="flex h-screen bg-gray-50 font-['ClashDisplay-Variable']">
      <ProfileSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <ProfileTopNavigation />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <ReduxProvider>
            <CartProvider groupId="profile">
              {children}
            </CartProvider>
          </ReduxProvider>
        </main>
      </div>
    </div>
  )
}
