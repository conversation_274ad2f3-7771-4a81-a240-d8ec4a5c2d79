"use client"

import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { FAQSection } from "@/components/help/FAQSection"
import { ContactForm } from '@/components/help/ContactForm' 
import { SupportResources } from '@/components/help/SupportResources' 
import { Search } from 'lucide-react'

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Implement search functionality here
    console.log('Searching for:', searchQuery)
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div>
        <h2 
          className="text-3xl text-[#2F4858] font-bold tracking-tight"
          style={{
            fontFamily: "ClashDisplay-Variable, sans-serif",
            letterSpacing: "-0.02em",
          }}
        >
          Help Center
        </h2>
        <p className="text-muted-foreground">
          Find answers, get support, and learn more about our platform
        </p>
      </div>

      {/* Search Bar */}
      <Card>
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="flex gap-2">
            <Input 
              placeholder="Search for help..." 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-grow"
            />
            <Button type="submit">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Help Content */}
      <Card>
        <CardHeader>
          <CardTitle>How can we help you?</CardTitle>
          <CardDescription>
            Browse through our FAQs, contact us, or explore our support resources
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="faq" className="w-full">
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-3">
              <TabsTrigger value="faq">FAQs</TabsTrigger>
              <TabsTrigger value="contact">Contact Us</TabsTrigger>
              <TabsTrigger value="resources">Support Resources</TabsTrigger>
            </TabsList>
            <TabsContent value="faq">
              <FAQSection />
            </TabsContent>
            <TabsContent value="contact">
              <ContactForm />
            </TabsContent>
            <TabsContent value="resources">
              <SupportResources />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

