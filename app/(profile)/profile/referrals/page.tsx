"use client";

import { Suspense, useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { useAuth } from "@/context/AuthContext";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { ReferralDashboardFallback } from "@/components/referrals/ReferralDashboardFallback";
import { ErrorBoundary } from "@/components/ui/ErrorBoundary";

// Dynamic imports to prevent chunk loading issues
const PremiumReferralDashboard = dynamic(
  () => import("@/components/referrals/PremiumReferralDashboard").then(mod => ({ default: mod.PremiumReferralDashboard })),
  {
    loading: () => <ReferralDashboardFallback userId="" />,
    ssr: false
  }
);

const ReferralDashboardSimple = dynamic(
  () => import("@/components/referrals/ReferralDashboardSimple").then(mod => ({ default: mod.ReferralDashboardSimple })),
  {
    loading: () => <ReferralDashboardFallback userId="" />,
    ssr: false
  }
);

export default function ReferralsPage() {
  const { user, isLoading } = useAuth();
  const [dashboardError, setDashboardError] = useState(false);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Please log in
          </h2>
          <p className="text-gray-600">
            You need to be logged in to view your referral dashboard.
          </p>
        </div>
      </div>
    );
  }

  // Error boundary for chunk loading issues
  useEffect(() => {
    const handleChunkError = (event: ErrorEvent) => {
      if (event.message.includes('ChunkLoadError') || event.message.includes('Loading chunk')) {
        console.warn('Chunk loading error detected, falling back to simple dashboard');
        setDashboardError(true);
      }
    };

    window.addEventListener('error', handleChunkError);
    return () => window.removeEventListener('error', handleChunkError);
  }, []);

  return (
    <div className="container mx-auto px-6 py-8">
      <ErrorBoundary
        fallback={<ReferralDashboardFallback userId={user._id} />}
        onError={(error) => {
          console.error('Referral dashboard error:', error);
          setDashboardError(true);
        }}
      >
        <Suspense fallback={<ReferralDashboardFallback userId={user._id} />}>
          {dashboardError ? (
            // Fallback to simple dashboard if chunk loading fails
            <ReferralDashboardFallback userId={user._id} />
          ) : (
            // Try to load premium dashboard
            <PremiumReferralDashboard userId={user._id} />
          )}
        </Suspense>
      </ErrorBoundary>
    </div>
  );
}
