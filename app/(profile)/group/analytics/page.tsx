"use client"

import { useState } from 'react'
import { useParams } from 'next/navigation'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { GroupOrderReduxAnalytics } from '@/components/group-orders/GroupOrderReduxAnalytics'

// This interface is used by the GroupOrderReduxAnalytics component
// We're defining it here for documentation purposes
interface _Analytics {
  totalGroupOrders: number;
  totalOrderValue: number;
  monthlyOrderTrends: Array<{
    month: string;
    totalOrders: number;
    totalValue: number;
  }>;
  topContributors: Array<{
    userId: string;
    userName: string;
    totalSpent: number;
    contributionPercentage: number;
  }>;
}

export default function GroupOrderAnalyticsPage() {
  const params = useParams()
  const groupId = params.groupId as string

  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1),
    endDate: new Date()
  })

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold">Group Order Analytics</h1>

      {/* Date Range Selector */}
      <div className="flex items-center space-x-4">
        <Select
          value={dateRange.startDate.getFullYear().toString()}
          onValueChange={(year) => setDateRange({
            startDate: new Date(parseInt(year), 0, 1),
            endDate: new Date(parseInt(year), 11, 31)
          })}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select Year" />
          </SelectTrigger>
          <SelectContent>
            {[2023, 2024, 2025].map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Analytics Component */}
      <GroupOrderReduxAnalytics groupId={groupId} />
    </div>
  )
}
