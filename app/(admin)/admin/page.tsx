// app/(admin)/admin/page.tsx

"use client"

import { DashboardStats } from "@/components/admin/DashboardStats"
import { SalesChart } from "@/components/admin/SalesChart"
import { TopStoresTable } from "@/components/admin/tables/TopStoresTable"
import { StokvelGroupsTable } from "@/components/admin/tables/StokvelGroupsTable"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Video} from 'lucide-react'
import { AddStokvelGroupModal } from "@/components/admin/forms/AddStokvelGroupModal"

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h2 
            className="text-3xl text-[#2F4858] font-bold tracking-tight"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            Dashboard
          </h2>
          <p className="text-muted-foreground">
            Welcome back to your Stokvel group buying overview.
          </p>
        </div>

        {/* Instead of a plain Button, use the modal trigger */}
        <AddStokvelGroupModal />
      </div>

      {/* Stats Cards */}
      <DashboardStats />

      {/* Charts and Meeting Card */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <SalesChart />
        
        <Card>
          <CardHeader>
            <CardTitle
              style={{
                fontFamily: "ClashDisplay-Variable, sans-serif",
                letterSpacing: "-0.02em",
              }}
            >
              Daily Meeting
            </CardTitle>
            <CardDescription>Group Leaders Sync at 2:30 PM</CardDescription>
          </CardHeader>
          <CardContent className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="rounded-full bg-secondary p-2">
                <Video className="h-4 w-4" />
              </div>
              <div>
                <p className="text-sm font-medium">Stokvel Group Leaders Meeting</p>
                <p className="text-sm text-muted-foreground">15 Participants</p>
              </div>
            </div>
            <Button variant="secondary">Join Now</Button>
          </CardContent>
        </Card>
      </div>

      {/* Stokvel Groups Table */}
      <StokvelGroupsTable />

      {/* Top Stores Table */}
      <TopStoresTable />
    </div>
  )
}
