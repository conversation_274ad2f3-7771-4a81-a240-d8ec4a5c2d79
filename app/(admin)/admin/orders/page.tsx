// import { Metadata } from "next"
// import { OrdersOverview } from "@/components/admin/orders/OrdersOverview"
// import { OrdersTable } from "@/components/admin/tables/OrdersTable"
// import { GroupOrdersTable } from "@/components/admin/orders/GroupOrdersTable"

// export const metadata: Metadata = {
//   title: "Orders | Stokvel Admin",
//   description: "Manage all orders and group orders",
// }

// export default function OrdersPage() {
//   return (
//     <div className="space-y-8 p-8">
//       <h1 className="text-3xl font-bold tracking-tight">Orders Management</h1>
//       <OrdersOverview />
//       <OrdersTable />
//       <GroupOrdersTable />
//     </div>
//   )
// }




// import { Metadata } from "next"
// import { OrdersOverview } from "@/components/admin/orders/OrdersOverview"
// import { OrdersTable } from "@/components/admin/tables/OrdersTable"
// import { GroupOrdersTable } from "@/components/admin/orders/GroupOrdersTable"
// import { OrdersTrendChart } from "@/components/admin/stats/OrdersTrendChart"
// import { TopSellingProductsChart } from "@/components/admin/stats/TopSellingProductsChart"

// export const metadata: Metadata = {
//   title: "Orders | Stokvel Admin",
//   description: "Manage all orders and group orders",
// }

// export default function OrdersPage() {
//   return (
//     <div className="space-y-8 p-8">
//       <h1 className="text-3xl font-bold tracking-tight">Orders Management</h1>
//       <OrdersOverview />
//       <div className="grid gap-8 md:grid-cols-2">
//         <OrdersTrendChart />
//         <TopSellingProductsChart />
//       </div>
//       <OrdersTable />
//       <GroupOrdersTable />
//     </div>
//   )
// }


// import { Metadata } from "next"
// import { OrdersOverview } from "@/components/admin/orders/OrdersOverview"
// import { OrdersTable } from "@/components/admin/tables/OrdersTable"
// import { GroupOrdersTable } from "@/components/admin/orders/GroupOrdersTable"
// import { OrdersTrendChart } from "@/components/admin/stats/OrdersTrendChart"
// import { TopSellingProductsChart } from "@/components/admin/stats/TopSellingProductsChart"
// import { GroupPurchasesChart } from "@/components/admin/stats/GroupPurchasesChart"

// export const metadata: Metadata = {
//   title: "Orders | Stokvel Admin",
//   description: "Manage all orders and group orders",
// }

// export default function OrdersPage() {
//   return (
//     <div className="space-y-8 p-8">
//       <h1 className="text-3xl font-bold tracking-tight">Orders Management</h1>
//       <OrdersOverview />
//       <div className="grid gap-8 md:grid-cols-2">
//         <OrdersTrendChart />
//         <TopSellingProductsChart />
//       </div>
//       <GroupPurchasesChart />
//       <OrdersTable />
//       <GroupOrdersTable />
//     </div>
//   )
// // }

// import { Metadata } from "next"
// import { OrdersOverview } from "@/components/admin/orders/OrdersOverview"
// import { OrdersTable } from "@/components/admin/tables/OrdersTable"
// import { GroupOrdersTable } from "@/components/admin/orders/GroupOrdersTable"
// import { OrdersTrendChart } from "@/components/admin/stats/OrdersTrendChart"
// import { TopSellingProductsChart } from "@/components/admin/stats/TopSellingProductsChart"
// import { GroupPurchasesChart } from "@/components/admin/stats/GroupPurchasesChart"
// import { OrderGroupForm } from "@/components/admin/forms/OrderGroupForm"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// export const metadata: Metadata = {
//   title: "Orders | Stokvel Admin",
//   description: "Manage all orders and group orders",
// }

// export default function OrdersPage() {
//   return (
//     <div className="space-y-8 p-8">
//       <h1 className="text-3xl font-bold tracking-tight">Orders Management</h1>
//       <OrdersOverview />
//       <div className="grid gap-8 md:grid-cols-2">
//         <OrdersTrendChart />
//         <TopSellingProductsChart />
//       </div>
//       <GroupPurchasesChart />
//       <div className="grid gap-8 md:grid-cols-2">
//         <Card>
//           <CardHeader>
//             <CardTitle>Create New Order Group</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <OrderGroupForm />
//           </CardContent>
//         </Card>
//         <GroupOrdersTable />
//       </div>
//       <OrdersTable />
//     </div>
//   )
// }





import { Metadata } from "next"
import { OrdersOverview } from "@/components/admin/orders/OrdersOverview"
import { OrdersTable } from "@/components/admin/tables/OrdersTable"
import { GroupOrdersReduxTable } from "@/components/admin/orders/GroupOrdersReduxTable"
import { OrdersTrendChart } from "@/components/admin/stats/OrdersTrendChart"
import { TopSellingProductsChart } from "@/components/admin/stats/TopSellingProductsChart"
import { GroupPurchasesChart } from "@/components/admin/stats/GroupPurchasesChart"

export const metadata: Metadata = {
  title: "Orders | Stokvel Admin",
  description: "Manage all orders and group orders",
}

export default function OrdersPage() {
  return (
    <div className="space-y-8 p-8">
      <h1 className="text-3xl font-bold tracking-tight">Orders Management</h1>
      <OrdersOverview />
      <div className="grid gap-8 md:grid-cols-2">
        <OrdersTrendChart />
        <TopSellingProductsChart />
      </div>
      <GroupPurchasesChart />
      <GroupOrdersReduxTable />
      <OrdersTable />
    </div>
  )
}

