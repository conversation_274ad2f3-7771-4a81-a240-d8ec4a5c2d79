// app/(admin)/admin/layout.tsx
"use client"

import "../../globals.css"
import { DashboardSidebar } from "@/components/navigation/DashboardSidebar"
import { AdminTopNavigation } from "@/components/navigation/AdminTopNavigation"

import { ReduxProvider } from "@/app/providers/ReduxProvider"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { useState } from "react"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [queryClient] = useState(() => new QueryClient())

  return (
    <QueryClientProvider client={queryClient}>
      <div className="flex h-screen bg-gray-50 font-['ClashDisplay-Variable']">
        <DashboardSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <AdminTopNavigation />
          <main className="flex-1 overflow-x-hidden overflow-y-auto p-8">
            <ReduxProvider>
              {children}
            </ReduxProvider>
          </main>
        </div>
      </div>
    </QueryClientProvider>
  )
}
