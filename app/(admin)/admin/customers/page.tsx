import { Metadata } from "next"
import { CustomerOverview } from "@/components/admin/customers/CustomerOverview"
import CustomersTable from "@/components/admin/tables/CustomersTable"
import { CustomerAcquisitionChart } from "@/components/admin/stats/CustomerAcquisitionChart"
import { TopCustomersChart } from "@/components/admin/stats/TopCustomersChart" 

export const metadata: Metadata = {
  title: "Customers | Stokvel Admin",
  description: "Manage and analyze customer data",
}

export default function CustomersPage() {
  return (
    <div className="space-y-8 p-8">
      <h1 className="text-3xl font-bold tracking-tight">Customer Management</h1>
      <CustomerOverview />
      <div className="grid gap-8 md:grid-cols-2">
        <CustomerAcquisitionChart />
        <TopCustomersChart />
      </div>
      <CustomersTable />
    </div>
  )
}
