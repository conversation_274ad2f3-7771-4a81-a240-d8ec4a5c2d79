import { Suspense } from 'react';
import { ArchivedProductCategoriesTable } from '@/components/admin/tables/ArchivedProductCategoriesTable';
import { LoaderIcon } from 'lucide-react';

export default function ProductCategoryArchivePage() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Product Category Archive</h1>
      </div>
      <div className="bg-white shadow-sm rounded-lg p-6">
        <Suspense 
          fallback={
            <div className="flex justify-center items-center h-64">
              <LoaderIcon className="h-8 w-8 animate-spin text-primary" />
            </div>
          }
        >
          <ArchivedProductCategoriesTable />
        </Suspense>
      </div>
    </div>
  );
}
