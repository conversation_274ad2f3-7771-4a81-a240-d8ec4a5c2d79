"use client"

import { <PERSON>, <PERSON><PERSON>hart, ResponsiveContainer, XAxis, YAxis, Tooltip, CartesianGrid } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const data = [
  { date: "Jan", sales: 4000 },
  { date: "Feb", sales: 3000 },
  { date: "Mar", sales: 5000 },
  { date: "Apr", sales: 4500 },
  { date: "May", sales: 6000 },
  { date: "Jun", sales: 5500 },
]

export function ProductSalesTrendChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Sales Trend</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <CartesianGrid stroke="#eee" strokeDasharray="5 5" />
            <Line type="monotone" dataKey="sales" stroke="#2A7C6C" />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}

