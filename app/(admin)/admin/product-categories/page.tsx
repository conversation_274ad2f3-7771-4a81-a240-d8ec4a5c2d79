// app/(admin)/admin/product-categories/page.tsx
import { ProductCategoriesTable } from "@/components/admin/tables/ProductCategoriesTable"
import { AddProductCategoryModal } from "@/components/admin/forms/AddProductCategoryModal"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ProductCategoriesPage() {
  return (
    <div className="space-y-6 p-8">
      <div className="flex justify-between items-center">
        <h2 
          className="text-3xl font-bold tracking-tight"
          style={{
            fontFamily: "ClashDisplay-Variable, sans-serif",
            letterSpacing: "-0.02em",
          }}
        >
          Product Categories
        </h2>
        <AddProductCategoryModal />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <ProductCategoriesTable />
        </CardContent>
      </Card>
    </div>
  )
}

