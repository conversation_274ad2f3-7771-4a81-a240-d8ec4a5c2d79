// Enhanced User Management Page
// Comprehensive user analytics, segmentation, and management

import { Metadata } from 'next';
import { EnhancedUserDashboard } from '@/components/admin/users/EnhancedUserDashboard';

export const metadata: Metadata = {
  title: 'Enhanced User Management | StockvelMarket Admin',
  description: 'Advanced user analytics, segmentation, and customer lifecycle management',
};

export default function EnhancedUsersPage() {
  return (
    <div className="container mx-auto py-6">
      <EnhancedUserDashboard />
    </div>
  );
}
