// app/(admin)/admin/locations/page.tsx

"use client";

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { MapPin, Building, Home, Map, BarChart3, Globe, Layers, Navigation } from "lucide-react";
import { ProvinceManagement } from "@/components/admin/locations/ProvinceManagement";
import { CityManagement } from "@/components/admin/locations/CityManagement";
import { TownshipManagement } from "@/components/admin/locations/TownshipManagement";
import { LocationManagement } from "@/components/admin/locations/LocationManagement";
import { LocationStats } from "@/components/admin/locations/LocationStats";

const tabConfig = [
  {
    value: "stats",
    label: "Overview",
    icon: BarChart3,
    description: "System statistics and insights",
    color: "from-blue-500 to-cyan-500"
  },
  {
    value: "provinces",
    label: "Provinces",
    icon: Globe,
    description: "Manage South African provinces",
    color: "from-emerald-500 to-teal-500"
  },
  {
    value: "cities",
    label: "Cities",
    icon: Building,
    description: "Manage cities and municipalities",
    color: "from-purple-500 to-violet-500"
  },
  {
    value: "townships",
    label: "Townships",
    icon: Layers,
    description: "Manage townships and suburbs",
    color: "from-orange-500 to-red-500"
  },
  {
    value: "locations",
    label: "Locations",
    icon: Navigation,
    description: "Manage specific locations",
    color: "from-pink-500 to-rose-500"
  }
];

export default function LocationsPage() {
  const [activeTab, setActiveTab] = useState("stats");

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Enhanced Header */}
        <div className="text-center space-y-4">
          <div className="inline-flex items-center gap-3 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-white/20 shadow-lg">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <MapPin className="h-4 w-4 text-white" />
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-0">
              Location System
            </Badge>
          </div>

          <h1
            className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent"
            style={{
              fontFamily: "ClashDisplay-Variable, sans-serif",
              letterSpacing: "-0.02em",
            }}
          >
            Location Management Hub
          </h1>

          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Manage the complete hierarchical location system for Stokvel groups across South Africa.
            From provinces to specific locations, maintain the geographic foundation of your platform.
          </p>
        </div>

        {/* Enhanced Navigation Tabs */}
        <div>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            {/* Custom Tab Navigation */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {tabConfig.map((tab, index) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.value;

                return (
                  <button
                    key={tab.value}
                    onClick={() => setActiveTab(tab.value)}
                    className={`relative p-6 rounded-2xl border transition-all duration-300 hover:scale-102 ${
                      isActive
                        ? 'bg-white shadow-xl border-blue-200 scale-105'
                        : 'bg-white/60 hover:bg-white/80 border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {/* Background Gradient */}
                    {isActive && (
                      <div
                        className={`absolute inset-0 bg-gradient-to-br ${tab.color} opacity-5 rounded-2xl`}
                      />
                    )}

                    {/* Icon */}
                    <div className={`w-12 h-12 mx-auto mb-3 rounded-xl flex items-center justify-center ${
                      isActive
                        ? `bg-gradient-to-br ${tab.color} text-white shadow-lg`
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      <Icon className="h-6 w-6" />
                    </div>

                    {/* Content */}
                    <div className="text-center space-y-1">
                      <h3 className={`font-semibold ${
                        isActive ? 'text-gray-900' : 'text-gray-700'
                      }`}>
                        {tab.label}
                      </h3>
                      <p className="text-xs text-gray-500 leading-relaxed">
                        {tab.description}
                      </p>
                    </div>

                    {/* Active Indicator */}
                    {isActive && (
                      <div
                        className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
                      />
                    )}
                  </button>
                );
              })}
            </div>

            {/* Enhanced Tab Content */}
            <TabsContent value="stats" className="mt-0">
              <LocationStats />
            </TabsContent>

            <TabsContent value="provinces" className="mt-0">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center">
                        <Globe className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl text-gray-900">Province Management</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">Manage South African provinces</p>
                      </div>
                    </div>
                    <Badge className="bg-emerald-100 text-emerald-700 border-emerald-200">
                      Level 1
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <ProvinceManagement />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cities" className="mt-0">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center">
                        <Building className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl text-gray-900">City Management</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">Manage cities and municipalities</p>
                      </div>
                    </div>
                    <Badge className="bg-purple-100 text-purple-700 border-purple-200">
                      Level 2
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <CityManagement />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="townships" className="mt-0">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                        <Layers className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl text-gray-900">Township Management</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">Manage townships and suburbs</p>
                      </div>
                    </div>
                    <Badge className="bg-orange-100 text-orange-700 border-orange-200">
                      Level 3
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <TownshipManagement />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="locations" className="mt-0">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center">
                        <Navigation className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-xl text-gray-900">Location Management</CardTitle>
                        <p className="text-sm text-gray-600 mt-1">Manage specific locations</p>
                      </div>
                    </div>
                    <Badge className="bg-pink-100 text-pink-700 border-pink-200">
                      Level 4
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <LocationManagement />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
