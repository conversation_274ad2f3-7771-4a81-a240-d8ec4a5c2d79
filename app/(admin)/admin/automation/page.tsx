// Advanced Automation Page
// Automation builder and workflow management

import { Metadata } from 'next';
import { AdvancedAutomationBuilder } from '@/components/admin/automation/AdvancedAutomationBuilder';

export const metadata: Metadata = {
  title: 'Advanced Automation | StockvelMarket Admin',
  description: 'Create and manage intelligent automation workflows with triggers, conditions, and actions',
};

export default function AutomationPage() {
  return (
    <div className="container mx-auto py-6">
      <AdvancedAutomationBuilder />
    </div>
  );
}
