import { configureStore } from '@reduxjs/toolkit';
import { productsApiSlice } from '@/lib/redux/features/products/productsApiSlice';
import { categoriesApiSlice } from '@/lib/redux/features/categories/categoriesApiSlice';
import { cartApiSlice } from '@/lib/redux/features/cart/cartApiSlice';
import cartReducer from '@/lib/redux/features/cart/cartSlice';
import { groupsApi } from '@/lib/redux/features/groups/groupsApiSlice';
import groupsReducer from '@/lib/redux/features/groups/groupsSlice';
import { locationsApi } from '@/lib/redux/features/locations/locationsApiSlice';
import { groupMembershipApiSlice } from '@/lib/redux/features/groupMembership/groupMembershipApiSlice';
import groupMembershipReducer from '@/lib/redux/features/groupMembership/groupMembershipSlice';
import { groupSavingsApiSlice } from '@/lib/redux/features/groupSavings/groupSavingsApiSlice';
import groupSavingsReducer from '@/lib/redux/features/groupSavings/groupSavingsSlice';
import { memberOrdersApiSlice } from '@/lib/redux/features/memberOrders/memberOrdersApiSlice';
import { wishlistApiSlice } from '@/lib/redux/features/wishlist/wishlistApiSlice';
import { ratingsApiSlice } from '@/lib/redux/features/ratings/ratingsApiSlice';

const rootReducer = {
  [productsApiSlice.reducerPath]: productsApiSlice.reducer,
  [categoriesApiSlice.reducerPath]: categoriesApiSlice.reducer,
  [cartApiSlice.reducerPath]: cartApiSlice.reducer,
  cart: cartReducer,
  [groupsApi.reducerPath]: groupsApi.reducer,
  groups: groupsReducer,
  [locationsApi.reducerPath]: locationsApi.reducer,
  [groupMembershipApiSlice.reducerPath]: groupMembershipApiSlice.reducer,
  groupMembership: groupMembershipReducer,
  [groupSavingsApiSlice.reducerPath]: groupSavingsApiSlice.reducer,
  groupSavings: groupSavingsReducer,
  [memberOrdersApiSlice.reducerPath]: memberOrdersApiSlice.reducer,
  [wishlistApiSlice.reducerPath]: wishlistApiSlice.reducer,
  [ratingsApiSlice.reducerPath]: ratingsApiSlice.reducer,
};

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(productsApiSlice.middleware)
      .concat(categoriesApiSlice.middleware)
      .concat(cartApiSlice.middleware)
      .concat(groupsApi.middleware)
      .concat(locationsApi.middleware)
      .concat(groupMembershipApiSlice.middleware)
      .concat(groupSavingsApiSlice.middleware)
      .concat(memberOrdersApiSlice.middleware)
      .concat(wishlistApiSlice.middleware)
      .concat(ratingsApiSlice.middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
