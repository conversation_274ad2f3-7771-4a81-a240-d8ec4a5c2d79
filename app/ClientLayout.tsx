// app/ClientLayout.tsx
"use client"

import { usePathname } from "next/navigation"
import { SiteHeader } from "@/components/navigation/site-header"
import { SiteFooter } from "@/components/navigation/site-footer"

export function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  const shouldShowHeaderAndFooter =
    !pathname.startsWith("/admin") && !pathname.startsWith("/profile") && !pathname.startsWith("/group")

  return (
    <>
      {shouldShowHeaderAndFooter && <SiteHeader />}
      <main>{children}</main>
      {shouldShowHeaderAndFooter && <SiteFooter />}
    </>
  )
}