// hooks/usePWA.ts
"use client";

import { useState, useEffect, useCallback } from 'react';
import {
  BeforeInstallPromptEvent,
  PWAInstallation,
  registerServiceWorker,
  unregisterServiceWorker,
  subscribeToPushNotifications,
  unsubscribeFromPushNotifications,
  requestNotificationPermission,
  showAddToHomeScreenPrompt,
  isStandalone,
  detectPlatform,
  isPWAInstallable,
  isAppInstalled,
  getInstallationInstructions,
  clearAppCache,
  getCacheSize
} from '@/lib/pwa';

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isStandalone: boolean;
  platform: 'ios' | 'android' | 'desktop' | 'unknown';
  canInstall: boolean;
  showInstallPrompt: boolean;
  isServiceWorkerRegistered: boolean;
  isNotificationSupported: boolean;
  notificationPermission: NotificationPermission;
  isPushSubscribed: boolean;
  isOnline: boolean;
  cacheSize: number;
  installationInstructions: {
    platform: string;
    instructions: string[];
    canShowPrompt: boolean;
  };
}

interface PWAActions {
  install: () => Promise<boolean>;
  registerSW: () => Promise<boolean>;
  unregisterSW: () => Promise<boolean>;
  requestNotifications: () => Promise<NotificationPermission>;
  subscribeToPush: () => Promise<boolean>;
  unsubscribeFromPush: () => Promise<boolean>;
  clearCache: () => Promise<void>;
  refreshCacheSize: () => Promise<void>;
  dismissInstallPrompt: () => void;
}

export function usePWA(): PWAState & PWAActions {
  const [state, setState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isStandalone: false,
    platform: 'unknown',
    canInstall: false,
    showInstallPrompt: false,
    isServiceWorkerRegistered: false,
    isNotificationSupported: false,
    notificationPermission: 'default',
    isPushSubscribed: false,
    isOnline: true,
    cacheSize: 0,
    installationInstructions: {
      platform: 'Unknown',
      instructions: [],
      canShowPrompt: false
    }
  });

  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);

  // Initialize PWA state
  useEffect(() => {
    const initializePWA = async () => {
      const platform = detectPlatform();
      const isStandaloneMode = isStandalone();
      const isInstalled = isAppInstalled();
      const isInstallable = isPWAInstallable();
      const instructions = getInstallationInstructions();
      
      // Check service worker registration
      let isServiceWorkerRegistered = false;
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration();
        isServiceWorkerRegistered = !!registration;
      }

      // Check notification support and permission
      const isNotificationSupported = 'Notification' in window;
      const notificationPermission = isNotificationSupported 
        ? Notification.permission 
        : 'denied' as NotificationPermission;

      // Check push subscription
      let isPushSubscribed = false;
      if (isServiceWorkerRegistered && 'serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.getRegistration();
          if (registration) {
            const subscription = await registration.pushManager.getSubscription();
            isPushSubscribed = !!subscription;
          }
        } catch (error) {
          console.error('Error checking push subscription:', error);
        }
      }

      // Get cache size
      const cacheSize = await getCacheSize();

      setState(prev => ({
        ...prev,
        platform,
        isStandalone: isStandaloneMode,
        isInstalled,
        isInstallable,
        canInstall: isInstallable && !isInstalled,
        showInstallPrompt: isInstallable && !isInstalled && !isStandaloneMode,
        isServiceWorkerRegistered,
        isNotificationSupported,
        notificationPermission,
        isPushSubscribed,
        cacheSize,
        installationInstructions: instructions
      }));
    };

    initializePWA();
  }, []);

  // Listen for beforeinstallprompt event
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const promptEvent = e as BeforeInstallPromptEvent;
      setDeferredPrompt(promptEvent);
      
      setState(prev => ({
        ...prev,
        isInstallable: true,
        canInstall: !prev.isInstalled,
        showInstallPrompt: !prev.isInstalled && !prev.isStandalone
      }));
    };

    const handleAppInstalled = () => {
      console.log('PWA was installed');
      setDeferredPrompt(null);
      
      setState(prev => ({
        ...prev,
        isInstalled: true,
        canInstall: false,
        showInstallPrompt: false
      }));
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => setState(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setState(prev => ({ ...prev, isOnline: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial online state
    setState(prev => ({ ...prev, isOnline: navigator.onLine }));

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Install the PWA
  const install = useCallback(async (): Promise<boolean> => {
    if (!deferredPrompt) {
      console.log('No install prompt available');
      return false;
    }

    try {
      const result = await showAddToHomeScreenPrompt(deferredPrompt);
      setDeferredPrompt(null);
      
      if (result) {
        setState(prev => ({
          ...prev,
          isInstalled: true,
          canInstall: false,
          showInstallPrompt: false
        }));
      }
      
      return result;
    } catch (error) {
      console.error('Error installing PWA:', error);
      return false;
    }
  }, [deferredPrompt]);

  // Register service worker
  const registerSW = useCallback(async (): Promise<boolean> => {
    try {
      const registration = await registerServiceWorker();
      const isRegistered = !!registration;
      
      setState(prev => ({
        ...prev,
        isServiceWorkerRegistered: isRegistered
      }));
      
      return isRegistered;
    } catch (error) {
      console.error('Error registering service worker:', error);
      return false;
    }
  }, []);

  // Unregister service worker
  const unregisterSW = useCallback(async (): Promise<boolean> => {
    try {
      const result = await unregisterServiceWorker();
      
      setState(prev => ({
        ...prev,
        isServiceWorkerRegistered: !result,
        isPushSubscribed: false
      }));
      
      return result;
    } catch (error) {
      console.error('Error unregistering service worker:', error);
      return false;
    }
  }, []);

  // Request notification permission
  const requestNotifications = useCallback(async (): Promise<NotificationPermission> => {
    try {
      const permission = await requestNotificationPermission();
      
      setState(prev => ({
        ...prev,
        notificationPermission: permission
      }));
      
      return permission;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return 'denied';
    }
  }, []);

  // Subscribe to push notifications
  const subscribeToPush = useCallback(async (): Promise<boolean> => {
    try {
      const subscription = await subscribeToPushNotifications();
      const isSubscribed = !!subscription;
      
      setState(prev => ({
        ...prev,
        isPushSubscribed: isSubscribed,
        notificationPermission: isSubscribed ? 'granted' : prev.notificationPermission
      }));
      
      return isSubscribed;
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      return false;
    }
  }, []);

  // Unsubscribe from push notifications
  const unsubscribeFromPush = useCallback(async (): Promise<boolean> => {
    try {
      const result = await unsubscribeFromPushNotifications();
      
      setState(prev => ({
        ...prev,
        isPushSubscribed: !result
      }));
      
      return result;
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error);
      return false;
    }
  }, []);

  // Clear app cache
  const clearCache = useCallback(async (): Promise<void> => {
    try {
      await clearAppCache();
      await refreshCacheSize();
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }, []);

  // Refresh cache size
  const refreshCacheSize = useCallback(async (): Promise<void> => {
    try {
      const size = await getCacheSize();
      setState(prev => ({ ...prev, cacheSize: size }));
    } catch (error) {
      console.error('Error refreshing cache size:', error);
    }
  }, []);

  // Dismiss install prompt
  const dismissInstallPrompt = useCallback(() => {
    setState(prev => ({
      ...prev,
      showInstallPrompt: false
    }));
  }, []);

  return {
    ...state,
    install,
    registerSW,
    unregisterSW,
    requestNotifications,
    subscribeToPush,
    unsubscribeFromPush,
    clearCache,
    refreshCacheSize,
    dismissInstallPrompt
  };
}
