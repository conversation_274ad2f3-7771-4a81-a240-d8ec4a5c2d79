// // hooks/useAuthStatus.ts
// import { useState, useEffect } from "react"
// import { useAuth } from "@/context/AuthContext"

// export function useAuthStatus() {
//   const auth = useAuth()
//   const [isLoading, setIsLoading] = useState(true)

//   useEffect(() => {
//     if (auth.user !== undefined) {
//       setIsLoading(false)
//     }
//   }, [auth.user])

//   return {
//     ...auth,
//     isLoading,
//   }
// }




// hooks/useAuthStatus.ts
import { useState, useEffect } from "react";
import { useAuth } from "@/context/AuthContext";

export function useAuthStatus() {
  const { user, loading } = useAuth(); // Destructure loading from AuthContext
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!loading) {
      setIsLoading(false);
    }
  }, [loading]);

  return {
    user,
    isAuthenticated: !!user,
    isLoading,
  };
}
