// hooks/useCreateProductMutation.ts

import { useMutation, useQueryClient } from "@tanstack/react-query"
import { createProduct } from "@/lib/frontendProductUtilities"
import { Product } from "@/types/product"

export function useCreateProductMutation() {
  const queryClient = useQueryClient()

  const mutation = useMutation<Product, Error, FormData>({
    mutationFn: createProduct,
    onSuccess: (newProduct) => {
      queryClient.setQueryData<Product[]>(["products"], (oldProducts = []) => [...oldProducts, newProduct])
    },
  })

  return {
    ...mutation,
    isLoading: mutation.status === "pending",
  }
}

