// // 


// "use client"

// import { useState, type ReactElement } from "react"

// export const useMultiStepForm = <T extends Record<string, string>>(steps: ReactElement[], initialData: T) => {
//   const [currentStepIndex, setCurrentStepIndex] = useState(0)
//   const [formData, setFormData] = useState<T>(initialData)

//   function next() {
//     setCurrentStepIndex((i) => {
//       if (i >= steps.length - 1) return i
//       return i + 1
//     })
//   }

//   function back() {
//     setCurrentStepIndex((i) => {
//       if (i <= 0) return i
//       return i - 1
//     })
//   }

//   function goTo(index: number) {
//     setCurrentStepIndex(index)
//   }

//   const updateFormData = (key: keyof T, value: string) => {
//     setFormData((prev) => ({ ...prev, [key]: value }))
//   }

//   return {
//     currentStepIndex,
//     step: steps[currentStepIndex],
//     steps,
//     isFirstStep: currentStepIndex === 0,
//     isLastStep: currentStepIndex === steps.length - 1,
//     goTo,
//     next,
//     back,
//     formData,
//     updateFormData,
//   }
// }


"use client"

import { useState, useCallback } from "react"

export const useMultiStepForm = <T extends Record<string, any>>(initialData: T) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [formData, setFormData] = useState<T>(initialData)

  // Define the total number of steps (can be made configurable if needed)
  const totalSteps = 4

  const next = useCallback(() => {
    setCurrentStepIndex((i) => {
      if (i >= totalSteps - 1) return i
      return i + 1
    })
  }, [totalSteps])

  const back = useCallback(() => {
    setCurrentStepIndex((i) => {
      if (i <= 0) return i
      return i - 1
    })
  }, [])

  const goTo = useCallback((index: number) => {
    if (index >= 0 && index < totalSteps) {
      setCurrentStepIndex(index)
    }
  }, [totalSteps])

  const updateFormData = useCallback((newData: Partial<T>) => {
    setFormData((prev) => ({ ...prev, ...newData }))
  }, [])

  return {
    currentStepIndex,
    totalSteps,
    isFirstStep: currentStepIndex === 0,
    isLastStep: currentStepIndex === totalSteps - 1,
    goTo,
    next,
    back,
    formData,
    updateFormData,
  }
}
