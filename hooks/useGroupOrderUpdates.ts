// hooks/useGroupOrderUpdates.ts
"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { getRealTimeService } from '@/lib/services/realTimeService';
import { 
  GroupOrderUpdate,
  CartUpdateEvent,
  PaymentUpdateEvent,
  OrderStatusUpdateEvent,
  DiscountMilestoneEvent,
  UserActivityEvent,
  NotificationEvent,
  ConnectionStatus
} from '@/types/realTimeUpdates';
import { toast } from 'sonner';

interface GroupOrderUpdatesState {
  connectionStatus: ConnectionStatus;
  lastUpdate: GroupOrderUpdate | null;
  recentActivity: UserActivityEvent[];
  notifications: NotificationEvent[];
  memberCount: number;
  totalValue: number;
  currentDiscount: number;
  nextMilestone: {
    threshold: number;
    discount: number;
    remaining: number;
  } | null;
}

interface UseGroupOrderUpdatesOptions {
  groupId: string;
  enableNotifications?: boolean;
  enableActivityTracking?: boolean;
  maxActivityItems?: number;
  onMemberJoined?: (member: { id: string; name: string }) => void;
  onMemberLeft?: (member: { id: string; name: string }) => void;
  onDiscountMilestone?: (milestone: DiscountMilestoneEvent) => void;
  onOrderUpdate?: (update: GroupOrderUpdate) => void;
}

export function useGroupOrderUpdates({
  groupId,
  enableNotifications = true,
  enableActivityTracking = true,
  maxActivityItems = 10,
  onMemberJoined,
  onMemberLeft,
  onDiscountMilestone,
  onOrderUpdate
}: UseGroupOrderUpdatesOptions) {
  const { user } = useAuth();
  const [state, setState] = useState<GroupOrderUpdatesState>({
    connectionStatus: 'disconnected',
    lastUpdate: null,
    recentActivity: [],
    notifications: [],
    memberCount: 0,
    totalValue: 0,
    currentDiscount: 0,
    nextMilestone: null
  });

  const [subscriptionId, setSubscriptionId] = useState<string | null>(null);

  // Handle group order updates
  const handleGroupOrderUpdate = useCallback((update: GroupOrderUpdate) => {
    setState(prev => ({
      ...prev,
      lastUpdate: update,
      memberCount: update.memberCount,
      totalValue: update.totalValue,
      currentDiscount: update.discountInfo?.currentDiscount || 0,
      nextMilestone: update.discountInfo?.nextMilestone || null
    }));

    // Handle specific update types
    switch (update.updateType) {
      case 'member_added':
        if (enableNotifications && update.memberId !== user?.id) {
          toast.success(`${update.memberName} joined the group order!`);
        }
        onMemberJoined?.({ id: update.memberId!, name: update.memberName! });
        break;

      case 'member_removed':
        if (enableNotifications && update.memberId !== user?.id) {
          toast.info(`${update.memberName} left the group order`);
        }
        onMemberLeft?.({ id: update.memberId!, name: update.memberName! });
        break;

      case 'item_added':
        if (enableNotifications && update.memberId !== user?.id) {
          toast.info(`${update.memberName} added ${update.itemName} to the order`);
        }
        break;

      case 'quantity_changed':
        if (enableNotifications && update.memberId !== user?.id) {
          const change = (update.newQuantity || 0) - (update.previousQuantity || 0);
          const action = change > 0 ? 'increased' : 'decreased';
          toast.info(`${update.memberName} ${action} ${update.itemName} quantity`);
        }
        break;
    }

    onOrderUpdate?.(update);
  }, [user?.id, enableNotifications, onMemberJoined, onMemberLeft, onOrderUpdate]);

  // Handle cart updates
  const handleCartUpdate = useCallback((update: CartUpdateEvent) => {
    if (enableActivityTracking) {
      const activity: UserActivityEvent = {
        userId: update.userId,
        userName: 'Member', // Would need to fetch user name
        groupId: update.groupId,
        activityType: 'added_to_cart',
        details: {
          productName: update.productName,
          quantity: update.quantity,
          updateType: update.updateType
        },
        timestamp: update.timestamp
      };

      setState(prev => ({
        ...prev,
        recentActivity: [activity, ...prev.recentActivity.slice(0, maxActivityItems - 1)]
      }));
    }
  }, [enableActivityTracking, maxActivityItems]);

  // Handle payment updates
  const handlePaymentUpdate = useCallback((update: PaymentUpdateEvent) => {
    if (enableNotifications) {
      switch (update.status) {
        case 'completed':
          toast.success('Payment processed successfully!');
          break;
        case 'failed':
          toast.error('Payment failed. Please try again.');
          break;
        case 'pending':
          toast.info('Payment is being processed...');
          break;
      }
    }
  }, [enableNotifications]);

  // Handle order status updates
  const handleOrderStatusUpdate = useCallback((update: OrderStatusUpdateEvent) => {
    if (enableNotifications) {
      const statusMessages = {
        confirmed: 'Order confirmed!',
        processing: 'Order is being processed',
        packed: 'Order has been packed',
        shipped: 'Order has been shipped',
        delivered: 'Order delivered successfully!',
        cancelled: 'Order was cancelled',
        failed: 'Order fulfillment failed'
      };

      const message = statusMessages[update.newStatus];
      if (message) {
        const toastType = ['delivered', 'confirmed'].includes(update.newStatus) ? 'success' :
                         ['cancelled', 'failed'].includes(update.newStatus) ? 'error' : 'info';
        
        if (toastType === 'success') toast.success(message);
        else if (toastType === 'error') toast.error(message);
        else toast.info(message);
      }
    }
  }, [enableNotifications]);

  // Handle discount milestones
  const handleDiscountMilestone = useCallback((milestone: DiscountMilestoneEvent) => {
    setState(prev => ({
      ...prev,
      currentDiscount: milestone.newDiscount,
      totalValue: milestone.totalValue,
      memberCount: milestone.memberCount
    }));

    if (enableNotifications) {
      switch (milestone.milestoneType) {
        case 'threshold_reached':
          toast.success(`🎉 Discount threshold reached! Now saving ${milestone.newDiscount}%`);
          break;
        case 'new_tier_unlocked':
          toast.success(`🚀 New discount tier unlocked! ${milestone.newDiscount}% off`);
          break;
        case 'bonus_applied':
          toast.success(`💰 Bonus discount applied! Extra ${milestone.newDiscount - milestone.previousDiscount}% off`);
          break;
      }
    }

    onDiscountMilestone?.(milestone);
  }, [enableNotifications, onDiscountMilestone]);

  // Handle user activity
  const handleUserActivity = useCallback((activity: UserActivityEvent) => {
    if (enableActivityTracking && activity.groupId === groupId) {
      setState(prev => ({
        ...prev,
        recentActivity: [activity, ...prev.recentActivity.slice(0, maxActivityItems - 1)]
      }));
    }
  }, [enableActivityTracking, groupId, maxActivityItems]);

  // Handle notifications
  const handleNotification = useCallback((notification: NotificationEvent) => {
    setState(prev => ({
      ...prev,
      notifications: [notification, ...prev.notifications.slice(0, 9)] // Keep last 10
    }));

    if (enableNotifications) {
      const toastFn = {
        success: toast.success,
        error: toast.error,
        warning: toast.warning,
        info: toast.info
      }[notification.type] || toast.info;

      toastFn(notification.message, {
        description: notification.title,
        action: notification.actionUrl ? {
          label: notification.actionText || 'View',
          onClick: () => window.open(notification.actionUrl, '_blank')
        } : undefined
      });
    }
  }, [enableNotifications]);

  // Handle connection status changes
  const handleConnectionStatusChange = useCallback((status: ConnectionStatus) => {
    setState(prev => ({ ...prev, connectionStatus: status }));

    if (enableNotifications) {
      switch (status) {
        case 'connected':
          toast.success('Connected to real-time updates');
          break;
        case 'disconnected':
          toast.warning('Disconnected from real-time updates');
          break;
        case 'reconnecting':
          toast.info('Reconnecting to real-time updates...');
          break;
        case 'error':
          toast.error('Connection error. Some features may not work.');
          break;
      }
    }
  }, [enableNotifications]);

  // Initialize real-time service
  useEffect(() => {
    if (!user?.id || !groupId) return;

    const service = getRealTimeService();

    // Set up event handlers
    service.setHandlers({
      onGroupOrderUpdate: handleGroupOrderUpdate,
      onCartUpdate: handleCartUpdate,
      onPaymentUpdate: handlePaymentUpdate,
      onOrderStatusUpdate: handleOrderStatusUpdate,
      onDiscountMilestone: handleDiscountMilestone,
      onUserActivity: handleUserActivity,
      onNotification: handleNotification,
      onConnectionStatusChange: handleConnectionStatusChange
    });

    // Connect and subscribe
    service.connect(user.id).then(() => {
      const subId = service.subscribe('group', groupId, [
        'group_order_updated',
        'member_joined',
        'member_left',
        'cart_updated',
        'payment_processed',
        'order_status_changed',
        'discount_milestone_reached',
        'user_activity',
        'notification_sent'
      ]);
      setSubscriptionId(subId);
    }).catch(error => {
      console.error('Failed to connect to real-time service:', error);
    });

    return () => {
      if (subscriptionId) {
        service.unsubscribe(subscriptionId);
      }
    };
  }, [user?.id, groupId, subscriptionId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (subscriptionId) {
        const service = getRealTimeService();
        service.unsubscribe(subscriptionId);
      }
    };
  }, []);

  // Helper functions
  const markNotificationAsRead = useCallback((notificationId: string) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.filter(n => n.id !== notificationId)
    }));
  }, []);

  const clearActivity = useCallback(() => {
    setState(prev => ({
      ...prev,
      recentActivity: []
    }));
  }, []);

  const getConnectionStatusColor = useCallback(() => {
    switch (state.connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'connecting': 
      case 'reconnecting': return 'text-yellow-600';
      case 'disconnected': return 'text-gray-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  }, [state.connectionStatus]);

  return {
    ...state,
    isConnected: state.connectionStatus === 'connected',
    markNotificationAsRead,
    clearActivity,
    getConnectionStatusColor
  };
}
