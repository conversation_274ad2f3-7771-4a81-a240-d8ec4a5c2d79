import { useMutation, useQueryClient } from "@tanstack/react-query"
import { updateProduct, UpdateProductInput } from "@/lib/frontendProductUtilities"

export function useUpdateProductMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (input: UpdateProductInput) => {
      return await updateProduct(input)
    },
    onSuccess: () => {
      // Invalidate and refetch products query
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
    onError: (error) => {
      console.error("Product update failed", error)
    },
  })
}
