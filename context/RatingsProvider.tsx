"use client";

import React, { createContext, useContext, ReactNode, useCallback } from "react";
import useSWR, { mutate } from "swr";
import type { Rating } from "@/types/rating";
import {
  createRating,
  updateRating,
  deleteRating,
  getProductRatings,
  getUserProductRating,
} from "@/lib/frontendRatingUtilities";

const RATINGS_KEY = (productId: string) => `/api/ratings/${productId}`;
const USER_RATING_KEY = (productId: string, userId: string) => 
  `/api/ratings/${productId}/user/${userId}`;

interface RatingsContextType {
  ratings: Rating[];
  userRating: Rating | null;
  isLoading: boolean;
  isError: boolean;
  createRating: (productId: string, rating: number, comment: string) => Promise<void>;
  updateRating: (ratingId: string, rating: number, comment: string) => Promise<void>;
  deleteRating: (ratingId: string) => Promise<void>;
  fetchProductRatings: (productId: string) => Promise<void>;
  fetchUserRating: (productId: string, userId: string) => Promise<void>;
  averageRating: number;
  totalRatings: number;
}

const RatingsContext = createContext<RatingsContextType | undefined>(undefined);

export const RatingsProvider = ({ 
  children,
  productId,
  userId 
}: { 
  children: ReactNode;
  productId: string;
  userId: string;
}) => {
  const { 
    data: ratings,
    error,
    isLoading: isRatingsLoading,
    mutate: mutateRatings
  } = useSWR<Rating[]>(
    RATINGS_KEY(productId),
    () => getProductRatings(productId)
  );

  const {
    data: userRating,
    error: userRatingError,
    isLoading: isUserRatingLoading,
    mutate: mutateUserRating
  } = useSWR<Rating | null>(
    USER_RATING_KEY(productId, userId),
    () => getUserProductRating(productId, userId)
  );

  const createRatingFn = useCallback(async (
    productId: string,
    rating: number,
    comment: string
  ) => {
    try {
      const newRating = await createRating({
        productId,
        rating,
        comment
      });
      mutateRatings((prev: Rating[] = []) => [...prev, newRating], false);
      mutateUserRating(newRating, false);
    } catch (err) {
      console.error("Error creating rating:", err);
      throw err;
    }
  }, [mutateRatings, mutateUserRating]);

  const updateRatingFn = useCallback(async (
    ratingId: string,
    rating: number,
    comment: string
  ) => {
    try {
      const updatedRating = await updateRating({
        id: ratingId,
        updateData: {
          rating,
          comment
        }
      });
      mutateRatings(
        (prev: Rating[] = []) => 
          prev.map((r) => (r._id === ratingId ? updatedRating : r)),
        false
      );
      mutateUserRating(updatedRating, false);
    } catch (err) {
      console.error("Error updating rating:", err);
      throw err;
    }
  }, [mutateRatings, mutateUserRating]);

  const deleteRatingFn = useCallback(async (ratingId: string) => {
    try {
      await deleteRating(ratingId);
      mutateRatings(
        (prev: Rating[] = []) => 
          prev.filter((r) => r._id !== ratingId),
        false
      );
      mutateUserRating(null, false);
    } catch (err) {
      console.error("Error deleting rating:", err);
      throw err;
    }
  }, [mutateRatings, mutateUserRating]);

  const fetchProductRatings = useCallback(async (productId: string) => {
    try {
      const fetchedRatings = await getProductRatings(productId);
      mutateRatings(fetchedRatings, false);
    } catch (err) {
      console.error("Error fetching product ratings:", err);
    }
  }, [mutateRatings]);

  const fetchUserRating = useCallback(async (productId: string, userId: string) => {
    try {
      const fetchedUserRating = await getUserProductRating(productId, userId);
      mutateUserRating(fetchedUserRating, false);
    } catch (err) {
      console.error("Error fetching user rating:", err);
    }
  }, [mutateUserRating]);

  // Calculate average rating and total ratings
  const averageRating = ratings ? ratings.reduce((acc, curr) => acc + curr.rating, 0) / (ratings.length || 1) : 0;
  const totalRatings = ratings?.length || 0;

  return (
    <RatingsContext.Provider
      value={{
        ratings: ratings || [],
        userRating: userRating || null,
        isLoading: isRatingsLoading || isUserRatingLoading,
        isError: !!error || !!userRatingError,
        createRating: createRatingFn,
        updateRating: updateRatingFn,
        deleteRating: deleteRatingFn,
        fetchProductRatings,
        fetchUserRating,
        averageRating,
        totalRatings,
      }}
    >
      {children}
    </RatingsContext.Provider>
  );
};

export const useRatings = () => {
  const context = useContext(RatingsContext);
  if (!context) {
    throw new Error("useRatings must be used within a RatingsProvider");
  }
  return context;
};