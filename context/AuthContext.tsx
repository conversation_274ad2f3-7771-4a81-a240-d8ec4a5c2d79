// context/AuthContext.tsx

"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { useRouter } from "next/navigation"
import type { User } from "@/types/user"
import { loginUser, loginUserForShopping, logoutUser, getLoggedInUser, registerUser } from "@/lib/frontendAuth"
import { getFirstUserGroupForRedirect } from "@/lib/frontendGroupMembershipUtilities"

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  login: (email: string, password: string, rememberMe: boolean, redirectToGroup?: boolean) => Promise<User>
  loginForShopping: (email: string, password: string, rememberMe?: boolean) => Promise<User>
  logout: () => Promise<void>
  signup: (name: string, email: string, phone: string, password: string, referralCode?: string) => Promise<User>
  loading: boolean
  error: string | null
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    const checkLoggedIn = async () => {
      try {
        setLoading(true)
        const existingUser = await getLoggedInUser()
        if (existingUser) {
          setUser(existingUser)
        } else {
          setUser(null)
        }
      } catch (error) {
        console.error("Error fetching logged-in user:", error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    checkLoggedIn()
  }, [])

  const login = async (email: string, password: string, rememberMe: boolean, redirectToGroup: boolean = false): Promise<User> => {
    try {
      setLoading(true)
      setError(null)
      console.log(`Attempting to login user: ${email}, redirectToGroup: ${redirectToGroup}`)

      const { user } = await loginUser(email, password, rememberMe)
      console.log(`Login successful, user:`, user)

      // The backend returns user with 'id', but our User type uses '_id'
      // We need to normalize this to match our User type
      const normalizedUser: User = {
        ...user,
        _id: (user as unknown as { id?: string })?.id || user._id || '', // Use id if available, fallback to _id
      }

      console.log(`User ID: ${normalizedUser._id}, Role: ${normalizedUser.role}`)
      setUser(normalizedUser)

      // Handle redirection based on role and group membership if redirectToGroup is true
      // Use the normalized user._id
      const userId = normalizedUser._id
      if (redirectToGroup && normalizedUser && userId) {
        console.log(`User authenticated with ID: ${userId}, role: ${normalizedUser.role}`)

        // If user is an admin, redirect to admin page
        if (normalizedUser.role === 'admin') {
          console.log(`Admin user detected, redirecting to /admin`)
          router.push('/admin')
          return normalizedUser
        }

        // For customers, check if they belong to any group
        if (normalizedUser.role === 'customer') {
          console.log(`Customer user detected, checking for groups`)
          try {
            // Get the first group the user belongs to
            console.log(`Fetching groups for user ID: ${userId}`)
            const groupId = await getFirstUserGroupForRedirect(userId)
            console.log(`Group ID result: ${groupId}`)

            // If a group is found, redirect to the group dashboard
            if (groupId) {
              console.log(`Group found, redirecting to /groups/${groupId}`)
              // Use the correct path format for the groups directory
              router.push(`/groups/${groupId}`)
            } else {
              // If no group is found, redirect to profile
              console.log(`No groups found, redirecting to /profile`)
              router.push('/profile')
            }
          } catch (groupError) {
            // If there's an error getting the group, log it and redirect to profile
            console.error("Error checking user groups for redirect:", groupError)
            console.log(`Error occurred, redirecting to /profile`)
            router.push('/profile')
          }
          return normalizedUser
        }

        // For other roles, redirect to home
        console.log(`Other role detected, redirecting to /`)
        router.push('/')
      } else {
        console.log(`Not redirecting: redirectToGroup=${redirectToGroup}, user._id=${normalizedUser?._id || 'undefined'}`)
      }

      return normalizedUser
    } catch (error) {
      console.error("Login failed:", error)
      setError("Login failed. Please check your credentials and try again.")
      throw error
    } finally {
      setLoading(false)
    }
  }

  /**
   * Login for shopping - doesn't redirect the user
   * This function is specifically for allowing users to login and add products to cart
   * without being redirected to their group page
   */
  const loginForShopping = async (email: string, password: string, rememberMe: boolean = true): Promise<User> => {
    try {
      setLoading(true)
      setError(null)
      console.log(`Attempting to login user for shopping: ${email}`)

      const { user } = await loginUserForShopping(email, password, rememberMe)
      console.log(`Login for shopping successful, user:`, user)

      // The backend returns user with 'id', but our User type uses '_id'
      // We need to normalize this to match our User type
      const normalizedUser: User = {
        ...user,
        _id: (user as unknown as { id?: string })?.id || user._id || '', // Use id if available, fallback to _id
      }

      console.log(`User ID: ${normalizedUser._id}, Role: ${normalizedUser.role}`)
      setUser(normalizedUser)

      // No redirection happens here - this is the key difference from the regular login
      return normalizedUser
    } catch (error) {
      console.error("Login for shopping failed:", error)
      setError("Login failed. Please check your credentials and try again.")
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      setLoading(true)
      setError(null)
      await logoutUser(user?._id || null)
      setUser(null)
      router.push("/")
    } catch (error) {
      console.error("Logout failed:", error)
      setError("Logout failed. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const signup = async (name: string, email: string, phone: string, password: string, referralCode?: string): Promise<User> => {
    try {
      setLoading(true)
      setError(null)
      console.log(`Attempting to register user: ${email}`)

      const { user } = await registerUser(name, email, phone, password, referralCode)
      if (!user) {
        throw new Error("User registration failed: No user data received")
      }

      console.log(`Registration successful, user:`, user)

      // The backend returns user with 'id', but our User type uses '_id'
      // We need to normalize this to match our User type (same as login functions)
      const normalizedUser: User = {
        ...user,
        _id: (user as unknown as { id?: string })?.id || user._id || '', // Use id if available, fallback to _id
      }

      console.log(`Normalized User ID: ${normalizedUser._id}, Role: ${normalizedUser.role}`)
      setUser(normalizedUser)

      return normalizedUser
    } catch (error) {
      console.error("Signup failed:", error)
      setError(error instanceof Error ? error.message : "Signup failed. Please try again.")
      throw error
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        login,
        loginForShopping,
        logout,
        signup,
        loading,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export default AuthProvider


