'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { useAuth } from '@/context/AuthContext';
import { DisplayShoppingCart, DisplayCartItem } from '@/types/unifiedCart';
import { 
  useGetShoppingCartQuery,
  useAddToCartMutation,
  useUpdateCartItemMutation,
  useRemoveFromCartMutation,
  useClearCartMutation
} from '@/lib/redux/features/cart/cartApiSlice';

interface CartContextType {
  // Cart data
  cart: DisplayShoppingCart | null;
  cartItems: DisplayCartItem[];
  totalItems: number;
  subtotal: number;
  isLoading: boolean;
  error: string | null;
  
  // Cart actions
  addToCart: (productId: string, quantity: number) => Promise<void>;
  updateCartItem: (productId: string, quantity: number) => Promise<void>;
  removeFromCart: (productId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => void;
  
  // Stable data for UI (prevents flickering)
  stableCartData: {
    totalItems: number;
    subtotal: number;
    items: DisplayCartItem[];
  };
}

const CartContext = createContext<CartContextType | undefined>(undefined);

interface CartContextProviderProps {
  children: React.ReactNode;
  groupId: string | null;
}

export function CartContextProvider({ children, groupId }: CartContextProviderProps) {
  const { user } = useAuth();
  const userId = user?._id || '';
  
  // Local state for stability
  const [stableCartData, setStableCartData] = useState({
    totalItems: 0,
    subtotal: 0,
    items: [] as DisplayCartItem[]
  });
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Single cart query - the source of truth
  const { 
    data: cart, 
    isLoading, 
    error: queryError, 
    refetch 
  } = useGetShoppingCartQuery({
    userId,
    groupId: groupId || undefined
  }, {
    skip: !userId || !groupId,
    pollingInterval: 120000, // 2 minutes - very conservative
    refetchOnMountOrArgChange: true,
    refetchOnFocus: false,
    refetchOnReconnect: true,
  });

  // Mutations
  const [addToCartMutation] = useAddToCartMutation();
  const [updateCartItemMutation] = useUpdateCartItemMutation();
  const [removeFromCartMutation] = useRemoveFromCartMutation();
  const [clearCartMutation] = useClearCartMutation();

  // Derived data with memoization
  const cartItems = useMemo(() => cart?.items || [], [cart?.items]);
  const totalItems = useMemo(() => 
    cartItems.reduce((sum, item) => sum + item.quantity, 0), 
    [cartItems]
  );
  const subtotal = useMemo(() => 
    cartItems.reduce((sum, item) => sum + item.subtotal, 0), 
    [cartItems]
  );

  // Track initial load
  useEffect(() => {
    if (!isLoading && !hasInitiallyLoaded && userId && groupId) {
      setHasInitiallyLoaded(true);
    }
  }, [isLoading, hasInitiallyLoaded, userId, groupId]);

  // Update stable data with debouncing
  useEffect(() => {
    if (hasInitiallyLoaded && !isLoading) {
      const timeoutId = setTimeout(() => {
        setStableCartData({
          totalItems,
          subtotal,
          items: cartItems
        });
      }, 200); // 200ms debounce

      return () => clearTimeout(timeoutId);
    }
  }, [hasInitiallyLoaded, isLoading, totalItems, subtotal, cartItems]);

  // Error handling
  useEffect(() => {
    if (queryError) {
      setError(queryError.toString());
    } else {
      setError(null);
    }
  }, [queryError]);

  // Cart actions with error handling
  const addToCart = useCallback(async (productId: string, quantity: number) => {
    if (!userId || !groupId) return;
    
    try {
      await addToCartMutation({
        userId,
        groupId,
        productId,
        quantity
      }).unwrap();
    } catch (error) {
      console.error('Failed to add to cart:', error);
      setError('Failed to add item to cart');
    }
  }, [userId, groupId, addToCartMutation]);

  const updateCartItem = useCallback(async (productId: string, quantity: number) => {
    if (!userId || !groupId) return;
    
    try {
      await updateCartItemMutation({
        userId,
        groupId,
        productId,
        quantity
      }).unwrap();
    } catch (error) {
      console.error('Failed to update cart item:', error);
      setError('Failed to update item quantity');
    }
  }, [userId, groupId, updateCartItemMutation]);

  const removeFromCart = useCallback(async (productId: string) => {
    if (!userId || !groupId) return;
    
    try {
      await removeFromCartMutation({
        userId,
        groupId,
        productId
      }).unwrap();
    } catch (error) {
      console.error('Failed to remove from cart:', error);
      setError('Failed to remove item from cart');
    }
  }, [userId, groupId, removeFromCartMutation]);

  const clearCart = useCallback(async () => {
    if (!userId || !groupId) return;
    
    try {
      await clearCartMutation({
        userId,
        groupId
      }).unwrap();
    } catch (error) {
      console.error('Failed to clear cart:', error);
      setError('Failed to clear cart');
    }
  }, [userId, groupId, clearCartMutation]);

  const refreshCart = useCallback(() => {
    refetch();
  }, [refetch]);

  const contextValue = useMemo(() => ({
    cart,
    cartItems,
    totalItems,
    subtotal,
    isLoading,
    error,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart,
    stableCartData
  }), [
    cart,
    cartItems,
    totalItems,
    subtotal,
    isLoading,
    error,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart,
    stableCartData
  ]);

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
}

export function useCartContext() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCartContext must be used within a CartContextProvider');
  }
  return context;
}
