<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Group Order</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h2 {
            margin-top: 0;
        }
        button {
            padding: 8px 15px;
            background-color: #2A7C6C;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Group Order</h1>
    
    <div class="section">
        <h2>User Info</h2>
        <button onclick="getUserInfo()">Get User Info</button>
        <div id="userResult" class="result">Click the button to get user info...</div>
    </div>
    
    <div class="section">
        <h2>User Groups</h2>
        <button onclick="getUserGroups()">Get User Groups</button>
        <div id="groupsResult" class="result">Click the button to get user groups...</div>
    </div>
    
    <div class="section">
        <h2>Shopping Cart</h2>
        <button onclick="getCart()">Get Cart</button>
        <div id="cartResult" class="result">Click the button to get cart...</div>
    </div>
    
    <div class="section">
        <h2>Create Group Order</h2>
        <div class="form-group">
            <label for="groupSelect">Select Group:</label>
            <select id="groupSelect"></select>
        </div>
        
        <div class="form-group">
            <label for="customerName">Customer Name:</label>
            <input type="text" id="customerName" value="Winston Mhango">
        </div>
        
        <div class="form-group">
            <label for="customerEmail">Customer Email:</label>
            <input type="email" id="customerEmail" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="customerAddress">Address:</label>
            <input type="text" id="customerAddress" value="123 Test Street">
        </div>
        
        <div class="form-group">
            <label for="customerCity">City:</label>
            <input type="text" id="customerCity" value="Johannesburg">
        </div>
        
        <div class="form-group">
            <label for="customerCountry">Country:</label>
            <input type="text" id="customerCountry" value="South Africa">
        </div>
        
        <div class="form-group">
            <label for="customerPostalCode">Postal Code:</label>
            <input type="text" id="customerPostalCode" value="2000">
        </div>
        
        <div class="form-group">
            <label for="paymentMethod">Payment Method:</label>
            <select id="paymentMethod">
                <option value="credit_card">Credit Card</option>
                <option value="debit_card">Debit Card</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="cash">Cash</option>
            </select>
        </div>
        
        <button onclick="createGroupOrder()">Create Group Order</button>
        <div id="orderResult" class="result">Fill the form and click the button to create a group order...</div>
    </div>
    
    <script>
        let userId = '';
        let groups = [];
        
        async function getUserInfo() {
            const resultDiv = document.getElementById('userResult');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-client-type': 'web',
                    },
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.user && data.user._id) {
                    userId = data.user._id;
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function getUserGroups() {
            if (!userId) {
                alert('Please get user info first to get the user ID');
                return;
            }
            
            const resultDiv = document.getElementById('groupsResult');
            const groupSelect = document.getElementById('groupSelect');
            resultDiv.textContent = 'Loading...';
            groupSelect.innerHTML = '';
            
            try {
                const response = await fetch(`/api/users/groups?userId=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                groups = data;
                
                // Populate the group select dropdown
                if (data && data.length > 0) {
                    data.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group._id;
                        option.textContent = group.name;
                        groupSelect.appendChild(option);
                    });
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function getCart() {
            if (!userId) {
                alert('Please get user info first to get the user ID');
                return;
            }
            
            const resultDiv = document.getElementById('cartResult');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch(`/api/shopping-cart/get?userId=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function createGroupOrder() {
            if (!userId) {
                alert('Please get user info first to get the user ID');
                return;
            }
            
            const groupId = document.getElementById('groupSelect').value;
            if (!groupId) {
                alert('Please select a group');
                return;
            }
            
            const customerInfo = {
                name: document.getElementById('customerName').value,
                email: document.getElementById('customerEmail').value,
                address: document.getElementById('customerAddress').value,
                city: document.getElementById('customerCity').value,
                country: document.getElementById('customerCountry').value,
                postalCode: document.getElementById('customerPostalCode').value,
            };
            
            const paymentMethod = document.getElementById('paymentMethod').value;
            const resultDiv = document.getElementById('orderResult');
            resultDiv.textContent = 'Creating group order...';
            
            try {
                const response = await fetch('/api/group-order/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId,
                        groupId,
                        customerInfo,
                        paymentMethod,
                    }),
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        // Initialize by getting user info when the page loads
        window.onload = getUserInfo;
    </script>
</body>
</html>
