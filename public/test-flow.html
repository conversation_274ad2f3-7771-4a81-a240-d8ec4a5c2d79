<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockvelMarket Test Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .step.active {
            border-color: #2A7C6C;
            background-color: #f0f9f7;
        }
        .step.completed {
            border-color: #4CAF50;
            background-color: #f0fff0;
        }
        h2 {
            margin-top: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #2A7C6C;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #2A7C6C;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .product-card {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
            background-color: white;
        }
        .product-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 3px;
        }
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            background-color: white;
        }
        .status {
            font-weight: bold;
            margin-top: 10px;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .loading {
            color: #2196F3;
        }
    </style>
</head>
<body>
    <h1>StockvelMarket Test Flow</h1>
    <p>This page will guide you through testing the group shopping functionality of StockvelMarket.</p>
    
    <div id="step1" class="step active">
        <h2><span class="step-number">1</span> Login <span id="step1Status"></span></h2>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="@Admin2020">
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" id="rememberMe" checked>
                Remember Me
            </label>
        </div>
        <button onclick="login()">Login</button>
        <div id="loginResult" class="result">Enter your credentials and click Login</div>
    </div>
    
    <div id="step2" class="step">
        <h2><span class="step-number">2</span> Get User Groups <span id="step2Status"></span></h2>
        <button onclick="getUserGroups()" disabled id="getUserGroupsBtn">Get User Groups</button>
        <div id="groupsResult" class="result">Login first to get user groups</div>
        <div class="form-group" style="display: none;" id="groupSelectContainer">
            <label for="groupSelect">Select Group for Shopping:</label>
            <select id="groupSelect"></select>
        </div>
    </div>
    
    <div id="step3" class="step">
        <h2><span class="step-number">3</span> Browse Products <span id="step3Status"></span></h2>
        <button onclick="getProducts()" disabled id="getProductsBtn">Browse Products</button>
        <div id="productsResult" class="result">Select a group first to browse products</div>
        <div id="productGrid" class="product-grid"></div>
    </div>
    
    <div id="step4" class="step">
        <h2><span class="step-number">4</span> View Cart <span id="step4Status"></span></h2>
        <button onclick="getCart()" disabled id="getCartBtn">View Cart</button>
        <div id="cartResult" class="result">Add products to cart first</div>
        <div id="cartItems"></div>
    </div>
    
    <div id="step5" class="step">
        <h2><span class="step-number">5</span> Create Group Order <span id="step5Status"></span></h2>
        <div class="form-group">
            <label for="customerName">Customer Name:</label>
            <input type="text" id="customerName" value="Winston Mhango">
        </div>
        <div class="form-group">
            <label for="customerEmail">Customer Email:</label>
            <input type="email" id="customerEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label for="customerAddress">Address:</label>
            <input type="text" id="customerAddress" value="123 Test Street">
        </div>
        <div class="form-group">
            <label for="customerCity">City:</label>
            <input type="text" id="customerCity" value="Johannesburg">
        </div>
        <div class="form-group">
            <label for="customerCountry">Country:</label>
            <input type="text" id="customerCountry" value="South Africa">
        </div>
        <div class="form-group">
            <label for="customerPostalCode">Postal Code:</label>
            <input type="text" id="customerPostalCode" value="2000">
        </div>
        <div class="form-group">
            <label for="paymentMethod">Payment Method:</label>
            <select id="paymentMethod">
                <option value="credit_card">Credit Card</option>
                <option value="debit_card">Debit Card</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="cash">Cash</option>
            </select>
        </div>
        <button onclick="createGroupOrder()" disabled id="createOrderBtn">Create Group Order</button>
        <div id="orderResult" class="result">View your cart first before creating an order</div>
    </div>
    
    <script>
        // Global variables
        let userId = '';
        let selectedGroupId = '';
        
        // Step 1: Login
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            const resultDiv = document.getElementById('loginResult');
            const statusSpan = document.getElementById('step1Status');
            
            setStepStatus(1, 'loading', 'Logging in...');
            resultDiv.textContent = 'Logging in...';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-client-type': 'web',
                    },
                    body: JSON.stringify({ email, password, rememberMe }),
                });
                
                const data = await response.json();
                resultDiv.textContent = 'Login response:\n' + JSON.stringify(data, null, 2);
                
                if (data.user) {
                    userId = data.user.id || data.user._id;
                    setStepStatus(1, 'success', '✓');
                    completeStep(1);
                    activateStep(2);
                    document.getElementById('getUserGroupsBtn').disabled = false;
                } else {
                    setStepStatus(1, 'error', '✗');
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                setStepStatus(1, 'error', '✗');
            }
        }
        
        // Step 2: Get User Groups
        async function getUserGroups() {
            if (!userId) {
                alert('Please login first');
                return;
            }
            
            const resultDiv = document.getElementById('groupsResult');
            const groupSelect = document.getElementById('groupSelect');
            const statusSpan = document.getElementById('step2Status');
            const groupSelectContainer = document.getElementById('groupSelectContainer');
            
            setStepStatus(2, 'loading', 'Loading groups...');
            resultDiv.textContent = 'Loading user groups...';
            groupSelect.innerHTML = '';
            
            try {
                const response = await fetch(`/api/users/groups?userId=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                
                if (data && data.length > 0) {
                    resultDiv.textContent = `Found ${data.length} groups:\n` + JSON.stringify(data, null, 2);
                    
                    // Populate the group select dropdown
                    data.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group._id;
                        option.textContent = group.name;
                        groupSelect.appendChild(option);
                    });
                    
                    groupSelectContainer.style.display = 'block';
                    selectedGroupId = data[0]._id;
                    
                    setStepStatus(2, 'success', '✓');
                    completeStep(2);
                    activateStep(3);
                    document.getElementById('getProductsBtn').disabled = false;
                } else {
                    resultDiv.textContent = 'No groups found for this user';
                    setStepStatus(2, 'error', '✗');
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                setStepStatus(2, 'error', '✗');
            }
        }
        
        // Step 3: Browse Products
        async function getProducts() {
            const resultDiv = document.getElementById('productsResult');
            const productGrid = document.getElementById('productGrid');
            const statusSpan = document.getElementById('step3Status');
            
            setStepStatus(3, 'loading', 'Loading products...');
            resultDiv.textContent = 'Loading products...';
            productGrid.innerHTML = '';
            
            try {
                const response = await fetch('/api/products/get-all', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                
                if (data && data.length > 0) {
                    resultDiv.textContent = `Found ${data.length} products`;
                    
                    // Display products in a grid
                    data.slice(0, 6).forEach(product => {
                        const card = document.createElement('div');
                        card.className = 'product-card';
                        
                        const imageUrl = product.image.startsWith('http') 
                            ? product.image 
                            : `/api/images/${product.image}`;
                        
                        card.innerHTML = `
                            <img src="${imageUrl}" alt="${product.name}" onerror="this.src='/products/placeholder.jpg'">
                            <h3>${product.name}</h3>
                            <p>R${product.price.toFixed(2)}</p>
                            <button onclick="addToCart('${product._id}')">Add to Cart</button>
                        `;
                        
                        productGrid.appendChild(card);
                    });
                    
                    setStepStatus(3, 'success', '✓');
                    completeStep(3);
                    document.getElementById('getCartBtn').disabled = false;
                } else {
                    resultDiv.textContent = 'No products found';
                    setStepStatus(3, 'error', '✗');
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                setStepStatus(3, 'error', '✗');
            }
        }
        
        // Add to Cart
        async function addToCart(productId) {
            if (!userId || !selectedGroupId) {
                alert('Missing user ID or group ID');
                return;
            }
            
            const groupId = document.getElementById('groupSelect').value || selectedGroupId;
            
            try {
                const response = await fetch('/api/shopping-cart/add-item', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId,
                        productId,
                        quantity: 1,
                        groupId
                    }),
                });
                
                const data = await response.json();
                alert(`Product added to cart!`);
                
                // Activate the cart step
                activateStep(4);
            } catch (error) {
                alert('Error adding to cart: ' + error.message);
            }
        }
        
        // Step 4: View Cart
        async function getCart() {
            if (!userId) {
                alert('Please login first');
                return;
            }
            
            const resultDiv = document.getElementById('cartResult');
            const cartItemsDiv = document.getElementById('cartItems');
            const statusSpan = document.getElementById('step4Status');
            
            setStepStatus(4, 'loading', 'Loading cart...');
            resultDiv.textContent = 'Loading cart...';
            cartItemsDiv.innerHTML = '';
            
            try {
                const response = await fetch(`/api/shopping-cart/get?userId=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                
                if (data && data.items && data.items.length > 0) {
                    resultDiv.textContent = `Cart has ${data.items.length} items. Total: R${data.total.toFixed(2)}`;
                    
                    // Display cart items
                    data.items.forEach(item => {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'cart-item';
                        
                        itemDiv.innerHTML = `
                            <div>
                                <strong>${item.product.name}</strong>
                                <p>Quantity: ${item.quantity}</p>
                                <p>Price: R${item.product.price.toFixed(2)}</p>
                            </div>
                            <button onclick="removeFromCart('${item.product._id}')">Remove</button>
                        `;
                        
                        cartItemsDiv.appendChild(itemDiv);
                    });
                    
                    setStepStatus(4, 'success', '✓');
                    completeStep(4);
                    activateStep(5);
                    document.getElementById('createOrderBtn').disabled = false;
                } else {
                    resultDiv.textContent = 'Your cart is empty';
                    cartItemsDiv.innerHTML = '<p>Your cart is empty. Please add some products first.</p>';
                    setStepStatus(4, 'error', '✗');
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                setStepStatus(4, 'error', '✗');
            }
        }
        
        // Remove from Cart
        async function removeFromCart(productId) {
            if (!userId || !selectedGroupId) {
                alert('Missing user ID or group ID');
                return;
            }
            
            const groupId = document.getElementById('groupSelect').value || selectedGroupId;
            
            try {
                const response = await fetch('/api/shopping-cart/remove-item', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId,
                        productId,
                        groupId
                    }),
                });
                
                const data = await response.json();
                alert(`Product removed from cart`);
                
                // Refresh the cart
                getCart();
            } catch (error) {
                alert('Error removing from cart: ' + error.message);
            }
        }
        
        // Step 5: Create Group Order
        async function createGroupOrder() {
            if (!userId) {
                alert('Please login first');
                return;
            }
            
            const groupId = document.getElementById('groupSelect').value || selectedGroupId;
            if (!groupId) {
                alert('Please select a group');
                return;
            }
            
            const customerInfo = {
                name: document.getElementById('customerName').value,
                email: document.getElementById('customerEmail').value,
                address: document.getElementById('customerAddress').value,
                city: document.getElementById('customerCity').value,
                country: document.getElementById('customerCountry').value,
                postalCode: document.getElementById('customerPostalCode').value,
            };
            
            const paymentMethod = document.getElementById('paymentMethod').value;
            const resultDiv = document.getElementById('orderResult');
            const statusSpan = document.getElementById('step5Status');
            
            setStepStatus(5, 'loading', 'Creating order...');
            resultDiv.textContent = 'Creating group order...';
            
            try {
                const response = await fetch('/api/group-order/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId,
                        groupId,
                        customerInfo,
                        paymentMethod,
                    }),
                });
                
                const data = await response.json();
                
                if (data && data._id) {
                    resultDiv.textContent = `Group order created successfully!\n` + JSON.stringify(data, null, 2);
                    setStepStatus(5, 'success', '✓');
                    completeStep(5);
                } else {
                    resultDiv.textContent = `Error creating group order: ${JSON.stringify(data)}`;
                    setStepStatus(5, 'error', '✗');
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                setStepStatus(5, 'error', '✗');
            }
        }
        
        // Helper functions
        function setStepStatus(stepNumber, status, text) {
            const statusSpan = document.getElementById(`step${stepNumber}Status`);
            statusSpan.textContent = text || '';
            statusSpan.className = status;
        }
        
        function activateStep(stepNumber) {
            for (let i = 1; i <= 5; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active');
            }
            const step = document.getElementById(`step${stepNumber}`);
            step.classList.add('active');
        }
        
        function completeStep(stepNumber) {
            const step = document.getElementById(`step${stepNumber}`);
            step.classList.add('completed');
        }
        
        // Initialize by activating step 1
        window.onload = function() {
            activateStep(1);
        };
    </script>
</body>
</html>
