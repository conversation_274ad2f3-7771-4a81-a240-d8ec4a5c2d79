<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cart Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h2 {
            margin-top: 0;
        }
        button {
            padding: 8px 15px;
            background-color: #2A7C6C;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .product-card {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
        }
        .product-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 3px;
        }
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Cart Functionality</h1>
    
    <div class="section">
        <h2>User Info</h2>
        <button onclick="getUserInfo()">Get User Info</button>
        <div id="userResult" class="result">Click the button to get user info...</div>
    </div>
    
    <div class="section">
        <h2>User Groups</h2>
        <button onclick="getUserGroups()">Get User Groups</button>
        <div id="groupsResult" class="result">Click the button to get user groups...</div>
    </div>
    
    <div class="section">
        <h2>Products</h2>
        <button onclick="getProducts()">Get Products</button>
        <div id="productsResult" class="result">Click the button to get products...</div>
        <div id="productGrid" class="product-grid"></div>
    </div>
    
    <div class="section">
        <h2>Shopping Cart</h2>
        <button onclick="getCart()">Get Cart</button>
        <div id="cartResult" class="result">Click the button to get cart...</div>
        <div id="cartItems"></div>
    </div>
    
    <script>
        let userId = '';
        let selectedGroupId = '';
        
        async function getUserInfo() {
            const resultDiv = document.getElementById('userResult');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-client-type': 'web',
                    },
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.user && data.user._id) {
                    userId = data.user._id;
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function getUserGroups() {
            if (!userId) {
                alert('Please get user info first to get the user ID');
                return;
            }
            
            const resultDiv = document.getElementById('groupsResult');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch(`/api/users/groups?userId=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data && data.length > 0) {
                    selectedGroupId = data[0]._id;
                    resultDiv.textContent += '\n\nSelected Group ID: ' + selectedGroupId;
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function getProducts() {
            const resultDiv = document.getElementById('productsResult');
            const productGrid = document.getElementById('productGrid');
            resultDiv.textContent = 'Loading...';
            productGrid.innerHTML = '';
            
            try {
                const response = await fetch('/api/products/get-all', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                resultDiv.textContent = `Found ${data.length} products`;
                
                // Display products in a grid
                data.slice(0, 6).forEach(product => {
                    const card = document.createElement('div');
                    card.className = 'product-card';
                    
                    const imageUrl = product.image.startsWith('http') 
                        ? product.image 
                        : `/api/images/${product.image}`;
                    
                    card.innerHTML = `
                        <img src="${imageUrl}" alt="${product.name}" onerror="this.src='/products/placeholder.jpg'">
                        <h3>${product.name}</h3>
                        <p>R${product.price.toFixed(2)}</p>
                        <button onclick="addToCart('${product._id}')">Add to Cart</button>
                    `;
                    
                    productGrid.appendChild(card);
                });
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function addToCart(productId) {
            if (!userId) {
                alert('Please get user info first to get the user ID');
                return;
            }
            
            if (!selectedGroupId) {
                alert('Please get user groups first to get a group ID');
                return;
            }
            
            try {
                const response = await fetch('/api/shopping-cart/add-item', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId,
                        productId,
                        quantity: 1,
                        groupId: selectedGroupId
                    }),
                });
                
                const data = await response.json();
                alert(`Product added to cart: ${JSON.stringify(data)}`);
                
                // Refresh the cart
                getCart();
            } catch (error) {
                alert('Error adding to cart: ' + error.message);
            }
        }
        
        async function getCart() {
            if (!userId) {
                alert('Please get user info first to get the user ID');
                return;
            }
            
            const resultDiv = document.getElementById('cartResult');
            const cartItemsDiv = document.getElementById('cartItems');
            resultDiv.textContent = 'Loading...';
            cartItemsDiv.innerHTML = '';
            
            try {
                const response = await fetch(`/api/shopping-cart/get?userId=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
                
                // Display cart items
                if (data.items && data.items.length > 0) {
                    data.items.forEach(item => {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'cart-item';
                        
                        itemDiv.innerHTML = `
                            <div>
                                <strong>${item.product.name}</strong>
                                <p>Quantity: ${item.quantity}</p>
                                <p>Price: R${item.product.price.toFixed(2)}</p>
                            </div>
                            <button onclick="removeFromCart('${item.product._id}')">Remove</button>
                        `;
                        
                        cartItemsDiv.appendChild(itemDiv);
                    });
                } else {
                    cartItemsDiv.innerHTML = '<p>Your cart is empty</p>';
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function removeFromCart(productId) {
            if (!userId || !selectedGroupId) {
                alert('Missing user ID or group ID');
                return;
            }
            
            try {
                const response = await fetch('/api/shopping-cart/remove-item', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId,
                        productId,
                        groupId: selectedGroupId
                    }),
                });
                
                const data = await response.json();
                alert(`Product removed from cart: ${JSON.stringify(data)}`);
                
                // Refresh the cart
                getCart();
            } catch (error) {
                alert('Error removing from cart: ' + error.message);
            }
        }
        
        // Initialize by getting user info when the page loads
        window.onload = getUserInfo;
    </script>
</body>
</html>
