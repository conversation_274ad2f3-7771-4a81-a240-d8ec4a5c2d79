

// middleware.ts

import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import { getCorsHeaders } from './lib/cors'; 

const secret = process.env.JWT_SECRET!; // Ensure this is set in your environment variables

export async function middleware(req: NextRequest) {
  const origin = req.headers.get('origin');
  const corsHeaders = getCorsHeaders(origin);

  // <PERSON>le preflight (OPTIONS) requests
  if (req.method === 'OPTIONS') {
    return NextResponse.json({}, { headers: corsHeaders, status: 200 });
  }

  const url = req.nextUrl.clone();

  // Handle referral code
  const referralCode = url.searchParams.get('referralCode');
  if (referralCode) {
    const response = NextResponse.redirect(url);
    response.cookies.set('referralCode', referralCode, {
      maxAge: 24 * 60 * 60, // 1 day in seconds
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });

    Object.entries(corsHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }

  // Define protected routes
  const protectedRoutes = ['/admin', '/profile'];
  const isProtectedRoute = protectedRoutes.some((route) =>
    req.nextUrl.pathname.startsWith(route)
  );

  if (isProtectedRoute) {
    let accessToken: string | undefined;

    // Determine client type; default to "web"
    const clientType = req.headers.get('x-client-type') || 'web';

    // If this is a "web" request, read the access token from cookies
    if (clientType === 'web') {
      accessToken = req.cookies.get('accessToken')?.value;
    } else {
      // Expect Authorization: Bearer <token> for "mobile"
      const authHeader = req.headers.get('authorization');
      if (authHeader?.startsWith('Bearer ')) {
        accessToken = authHeader.substring(7);
      }
    }

    if (!accessToken) {
      // Redirect to homepage if no access token is found
      const response = NextResponse.redirect(new URL('/', req.url));
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      return response;
    }

    try {
      // Verify the access token
      const { payload } = await jwtVerify(
        accessToken,
        new TextEncoder().encode(secret)
      );

      // Check for admin role when accessing /admin routes
      if (
        req.nextUrl.pathname.startsWith('/admin') &&
        payload.role !== 'admin'
      ) {
        // Redirect non-admin users to /dashboard
        const response = NextResponse.redirect(
          new URL('/profile', req.url)
        );
        Object.entries(corsHeaders).forEach(([key, value]) => {
          response.headers.set(key, value);
        });
        return response;
      }

      // Token is valid and user has appropriate role; proceed to the requested route
      const response = NextResponse.next();
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      return response;
    } catch (error) {
      console.error('JWT verification failed:', error);

      // Redirect to homepage on token verification failure
      const response = NextResponse.redirect(new URL('/', req.url));
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      return response;
    }
  }

  // For non-protected routes, proceed as normal and append CORS headers
  const response = NextResponse.next();
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

export const config = {
  matcher: [
    '/admin/:path*',
    '/profile/:path*',
    // Add other protected routes as needed
  ],
};


