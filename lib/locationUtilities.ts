// lib/locationUtilities.ts

import { Province, IProvince } from '@/models/Province';
import { City, ICity } from '@/models/City';
import { Township, ITownship } from '@/models/Township';
import { Location, ILocation } from '@/models/Location';
import mongoose from 'mongoose';

// ===== PROVINCE CRUD OPERATIONS =====

/**
 * CREATE a new Province
 */
export async function createProvince(name: string, code: string): Promise<IProvince> {
  const province = new Province({ name, code });
  return await province.save();
}

/**
 * READ: Get all Provinces
 */
export async function getAllProvinces(): Promise<IProvince[]> {
  return Province.find({ isActive: true }).sort({ name: 1 }).exec();
}

/**
 * READ: Get a single Province by ID
 */
export async function getProvinceById(id: string): Promise<IProvince | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Province.findById(id).exec();
}

/**
 * READ: Get a Province by code
 */
export async function getProvinceByCode(code: string): Promise<IProvince | null> {
  return Province.findOne({ code: code.toUpperCase(), isActive: true }).exec();
}

/**
 * UPDATE a Province by ID
 */
export async function updateProvince(
  id: string,
  updateData: Partial<IProvince>
): Promise<IProvince | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Province.findByIdAndUpdate(id, updateData, { new: true }).exec();
}

/**
 * DELETE a Province by ID (soft delete)
 */
export async function deleteProvince(id: string): Promise<IProvince | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Province.findByIdAndUpdate(id, { isActive: false }, { new: true }).exec();
}

// ===== CITY CRUD OPERATIONS =====

/**
 * CREATE a new City
 */
export async function createCity(name: string, provinceId: string): Promise<ICity> {
  const city = new City({ 
    name, 
    provinceId: new mongoose.Types.ObjectId(provinceId) 
  });
  return await city.save();
}

/**
 * READ: Get all Cities by Province
 */
export async function getCitiesByProvince(provinceId: string): Promise<ICity[]> {
  if (!mongoose.Types.ObjectId.isValid(provinceId)) return [];
  return City.find({ 
    provinceId: new mongoose.Types.ObjectId(provinceId), 
    isActive: true 
  }).sort({ name: 1 }).exec();
}

/**
 * READ: Get a single City by ID
 */
export async function getCityById(id: string): Promise<ICity | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return City.findById(id).populate('provinceId').exec();
}

/**
 * UPDATE a City by ID
 */
export async function updateCity(
  id: string,
  updateData: Partial<ICity>
): Promise<ICity | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return City.findByIdAndUpdate(id, updateData, { new: true }).exec();
}

/**
 * DELETE a City by ID (soft delete)
 */
export async function deleteCity(id: string): Promise<ICity | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return City.findByIdAndUpdate(id, { isActive: false }, { new: true }).exec();
}

// ===== TOWNSHIP CRUD OPERATIONS =====

/**
 * CREATE a new Township
 */
export async function createTownship(name: string, cityId: string): Promise<ITownship> {
  const township = new Township({ 
    name, 
    cityId: new mongoose.Types.ObjectId(cityId) 
  });
  return await township.save();
}

/**
 * READ: Get all Townships by City
 */
export async function getTownshipsByCity(cityId: string): Promise<ITownship[]> {
  if (!mongoose.Types.ObjectId.isValid(cityId)) return [];
  return Township.find({ 
    cityId: new mongoose.Types.ObjectId(cityId), 
    isActive: true 
  }).sort({ name: 1 }).exec();
}

/**
 * READ: Get a single Township by ID
 */
export async function getTownshipById(id: string): Promise<ITownship | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Township.findById(id).populate({
    path: 'cityId',
    populate: {
      path: 'provinceId'
    }
  }).exec();
}

/**
 * UPDATE a Township by ID
 */
export async function updateTownship(
  id: string,
  updateData: Partial<ITownship>
): Promise<ITownship | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Township.findByIdAndUpdate(id, updateData, { new: true }).exec();
}

/**
 * DELETE a Township by ID (soft delete)
 */
export async function deleteTownship(id: string): Promise<ITownship | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Township.findByIdAndUpdate(id, { isActive: false }, { new: true }).exec();
}

// ===== LOCATION CRUD OPERATIONS =====

/**
 * CREATE a new Location
 */
export async function createLocation(
  name: string, 
  townshipId: string, 
  description?: string
): Promise<ILocation> {
  const location = new Location({ 
    name, 
    townshipId: new mongoose.Types.ObjectId(townshipId),
    description 
  });
  return await location.save();
}

/**
 * READ: Get all Locations by Township
 */
export async function getLocationsByTownship(townshipId: string): Promise<ILocation[]> {
  if (!mongoose.Types.ObjectId.isValid(townshipId)) return [];
  return Location.find({ 
    townshipId: new mongoose.Types.ObjectId(townshipId), 
    isActive: true 
  }).sort({ name: 1 }).exec();
}

/**
 * READ: Get a single Location by ID
 */
export async function getLocationById(id: string): Promise<ILocation | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Location.findById(id).populate({
    path: 'townshipId',
    populate: {
      path: 'cityId',
      populate: {
        path: 'provinceId'
      }
    }
  }).exec();
}

/**
 * UPDATE a Location by ID
 */
export async function updateLocation(
  id: string,
  updateData: Partial<ILocation>
): Promise<ILocation | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Location.findByIdAndUpdate(id, updateData, { new: true }).exec();
}

/**
 * DELETE a Location by ID (soft delete)
 */
export async function deleteLocation(id: string): Promise<ILocation | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Location.findByIdAndUpdate(id, { isActive: false }, { new: true }).exec();
}

// ===== HELPER FUNCTIONS =====

/**
 * Get full location hierarchy for a location ID
 */
export async function getLocationHierarchy(locationId: string) {
  if (!mongoose.Types.ObjectId.isValid(locationId)) return null;
  
  const location = await Location.findById(locationId).populate({
    path: 'townshipId',
    populate: {
      path: 'cityId',
      populate: {
        path: 'provinceId'
      }
    }
  }).exec();
  
  return location;
}

/**
 * Search locations by name across all levels
 */
export async function searchLocationsByName(searchTerm: string, limit: number = 10) {
  const searchRegex = new RegExp(searchTerm, 'i');
  
  const [provinces, cities, townships, locations] = await Promise.all([
    Province.find({ name: searchRegex, isActive: true }).limit(limit),
    City.find({ name: searchRegex, isActive: true }).populate('provinceId').limit(limit),
    Township.find({ name: searchRegex, isActive: true }).populate({
      path: 'cityId',
      populate: { path: 'provinceId' }
    }).limit(limit),
    Location.find({ name: searchRegex, isActive: true }).populate({
      path: 'townshipId',
      populate: {
        path: 'cityId',
        populate: { path: 'provinceId' }
      }
    }).limit(limit)
  ]);
  
  return {
    provinces,
    cities,
    townships,
    locations
  };
}

/**
 * Get location statistics
 */
export async function getLocationStats() {
  const [provinceCount, cityCount, townshipCount, locationCount] = await Promise.all([
    Province.countDocuments({ isActive: true }),
    City.countDocuments({ isActive: true }),
    Township.countDocuments({ isActive: true }),
    Location.countDocuments({ isActive: true })
  ]);
  
  return {
    provinces: provinceCount,
    cities: cityCount,
    townships: townshipCount,
    locations: locationCount
  };
}
