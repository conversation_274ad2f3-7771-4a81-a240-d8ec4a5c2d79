// lib/redux/hooks/useGroupMembership.ts
import { useCallback, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '@/lib/redux/hooks';
import {
  selectUserGroups,
  selectCurrentGroupId,
  selectCurrentGroup,
  setCurrentGroupId,
  setUserGroups
} from '@/lib/redux/features/groupMembership/groupMembershipSlice';
import {
  useListUserGroupsQuery
  // Commented out unused exports
  // useCheckGroupMembershipQuery,
  // useCheckMembershipByEmailQuery,
  // useCheckAnyGroupMembershipQuery
} from '@/lib/redux/features/groupMembership/groupMembershipApiSlice';
// Commented out unused type
// import type { StokvelGroup } from '@/types/stokvelgroup';

export function useGroupMembership(userId?: string) {
  const dispatch = useAppDispatch();
  const userGroups = useAppSelector(selectUserGroups);
  const currentGroupId = useAppSelector(selectCurrentGroupId);
  const currentGroup = useAppSelector(selectCurrentGroup);

  // For debugging
  console.log('useGroupMembership called with userId:', userId);
  console.log('Current userGroups in state:', userGroups);

  // Fetch user groups if userId is provided
  const {
    data: fetchedGroups, // Remove underscore to use the data
    isLoading,
    error,
    refetch
  } = useListUserGroupsQuery(userId || '', {
    skip: !userId,
    // RTK Query automatically handles retries
  });

  // If we have fetched groups but they're not in the Redux store, update the store
  useEffect(() => {
    if (fetchedGroups && fetchedGroups.length > 0 && userGroups.length === 0) {
      console.log('Updating userGroups in Redux store:', fetchedGroups);
      dispatch(setUserGroups(fetchedGroups));
    }
  }, [fetchedGroups, userGroups.length, dispatch]);

  // Log any errors with more details
  if (error) {
    console.error('Error fetching user groups:', {
      error,
      userId
    });
  }

  // Set the current group
  const setCurrentGroup = useCallback((groupId: string | null) => {
    dispatch(setCurrentGroupId(groupId));
  }, [dispatch]);

  // Check if user is a member of a specific group
  const checkGroupMembership = useCallback((groupId: string) => {
    if (!userId) return false;
    return userGroups.some(group => group._id === groupId);
  }, [userId, userGroups]);

  return {
    userGroups,
    currentGroupId,
    currentGroup,
    isLoading,
    error,
    setCurrentGroup,
    checkGroupMembership,
    refetchGroups: refetch
  };
}

export default useGroupMembership;
