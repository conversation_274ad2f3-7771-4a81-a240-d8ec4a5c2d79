// lib/redux/hooks/useGroupSavings.ts
import { useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '@/lib/redux/hooks';
import {
  selectSavingsGoals,
  selectContributions,
  selectSelectedGoalId,
  selectSelectedGoal,
  // Commented out unused selectors
  // selectGoalById,
  // selectGoalProgress,
  // selectContributionsByGoalId,
  setSelectedGoalId
} from '@/lib/redux/features/groupSavings/groupSavingsSlice';
import {
  useGetGroupSavingsGoalsQuery,
  // Commented out unused queries
  // useGetSavingsGoalByIdQuery,
  useCreateSavingsGoalMutation,
  useUpdateSavingsGoalMutation,
  useDeleteSavingsGoalMutation,
  // useGetGoalContributionsQuery,
  useAddContributionMutation,
  // useGetUserContributionsQuery,
  CreateSavingsGoalInput,
  UpdateSavingsGoalInput,
  AddContributionInput
} from '@/lib/redux/features/groupSavings/groupSavingsApiSlice';

export function useGroupSavings(groupId?: string) {
  const dispatch = useAppDispatch();
  const savingsGoals = useAppSelector(selectSavingsGoals);
  const contributions = useAppSelector(selectContributions);
  const selectedGoalId = useAppSelector(selectSelectedGoalId);
  const selectedGoal = useAppSelector(selectSelectedGoal);

  // Fetch savings goals if groupId is provided
  const {
    data: _fetchedGoals, // Prefix with underscore to mark as unused
    isLoading: isLoadingGoals,
    error: goalsError,
    refetch: refetchGoals
  } = useGetGroupSavingsGoalsQuery(groupId || '', { skip: !groupId });

  // Create savings goal mutation
  const [createSavingsGoal, { isLoading: isCreating }] = useCreateSavingsGoalMutation();

  // Update savings goal mutation
  const [updateSavingsGoal, { isLoading: isUpdating }] = useUpdateSavingsGoalMutation();

  // Delete savings goal mutation
  const [deleteSavingsGoal, { isLoading: isDeleting }] = useDeleteSavingsGoalMutation();

  // Add contribution mutation
  const [addContribution, { isLoading: isAddingContribution }] = useAddContributionMutation();

  // Set the selected goal
  const setSelectedGoal = useCallback((goalId: string | null) => {
    dispatch(setSelectedGoalId(goalId));
  }, [dispatch]);

  // Get a goal by ID
  const getGoalById = useCallback((goalId: string) => {
    return savingsGoals.find(goal => goal._id === goalId) || null;
  }, [savingsGoals]);

  // Get goal progress
  const getGoalProgress = useCallback((goalId: string) => {
    const goal = getGoalById(goalId);
    if (!goal) return 0;
    return Math.min((goal.currentAmount / goal.targetAmount) * 100, 100);
  }, [getGoalById]);

  // Get contributions for a goal
  const getContributionsByGoalId = useCallback((goalId: string) => {
    return contributions.filter(contribution => contribution.goalId === goalId);
  }, [contributions]);

  // Create a new savings goal
  const handleCreateSavingsGoal = useCallback(async (data: Omit<CreateSavingsGoalInput, 'groupId'>) => {
    if (!groupId) throw new Error('Group ID is required');

    try {
      const result = await createSavingsGoal({
        ...data,
        groupId
      }).unwrap();

      return result;
    } catch (error) {
      console.error('Failed to create savings goal:', error);
      throw error;
    }
  }, [groupId, createSavingsGoal]);

  // Update a savings goal
  const handleUpdateSavingsGoal = useCallback(async (data: UpdateSavingsGoalInput) => {
    try {
      const result = await updateSavingsGoal(data).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to update savings goal:', error);
      throw error;
    }
  }, [updateSavingsGoal]);

  // Delete a savings goal
  const handleDeleteSavingsGoal = useCallback(async (goalId: string) => {
    try {
      await deleteSavingsGoal(goalId).unwrap();
      return true;
    } catch (error) {
      console.error('Failed to delete savings goal:', error);
      throw error;
    }
  }, [deleteSavingsGoal]);

  // Add a contribution
  const handleAddContribution = useCallback(async (data: Omit<AddContributionInput, 'goalId'>, goalId: string) => {
    try {
      const result = await addContribution({
        ...data,
        goalId
      }).unwrap();

      return result;
    } catch (error) {
      console.error('Failed to add contribution:', error);
      throw error;
    }
  }, [addContribution]);

  return {
    savingsGoals,
    contributions,
    selectedGoalId,
    selectedGoal,
    isLoadingGoals,
    goalsError,
    isCreating,
    isUpdating,
    isDeleting,
    isAddingContribution,
    setSelectedGoal,
    getGoalById,
    getGoalProgress,
    getContributionsByGoalId,
    createSavingsGoal: handleCreateSavingsGoal,
    updateSavingsGoal: handleUpdateSavingsGoal,
    deleteSavingsGoal: handleDeleteSavingsGoal,
    addContribution: handleAddContribution,
    refetchGoals
  };
}

export default useGroupSavings;
