// lib/redux/hooks/useAuthWithSignup.ts
import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';

export function useAuthWithSignup() {
  const auth = useAuth();
  const [isSigningUp, setIsSigningUp] = useState(false);

  const signup = async (name: string, email: string, phone: string, password: string) => {
    try {
      setIsSigningUp(true);
      const result = await auth.signup(name, email, phone, password);
      return result;
    } finally {
      setIsSigningUp(false);
    }
  };

  return {
    ...auth,
    signup,
    isSigningUp,
  };
}
