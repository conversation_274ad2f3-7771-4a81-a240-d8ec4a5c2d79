// lib/redux/hooks/useGroups.ts
import { useCallback } from 'react';
import { useAppSelector, useAppDispatch } from '@/lib/redux/hooks';
import {
  selectAllGroups,
  selectSelectedGroupId,
  selectSelectedGroup,
  // Commented out unused selector
  // selectGroupById,
  setSelectedGroupId
} from '@/lib/redux/features/groups/groupsSlice';
import {
  useGetAllStokvelGroupsQuery,
  // Commented out unused query
  // useCheckUserByEmailQuery,
  useJoinGroupMutation,
  useDeleteGroupMutation,
  useUpdateGroupMutation,
  UpdateGroupInput
} from '@/lib/redux/features/groups/groupsApiSlice';

export function useGroups() {
  const dispatch = useAppDispatch();
  const allGroups = useAppSelector(selectAllGroups);
  const selectedGroupId = useAppSelector(selectSelectedGroupId);
  const selectedGroup = useAppSelector(selectSelectedGroup);

  // Fetch all groups
  const {
    data: _fetchedGroups, // Prefix with underscore to mark as unused
    isLoading,
    error,
    refetch
  } = useGetAllStokvelGroupsQuery();

  // Join group mutation
  const [joinGroup, { isLoading: isJoining }] = useJoinGroupMutation();

  // Delete group mutation
  const [deleteGroup, { isLoading: isDeleting }] = useDeleteGroupMutation();

  // Update group mutation
  const [updateGroupMutation, { isLoading: isUpdating }] = useUpdateGroupMutation();

  // Set the selected group
  const setSelectedGroup = useCallback((groupId: string | null) => {
    dispatch(setSelectedGroupId(groupId));
  }, [dispatch]);

  // Get a group by ID
  const getGroupById = useCallback((groupId: string) => {
    return allGroups.find(group => group._id === groupId) || null;
  }, [allGroups]);

  // Join a group
  const handleJoinGroup = useCallback(async (userId: string, groupId: string, isRelocation: boolean = false) => {
    try {
      const result = await joinGroup({
        userId,
        groupId,
        isRelocation
      }).unwrap();

      return result;
    } catch (error) {
      console.error('Failed to join group:', error);
      throw error;
    }
  }, [joinGroup]);

  // Remove a group
  const handleRemoveGroup = useCallback(async (groupId: string) => {
    try {
      const result = await deleteGroup(groupId).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to delete group:', error);
      throw error;
    }
  }, [deleteGroup]);

  // Update a group
  const handleUpdateGroup = useCallback(async (data: UpdateGroupInput) => {
    try {
      const result = await updateGroupMutation(data).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to update group:', error);
      throw error;
    }
  }, [updateGroupMutation]);

  return {
    allGroups,
    selectedGroupId,
    selectedGroup,
    isLoading,
    error,
    isJoining,
    isDeleting,
    isUpdating,
    setSelectedGroup,
    getGroupById,
    joinGroup: handleJoinGroup,
    removeGroup: handleRemoveGroup,
    updateGroup: handleUpdateGroup,
    refetchGroups: refetch
  };
}

export default useGroups;
