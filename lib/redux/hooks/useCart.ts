import { useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  useGetShoppingCartQuery,
  useAddToCartMutation,
  useUpdateCartItemMutation,
  useRemoveFromCartMutation,
  useClearCartMutation
} from '@/lib/redux/features/cart/cartApiSlice';
import {
  selectCart,
  selectCartItems,
  selectTotalItems,
  selectSubtotal,
  selectIsNotificationVisible
} from '@/lib/redux/features/cart/cartSlice';
import { useAppSelector } from '@/lib/redux/hooks';
import {
  AddToCartInput,
  UpdateCartItemInput,
  RemoveFromCartInput,
  ClearCartInput
} from '@/types/unifiedCart';

export function useCart(groupId: string) {
  const { user } = useAuth();
  const userId = user?._id || '';

  // Reduced console logging for stability
  // console.log('useCart hook - groupId:', groupId);
  // console.log('useCart hook - userId:', userId);

  // RTK Query hooks - use existing query from CartProvider, don't create new one
  const { refetch, isLoading } = useGetShoppingCartQuery({
    userId,
    groupId
  }, {
    skip: !userId,
    // Don't refetch automatically, rely on CartProvider for data fetching
    refetchOnMountOrArgChange: false,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  });
  const [addToCartMutation] = useAddToCartMutation();
  const [updateCartItemMutation] = useUpdateCartItemMutation();
  const [removeFromCartMutation] = useRemoveFromCartMutation();
  const [clearCartMutation] = useClearCartMutation();

  // Redux selectors
  const cart = useAppSelector(selectCart);
  const cartItems = useAppSelector(selectCartItems);
  const totalItems = useAppSelector(selectTotalItems);
  const subtotal = useAppSelector(selectSubtotal);
  const isNotificationVisible = useAppSelector(selectIsNotificationVisible);

  // Reduced console logging for stability
  // console.log('useCart hook - cart:', cart);
  // console.log('useCart hook - cartItems:', cartItems);
  // console.log('useCart hook - totalItems:', totalItems);

  // Add to cart function
  const addToCart = useCallback(async (productId: string, quantity: number = 1) => {
    if (!userId) return null;

    const input: AddToCartInput = {
      userId,
      productId,
      quantity,
      groupId
    };

    try {
      const result = await addToCartMutation(input).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      return null;
    }
  }, [userId, groupId, addToCartMutation]);

  // Update cart item function
  const updateCartItem = useCallback(async (productId: string, quantity: number) => {
    if (!userId) return null;

    const input: UpdateCartItemInput = {
      userId,
      productId,
      quantity,
      groupId
    };

    try {
      const result = await updateCartItemMutation(input).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to update cart item:', error);
      return null;
    }
  }, [userId, groupId, updateCartItemMutation]);

  // Remove from cart function
  const removeFromCart = useCallback(async (productId: string) => {
    if (!userId) return null;

    const input: RemoveFromCartInput = {
      userId,
      productId,
      groupId
    };

    try {
      const result = await removeFromCartMutation(input).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to remove item from cart:', error);
      return null;
    }
  }, [userId, groupId, removeFromCartMutation]);

  // Clear cart function
  const clearCart = useCallback(async () => {
    if (!userId) return;

    const input: ClearCartInput = {
      userId,
      groupId
    };

    try {
      await clearCartMutation(input).unwrap();
    } catch (error) {
      console.error('Failed to clear cart:', error);
    }
  }, [userId, groupId, clearCartMutation]);

  // Increment quantity function
  const incrementQuantity = useCallback(async (productId: string, currentQuantity: number) => {
    return updateCartItem(productId, currentQuantity + 1);
  }, [updateCartItem]);

  // Decrement quantity function
  const decrementQuantity = useCallback(async (productId: string, currentQuantity: number) => {
    if (currentQuantity <= 1) return null;
    return updateCartItem(productId, currentQuantity - 1);
  }, [updateCartItem]);

  return {
    cart,
    cartItems,
    totalItems,
    subtotal,
    isLoading,
    isNotificationVisible,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    incrementQuantity,
    decrementQuantity,
    refreshCart: refetch
  };
}
