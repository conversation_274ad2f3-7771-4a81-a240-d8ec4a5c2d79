// lib/redux/features/memberOrders/memberOrdersApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { IMemberOrder, MemberOrderStatus } from '@/models/MemberOrder';

export interface MemberOrderSummary {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  ordersByStatus: Record<string, number>;
  recentOrders: IMemberOrder[];
}

export interface UpdateMemberOrderStatusInput {
  orderId: string;
  status: MemberOrderStatus;
  notes?: string;
}

export interface CancelMemberOrderInput {
  orderId: string;
  reason?: string;
}

export const memberOrdersApiSlice = createApi({
  reducerPath: 'memberOrdersApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['MemberOrder', 'MemberOrderSummary'],
  endpoints: (builder) => ({
    // Get member orders by user ID
    getMemberOrdersByUser: builder.query<IMemberOrder[], string>({
      query: (userId) => `/member-orders?userId=${userId}`,
      providesTags: (result, error, userId) => [
        { type: 'MemberOrder', id: 'LIST' },
        { type: 'MemberOrder', id: userId },
      ],
    }),

    // Get member order summary
    getMemberOrderSummary: builder.query<MemberOrderSummary, string>({
      query: (userId) => `/member-orders?userId=${userId}&summary=true`,
      providesTags: (result, error, userId) => [
        { type: 'MemberOrderSummary', id: userId },
      ],
    }),

    // Get single member order by ID
    getMemberOrderById: builder.query<IMemberOrder, string>({
      query: (orderId) => `/member-orders/${orderId}`,
      providesTags: (result, error, orderId) => [
        { type: 'MemberOrder', id: orderId },
      ],
    }),

    // Update member order status
    updateMemberOrderStatus: builder.mutation<IMemberOrder, UpdateMemberOrderStatusInput>({
      query: ({ orderId, status, notes }) => ({
        url: `/member-orders/${orderId}`,
        method: 'PATCH',
        body: { status, notes },
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'MemberOrder', id: orderId },
        { type: 'MemberOrder', id: 'LIST' },
        { type: 'MemberOrderSummary', id: result?.userId.toString() },
      ],
    }),

    // Cancel member order
    cancelMemberOrder: builder.mutation<IMemberOrder, CancelMemberOrderInput>({
      query: ({ orderId, reason }) => ({
        url: `/member-orders/${orderId}`,
        method: 'PATCH',
        body: { action: 'cancel', notes: reason },
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'MemberOrder', id: orderId },
        { type: 'MemberOrder', id: 'LIST' },
        { type: 'MemberOrderSummary', id: result?.userId.toString() },
      ],
    }),

    // Get member orders by group (for group admins)
    getMemberOrdersByGroup: builder.query<IMemberOrder[], string>({
      query: (groupId) => `/member-orders?groupId=${groupId}`,
      providesTags: (result, error, groupId) => [
        { type: 'MemberOrder', id: `GROUP_${groupId}` },
      ],
    }),
  }),
});

export const {
  useGetMemberOrdersByUserQuery,
  useGetMemberOrderSummaryQuery,
  useGetMemberOrderByIdQuery,
  useUpdateMemberOrderStatusMutation,
  useCancelMemberOrderMutation,
  useGetMemberOrdersByGroupQuery,
} = memberOrdersApiSlice;
