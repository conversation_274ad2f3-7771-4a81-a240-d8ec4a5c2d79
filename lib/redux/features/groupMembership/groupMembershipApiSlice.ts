import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { StokvelGroup } from '@/types/stokvelgroup';

// Define the response type for the check membership by email endpoint
interface CheckMembershipByEmailResponse {
  isMember: boolean;
  group: {
    _id: string;
    groupName: string;
    geolocation: string;
  } | null;
}

export const groupMembershipApiSlice = createApi({
  reducerPath: 'groupMembershipApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    credentials: 'include', // Important for cookies
  }),
  tagTypes: ['GroupMembership'],
  endpoints: (builder) => ({
    // Get user's groups
    listUserGroups: builder.query<StokvelGroup[], string>({
      query: (userId) => {
        console.log('listUserGroups query for userId:', userId);
        return `/users/groups?userId=${userId}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: 'GroupMembership' as const, id: _id })),
              { type: 'GroupMembership', id: 'LIST' },
            ]
          : [{ type: 'GroupMembership', id: 'LIST' }],
      // Add retry logic for network failures
      extraOptions: {
        maxRetries: 3,
      },
      // Add error handling
      async onQueryStarted(userId, { queryFulfilled }) {
        try {
          console.log('Starting listUserGroups query for userId:', userId);
          await queryFulfilled;
          console.log('listUserGroups query completed successfully for userId:', userId);
        } catch (error) {
          const err = error as Error;
          console.error('listUserGroups query failed:', {
            userId,
            error,
            errorMessage: err.message,
            errorStack: err.stack
          });
        }
      },
      // Transform error response
      transformErrorResponse: (response, meta, arg) => {
        // Only log detailed error in development
        if (process.env.NODE_ENV !== 'production') {
          console.error('listUserGroups error:', {
            status: response.status,
            data: response.data,
            userId: arg
          });
        } else {
          // In production, log minimal information
          console.error(`listUserGroups error: ${response.status}`);
        }
        return response;
      },
    }),

    // Check membership in specific group
    checkGroupMembership: builder.query<{ isMember: boolean }, { groupId: string; userId: string }>({
      query: ({ groupId, userId }) => ({
        url: `/groups/${groupId}/check-membership`,
        method: 'POST',
        body: { userId },
      }),
      providesTags: (result, error, { groupId }) => [{ type: 'GroupMembership', id: groupId }],
      // Add error handling
      transformErrorResponse: (response, meta, arg) => {
        if (process.env.NODE_ENV !== 'production') {
          console.error('checkGroupMembership error:', {
            status: response.status,
            data: response.data,
            groupId: arg.groupId,
            userId: arg.userId
          });
        }
        return response;
      },
    }),

    // This endpoint has been replaced with the more flexible version below

    // Check if user is member of any group by email
    checkMembershipByEmail: builder.query<CheckMembershipByEmailResponse, string>({
      query: (email) => `/users/check-membership-by-email?email=${encodeURIComponent(email)}`,
      providesTags: (result) => result?.isMember ? ['GroupMembership'] : [],
      // Add error handling
      transformErrorResponse: (response, meta, arg) => {
        if (process.env.NODE_ENV !== 'production') {
          console.error('checkMembershipByEmail error:', {
            status: response.status,
            data: response.data,
            email: arg
          });
        }
        return response;
      },
    }),

    // Check if user is member of any group (by userId or email)
    checkAnyGroupMembership: builder.query<{ isMemberOfAnyGroup: boolean; groupIds?: string[] }, { userId?: string; email?: string }>({
      query: ({ userId, email }) => {
        const params = new URLSearchParams();
        if (userId) params.append('userId', userId);
        if (email) params.append('email', encodeURIComponent(email));
        return `/users/check-any-group-membership?${params.toString()}`;
      },
      providesTags: ['GroupMembership'],
      // Add error handling
      transformErrorResponse: (response, meta, arg) => {
        if (process.env.NODE_ENV !== 'production') {
          console.error('checkAnyGroupMembership error:', {
            status: response.status,
            data: response.data,
            userId: arg.userId,
            email: arg.email
          });
        }
        return response;
      },
    }),

    // Get a group by ID
    getGroupById: builder.query<StokvelGroup, string>({
      query: (groupId) => `/stokvel-groups/${groupId}`,
      providesTags: (result, error, groupId) => [{ type: 'GroupMembership', id: groupId }],
      // Add error handling
      transformErrorResponse: (response, meta, arg) => {
        if (process.env.NODE_ENV !== 'production') {
          console.error('getGroupById error:', {
            status: response.status,
            data: response.data,
            groupId: arg
          });
        }
        return response;
      },
    }),
  }),
});

export const {
  useListUserGroupsQuery,
  useCheckGroupMembershipQuery,
  useCheckAnyGroupMembershipQuery,
  useCheckMembershipByEmailQuery,
  useGetGroupByIdQuery,
} = groupMembershipApiSlice;
