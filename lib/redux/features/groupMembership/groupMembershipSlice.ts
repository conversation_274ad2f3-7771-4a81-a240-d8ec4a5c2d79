// lib/redux/features/groupMembership/groupMembershipSlice.ts
import { createSlice, createSelector, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '@/app/store';
import type { StokvelGroup } from '@/types/stokvelgroup';
import { groupMembershipApiSlice } from './groupMembershipApiSlice';

interface GroupMembershipState {
  userGroups: StokvelGroup[];
  currentGroupId: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: GroupMembershipState = {
  userGroups: [],
  currentGroupId: null,
  isLoading: false,
  error: null,
};

export const groupMembershipSlice = createSlice({
  name: 'groupMembership',
  initialState,
  reducers: {
    setUserGroups: (state, action: PayloadAction<StokvelGroup[]>) => {
      state.userGroups = action.payload;
    },
    setCurrentGroupId: (state, action: PayloadAction<string | null>) => {
      state.currentGroupId = action.payload;
    },
    clearUserGroups: (state) => {
      state.userGroups = [];
      state.currentGroupId = null;
    },
  },
  extraReducers: (builder) => {
    // Handle listUserGroups query
    builder.addMatcher(
      groupMembershipApiSlice.endpoints.listUserGroups.matchFulfilled,
      (state, { payload }) => {
        state.userGroups = payload;
        state.isLoading = false;
        state.error = null;
      }
    );
    builder.addMatcher(
      groupMembershipApiSlice.endpoints.listUserGroups.matchPending,
      (state) => {
        state.isLoading = true;
        state.error = null;
      }
    );
    builder.addMatcher(
      groupMembershipApiSlice.endpoints.listUserGroups.matchRejected,
      (state, { error }) => {
        state.isLoading = false;
        state.error = error.message || 'Failed to fetch user groups';
      }
    );
  },
});

// Export actions
export const { setUserGroups, setCurrentGroupId, clearUserGroups } = groupMembershipSlice.actions;

// Selectors
export const selectUserGroups = (state: RootState) => state.groupMembership.userGroups;
export const selectCurrentGroupId = (state: RootState) => state.groupMembership.currentGroupId;
export const selectIsLoading = (state: RootState) => state.groupMembership.isLoading;
export const selectError = (state: RootState) => state.groupMembership.error;

// Derived selectors
export const selectCurrentGroup = createSelector(
  [selectUserGroups, selectCurrentGroupId],
  (userGroups, currentGroupId) => {
    if (!currentGroupId) return null;
    return userGroups.find(group => group._id === currentGroupId) || null;
  }
);

export const selectIsUserInGroup = createSelector(
  [selectUserGroups, (state, groupId: string) => groupId],
  (userGroups, groupId) => {
    return userGroups.some(group => group._id === groupId);
  }
);

export default groupMembershipSlice.reducer;
