// lib/redux/features/groupSavings/groupSavingsApiSlice.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export interface SavingsGoal {
  _id: string;
  groupId: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  deadline: string;
  createdAt: string;
  updatedAt: string;
}

export interface SavingsContribution {
  _id: string;
  userId: string;
  userName: string;
  goalId: string;
  amount: number;
  date: string;
  notes?: string;
}

export interface CreateSavingsGoalInput {
  groupId: string;
  name: string;
  targetAmount: number;
  deadline: string;
}

export interface UpdateSavingsGoalInput {
  goalId: string;
  name?: string;
  targetAmount?: number;
  deadline?: string;
}

export interface AddContributionInput {
  userId: string;
  goalId: string;
  amount: number;
  notes?: string;
}

export const groupSavingsApiSlice = createApi({
  reducerPath: 'groupSavingsApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['SavingsGoals', 'Contributions'],
  endpoints: (builder) => ({
    getGroupSavingsGoals: builder.query<SavingsGoal[], string>({
      query: (groupId) => `/groups/${groupId}/savings-goals`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: 'SavingsGoals' as const, id: _id })),
              { type: 'SavingsGoals', id: 'LIST' },
            ]
          : [{ type: 'SavingsGoals', id: 'LIST' }],
    }),
    
    getSavingsGoalById: builder.query<SavingsGoal, string>({
      query: (goalId) => `/savings-goals/${goalId}`,
      providesTags: (result, error, goalId) => [{ type: 'SavingsGoals', id: goalId }],
    }),
    
    createSavingsGoal: builder.mutation<SavingsGoal, CreateSavingsGoalInput>({
      query: (data) => ({
        url: '/savings-goals',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'SavingsGoals', id: 'LIST' }],
    }),
    
    updateSavingsGoal: builder.mutation<SavingsGoal, UpdateSavingsGoalInput>({
      query: (data) => ({
        url: `/savings-goals/${data.goalId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { goalId }) => [{ type: 'SavingsGoals', id: goalId }],
    }),
    
    deleteSavingsGoal: builder.mutation<void, string>({
      query: (goalId) => ({
        url: `/savings-goals/${goalId}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'SavingsGoals', id: 'LIST' }],
    }),
    
    getGoalContributions: builder.query<SavingsContribution[], string>({
      query: (goalId) => `/savings-goals/${goalId}/contributions`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: 'Contributions' as const, id: _id })),
              { type: 'Contributions', id: 'LIST' },
            ]
          : [{ type: 'Contributions', id: 'LIST' }],
    }),
    
    addContribution: builder.mutation<SavingsContribution, AddContributionInput>({
      query: (data) => ({
        url: `/savings-goals/${data.goalId}/contributions`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { goalId }) => [
        { type: 'Contributions', id: 'LIST' },
        { type: 'SavingsGoals', id: goalId },
      ],
    }),
    
    getUserContributions: builder.query<SavingsContribution[], { userId: string; groupId: string }>({
      query: ({ userId, groupId }) => `/users/${userId}/groups/${groupId}/contributions`,
      providesTags: ['Contributions'],
    }),
  }),
});

export const {
  useGetGroupSavingsGoalsQuery,
  useGetSavingsGoalByIdQuery,
  useCreateSavingsGoalMutation,
  useUpdateSavingsGoalMutation,
  useDeleteSavingsGoalMutation,
  useGetGoalContributionsQuery,
  useAddContributionMutation,
  useGetUserContributionsQuery,
} = groupSavingsApiSlice;
