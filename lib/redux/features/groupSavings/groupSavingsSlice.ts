// lib/redux/features/groupSavings/groupSavingsSlice.ts
import { createSlice, createSelector, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '@/app/store';
import { 
  SavingsGoal, 
  SavingsContribution,
  groupSavingsApiSlice 
} from './groupSavingsApiSlice';

interface GroupSavingsState {
  savingsGoals: SavingsGoal[];
  contributions: SavingsContribution[];
  selectedGoalId: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: GroupSavingsState = {
  savingsGoals: [],
  contributions: [],
  selectedGoalId: null,
  isLoading: false,
  error: null,
};

export const groupSavingsSlice = createSlice({
  name: 'groupSavings',
  initialState,
  reducers: {
    setSavingsGoals: (state, action: PayloadAction<SavingsGoal[]>) => {
      state.savingsGoals = action.payload;
    },
    setContributions: (state, action: PayloadAction<SavingsContribution[]>) => {
      state.contributions = action.payload;
    },
    setSelectedGoalId: (state, action: PayloadAction<string | null>) => {
      state.selectedGoalId = action.payload;
    },
    clearSavingsData: (state) => {
      state.savingsGoals = [];
      state.contributions = [];
      state.selectedGoalId = null;
    },
  },
  extraReducers: (builder) => {
    // Handle getGroupSavingsGoals query
    builder.addMatcher(
      groupSavingsApiSlice.endpoints.getGroupSavingsGoals.matchFulfilled,
      (state, { payload }) => {
        state.savingsGoals = payload;
        state.isLoading = false;
        state.error = null;
      }
    );
    builder.addMatcher(
      groupSavingsApiSlice.endpoints.getGroupSavingsGoals.matchPending,
      (state) => {
        state.isLoading = true;
        state.error = null;
      }
    );
    builder.addMatcher(
      groupSavingsApiSlice.endpoints.getGroupSavingsGoals.matchRejected,
      (state, { error }) => {
        state.isLoading = false;
        state.error = error.message || 'Failed to fetch savings goals';
      }
    );
    
    // Handle getGoalContributions query
    builder.addMatcher(
      groupSavingsApiSlice.endpoints.getGoalContributions.matchFulfilled,
      (state, { payload }) => {
        state.contributions = payload;
        state.isLoading = false;
        state.error = null;
      }
    );
  },
});

// Export actions
export const { 
  setSavingsGoals, 
  setContributions, 
  setSelectedGoalId, 
  clearSavingsData 
} = groupSavingsSlice.actions;

// Selectors
export const selectSavingsGoals = (state: RootState) => state.groupSavings.savingsGoals;
export const selectContributions = (state: RootState) => state.groupSavings.contributions;
export const selectSelectedGoalId = (state: RootState) => state.groupSavings.selectedGoalId;
export const selectIsLoading = (state: RootState) => state.groupSavings.isLoading;
export const selectError = (state: RootState) => state.groupSavings.error;

// Derived selectors
export const selectSelectedGoal = createSelector(
  [selectSavingsGoals, selectSelectedGoalId],
  (savingsGoals, selectedGoalId) => {
    if (!selectedGoalId) return null;
    return savingsGoals.find(goal => goal._id === selectedGoalId) || null;
  }
);

export const selectGoalById = createSelector(
  [selectSavingsGoals, (state, goalId: string) => goalId],
  (savingsGoals, goalId) => {
    return savingsGoals.find(goal => goal._id === goalId) || null;
  }
);

export const selectGoalProgress = createSelector(
  [selectGoalById],
  (goal) => {
    if (!goal) return 0;
    return Math.min((goal.currentAmount / goal.targetAmount) * 100, 100);
  }
);

export const selectContributionsByGoalId = createSelector(
  [selectContributions, (state, goalId: string) => goalId],
  (contributions, goalId) => {
    return contributions.filter(contribution => contribution.goalId === goalId);
  }
);

export default groupSavingsSlice.reducer;
