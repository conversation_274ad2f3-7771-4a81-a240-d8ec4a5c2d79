// lib/redux/features/ratings/ratingsApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { IProductRating } from '@/models/ProductRating';

export interface RatingSummary {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  recentRatings: IProductRating[];
}

export interface RatingsResponse {
  ratings: IProductRating[];
  total: number;
  hasMore: boolean;
}

export interface CreateRatingInput {
  userId: string;
  productId: string;
  rating: number;
  review?: string;
  title?: string;
}

export interface UpdateRatingInput {
  ratingId: string;
  userId: string;
  rating?: number;
  review?: string;
  title?: string;
}

export interface RatingActionInput {
  ratingId: string;
  userId: string;
  action: 'helpful' | 'report';
}

export interface RelatedProductsResponse {
  products: any[];
  count: number;
}

export const ratingsApiSlice = createApi({
  reducerPath: 'ratingsApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['Rating', 'RatingSummary', 'UserRating', 'RelatedProducts'],
  endpoints: (builder) => ({
    // Get product rating summary
    getProductRatingSummary: builder.query<RatingSummary, string>({
      query: (productId) => `/products/${productId}/ratings?summary=true`,
      providesTags: (result, error, productId) => [
        { type: 'RatingSummary', id: productId },
      ],
    }),

    // Get product ratings with pagination
    getProductRatings: builder.query<RatingsResponse, {
      productId: string;
      page?: number;
      limit?: number;
      sortBy?: 'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful';
    }>({
      query: ({ productId, page = 1, limit = 10, sortBy = 'newest' }) => 
        `/products/${productId}/ratings?page=${page}&limit=${limit}&sortBy=${sortBy}`,
      providesTags: (result, error, { productId }) => [
        { type: 'Rating', id: 'LIST' },
        { type: 'Rating', id: productId },
      ],
    }),

    // Get user's rating for a specific product
    getUserProductRating: builder.query<{ rating: IProductRating | null }, {
      productId: string;
      userId: string;
    }>({
      query: ({ productId, userId }) => 
        `/products/${productId}/ratings?userRating=true&userId=${userId}`,
      providesTags: (result, error, { productId, userId }) => [
        { type: 'UserRating', id: `${userId}-${productId}` },
      ],
    }),

    // Create a new rating
    createRating: builder.mutation<{ rating: IProductRating }, CreateRatingInput>({
      query: ({ productId, ...body }) => ({
        url: `/products/${productId}/ratings`,
        method: 'POST',
        body,
      }),
      invalidatesTags: (result, error, { productId, userId }) => [
        { type: 'Rating', id: 'LIST' },
        { type: 'Rating', id: productId },
        { type: 'RatingSummary', id: productId },
        { type: 'UserRating', id: `${userId}-${productId}` },
      ],
    }),

    // Update a rating
    updateRating: builder.mutation<{ rating: IProductRating }, UpdateRatingInput>({
      query: ({ ratingId, ...body }) => ({
        url: `/ratings/${ratingId}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: 'Rating', id: 'LIST' },
        { type: 'RatingSummary', id: 'LIST' },
        { type: 'UserRating', id: `${userId}-LIST` },
      ],
    }),

    // Delete a rating
    deleteRating: builder.mutation<{ message: string }, { ratingId: string; userId: string }>({
      query: ({ ratingId, userId }) => ({
        url: `/ratings/${ratingId}?userId=${userId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: 'Rating', id: 'LIST' },
        { type: 'RatingSummary', id: 'LIST' },
        { type: 'UserRating', id: `${userId}-LIST` },
      ],
    }),

    // Mark rating as helpful or report
    ratingAction: builder.mutation<{ rating?: IProductRating; message: string }, RatingActionInput>({
      query: ({ ratingId, ...body }) => ({
        url: `/ratings/${ratingId}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: (result, error, { ratingId }) => [
        { type: 'Rating', id: ratingId },
        { type: 'Rating', id: 'LIST' },
      ],
    }),

    // Get related products
    getRelatedProducts: builder.query<RelatedProductsResponse, { productId: string; limit?: number }>({
      query: ({ productId, limit = 6 }) => 
        `/products/${productId}/related?limit=${limit}`,
      providesTags: (result, error, { productId }) => [
        { type: 'RelatedProducts', id: productId },
      ],
    }),
  }),
});

export const {
  useGetProductRatingSummaryQuery,
  useGetProductRatingsQuery,
  useGetUserProductRatingQuery,
  useCreateRatingMutation,
  useUpdateRatingMutation,
  useDeleteRatingMutation,
  useRatingActionMutation,
  useGetRelatedProductsQuery,
} = ratingsApiSlice;
