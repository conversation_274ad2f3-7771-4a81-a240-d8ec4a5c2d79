// lib/redux/features/userProfile/userProfileApiSlice.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { CartItem } from '@/types/cart';

export interface GroupHistoryItem {
  _id: string;
  name: string;
  geolocation: string;
  joinedAt: string;
  leftAt: string | null;
  isCurrent: boolean;
  hasPendingItems: boolean;
  pendingItemsCount: number;
}

export interface PendingCartItem extends CartItem {
  groupId: string;
  groupName: string;
}

export interface TransferCartItemsRequest {
  userId: string;
  sourceGroupId: string;
  targetGroupId: string;
  cartItemIds: string[];
}

export const userProfileApiSlice = createApi({
  reducerPath: 'userProfileApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    credentials: 'include',
  }),
  tagTypes: ['UserGroups', 'CartItems'],
  endpoints: (builder) => ({
    getUserGroupHistory: builder.query<GroupHistoryItem[], string>({
      query: (userId) => `/users/${userId}/group-history`,
      providesTags: ['UserGroups'],
    }),
    
    getPendingCartItems: builder.query<PendingCartItem[], { userId: string; groupId: string }>({
      query: ({ userId, groupId }) => `/users/${userId}/groups/${groupId}/pending-items`,
      providesTags: ['CartItems'],
    }),
    
    transferCartItems: builder.mutation<{ success: boolean; message: string }, TransferCartItemsRequest>({
      query: (data) => ({
        url: '/cart/transfer-items',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CartItems', 'UserGroups'],
    }),
  }),
});

export const {
  useGetUserGroupHistoryQuery,
  useGetPendingCartItemsQuery,
  useTransferCartItemsMutation,
} = userProfileApiSlice;

