// lib/redux/features/locations/locationsApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { 
  Province, 
  City, 
  Township, 
  Location,
  ProvinceResponse,
  CityResponse,
  TownshipResponse,
  LocationResponse,
  LocationSearchResults,
  LocationStatsResponse,
  CreateProvinceData,
  UpdateProvinceData,
  CreateCityData,
  UpdateCityData,
  CreateTownshipData,
  UpdateTownshipData,
  CreateLocationData,
  UpdateLocationData
} from '@/types/locations';

export const locationsApi = createApi({
  reducerPath: 'locationsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Province', 'City', 'Township', 'Location', 'LocationStats'],
  endpoints: (builder) => ({
    // ===== PROVINCE ENDPOINTS =====
    getProvinces: builder.query<ProvinceResponse, void>({
      query: () => 'api/locations/provinces',
      providesTags: ['Province'],
    }),
    
    getProvinceById: builder.query<ProvinceResponse, string>({
      query: (id) => `api/locations/provinces?id=${id}`,
      providesTags: (result, error, id) => [{ type: 'Province', id }],
    }),
    
    createProvince: builder.mutation<ProvinceResponse, CreateProvinceData>({
      query: (data) => ({
        url: 'api/locations/provinces',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Province'],
    }),
    
    updateProvince: builder.mutation<ProvinceResponse, UpdateProvinceData>({
      query: (data) => ({
        url: 'api/locations/provinces',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Province', id },
        'Province',
      ],
    }),
    
    deleteProvince: builder.mutation<ProvinceResponse, string>({
      query: (id) => ({
        url: `api/locations/provinces?id=${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Province', id },
        'Province',
      ],
    }),

    // ===== CITY ENDPOINTS =====
    getCitiesByProvince: builder.query<CityResponse, string>({
      query: (provinceId) => `api/locations/cities/${provinceId}`,
      providesTags: (result, error, provinceId) => [
        { type: 'City', id: `province-${provinceId}` },
        { type: 'City', id: 'LIST' },
      ],
    }),
    
    getCityById: builder.query<CityResponse, { provinceId: string; cityId: string }>({
      query: ({ provinceId, cityId }) => `api/locations/cities/${provinceId}?id=${cityId}`,
      providesTags: (result, error, { cityId }) => [{ type: 'City', id: cityId }],
    }),
    
    createCity: builder.mutation<CityResponse, CreateCityData>({
      query: ({ name, provinceId }) => ({
        url: `api/locations/cities/${provinceId}`,
        method: 'POST',
        body: { name },
      }),
      invalidatesTags: (result, error, { provinceId }) => [
        { type: 'City', id: `province-${provinceId}` },
        { type: 'City', id: 'LIST' },
      ],
    }),
    
    updateCity: builder.mutation<CityResponse, UpdateCityData & { provinceId: string }>({
      query: ({ provinceId, ...data }) => ({
        url: `api/locations/cities/${provinceId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id, provinceId }) => [
        { type: 'City', id },
        { type: 'City', id: `province-${provinceId}` },
        { type: 'City', id: 'LIST' },
      ],
    }),
    
    deleteCity: builder.mutation<CityResponse, { provinceId: string; cityId: string }>({
      query: ({ provinceId, cityId }) => ({
        url: `api/locations/cities/${provinceId}?id=${cityId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { cityId, provinceId }) => [
        { type: 'City', id: cityId },
        { type: 'City', id: `province-${provinceId}` },
        { type: 'City', id: 'LIST' },
      ],
    }),

    // ===== TOWNSHIP ENDPOINTS =====
    getTownshipsByCity: builder.query<TownshipResponse, string>({
      query: (cityId) => `api/locations/townships/${cityId}`,
      providesTags: (result, error, cityId) => [
        { type: 'Township', id: `city-${cityId}` },
        { type: 'Township', id: 'LIST' },
      ],
    }),
    
    getTownshipById: builder.query<TownshipResponse, { cityId: string; townshipId: string }>({
      query: ({ cityId, townshipId }) => `api/locations/townships/${cityId}?id=${townshipId}`,
      providesTags: (result, error, { townshipId }) => [{ type: 'Township', id: townshipId }],
    }),
    
    createTownship: builder.mutation<TownshipResponse, CreateTownshipData>({
      query: ({ name, cityId }) => ({
        url: `api/locations/townships/${cityId}`,
        method: 'POST',
        body: { name },
      }),
      invalidatesTags: (result, error, { cityId }) => [
        { type: 'Township', id: `city-${cityId}` },
        { type: 'Township', id: 'LIST' },
      ],
    }),
    
    updateTownship: builder.mutation<TownshipResponse, UpdateTownshipData & { cityId: string }>({
      query: ({ cityId, ...data }) => ({
        url: `api/locations/townships/${cityId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id, cityId }) => [
        { type: 'Township', id },
        { type: 'Township', id: `city-${cityId}` },
        { type: 'Township', id: 'LIST' },
      ],
    }),
    
    deleteTownship: builder.mutation<TownshipResponse, { cityId: string; townshipId: string }>({
      query: ({ cityId, townshipId }) => ({
        url: `api/locations/townships/${cityId}?id=${townshipId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { townshipId, cityId }) => [
        { type: 'Township', id: townshipId },
        { type: 'Township', id: `city-${cityId}` },
        { type: 'Township', id: 'LIST' },
      ],
    }),

    // ===== LOCATION ENDPOINTS =====
    getLocationsByTownship: builder.query<LocationResponse, string>({
      query: (townshipId) => `api/locations/locations/${townshipId}`,
      providesTags: (result, error, townshipId) => [
        { type: 'Location', id: `township-${townshipId}` },
        { type: 'Location', id: 'LIST' },
      ],
    }),
    
    getLocationById: builder.query<LocationResponse, { townshipId: string; locationId: string }>({
      query: ({ townshipId, locationId }) => `api/locations/locations/${townshipId}?id=${locationId}`,
      providesTags: (result, error, { locationId }) => [{ type: 'Location', id: locationId }],
    }),
    
    createLocation: builder.mutation<LocationResponse, CreateLocationData>({
      query: ({ name, townshipId, description }) => ({
        url: `api/locations/locations/${townshipId}`,
        method: 'POST',
        body: { name, description },
      }),
      invalidatesTags: (result, error, { townshipId }) => [
        { type: 'Location', id: `township-${townshipId}` },
        { type: 'Location', id: 'LIST' },
      ],
    }),
    
    updateLocation: builder.mutation<LocationResponse, UpdateLocationData & { townshipId: string }>({
      query: ({ townshipId, ...data }) => ({
        url: `api/locations/locations/${townshipId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id, townshipId }) => [
        { type: 'Location', id },
        { type: 'Location', id: `township-${townshipId}` },
        { type: 'Location', id: 'LIST' },
      ],
    }),
    
    deleteLocation: builder.mutation<LocationResponse, { townshipId: string; locationId: string }>({
      query: ({ townshipId, locationId }) => ({
        url: `api/locations/locations/${townshipId}?id=${locationId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { locationId, townshipId }) => [
        { type: 'Location', id: locationId },
        { type: 'Location', id: `township-${townshipId}` },
        { type: 'Location', id: 'LIST' },
      ],
    }),

    // ===== UTILITY ENDPOINTS =====
    searchLocations: builder.query<LocationSearchResults, { query: string; limit?: number }>({
      query: ({ query, limit = 10 }) => `api/locations/search?q=${encodeURIComponent(query)}&limit=${limit}`,
    }),
    
    getLocationStats: builder.query<LocationStatsResponse, void>({
      query: () => 'api/locations/stats',
      providesTags: ['LocationStats'],
    }),

    // ===== SEEDING & MIGRATION ENDPOINTS =====
    seedLocations: builder.mutation<{ success: boolean; message: string }, void>({
      query: () => ({
        url: 'api/locations/seed',
        method: 'POST',
      }),
      invalidatesTags: ['Province', 'City', 'Township', 'Location', 'LocationStats'],
    }),

    migrateGroups: builder.mutation<{
      success: boolean;
      message: string;
      summary: { total: number; migrated: number; created: number; errors: number }
    }, void>({
      query: () => ({
        url: 'api/locations/migrate',
        method: 'POST',
      }),
      invalidatesTags: ['LocationStats'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Province hooks
  useGetProvincesQuery,
  useGetProvinceByIdQuery,
  useCreateProvinceMutation,
  useUpdateProvinceMutation,
  useDeleteProvinceMutation,
  
  // City hooks
  useGetCitiesByProvinceQuery,
  useLazyGetCitiesByProvinceQuery,
  useGetCityByIdQuery,
  useCreateCityMutation,
  useUpdateCityMutation,
  useDeleteCityMutation,
  
  // Township hooks
  useGetTownshipsByCityQuery,
  useLazyGetTownshipsByCityQuery,
  useGetTownshipByIdQuery,
  useCreateTownshipMutation,
  useUpdateTownshipMutation,
  useDeleteTownshipMutation,
  
  // Location hooks
  useGetLocationsByTownshipQuery,
  useLazyGetLocationsByTownshipQuery,
  useGetLocationByIdQuery,
  useCreateLocationMutation,
  useUpdateLocationMutation,
  useDeleteLocationMutation,
  
  // Utility hooks
  useSearchLocationsQuery,
  useLazySearchLocationsQuery,
  useGetLocationStatsQuery,

  // Seeding & Migration hooks
  useSeedLocationsMutation,
  useMigrateGroupsMutation,
} = locationsApi;
