// lib/redux/features/groups/groupsApiSlice.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { StokvelGroup } from '@/types/stokvelgroup';
import type { User } from '@/types/user';

export interface JoinGroupRequest {
  userId: string;
  groupId: string;
  isRelocation?: boolean;
}

export interface LocationRequestPayload {
  location: string;
  userId?: string; // Make userId optional to maintain backward compatibility
}

export interface UpdateGroupInput {
  id: string;
  updateData: {
    name?: string;
    geolocation?: string;
    totalSales?: number;
    avgOrderValue?: number;
    activeOrders?: number;
  };
}

export interface GroupMember {
  userId: string;
  name: string;
  email: string;
  phone: string;
  totalOrders: string;
  totalSpent: string;
  joinedAt: string;
}

export const groupsApi = createApi({
  reducerPath: 'groupsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/',
    credentials: 'include',
    prepareHeaders: (headers) => {
      // Add authorization header if token exists
      const token = localStorage.getItem('token');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('Content-Type', 'application/json');
      headers.set('x-client-type', 'web');
      return headers;
    },
  }),
  tagTypes: ['Groups', 'User', 'GroupMembers'],
  endpoints: (builder) => ({
    getAllStokvelGroups: builder.query<StokvelGroup[], void>({
      query: () => 'api/stokvel-groups/get-all',
      providesTags: ['Groups'],
    }),

    checkUserByEmail: builder.query<User | null, string>({
      query: (email) => `api/users/find-by-email?email=${encodeURIComponent(email)}`,
      transformResponse: (response: { user: User }) => response.user || null,
      providesTags: (result) => result ? [{ type: 'User', id: result._id }] : [],
    }),

    joinGroup: builder.mutation<{ success: boolean; message: string; group: StokvelGroup }, JoinGroupRequest>({
      query: (data) => ({
        url: data.isRelocation ? 'api/stokvel-groups/join-or-relocate' : 'api/stokvel-groups/join',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Groups', 'User'],
    }),

    storeLocationRequest: builder.mutation<void, LocationRequestPayload>({
      query: (data) => ({
        url: 'api/location-requests',
        method: 'POST',
        body: data,
      }),
    }),

    deleteGroup: builder.mutation<{ success: boolean; message: string }, string>({
      query: (groupId) => ({
        url: `api/stokvel-groups/delete/${groupId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Groups'],
    }),

    updateGroup: builder.mutation<{ success: boolean; message: string; group: StokvelGroup }, UpdateGroupInput>({
      query: (data) => ({
        url: `api/stokvel-groups/update`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Groups'],
    }),

    getGroupMembers: builder.query<GroupMember[], string>({
      query: (groupId) => `api/groups/${groupId}/all-members`,
      providesTags: (result, error, groupId) => [
        { type: 'GroupMembers', id: groupId },
        { type: 'GroupMembers', id: 'LIST' },
      ],
    }),
  }),
});

export const {
  useGetAllStokvelGroupsQuery,
  useCheckUserByEmailQuery,
  useLazyCheckUserByEmailQuery,
  useJoinGroupMutation,
  useStoreLocationRequestMutation,
  useDeleteGroupMutation,
  useUpdateGroupMutation,
  useGetGroupMembersQuery,
} = groupsApi;


