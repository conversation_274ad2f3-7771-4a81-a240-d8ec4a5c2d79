// lib/redux/features/groups/groupsSlice.ts
import { createSlice, createSelector, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '@/app/store';
import type { StokvelGroup } from '@/types/stokvelgroup';
import { groupsApi } from './groupsApiSlice';

interface GroupsState {
  allGroups: StokvelGroup[];
  selectedGroupId: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: GroupsState = {
  allGroups: [],
  selectedGroupId: null,
  isLoading: false,
  error: null,
};

export const groupsSlice = createSlice({
  name: 'groups',
  initialState,
  reducers: {
    setAllGroups: (state, action: PayloadAction<StokvelGroup[]>) => {
      state.allGroups = action.payload;
    },
    setSelectedGroupId: (state, action: PayloadAction<string | null>) => {
      state.selectedGroupId = action.payload;
    },
    clearGroups: (state) => {
      state.allGroups = [];
      state.selectedGroupId = null;
    },
  },
  extraReducers: (builder) => {
    // Handle getAllStokvelGroups query
    builder.addMatcher(
      groupsApi.endpoints.getAllStokvelGroups.matchFulfilled,
      (state, { payload }) => {
        state.allGroups = payload;
        state.isLoading = false;
        state.error = null;
      }
    );
    builder.addMatcher(
      groupsApi.endpoints.getAllStokvelGroups.matchPending,
      (state) => {
        state.isLoading = true;
        state.error = null;
      }
    );
    builder.addMatcher(
      groupsApi.endpoints.getAllStokvelGroups.matchRejected,
      (state, { error }) => {
        state.isLoading = false;
        state.error = error.message || 'Failed to fetch groups';
      }
    );
  },
});

// Export actions
export const { setAllGroups, setSelectedGroupId, clearGroups } = groupsSlice.actions;

// Selectors
export const selectAllGroups = (state: RootState) => state.groups.allGroups;
export const selectSelectedGroupId = (state: RootState) => state.groups.selectedGroupId;
export const selectIsLoading = (state: RootState) => state.groups.isLoading;
export const selectError = (state: RootState) => state.groups.error;

// Derived selectors
export const selectSelectedGroup = createSelector(
  [selectAllGroups, selectSelectedGroupId],
  (allGroups, selectedGroupId) => {
    if (!selectedGroupId) return null;
    return allGroups.find(group => group._id === selectedGroupId) || null;
  }
);

export const selectGroupById = createSelector(
  [selectAllGroups, (state, groupId: string) => groupId],
  (allGroups, groupId) => {
    return allGroups.find(group => group._id === groupId) || null;
  }
);

export default groupsSlice.reducer;
