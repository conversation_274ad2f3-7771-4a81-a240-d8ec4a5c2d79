import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { Product } from '@/types/product';
import type { UpdateProductInput } from '@/lib/frontendProductUtilities';

// Define a service using a base URL and expected endpoints
export const productsApiSlice = createApi({
  reducerPath: 'productsApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/products' }), // Base path for product endpoints
  tagTypes: ['Product', 'ArchivedProduct'], // Define tags for cache invalidation
  endpoints: (builder) => ({
    // Endpoint to get all active products
    getAllProducts: builder.query<Product[], void>({
      query: () => '/get-all', // Endpoint path relative to baseQuery
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: 'Product' as const, id: _id })),
              { type: 'Product', id: 'LIST' },
            ]
          : [{ type: 'Product', id: 'LIST' }],
    }),
    // Endpoint to get a product by ID
    getProductById: builder.query<Product, string>({
      query: (productId) => `/get/${productId}`, // Endpoint path relative to baseQuery
      providesTags: (result, error, productId) => [{ type: 'Product', id: productId }],
    }),
    // Endpoint to get all archived products
    getArchivedProducts: builder.query<Product[], void>({
      query: () => '/get-archived',
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: 'ArchivedProduct' as const, id: _id })),
              { type: 'ArchivedProduct', id: 'LIST' },
            ]
          : [{ type: 'ArchivedProduct', id: 'LIST' }],
    }),
    // Endpoint to create a new product (expects FormData)
    createProduct: builder.mutation<Product, FormData>({
      query: (formData) => ({
        url: '/create',
        method: 'POST',
        body: formData, // RTK Query handles FormData correctly
        // Note: Content-Type header is automatically set by the browser for FormData
      }),
      invalidatesTags: [{ type: 'Product', id: 'LIST' }], // Invalidate the list on creation
    }),
    // Endpoint to update a product
    updateProduct: builder.mutation<Product, UpdateProductInput>({
      query: (input) => ({
        url: `/update`, // Assuming update endpoint is at /api/products/update
        method: 'PATCH', // Or PUT, depending on your API
        body: input, // Send { id: productId, updateData: {...} }
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Product', id }], // Invalidate specific product cache
    }),
    // Endpoint to delete (archive) a product
    deleteProduct: builder.mutation<void, string>({
      query: (productId) => ({
        url: `/delete`, // Assuming delete endpoint is at /api/products/delete
        method: 'DELETE',
        body: { id: productId }, // Send ID in the body
      }),
      invalidatesTags: (result, error, productId) => [
        { type: 'Product', id: 'LIST' },
        { type: 'Product', id: productId },
        { type: 'ArchivedProduct', id: 'LIST' }, // Invalidate archived list as well
      ],
    }),
    // Endpoint to restore an archived product
    restoreArchivedProduct: builder.mutation<Product, string>({
      query: (productId) => ({
        url: `/restore`, // Assuming restore endpoint is at /api/products/restore
        method: 'POST', // Or PATCH/PUT
        body: { id: productId }, // Send ID in the body
      }),
      invalidatesTags: (result, error, productId) => [
        { type: 'Product', id: 'LIST' },
        { type: 'ArchivedProduct', id: 'LIST' },
        { type: 'ArchivedProduct', id: productId },
      ],
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetAllProductsQuery,
  useGetProductByIdQuery,
  useGetArchivedProductsQuery,
  useCreateProductMutation,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useRestoreArchivedProductMutation,
} = productsApiSlice;
