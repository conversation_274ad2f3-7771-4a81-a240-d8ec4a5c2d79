import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '@/app/store';
import { DisplayShoppingCart, DisplayCartItem } from '@/types/unifiedCart';
import { cartApiSlice } from './cartApiSlice';

// Define the initial state
interface CartState {
  cart: DisplayShoppingCart | null;
  displayItems: DisplayCartItem[];
  isNotificationVisible: boolean;
  loading: boolean;
  error: string | null;
}

const initialState: CartState = {
  cart: null,
  displayItems: [],
  isNotificationVisible: false,
  loading: false,
  error: null,
};

// Create the cart slice
export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    // Set the entire cart
    setCart: (state, action: PayloadAction<DisplayShoppingCart | null>) => {
      state.cart = action.payload;

      // Transform cart items to display items
      if (action.payload && action.payload.items && action.payload.items.length > 0) {
        state.displayItems = action.payload.items;
      } else {
        state.displayItems = [];
      }
    },

    // Show notification
    showNotification: (state) => {
      state.isNotificationVisible = true;
    },

    // Hide notification
    hideNotification: (state) => {
      state.isNotificationVisible = false;
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // Set error state
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Clear cart in state (used when logging out)
    clearCartState: (state) => {
      state.cart = null;
      state.displayItems = [];
    },
  },
  extraReducers: (builder) => {
    // Handle the fulfilled state of getShoppingCart query
    builder.addMatcher(
      cartApiSlice.endpoints.getShoppingCart.matchFulfilled,
      (state, { payload }) => {
        // Use the payload directly since it's already transformed by the API slice
        state.cart = payload;
        state.displayItems = payload.items;
        state.loading = false;
        state.error = null;
      }
    );

    // Handle the pending state of getShoppingCart query
    builder.addMatcher(
      cartApiSlice.endpoints.getShoppingCart.matchPending,
      (state) => {
        state.loading = true;
      }
    );

    // Handle the rejected state of getShoppingCart query
    builder.addMatcher(
      cartApiSlice.endpoints.getShoppingCart.matchRejected,
      (state, { error }) => {
        state.loading = false;
        state.error = error.message || 'Failed to fetch cart';
      }
    );

    // Handle the fulfilled state of clearCart mutation
    builder.addMatcher(
      cartApiSlice.endpoints.clearCart.matchFulfilled,
      (state) => {
        // Clear the cart state when cart is successfully cleared
        state.cart = null;
        state.displayItems = [];
        state.loading = false;
        state.error = null;
      }
    );

    // Handle the pending state of clearCart mutation
    builder.addMatcher(
      cartApiSlice.endpoints.clearCart.matchPending,
      (state) => {
        state.loading = true;
      }
    );

    // Handle the rejected state of clearCart mutation
    builder.addMatcher(
      cartApiSlice.endpoints.clearCart.matchRejected,
      (state, { error }) => {
        state.loading = false;
        state.error = error.message || 'Failed to clear cart';
      }
    );

    // Handle the fulfilled state of createOrUpdateGroupOrder mutation
    builder.addMatcher(
      cartApiSlice.endpoints.createOrUpdateGroupOrder.matchFulfilled,
      (state) => {
        // Clear the cart state when order is successfully created
        state.cart = null;
        state.displayItems = [];
        state.loading = false;
        state.error = null;
      }
    );
  },
});

// Export actions
export const {
  setCart,
  showNotification,
  hideNotification,
  setLoading,
  setError,
  clearCartState,
} = cartSlice.actions;

// Selectors
export const selectCart = (state: RootState) => state.cart.cart;
export const selectCartItems = (state: RootState) => state.cart.cart?.items || [];
export const selectDisplayItems = (state: RootState) => state.cart.displayItems;
export const selectTotalItems = (state: RootState) =>
  state.cart.displayItems.reduce((sum, item) => sum + item.quantity, 0) || 0;
export const selectSubtotal = (state: RootState) =>
  state.cart.displayItems.reduce((sum, item) => sum + item.subtotal, 0) || 0;
export const selectIsNotificationVisible = (state: RootState) => state.cart.isNotificationVisible;
export const selectCartError = (state: RootState) => state.cart.error;
export const selectCartLoading = (state: RootState) => state.cart.loading;

// Export reducer
export default cartSlice.reducer;
