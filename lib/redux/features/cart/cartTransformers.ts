// lib/redux/features/cart/cartTransformers.ts
import {
  DisplayCartItem,
  DisplayShoppingCart,
  GroupOrder,
  GroupOrderItem,
  UserContribution,
  DiscountCalculationResult
} from '@/types/unifiedCart';
import { GroupOrderStatus } from '@/types/shoppingCartConstants';

/**
 * Transforms a raw cart response from the API into a standardized shopping cart
 */
export const transformCartResponse = (response: Record<string, unknown>): DisplayShoppingCart => {
  // Debug the incoming response
  console.log('transformCartResponse - raw response:', JSON.stringify(response, null, 2));

  if (!response) {
    console.log('transformCartResponse - empty response, returning empty cart');
    return createEmptyCart();
  }

  // Ensure items is an array before mapping
  const itemsArray = Array.isArray(response.items) ? response.items : [];
  const items = itemsArray.map(transformCartItem) || [];
  console.log('transformCartResponse - transformed items:', items);

  // Safely extract properties with type checking
  const _id = typeof response._id === 'string' ? response._id : '';

  // Handle user property which could be an object or not exist
  let userId = '';
  if (response.user && typeof response.user === 'object' && '_id' in response.user) {
    userId = String(response.user._id) || '';
  } else if (response.userId && typeof response.userId === 'string') {
    userId = response.userId;
  }

  const groupId = typeof response.groupId === 'string' ? response.groupId : '';
  const isFinalized = Boolean(response.isFinalized);

  // Handle dates
  let createdAt: string | Date | undefined = undefined;
  if (response.createdAt) {
    if (typeof response.createdAt === 'string') {
      createdAt = response.createdAt;
    } else if (response.createdAt instanceof Date) {
      createdAt = response.createdAt;
    }
  }

  let updatedAt: string | Date | undefined = undefined;
  if (response.updatedAt) {
    if (typeof response.updatedAt === 'string') {
      updatedAt = response.updatedAt;
    } else if (response.updatedAt instanceof Date) {
      updatedAt = response.updatedAt;
    }
  }

  const result: DisplayShoppingCart = {
    _id,
    userId,
    groupId,
    items,
    total: calculateTotal(items),
    isFinalized,
    createdAt,
    updatedAt
  };

  console.log('transformCartResponse - final result:', result);
  return result;
};

/**
 * Transforms a raw cart item from the API into a standardized display cart item
 */
export const transformCartItem = (item: Record<string, unknown>): DisplayCartItem => {
  // Debug the incoming item structure
  console.log('Transforming cart item:', JSON.stringify(item, null, 2));

  // Handle different possible structures of the product field
  let productId = '';
  let name = 'Unknown Product';
  let price = 0;
  let image = '';
  let description = '';

  if (item.product) {
    if (typeof item.product === 'string') {
      // If product is just a string ID
      productId = item.product;
    } else if (typeof item.product === 'object') {
      // If product is an object with properties
      const product = item.product as Record<string, unknown>;
      productId = product._id?.toString() || '';
      name = product.name?.toString() || 'Unknown Product';
      price = typeof product.price === 'number' ? product.price : 0;
      image = product.image?.toString() || '';
      description = product.description?.toString() || '';
    }
  }

  // Ensure quantity is a number
  const quantity = typeof item.quantity === 'number' ? item.quantity : 1;

  // Calculate subtotal
  const subtotal = price * quantity;

  return {
    _id: item._id?.toString() || '',
    productId,
    name,
    price,
    image,
    description,
    quantity,
    subtotal
  };
};

/**
 * Creates an empty shopping cart
 */
export const createEmptyCart = (): DisplayShoppingCart => ({
  _id: '',
  userId: '',
  groupId: '',
  items: [],
  total: 0,
  isFinalized: false
});

/**
 * Calculates the total price of all items in the cart
 */
export const calculateTotal = (items: DisplayCartItem[]): number => {
  return items.reduce((sum, item) => sum + item.subtotal, 0);
};

/**
 * Transforms a raw group order response from the API into a standardized group order
 */
export const transformGroupOrderResponse = (response: Record<string, unknown>): GroupOrder => {
  if (!response) {
    return createEmptyGroupOrder();
  }

  // Ensure arrays before mapping
  const orderItemsArray = Array.isArray(response.orderItems) ? response.orderItems : [];
  const userContributionsArray = Array.isArray(response.userContributions) ? response.userContributions : [];

  const orderItems = orderItemsArray.map(transformGroupOrderItem) || [];
  const userContributions = userContributionsArray.map(transformUserContribution) || [];

  // Safely extract properties with type checking
  const _id = typeof response._id === 'string' ? response._id : '';
  const groupId = typeof response.groupId === 'string' ? response.groupId : '';
  const totalOrderValue = typeof response.totalOrderValue === 'number' ? response.totalOrderValue : 0;

  // Handle status with proper type checking for GroupOrderStatus
  let status: GroupOrderStatus = GroupOrderStatus.DRAFT;
  if (typeof response.status === 'string') {
    switch (response.status) {
      case 'pending':
        status = GroupOrderStatus.PENDING;
        break;
      case 'processing':
        status = GroupOrderStatus.PROCESSING;
        break;
      case 'ready_for_delivery':
        status = GroupOrderStatus.READY_FOR_DELIVERY;
        break;
      case 'shipped':
        status = GroupOrderStatus.SHIPPED;
        break;
      case 'completed':
        status = GroupOrderStatus.COMPLETED;
        break;
      case 'cancelled':
        status = GroupOrderStatus.CANCELLED;
        break;
      default:
        status = GroupOrderStatus.DRAFT;
    }
  }

  // Handle arrays
  const statusHistory = Array.isArray(response.statusHistory) ? response.statusHistory : [];
  const milestones = Array.isArray(response.milestones) ? response.milestones : [];
  const bulkDiscountTiers = Array.isArray(response.bulkDiscountTiers) ? response.bulkDiscountTiers : [];

  // Handle dates
  let orderPlacedAt = new Date();
  if (response.orderPlacedAt) {
    try {
      orderPlacedAt = new Date(response.orderPlacedAt as string | number | Date);
    } catch {
      console.error('Invalid orderPlacedAt date:', response.orderPlacedAt);
    }
  }

  let lastUpdatedAt = new Date();
  if (response.lastUpdatedAt) {
    try {
      lastUpdatedAt = new Date(response.lastUpdatedAt as string | number | Date);
    } catch {
      console.error('Invalid lastUpdatedAt date:', response.lastUpdatedAt);
    }
  }

  // Handle payment status with proper type checking
  let paymentStatus: 'unpaid' | 'partially_paid' | 'fully_paid' = 'unpaid';
  if (typeof response.paymentStatus === 'string') {
    switch (response.paymentStatus) {
      case 'partially_paid':
        paymentStatus = 'partially_paid';
        break;
      case 'fully_paid':
        paymentStatus = 'fully_paid';
        break;
      default:
        paymentStatus = 'unpaid';
    }
  }
  const groupOrderNotes = typeof response.groupOrderNotes === 'string' ? response.groupOrderNotes : undefined;

  // Handle appliedDiscountTier which could be an object
  let appliedDiscountTier = undefined;
  if (response.appliedDiscountTier && typeof response.appliedDiscountTier === 'object') {
    const tier = response.appliedDiscountTier as Record<string, unknown>;
    if ('threshold' in tier && 'discountPercentage' in tier) {
      appliedDiscountTier = {
        threshold: typeof tier.threshold === 'number' ? tier.threshold : 0,
        discountPercentage: typeof tier.discountPercentage === 'number' ? tier.discountPercentage : 0
      };
    }
  }

  return {
    _id,
    groupId,
    orderItems,
    userContributions,
    totalOrderValue,
    status,
    statusHistory,
    milestones,
    bulkDiscountTiers,
    appliedDiscountTier,
    orderPlacedAt,
    lastUpdatedAt,
    paymentStatus,
    groupOrderNotes
  };
};

/**
 * Transforms a raw group order item from the API into a standardized group order item
 */
export const transformGroupOrderItem = (item: Record<string, unknown>): GroupOrderItem => {
  // Handle product which could be an object or string
  let productId = '';
  let productObject = undefined;

  if (item.product) {
    if (typeof item.product === 'string') {
      productId = item.product;
    } else if (typeof item.product === 'object') {
      const product = item.product as Record<string, unknown>;
      productId = typeof product._id === 'string' ? product._id : '';

      productObject = {
        _id: typeof product._id === 'string' ? product._id : '',
        name: typeof product.name === 'string' ? product.name : 'Unknown Product',
        price: typeof product.price === 'number' ? product.price : 0,
        image: typeof product.image === 'string' ? product.image : ''
      };
    }
  }

  return {
    productId,
    product: productObject,
    quantity: typeof item.quantity === 'number' ? item.quantity : 0,
    userId: typeof item.userId === 'string' ? item.userId : '',
    unitPrice: typeof item.unitPrice === 'number' ? item.unitPrice : 0,
    subtotal: typeof item.subtotal === 'number' ? item.subtotal : 0
  };
};

/**
 * Transforms a raw user contribution from the API into a standardized user contribution
 */
export const transformUserContribution = (contribution: Record<string, unknown>): UserContribution => {
  // Safely extract properties with type checking
  const userId = typeof contribution.userId === 'string' ? contribution.userId : '';
  const userName = typeof contribution.userName === 'string' ? contribution.userName : 'Unknown User';
  const totalSpent = typeof contribution.totalSpent === 'number' ? contribution.totalSpent : 0;
  const contributionPercentage = typeof contribution.contributionPercentage === 'number' ? contribution.contributionPercentage : 0;

  // Handle items array
  const itemsArray = Array.isArray(contribution.items) ? contribution.items : [];
  const items = itemsArray.map(transformGroupOrderItem);

  return {
    userId,
    userName,
    totalSpent,
    contributionPercentage,
    items
  };
};

/**
 * Creates an empty group order
 */
export const createEmptyGroupOrder = (): GroupOrder => ({
  _id: '',
  groupId: '',
  orderItems: [],
  userContributions: [],
  totalOrderValue: 0,
  status: GroupOrderStatus.DRAFT,
  statusHistory: [],
  milestones: [],
  bulkDiscountTiers: [],
  orderPlacedAt: new Date(),
  lastUpdatedAt: new Date(),
  paymentStatus: 'unpaid'
});

/**
 * Calculates discount based on total order value and discount tiers
 */
export const calculateDiscount = (
  totalOrderValue: number,
  discountTiers: { threshold: number; discountPercentage: number }[]
): DiscountCalculationResult => {
  const sortedTiers = [...discountTiers].sort((a, b) => b.threshold - a.threshold);
  const applicableTier = sortedTiers.find((tier) => totalOrderValue >= tier.threshold);

  if (!applicableTier) {
    return {
      discountPercentage: 0,
      discountAmount: 0,
      finalOrderValue: totalOrderValue
    };
  }

  const discountAmount = totalOrderValue * (applicableTier.discountPercentage / 100);
  const finalOrderValue = totalOrderValue - discountAmount;

  return {
    discountPercentage: applicableTier.discountPercentage,
    discountAmount,
    finalOrderValue,
    appliedTier: applicableTier
  };
};
