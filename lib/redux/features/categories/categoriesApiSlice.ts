import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { ProductCategory } from '@/types/productCategory';

export const categoriesApiSlice = createApi({
  reducerPath: 'categoriesApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/product-categories' }),
  tagTypes: ['Category'],
  endpoints: (builder) => ({
    getCategories: builder.query<ProductCategory[], void>({
      query: () => '/get-all',
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ _id }) => ({ type: 'Category' as const, id: _id })),
              { type: 'Category', id: 'LIST' },
            ]
          : [{ type: 'Category', id: 'LIST' }],
    }),
    createCategory: builder.mutation<ProductCategory, Partial<ProductCategory>>({
      query: (category) => ({
        url: '/create',
        method: 'POST',
        body: category,
      }),
      invalidatesTags: [{ type: 'Category', id: 'LIST' }],
    }),
    updateCategory: builder.mutation<ProductCategory, { id: string; updateData: Partial<ProductCategory> }>({
      query: ({ id, updateData }) => ({
        url: '/update',
        method: 'PATCH',
        body: { id, updateData },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Category', id }],
    }),
    deleteCategory: builder.mutation<void, string>({
      query: (id) => ({
        url: '/delete',
        method: 'DELETE',
        body: { id },
      }),
      invalidatesTags: [{ type: 'Category', id: 'LIST' }],
    }),
  }),
});

export const {
  useGetCategoriesQuery,
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation,
} = categoriesApiSlice;
