// lib/redux/features/wishlist/wishlistApiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { IWishlist } from '@/models/Wishlist';

export interface WishlistSummary {
  totalWishlists: number;
  totalItems: number;
  totalValue: number;
  recentlyAdded: any[];
  topPriority: any[];
}

export interface AddToWishlistInput {
  userId: string;
  productId: string;
  wishlistId?: string;
  notes?: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface CreateWishlistInput {
  userId: string;
  name?: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface UpdateWishlistInput {
  wishlistId: string;
  userId: string;
  name?: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface RemoveFromWishlistInput {
  wishlistId: string;
  userId: string;
  productId: string;
}

export interface CheckProductInWishlistInput {
  userId: string;
  productId: string;
}

export const wishlistApiSlice = createApi({
  reducerPath: 'wishlistApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  tagTypes: ['Wishlist', 'WishlistSummary', 'ProductWishlistStatus'],
  endpoints: (builder) => ({
    // Get user's wishlists
    getUserWishlists: builder.query<IWishlist[], string>({
      query: (userId) => `/wishlist?userId=${userId}`,
      providesTags: (result, error, userId) => [
        { type: 'Wishlist', id: 'LIST' },
        { type: 'Wishlist', id: userId },
      ],
    }),

    // Get wishlist summary
    getWishlistSummary: builder.query<WishlistSummary, string>({
      query: (userId) => `/wishlist?userId=${userId}&summary=true`,
      providesTags: (result, error, userId) => [
        { type: 'WishlistSummary', id: userId },
      ],
    }),

    // Get single wishlist by ID
    getWishlistById: builder.query<IWishlist, { wishlistId: string; userId?: string }>({
      query: ({ wishlistId, userId }) => 
        `/wishlist/${wishlistId}${userId ? `?userId=${userId}` : ''}`,
      providesTags: (result, error, { wishlistId }) => [
        { type: 'Wishlist', id: wishlistId },
      ],
    }),

    // Check if product is in wishlist
    checkProductInWishlist: builder.query<{ isInWishlist: boolean }, CheckProductInWishlistInput>({
      query: ({ userId, productId }) => 
        `/wishlist?userId=${userId}&productId=${productId}&checkProduct=true`,
      providesTags: (result, error, { userId, productId }) => [
        { type: 'ProductWishlistStatus', id: `${userId}-${productId}` },
      ],
    }),

    // Add product to wishlist
    addToWishlist: builder.mutation<{ wishlist: IWishlist; wishlistId: string }, AddToWishlistInput>({
      query: (input) => ({
        url: '/wishlist',
        method: 'POST',
        body: { action: 'add-to-wishlist', ...input },
      }),
      invalidatesTags: (result, error, { userId, productId }) => [
        { type: 'Wishlist', id: 'LIST' },
        { type: 'Wishlist', id: userId },
        { type: 'WishlistSummary', id: userId },
        { type: 'ProductWishlistStatus', id: `${userId}-${productId}` },
      ],
    }),

    // Create new wishlist
    createWishlist: builder.mutation<{ wishlist: IWishlist; wishlistId: string }, CreateWishlistInput>({
      query: (input) => ({
        url: '/wishlist',
        method: 'POST',
        body: { action: 'create-wishlist', ...input },
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: 'Wishlist', id: 'LIST' },
        { type: 'Wishlist', id: userId },
        { type: 'WishlistSummary', id: userId },
      ],
    }),

    // Update wishlist
    updateWishlist: builder.mutation<{ wishlist: IWishlist }, UpdateWishlistInput>({
      query: ({ wishlistId, ...input }) => ({
        url: `/wishlist/${wishlistId}`,
        method: 'PATCH',
        body: input,
      }),
      invalidatesTags: (result, error, { wishlistId, userId }) => [
        { type: 'Wishlist', id: wishlistId },
        { type: 'Wishlist', id: userId },
        { type: 'WishlistSummary', id: userId },
      ],
    }),

    // Remove item from wishlist
    removeFromWishlist: builder.mutation<{ wishlist: IWishlist }, RemoveFromWishlistInput>({
      query: ({ wishlistId, ...input }) => ({
        url: `/wishlist/${wishlistId}`,
        method: 'PATCH',
        body: { action: 'remove-item', ...input },
      }),
      invalidatesTags: (result, error, { wishlistId, userId, productId }) => [
        { type: 'Wishlist', id: wishlistId },
        { type: 'Wishlist', id: userId },
        { type: 'WishlistSummary', id: userId },
        { type: 'ProductWishlistStatus', id: `${userId}-${productId}` },
      ],
    }),

    // Delete wishlist
    deleteWishlist: builder.mutation<{ message: string }, { wishlistId: string; userId: string }>({
      query: ({ wishlistId, userId }) => ({
        url: `/wishlist/${wishlistId}?userId=${userId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { wishlistId, userId }) => [
        { type: 'Wishlist', id: wishlistId },
        { type: 'Wishlist', id: 'LIST' },
        { type: 'Wishlist', id: userId },
        { type: 'WishlistSummary', id: userId },
      ],
    }),
  }),
});

export const {
  useGetUserWishlistsQuery,
  useGetWishlistSummaryQuery,
  useGetWishlistByIdQuery,
  useCheckProductInWishlistQuery,
  useAddToWishlistMutation,
  useCreateWishlistMutation,
  useUpdateWishlistMutation,
  useRemoveFromWishlistMutation,
  useDeleteWishlistMutation,
} = wishlistApiSlice;
