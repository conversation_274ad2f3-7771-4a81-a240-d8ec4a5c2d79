// lib/frontendProductUtilities.ts

"use client"

import { useQuery, useMutation, useQueryClient, type UseMutationResult } from "@tanstack/react-query"
import { Product } from "../types/product"
import axios from 'axios';

export type CreateProductInput = FormData

export interface UpdateProductInput {
  id: string
  updateData: Partial<Omit<Product, "_id" | "createdAt" | "updatedAt">> & {
    image?: string
  }
}

export async function getAllProducts(): Promise<Product[]> {
  const response = await fetch("/api/products/get-all", { method: "GET" })
  if (!response.ok) {
    throw new Error("Failed to fetch Products")
  }
  return response.json()
}

export async function createProduct(data: CreateProductInput): Promise<Product> {
  const response = await fetch("/api/products/create", {
    method: "POST",
    body: data,
  })
  if (!response.ok) {
    throw new Error("Failed to create Product")
  }
  return response.json()
}

export async function updateProduct(input: UpdateProductInput): Promise<Product> {
  const response = await fetch("/api/products/update", {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(input),
  })
  if (!response.ok) {
    throw new Error("Failed to update Product")
  }
  return response.json()
}

export async function deleteProduct(id: string): Promise<void> {
  const response = await fetch("/api/products/delete", {
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ id }),
  })
  if (!response.ok) {
    throw new Error("Failed to delete Product")
  }
}

export async function getArchivedProducts(): Promise<Product[]> {
  const response = await fetch("/api/products/get-archived", { method: "GET" });
  if (!response.ok) {
    throw new Error("Failed to fetch Archived Products");
  }
  const data = await response.json();
  
  // Ensure we return an array
  return Array.isArray(data) 
    ? data 
    : [];
}

export const restoreArchivedProduct = async (productId: string): Promise<Product> => {
  try {
    const response = await fetch("/api/products/restore", {
      method: "POST",
      credentials: 'include', // Include cookies for authentication
      headers: { 
        "Content-Type": "application/json" 
      },
      body: JSON.stringify({ productId }),
    });
    
    // Try to parse the response body
    const responseBody = await response.text();

    if (!response.ok) {
      // Attempt to parse error details from the response
      let errorMessage = "Failed to restore archived product";
      try {
        const errorData = JSON.parse(responseBody);
        errorMessage = errorData.error || errorMessage;
      } catch {
        // If parsing fails, use the response text or status
        errorMessage = responseBody || `HTTP error! status: ${response.status}`;
      }

      throw new Error(errorMessage);
    }
    
    // Parse and return the restored product
    return JSON.parse(responseBody);
  } catch (error) {
    console.error("Restore archived product error:", error);
    throw error;
  }
}

export const archiveProduct = async (productId: string) => {
  try {
    const response = await axios.delete(`/api/products/delete?id=${productId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.status !== 200) {
      throw new Error('Failed to archive product');
    }

    return response.data;
  } catch (error) {
    console.error('Error archiving product:', error);
    throw error;
  }
};

export function useGetAllProducts() {
  return useQuery<Product[], Error>({
    queryKey: ["products"],
    queryFn: getAllProducts,
  })
}

export function useCreateProduct(): UseMutationResult<Product, Error, CreateProductInput, unknown> {
  const queryClient = useQueryClient()

  return useMutation<Product, Error, CreateProductInput, unknown>({
    mutationFn: createProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}

export function useUpdateProduct(): UseMutationResult<Product, Error, UpdateProductInput, unknown> {
  const queryClient = useQueryClient()

  return useMutation<Product, Error, UpdateProductInput, unknown>({
    mutationFn: updateProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}

export function useDeleteProduct(): UseMutationResult<void, Error, string, unknown> {
  const queryClient = useQueryClient()

  return useMutation<void, Error, string, unknown>({
    mutationFn: deleteProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}

export function useGetArchivedProducts() {
  return useQuery<Product[], Error>({
    queryKey: ["archived-products"],
    queryFn: getArchivedProducts,
  })
}
