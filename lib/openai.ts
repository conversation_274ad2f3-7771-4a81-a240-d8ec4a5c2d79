// // lib/openai.ts
// import { openai as OpenAIClient } from '@ai-sdk/openai';

// export const openai = (modelName: string) =>
//   new OpenAIClient({
//     apiKey: process.env.OPENAI_API_KEY || '',
//     modelName,
//   });


// lib/openai.ts
import { openai } from '@ai-sdk/openai';

// Here we directly pass a literal model id. 
// For example, using the chat model "gpt-3.5-turbo-instruct":
export const openaiModel = () => openai("gpt-3.5-turbo-instruct");
