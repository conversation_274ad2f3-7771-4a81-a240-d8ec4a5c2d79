// lib/backendGroupUtilities.ts

import { StokvelGroup, IStokvelGroup } from '@/models/StokvelGroup';
import mongoose from 'mongoose';

/**
 * CREATE a new StokvelGroup
 */
export async function createStokvelGroup(
  name: string,
  description: string,
  admin: mongoose.Types.ObjectId,
  geolocation: string,
  members: mongoose.Types.ObjectId[] = []
): Promise<IStokvelGroup> {
  const newGroup = new StokvelGroup({
    name,
    description,
    admin,
    geolocation,
    members,
  });
  return await newGroup.save();
}

/**
 * READ: Get all StokvelGroups
 */
export async function getAllStokvelGroups(): Promise<IStokvelGroup[]> {
  return StokvelGroup.find().exec();
}

/**
 * READ: Get a single StokvelGroup by its ID
 */
export async function getStokvelGroupById(id: string): Promise<IStokvelGroup | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return StokvelGroup.findById(id).exec();
}

/**
 * UPDATE a StokvelGroup by ID
 */
export async function updateStokvelGroup(
  id: string,
  updateData: Partial<IStokvelGroup>
): Promise<IStokvelGroup | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return StokvelGroup.findByIdAndUpdate(id, updateData, { new: true }).exec();
}

/**
 * DELETE a StokvelGroup by ID
 */
export async function deleteStokvelGroup(id: string): Promise<IStokvelGroup | null> {
    if (!mongoose.Types.ObjectId.isValid(id)) return null;
    return StokvelGroup.findByIdAndDelete(id).exec();
  }
  
