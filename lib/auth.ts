

// lib/auth.ts
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { User } from '@/models/User';
import { connectToDatabase } from './dbconnect';
import createTransporter from './oauth2';
import crypto from 'crypto';
import { NextRequest, NextResponse } from 'next/server';

// Constants
const ACCESS_TOKEN_EXPIRY = '2h'; // Access token expiration
const REFRESH_TOKEN_EXPIRY_REMEMBER_MS = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
const REFRESH_TOKEN_EXPIRY_DEFAULT_MS = 1 * 24 * 60 * 60 * 1000; // 1 day in milliseconds
const REFRESH_TOKEN_EXPIRY_RESET_PASSWORD_MS = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

// ---------------------- TOKEN MANAGEMENT ----------------------

// Generate Access Token
export const generateAccessToken = (userId: string, role: string): string => {
  return jwt.sign({ id: userId, role }, process.env.JWT_SECRET!, { expiresIn: ACCESS_TOKEN_EXPIRY });
};

// Generate Refresh Token
export const generateRefreshToken = async (
  userId: string,
  role: string,
  tokenVersion: number,
  rememberMe: boolean
): Promise<string> => {
  if (!process.env.JWT_REFRESH_SECRET) {
    throw new Error('JWT_REFRESH_SECRET is not defined');
  }

  const refreshToken = jwt.sign(
    { id: userId, role, version: tokenVersion, rememberMe },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: rememberMe ? '30d' : '1d' }
  );

  const hashedRefreshToken = crypto.createHash('sha256').update(refreshToken).digest('hex');

  const refreshTokenExpiry = new Date(
    Date.now() + (rememberMe ? REFRESH_TOKEN_EXPIRY_REMEMBER_MS : REFRESH_TOKEN_EXPIRY_DEFAULT_MS)
  );

  await User.findByIdAndUpdate(userId, {
    refreshTokenHash: hashedRefreshToken,
    refreshTokenExpiry,
  });

  return refreshToken;
};

// Verify Access Token
export const verifyAccessToken = async (token: string): Promise<jwt.JwtPayload> => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!) as jwt.JwtPayload;
  } catch (error) {
    console.error('Access token verification failed:', error);
    throw new Error('Invalid or expired access token');
  }
};

// Verify Refresh Token
export const verifyRefreshToken = async (token: string): Promise<jwt.JwtPayload> => {
  if (!process.env.JWT_REFRESH_SECRET) {
    throw new Error('JWT_REFRESH_SECRET is not defined');
  }

  const payload = jwt.verify(token, process.env.JWT_REFRESH_SECRET) as jwt.JwtPayload;
  const user = await User.findById(payload.id);

  if (!user) {
    console.error('Refresh token validation failed: User not found');
    throw new Error('Invalid or expired refresh token');
  }

  if (user.refreshTokenHash) {
    const hashedIncomingToken = crypto.createHash('sha256').update(token).digest('hex');
    if (user.refreshTokenHash !== hashedIncomingToken) {
      console.error('Refresh token validation failed: Token mismatch');
      throw new Error('Invalid refresh token');
    }
  } else {
    console.error('Refresh token validation failed: No refresh token hash found');
    throw new Error('Invalid refresh token');
  }

  if (user.tokenVersion !== payload.version) {
    console.error(`Token version mismatch: user.tokenVersion=${user.tokenVersion}, token.version=${payload.version}`);
    throw new Error('Token version mismatch. Refresh token is invalid.');
  }

  if (user.refreshTokenExpiry && new Date() > user.refreshTokenExpiry) {
    console.error('Refresh token has expired');
    throw new Error('Refresh token has expired');
  }

  return payload;
};

// ---------------------- PASSWORD MANAGEMENT ----------------------

// Hash Password
export const hashPassword = async (password: string): Promise<string> => bcrypt.hash(password, 12);

// Compare Passwords
export const comparePasswords = async (plainPassword: string, hashedPassword: string): Promise<boolean> =>
  bcrypt.compare(plainPassword, hashedPassword);

// Generate Reset Code
export const generateResetCode = (): string => Math.floor(100000 + Math.random() * 900000).toString();

// Create Reset Code
export const createResetCode = async (userId: string): Promise<string> => {
  const resetCode = generateResetCode();
  const resetCodeExpires = new Date(Date.now() + 600000); // 10 minutes
  await User.findByIdAndUpdate(userId, { resetCode, resetCodeExpires });
  return resetCode;
};

// Send Password Reset Email
export const sendPasswordResetEmail = async (email: string, resetCode: string): Promise<void> => {
  const mailOptions = {
    from: process.env.GMAIL_USER,
    to: email,
    subject: 'Password Reset Code',
    text: `Your password reset code is: ${resetCode}. It is valid for 10 minutes.`,
  };

  try {
    const transporter = await createTransporter();
    await transporter.sendMail(mailOptions);
  } catch (error) {
    console.error('Error verifying access token:', error);
    throw new Error('Invalid or expired access token');
  }
  
};

// Reset Password
export const resetPassword = async (email: string, newPassword: string): Promise<string> => {
  await connectToDatabase();
  const user = await User.findOne({ email });

  if (!user) {
    throw new Error('User not found');
  }

  user.password = await hashPassword(newPassword); // Hash and update password
  user.tokenVersion += 1; // Increment token version

  const newRefreshToken = await generateRefreshToken(
    user._id.toString(),
    user.role,
    user.tokenVersion,
    false
  );

  user.refreshTokenHash = crypto.createHash('sha256').update(newRefreshToken).digest('hex'); // Update refreshTokenHash
  user.refreshTokenExpiry = new Date(Date.now() + REFRESH_TOKEN_EXPIRY_RESET_PASSWORD_MS); // 7 days
  await user.save(); // Ensure changes are persisted
  return newRefreshToken;
};

// Authenticate User
export const authenticateUser = async (
  email: string,
  password: string,
  rememberMe: boolean
): Promise<{
  user: { id: string; email: string; name: string; role: string };
  accessToken: string;
  refreshToken: string;
  accessTokenExpiresIn: number;
}> => {
  await connectToDatabase();
  const user = await User.findOne({ email });

  if (!user) {
    console.error(`Authentication failed: User not found for email: ${email}`);
    throw new Error('Invalid email or password');
  }

  const isPasswordValid = await comparePasswords(password, user.password);
  if (!isPasswordValid) {
    console.error(`Authentication failed: Invalid password for user with email: ${email}`);
    throw new Error('Invalid email or password');
  }

  console.log(`Authentication successful for user: ${user.email}`);

  const accessToken = generateAccessToken(user._id.toString(), user.role);
  const accessTokenExpiresIn = 2 * 60 * 60; // 2 hours in seconds
  const refreshToken = await generateRefreshToken(user._id.toString(), user.role, user.tokenVersion, rememberMe);

  return {
    user: {
      id: user._id.toString(),
      email: user.email,
      name: user.name,
      role: user.role,
    },
    accessToken,
    refreshToken,
    accessTokenExpiresIn,
  };
};

// Refresh Access Token
export const refreshAccessToken = async (
  refreshToken: string
): Promise<{ accessToken: string; refreshToken: string; rememberMe: boolean }> => {
  const payload = await verifyRefreshToken(refreshToken);
  const user = await User.findById(payload.id);

  if (!user) {
    throw new Error('User not found');
  }

  const newAccessToken = generateAccessToken(user._id.toString(), user.role);
  const newRefreshToken = await generateRefreshToken(
    user._id.toString(),
    user.role,
    user.tokenVersion,
    payload.rememberMe
  );

  return { accessToken: newAccessToken, refreshToken: newRefreshToken, rememberMe: payload.rememberMe };
};

// Logout User
export const logoutUser = async (userId: string | null, refreshToken?: string): Promise<void> => {
  await connectToDatabase();

  if (userId) {
    // Logout using userId: Increment tokenVersion and clear token-related fields
    await User.findByIdAndUpdate(userId, {
      $inc: { tokenVersion: 1 },
      $unset: { refreshTokenHash: 1, refreshTokenExpiry: 1 },
    });
  } else if (refreshToken) {
    // Logout using refreshToken: Find the user by hashed token and perform the same operation
    const hashedToken = crypto.createHash('sha256').update(refreshToken).digest('hex');
    const user = await User.findOne({ refreshTokenHash: hashedToken });

    if (user) {
      await User.findByIdAndUpdate(user._id, {
        $inc: { tokenVersion: 1 },
        $unset: { refreshTokenHash: 1, refreshTokenExpiry: 1 },
      });
    } else {
      throw new Error('Invalid or expired refresh token');
    }
  } else {
    throw new Error('Either userId or refreshToken must be provided');
  }
};

// ---------------------- AUTHORIZATION ----------------------


// Higher-Order Function for Protected Routes
export const requireAuth = (handler: (req: NextRequest, res: NextResponse, user: jwt.JwtPayload) => Promise<Response>) => {
  return async (req: NextRequest, res: NextResponse) => {
    const authHeader = req.headers.get('authorization');

    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header is required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      return NextResponse.json({ error: 'Token not provided' }, { status: 401 });
    }

    try {
      const payload = await verifyAccessToken(token);
      return handler(req, res, payload);
    } 
    
    // catch (_error) {
    //   // handle the error in a generic way without referencing _error
    //   return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 });
    // }
    catch {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 });
    }
    
    
  };
};


export const verifyResetCode = async (email: string, code: string): Promise<string> => {
  const user = await User.findOne({
    email,
    resetCode: code,
    resetCodeExpires: { $gt: Date.now() },
  });

  if (!user) {
    throw new Error('Invalid or expired reset code');
  }

  return user._id.toString();
};
