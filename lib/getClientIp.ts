
// lib/getClientIp.ts

import { NextRequest } from 'next/server';

export function getClientIp(req: NextRequest): string {
  // First check the 'x-forwarded-for' header
  const forwardedFor = req.headers.get('x-forwarded-for');
  if (forwardedFor) {
    // x-forwarded-for can contain multiple IP addresses in a comma-separated list:
    // e.g., "client IP, proxy1 IP, proxy2 IP"
    // So take the first part as the "actual" client IP
    const clientIp = forwardedFor.split(',')[0].trim();
    if (clientIp) {
      return clientIp;
    }
  }

  // Other headers you might check if your environment sets them:
  // const xRealIp = req.headers.get('x-real-ip');
  // if (xRealIp) {
  //   return xRealIp;
  // }

  // Fallback if we can’t determine from headers
  return 'unknown';
}
