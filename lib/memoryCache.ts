type CacheValue = string | number | boolean | object | null;

interface CacheItem {
  value: CacheValue;
  expires: number | null;
}

class MemoryCache {
  private cache: Map<string, CacheItem>;

  constructor() {
    this.cache = new Map();
  }

  set(key: string, value: CacheValue, ttl?: number): void {
    const expires = ttl ? Date.now() + ttl * 1000 : null;
    this.cache.set(key, { value, expires });
  }

  get(key: string): CacheValue | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (item.expires && item.expires < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  del(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }
}

// Export a singleton instance
export const memoryCache = new MemoryCache();
