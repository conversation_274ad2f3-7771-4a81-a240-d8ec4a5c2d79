// lib/langchain/documents.ts
import { Document } from "@langchain/core/documents";
import { StokvelGroup as StokvelGroupModel } from "@/models/StokvelGroup";
import type { StokvelGroup } from "@/types/stokvelgroup";
import { Product } from "@/models/Product";
// import { ShoppingCart } from "@/models/ShoppingCart";

export async function loadDocumentsFromDB() {
  const documents: Document[] = [];
  
  // Load products
  const products = await Product.find().lean();
  products.forEach(product => {
    documents.push(
      new Document({
        pageContent: `Product: ${product.name}. Price: R${product.price}. Description: ${product.description}. Stock available: ${product.stock} units.`,
        metadata: { type: 'product', id: product._id.toString() }
      })
    );
  });

  // Load stokvel groups
  const groups = (await StokvelGroupModel.find().lean()) as unknown as StokvelGroup[];
  groups.forEach(group => {
    documents.push(
      new Document({
        pageContent: `Stokvel Group: ${group.name}. Description: ${group.description}. Members: ${group.members.length}`,
        metadata: { type: 'group', id: group._id }  // group._id is already a string
      })
    );
  });

  // Add general information about the platform
  documents.push(
    new Document({
      pageContent: `StockVel Commerce is a group buying platform that allows users to form groups (stokvels) and make bulk purchases to get better prices. Users can create or join groups, participate in group orders, and benefit from bulk discounts.`,
      metadata: { type: 'general', id: 'platform-info' }
    })
  );

  return documents;
}









