  // lib/langchain/memory.ts
import { BufferMemory } from "langchain/memory";
import { MongoDBChatMessageHistory } from "@langchain/community/stores/message/mongodb";
import { MongoClient } from "mongodb";
import { v4 as uuidv4 } from 'uuid';

export async function createChatMemory(sessionId: string = uuidv4()) {
  const collectionName = "chatHistory";
  const connectionString = process.env.MONGODB_URL!;

  const client = new MongoClient(connectionString);
  await client.connect();
  const collection = client.db().collection(collectionName);

  const messageHistory = new MongoDBChatMessageHistory({
    collection,
    sessionId,
  });

  return new BufferMemory({
    chatHistory: messageHistory,
    returnMessages: true,
    memoryKey: "chat_history",
  });
}






