// lib/inMemoryCache.ts
import { IGroupOrder } from '@/models/GroupOrder'
import { IShoppingCart } from '@/models/ShoppingCart'
import { GroupOrderAnalytics } from './groupOrderAnalytics'

class InMemoryCache {
  private groupOrderCache: Map<string, IGroupOrder> = new Map()
  private shoppingCartCache: Map<string, IShoppingCart> = new Map()
  private analyticsCache: Map<string, GroupOrderAnalytics> = new Map()
  private hitCount: number = 0;
  private missCount: number = 0;

  // Group Order Caching
  setGroupOrder(key: string, value: IGroupOrder, ttl?: number): void {
    this.groupOrderCache.set(key, value)
    
    if (ttl) {
      setTimeout(() => {
        this.groupOrderCache.delete(key)
      }, ttl)
    }
  }

  getGroupOrder(key: string): IGroupOrder | undefined {
    const value = this.groupOrderCache.get(key);
    if (value) {
      this.hitCount++;
    } else {
      this.missCount++;
    }
    return value;
  }

  deleteGroupOrder(key: string): void {
    this.groupOrderCache.delete(key)
  }

  // Shopping Cart Caching
  setShoppingCart(key: string, value: IShoppingCart, ttl?: number): void {
    this.shoppingCartCache.set(key, value)
    
    if (ttl) {
      setTimeout(() => {
        this.shoppingCartCache.delete(key)
      }, ttl)
    }
  }

  getShoppingCart(key: string): IShoppingCart | undefined {
    const value = this.shoppingCartCache.get(key);
    if (value) {
      this.hitCount++;
    } else {
      this.missCount++;
    }
    return value;
  }

  deleteShoppingCart(key: string): void {
    this.shoppingCartCache.delete(key)
  }

  // Analytics Caching
  setAnalytics(key: string, value: GroupOrderAnalytics, ttl?: number): void {
    this.analyticsCache.set(key, value)
    
    if (ttl) {
      setTimeout(() => {
        this.analyticsCache.delete(key)
      }, ttl)
    }
  }

  getAnalytics(key: string): GroupOrderAnalytics | undefined {
    const value = this.analyticsCache.get(key);
    if (value) {
      this.hitCount++;
    } else {
      this.missCount++;
    }
    return value;
  }

  deleteAnalytics(key: string): void {
    this.analyticsCache.delete(key)
  }

  // Clear all caches
  clearAll(): void {
    this.groupOrderCache.clear()
    this.shoppingCartCache.clear()
    this.analyticsCache.clear()
    this.hitCount = 0;
    this.missCount = 0;
  }

  calculateHitRate(): number {
    if (this.hitCount + this.missCount === 0) {
      return 0;
    }
    return (this.hitCount / (this.hitCount + this.missCount)) * 100;
  }

  getCacheStats() {
    const groupOrderCacheSize = this.groupOrderCache.size;
    const shoppingCartCacheSize = this.shoppingCartCache.size;
    const analyticsCacheSize = this.analyticsCache.size;

    const totalCacheSize = groupOrderCacheSize + shoppingCartCacheSize + analyticsCacheSize;

    const hitRate = this.calculateHitRate();

    return {
      cacheSize: totalCacheSize,
      hitRate: hitRate,
      groupOrderCacheSize,
      shoppingCartCacheSize,
      analyticsCacheSize,
    };
  }
}

// Singleton instance
export const inMemoryCache = new InMemoryCache()
export default inMemoryCache
