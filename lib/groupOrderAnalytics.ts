import mongoose from 'mongoose'
import { GroupOrder } from '@/models/GroupOrder'

interface GroupOrderQuery {
  groupId?: mongoose.Types.ObjectId;
  createdAt?: {
    $gte?: Date;
    $lte?: Date;
  };
}

export interface GroupOrderAnalytics {
  totalGroupOrders: number
  totalOrderValue: number
  averageOrderValue: number
  topContributors: Array<{
    userId: mongoose.Types.ObjectId
    userName: string
    totalSpent: number
    contributionPercentage: number
  }>
  orderStatusDistribution: Record<string, number>
  monthlyOrderTrends: Array<{
    month: string
    totalOrders: number
    totalValue: number
  }>
}

export class GroupOrderAnalyticsService {
  static async generateComprehensiveReport(
    groupId?: mongoose.Types.ObjectId, 
    startDate?: Date, 
    endDate?: Date
  ): Promise<GroupOrderAnalytics> {
    const query: GroupOrderQuery = {};
    
    if (groupId) query.groupId = groupId;
    if (startDate && endDate) {
      query.createdAt = {
        $gte: startDate,
        $lte: endDate
      };
    }

    // Total Group Orders
    const totalGroupOrders = await GroupOrder.countDocuments(query)

    // Total and Average Order Value
    const aggregateResults = await GroupOrder.aggregate([
      { $match: query },
      { 
        $group: {
          _id: null,
          totalOrderValue: { $sum: '$totalOrderValue' },
          averageOrderValue: { $avg: '$totalOrderValue' }
        }
      }
    ])

    const { totalOrderValue, averageOrderValue } = aggregateResults[0] || {
      totalOrderValue: 0,
      averageOrderValue: 0
    }

    // Top Contributors
    const topContributors = await GroupOrder.aggregate([
      { $match: query },
      { $unwind: '$userContributions' },
      { 
        $group: {
          _id: {
            userId: '$userContributions.userId',
            userName: '$userContributions.userName'
          },
          totalSpent: { $sum: '$userContributions.totalSpent' }
        }
      },
      { $sort: { totalSpent: -1 } },
      { $limit: 5 },
      {
        $project: {
          userId: '$_id.userId',
          userName: '$_id.userName',
          totalSpent: 1,
          contributionPercentage: {
            $multiply: [
              { $divide: ['$totalSpent', totalOrderValue] }, 
              100
            ]
          }
        }
      }
    ])

    // Order Status Distribution
    const orderStatusResults = await GroupOrder.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]).exec()

    const orderStatusDistribution = orderStatusResults.reduce((acc: Record<string, number>, status: { _id: string; count: number }) => {
      acc[status._id] = status.count;
      return acc;
    }, {});

    // Monthly Order Trends
    const monthlyOrderTrends = await GroupOrder.aggregate([
      { $match: query },
      {
        $group: {
          _id: { 
            $dateToString: { 
              format: '%Y-%m', 
              date: '$createdAt' 
            } 
          },
          totalOrders: { $sum: 1 },
          totalValue: { $sum: '$totalOrderValue' }
        }
      },
      { $sort: { _id: 1 } }
    ])

    return {
      totalGroupOrders,
      totalOrderValue,
      averageOrderValue,
      topContributors,
      orderStatusDistribution,
      monthlyOrderTrends: monthlyOrderTrends.map(trend => ({
        month: trend._id,
        totalOrders: trend.totalOrders,
        totalValue: trend.totalValue
      }))
    }
  }

  // Additional methods for generating specific reports
  static async generateUserContributionReport(userId: mongoose.Types.ObjectId) {
    // Detailed report for a specific user's contributions
    return await GroupOrder.aggregate([
      { $match: { 'userContributions.userId': userId } },
      { $unwind: '$userContributions' },
      { 
        $match: { 
          'userContributions.userId': userId 
        } 
      },
      {
        $group: {
          _id: null,
          totalGroupOrders: { $sum: 1 },
          totalSpent: { $sum: '$userContributions.totalSpent' },
          averageOrderValue: { $avg: '$userContributions.totalSpent' }
        }
      }
    ])
  }
}
