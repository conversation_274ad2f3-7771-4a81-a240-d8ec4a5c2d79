import type { ShoppingCart, Product } from '@/lib/frontendShoppingCartUtilities';

const STORAGE_KEYS = {
  CART: 'stokvel-cart',
  CART_ITEMS: 'stokvel-cart-items',
  CART_TOTALS: 'stokvel-cart-totals',
} as const;

interface CartTotals {
  totalItems: number;
  subtotal: number;
}

// Helper functions to avoid 'this' context issues
const saveToLocalStorage = (key: string, data: unknown): void => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.setItem(key, JSON.stringify(data));
    }
  } catch (error) {
    console.error(`Error saving to local storage for key ${key}:`, error);
  }
};

const getFromLocalStorage = <T>(key: string): T | null => {
  try {
    if (typeof window !== 'undefined') {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    }
    return null;
  } catch (error) {
    console.error(`Error reading from local storage for key ${key}:`, error);
    return null;
  }
};

export const hybridCartStorage = {
  // Get cart from local storage
  getLocalCart: (): ShoppingCart | null => {
    const cartData = getFromLocalStorage(STORAGE_KEYS.CART);
    if (
      cartData !== null &&
      typeof cartData === 'object' &&
      'items' in cartData &&
      'total' in cartData &&
      'isFinalized' in cartData &&
      Array.isArray((cartData as ShoppingCart).items)
    ) {
      return cartData as ShoppingCart;
    }
    return null;
  },

  // Save cart to local storage
  saveLocalCart: (cart: ShoppingCart): void => {
    saveToLocalStorage(STORAGE_KEYS.CART, cart);
    hybridCartStorage.updateLocalTotals(cart);
  },

  // Update cart totals in local storage
  updateLocalTotals: (cart: ShoppingCart): void => {
    const totals: CartTotals = {
      totalItems: cart.items.reduce((sum, item) => sum + item.quantity, 0),
      subtotal: cart.items.reduce((sum, item) =>
        sum + (Number(item.product.price) * item.quantity), 0),
    };
    saveToLocalStorage(STORAGE_KEYS.CART_TOTALS, totals);
  },

  // Get cart totals from local storage
  getLocalTotals: (): CartTotals => {
    const totals = getFromLocalStorage<CartTotals>(STORAGE_KEYS.CART_TOTALS);
    if (totals && 'totalItems' in totals && 'subtotal' in totals) {
      return totals;
    }
    return { totalItems: 0, subtotal: 0 };
  },

  // Get cart with fallback strategy
  getCart: async (userId: string): Promise<ShoppingCart | null> => {
    try {
      // Try local storage first
      const localCart = hybridCartStorage.getLocalCart();
      if (localCart) {
        return localCart;
      }

      // If local storage fails, fetch from API
      const response = await fetch(`/api/shopping-cart/get?userId=${userId}`);
      if (!response.ok) throw new Error('Failed to fetch cart');
      const serverCart = await response.json();

      // Update local storage with fresh data
      if (serverCart) {
        hybridCartStorage.saveLocalCart(serverCart);
      }
      return serverCart;

    } catch (error) {
      console.error('Error fetching cart:', error);
      return null;
    }
  },

  // Add item to cart
  addToCart: async (
    userId: string,
    productId: string,
    quantity: number,
    groupId: string
  ): Promise<ShoppingCart | null> => {
    try {
      // Update local storage immediately for optimistic UI
      const localCart = hybridCartStorage.getLocalCart();
      if (localCart) {
        // Find if product already exists in cart
        const existingItemIndex = localCart.items.findIndex(
          item => item.product._id === productId
        );

        if (existingItemIndex > -1) {
          // Set the quantity to the new value instead of incrementing
          // This prevents multiple items being added when clicking once
          localCart.items[existingItemIndex].quantity = quantity;
        } else {
          // Add new item (Note: This is temporary until server response)
          localCart.items.push({
            product: { _id: productId } as Product, // Temporary product object
            quantity: quantity,
            _id: undefined,
            image: '',
            name: '',
            price: 0  // Using 0 as a temporary value until server response
          });
        }
        hybridCartStorage.saveLocalCart(localCart);
      }

      // Update server
      const response = await fetch('/api/shopping-cart/add-item', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, productId, quantity, groupId }),
      });

      if (!response.ok) throw new Error('Failed to add item to cart');
      const updatedCart = await response.json();

      // Update local storage with server response
      hybridCartStorage.saveLocalCart(updatedCart);

      return updatedCart;
    } catch (error) {
      console.error('Error adding item to cart:', error);
      return null;
    }
  },

  // Update cart item quantity
  updateQuantity: async (
    userId: string,
    productId: string,
    quantity: number,
    groupId: string
  ): Promise<ShoppingCart | null> => {
    try {
      // Update local storage immediately for instant UI feedback
      const localCart = hybridCartStorage.getLocalCart();
      if (localCart) {
        const updatedItems = localCart.items.map(item =>
          item.product._id === productId ? { ...item, quantity } : item
        );
        const updatedLocalCart = { ...localCart, items: updatedItems };
        hybridCartStorage.saveLocalCart(updatedLocalCart);
      }

      // Update server
      const response = await fetch('/api/shopping-cart/update-quantity', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, productId, quantity, groupId }),
      });

      if (!response.ok) throw new Error('Failed to update quantity');
      const serverCart = await response.json();

      // Update local storage with server response
      hybridCartStorage.saveLocalCart(serverCart);

      return serverCart;
    } catch (error) {
      console.error('Error updating quantity:', error);
      return null;
    }
  },

  // Remove item from cart
  removeFromCart: async (
    userId: string,
    productId: string,
    groupId: string
  ): Promise<ShoppingCart | null> => {
    try {
      // Update local storage immediately
      const localCart = hybridCartStorage.getLocalCart();
      let updatedLocalCart = null;

      if (localCart) {
        const updatedItems = localCart.items.filter(item =>
          item.product._id !== productId
        );
        updatedLocalCart = { ...localCart, items: updatedItems };
        hybridCartStorage.saveLocalCart(updatedLocalCart);
      }

      // Update server
      const response = await fetch('/api/shopping-cart/remove-item', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, productId, groupId }),
      });

      if (!response.ok) {
        console.warn('Server returned error when removing item, but local cart was updated');
        // Return the local cart as a fallback
        return updatedLocalCart;
      }

      const serverCart = await response.json();

      // Update local storage with server response
      hybridCartStorage.saveLocalCart(serverCart);

      return serverCart;
    } catch (error) {
      console.error('Error removing item from cart:', error);
      return null;
    }
  },

  // Clear cart
  clearCart: async (userId: string, groupId: string): Promise<void> => {
    try {
      // Clear local storage immediately
      localStorage.removeItem(STORAGE_KEYS.CART);
      localStorage.removeItem(STORAGE_KEYS.CART_TOTALS);

      // Clear server cart
      const response = await fetch('/api/shopping-cart/clear', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, groupId }),
      });

      if (!response.ok) throw new Error('Failed to clear cart');
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  }
};

