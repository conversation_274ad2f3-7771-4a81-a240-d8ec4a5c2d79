// // lib/frontendSubscriptionUtilities.ts
//   import type {IYearEndBundle} from '../models/YearEndBundle'
//   import type {IGrocerySchoolBundle} from '../models/GrocerySchoolBundle'
//   import type {IMonthlyGroceries} from '../models/MonthlyGroceries'
//   import type {IFuneralBenefits} from '../models/FuneralBenefits'
  
//   type SubscriptionType = "yearEnd" | "grocerySchool" | "monthlyGroceries" | "funeralBenefits"
  
//   async function createSubscription(type: SubscriptionType, data: any): Promise<any> {
//     const endpoint = `/api/subscriptions/${type}`
//     const response = await fetch(endpoint, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify(data),
//     })
//     if (!response.ok) {
//       throw new Error(`Failed to create ${type} subscription`)
//     }
//     return response.json()
//   }
  
//   async function getSubscription(type: SubscriptionType, id: string): Promise<any> {
//     const endpoint = `/api/subscriptions/${type}/${id}`
//     const response = await fetch(endpoint)
//     if (!response.ok) {
//       throw new Error(`Failed to fetch ${type} subscription`)
//     }
//     return response.json()
//   }
  
//   async function updateSubscription(type: SubscriptionType, id: string, data: any): Promise<any> {
//     const endpoint = `/api/subscriptions/${type}/${id}`
//     const response = await fetch(endpoint, {
//       method: "PUT",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify(data),
//     })
//     if (!response.ok) {
//       throw new Error(`Failed to update ${type} subscription`)
//     }
//     return response.json()
//   }
  
//   async function deleteSubscription(type: SubscriptionType, id: string): Promise<void> {
//     const endpoint = `/api/subscriptions/${type}/${id}`
//     const response = await fetch(endpoint, {
//       method: "DELETE",
//     })
//     if (!response.ok) {
//       throw new Error(`Failed to delete ${type} subscription`)
//     }
//   }
  
//   export const yearEndBundle = {
//     create: (data: Omit<IYearEndBundle, "_id" | "createdAt" | "updatedAt">) => createSubscription("yearEnd", data),
//     get: (id: string) => getSubscription("yearEnd", id),
//     update: (id: string, data: Partial<IYearEndBundle>) => updateSubscription("yearEnd", id, data),
//     delete: (id: string) => deleteSubscription("yearEnd", id),
//   }
  
//   export const grocerySchoolBundle = {
//     create: (data: Omit<IGrocerySchoolBundle, "_id" | "createdAt" | "updatedAt">) =>
//       createSubscription("grocerySchool", data),
//     get: (id: string) => getSubscription("grocerySchool", id),
//     update: (id: string, data: Partial<IGrocerySchoolBundle>) => updateSubscription("grocerySchool", id, data),
//     delete: (id: string) => deleteSubscription("grocerySchool", id),
//   }
  
//   export const monthlyGroceries = {
//     create: (data: Omit<IMonthlyGroceries, "_id" | "createdAt" | "updatedAt">) =>
//       createSubscription("monthlyGroceries", data),
//     get: (id: string) => getSubscription("monthlyGroceries", id),
//     update: (id: string, data: Partial<IMonthlyGroceries>) => updateSubscription("monthlyGroceries", id, data),
//     delete: (id: string) => deleteSubscription("monthlyGroceries", id),
//   }
  
//   export const funeralBenefits = {
//     create: (data: Omit<IFuneralBenefits, "_id" | "createdAt" | "updatedAt">) =>
//       createSubscription("funeralBenefits", data),
//     get: (id: string) => getSubscription("funeralBenefits", id),
//     update: (id: string, data: Partial<IFuneralBenefits>) => updateSubscription("funeralBenefits", id, data),
//     delete: (id: string) => deleteSubscription("funeralBenefits", id),
//   }
  
  




import type {
  YearEndBundleType,
  MonthlyGroceriesType,
  GrocerySchoolBundleType,
  FuneralBenefitsType,
  SubscriptionType,
} from "../types/subscriptionTypes"

type SubscriptionTypeString = "yearEnd" | "grocerySchool" | "monthlyGroceries" | "funeralBenefits"

type CreateSubscriptionData<T> = Omit<T, "id" | "createdAt" | "updatedAt">
type UpdateSubscriptionData<T> = Partial<T>

async function createSubscription<T extends SubscriptionType>(
  type: SubscriptionTypeString,
  data: CreateSubscriptionData<T>,
): Promise<T> {
  const endpoint = `/api/subscriptions/${type}`
  const response = await fetch(endpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error(`Failed to create ${type} subscription`)
  }
  return response.json()
}

async function getSubscription<T extends SubscriptionType>(type: SubscriptionTypeString, id: string): Promise<T> {
  const endpoint = `/api/subscriptions/${type}/${id}`
  const response = await fetch(endpoint)
  if (!response.ok) {
    throw new Error(`Failed to fetch ${type} subscription`)
  }
  return response.json()
}

async function updateSubscription<T extends SubscriptionType>(
  type: SubscriptionTypeString,
  id: string,
  data: UpdateSubscriptionData<T>,
): Promise<T> {
  const endpoint = `/api/subscriptions/${type}/${id}`
  const response = await fetch(endpoint, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error(`Failed to update ${type} subscription`)
  }
  return response.json()
}

async function deleteSubscription(type: SubscriptionTypeString, id: string): Promise<void> {
  const endpoint = `/api/subscriptions/${type}/${id}`
  const response = await fetch(endpoint, {
    method: "DELETE",
  })
  if (!response.ok) {
    throw new Error(`Failed to delete ${type} subscription`)
  }
}

export const yearEndBundle = {
  create: (data: CreateSubscriptionData<YearEndBundleType>) => createSubscription<YearEndBundleType>("yearEnd", data),
  get: (id: string) => getSubscription<YearEndBundleType>("yearEnd", id),
  update: (id: string, data: UpdateSubscriptionData<YearEndBundleType>) =>
    updateSubscription<YearEndBundleType>("yearEnd", id, data),
  delete: (id: string) => deleteSubscription("yearEnd", id),
}

export const grocerySchoolBundle = {
  create: (data: CreateSubscriptionData<GrocerySchoolBundleType>) =>
    createSubscription<GrocerySchoolBundleType>("grocerySchool", data),
  get: (id: string) => getSubscription<GrocerySchoolBundleType>("grocerySchool", id),
  update: (id: string, data: UpdateSubscriptionData<GrocerySchoolBundleType>) =>
    updateSubscription<GrocerySchoolBundleType>("grocerySchool", id, data),
  delete: (id: string) => deleteSubscription("grocerySchool", id),
}

export const monthlyGroceries = {
  create: (data: CreateSubscriptionData<MonthlyGroceriesType>) =>
    createSubscription<MonthlyGroceriesType>("monthlyGroceries", data),
  get: (id: string) => getSubscription<MonthlyGroceriesType>("monthlyGroceries", id),
  update: (id: string, data: UpdateSubscriptionData<MonthlyGroceriesType>) =>
    updateSubscription<MonthlyGroceriesType>("monthlyGroceries", id, data),
  delete: (id: string) => deleteSubscription("monthlyGroceries", id),
}

export const funeralBenefits = {
  create: (data: CreateSubscriptionData<FuneralBenefitsType>) =>
    createSubscription<FuneralBenefitsType>("funeralBenefits", data),
  get: (id: string) => getSubscription<FuneralBenefitsType>("funeralBenefits", id),
  update: (id: string, data: UpdateSubscriptionData<FuneralBenefitsType>) =>
    updateSubscription<FuneralBenefitsType>("funeralBenefits", id, data),
  delete: (id: string) => deleteSubscription("funeralBenefits", id),
}

