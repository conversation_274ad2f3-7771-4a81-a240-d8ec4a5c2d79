
// lib/frontendShoppingCartUtilities.ts

import { useQuery, useMutation, useQueryClient, UseMutationResult } from "@tanstack/react-query";
import { Key } from 'react';

// --- Type definitions for the cart and API input ---

// lib/frontendShoppingCartUtilities.ts

// Update the Product interface
export interface Product {
  _id: string;
  name: string;
  price: number;
  image: string;
  description?: string;
  category?: string;
  stock?: number;
}

// Update the CartItem interface
// export interface CartItem {
//   _id: string;
//   product: Product;
//   quantity: number;
// }

// Update the ShoppingCart interface
export interface ShoppingCart {
  _id: string;
  user: string;
  items: CartItem[];
  total: number;
  groupId?: string;
  groupOrderId?: string | null;
  isFinalized: boolean;
  createdAt: Date;
  updatedAt: Date;
}




// export interface Product {
//   image: string;
//   id: string;
//   name: string;
//   price: number;
// }

export interface CartItem {
  _id: Key | null | undefined;
  image: string; // Changed from any to string since it's an image URL/path
  name: string;
  price: number; // Changed from any to number since it's a price value
  product: Product;
  quantity: number;
}

// export interface ShoppingCart {
//   _id: string;
//   user: string;
//   items: CartItem[];
//   total: number;
//   createdAt: Date;
//   updatedAt: Date;
// }

export interface AddToCartInput {
  userId: string;
  productId: string;
  quantity: number;
  groupId: string;
}

export interface UpdateCartItemInput {
  userId: string;
  productId: string;
  quantity: number;
}

// --- API calls via fetch ---

export async function getShoppingCart(userId: string): Promise<ShoppingCart> {
  const response = await fetch(`/api/shopping-cart/get?userId=${userId}`, { method: "GET" });
  if (!response.ok) {
    throw new Error("Failed to fetch ShoppingCart");
  }
  return response.json();
}

export async function addToCart(data: AddToCartInput): Promise<ShoppingCart> {
  const response = await fetch("/api/shopping-cart/add-item", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    const errorMessage = await response.json();
    throw new Error(errorMessage.error || "Failed to add item to cart");
  }
  return response.json();
}

export async function updateCartItem(input: UpdateCartItemInput): Promise<ShoppingCart> {
  const response = await fetch("/api/shopping-cart/update-quantity", {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(input),
  });
  if (!response.ok) {
    throw new Error("Failed to update cart item");
  }
  return response.json();
}

export async function removeFromCart(userId: string, productId: string): Promise<ShoppingCart> {
  const response = await fetch("/api/shopping-cart/remove-item", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ userId, productId }),
  });
  if (!response.ok) {
    throw new Error("Failed to remove item from cart");
  }
  return response.json();
}

export async function clearCart(userId: string): Promise<ShoppingCart> {
  const response = await fetch("/api/shopping-cart/clear", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ userId }),
  });
  if (!response.ok) {
    throw new Error("Failed to clear cart");
  }
  return response.json();
}

export async function fetchGroupOrders(groupId: string) {
  const response = await fetch(`/api/shopping-cart/group-orders/${groupId}`);
  if (!response.ok) {
    throw new Error('Failed to fetch group orders');
  }
  return await response.json();
}

// Additional group order related calls
export async function calculateGroupDiscount(groupId: string): Promise<{
  discountPercentage: number;
  discountAmount: number;
  finalOrderValue: number;
}> {
  const response = await fetch(`/api/group-order/discount?groupId=${groupId}`, { method: "GET" });
  if (!response.ok) {
    throw new Error("Failed to calculate group discount");
  }
  return response.json();
}
interface GroupOrderAnalytics {
  totalOrders: number;
  averageOrderValue: number;
  totalRevenue: number;
  popularProducts: {
    productId: string;
    name: string;
    quantity: number;
  }[];
  ordersByStatus: {
    [key: string]: number;
  };
}

export async function generateGroupOrderAnalytics(params: {
  groupId?: string;
  startDate?: Date;
  endDate?: Date;
}): Promise<GroupOrderAnalytics> {
  const queryParams = new URLSearchParams();
  if (params.groupId) queryParams.append("groupId", params.groupId);
  if (params.startDate) queryParams.append("startDate", params.startDate.toISOString());
  if (params.endDate) queryParams.append("endDate", params.endDate.toISOString());

  const response = await fetch(`/api/group-order/analytics?${queryParams.toString()}`, { method: "GET" });
  if (!response.ok) {
    throw new Error("Failed to generate group order analytics");
  }
  return response.json();
}

export async function updateGroupOrderStatus(data: {
  orderId: string;
  newStatus: string;
  userId: string;
}): Promise<void> {
  const response = await fetch("/api/group-order/update-status", {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error("Failed to update group order status");
  }
  return;
}

export interface TransferCartItemsInput {
  userId: string;
  sourceGroupId: string;
  targetGroupId: string;
}

export async function transferCartItems(data: TransferCartItemsInput): Promise<ShoppingCart> {
  const response = await fetch("/api/shopping-cart/transfer-items", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Failed to transfer cart items");
  }
  const result = await response.json();
  return result.targetCart;
}

// --- React Query hooks ---

export function useGetShoppingCart(userId: string) {
  return useQuery<ShoppingCart, Error>({
    queryKey: ["shoppingCart", userId],
    queryFn: () => getShoppingCart(userId),
  });
}

export function useAddToCart(): UseMutationResult<ShoppingCart, Error, AddToCartInput, unknown> {
  const queryClient = useQueryClient();
  return useMutation<ShoppingCart, Error, AddToCartInput, unknown>({
    mutationFn: addToCart,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["shoppingCart", data.user] });
    },
  });
}

export function useUpdateCartItem(): UseMutationResult<ShoppingCart, Error, UpdateCartItemInput, unknown> {
  const queryClient = useQueryClient();
  return useMutation<ShoppingCart, Error, UpdateCartItemInput, unknown>({
    mutationFn: updateCartItem,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["shoppingCart", data.user] });
    },
  });
}

export function useRemoveFromCart(): UseMutationResult<ShoppingCart, Error, { userId: string; productId: string }, unknown> {
  const queryClient = useQueryClient();
  return useMutation<ShoppingCart, Error, { userId: string; productId: string }, unknown>({
    mutationFn: ({ userId, productId }) => removeFromCart(userId, productId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["shoppingCart", data.user] });
    },
  });
}

export function useClearCart(): UseMutationResult<ShoppingCart, Error, string, unknown> {
  const queryClient = useQueryClient();
  return useMutation<ShoppingCart, Error, string, unknown>({
    mutationFn: clearCart,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["shoppingCart", data.user] });
    },
  });
}

export function useTransferCartItems(): UseMutationResult<ShoppingCart, Error, TransferCartItemsInput, unknown> {
  const queryClient = useQueryClient();
  return useMutation<ShoppingCart, Error, TransferCartItemsInput, unknown>({
    mutationFn: transferCartItems,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["shoppingCart", data.user] });
    },
  });
}
