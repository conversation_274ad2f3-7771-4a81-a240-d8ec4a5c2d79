// lib/seedData/southAfricanLocations.ts

/**
 * Comprehensive South African location data for seeding the hierarchical location system
 * Structure: Provinces → Cities → Townships → Locations
 */

export const SOUTH_AFRICAN_LOCATIONS = {
  provinces: [
    { name: "Gauteng", code: "GP" },
    { name: "Western Cape", code: "WC" },
    { name: "KwaZulu-Natal", code: "KZN" },
    { name: "Eastern Cape", code: "EC" },
    { name: "Free State", code: "FS" },
    { name: "Limpopo", code: "LP" },
    { name: "Mpumalanga", code: "MP" },
    { name: "North West", code: "NW" },
    { name: "Northern Cape", code: "NC" }
  ],
  
  cities: {
    "GP": [
      "Johannesburg",
      "Pretoria", 
      "Ekurhuleni",
      "Sedibeng",
      "West Rand"
    ],
    "WC": [
      "Cape Town",
      "Stellenbosch",
      "Paarl",
      "George",
      "Worcester",
      "Mossel Bay"
    ],
    "KZN": [
      "Durban",
      "Pietermaritzburg",
      "Newcastle",
      "Ladysmith",
      "Richards Bay",
      "Port Shepstone"
    ],
    "EC": [
      "Port Elizabeth",
      "East London",
      "Mthatha",
      "Grahamstown",
      "King William's Town"
    ],
    "FS": [
      "Bloemfontein",
      "Welkom",
      "Kroonstad",
      "Bethlehem",
      "Sasolburg"
    ],
    "LP": [
      "Polokwane",
      "Thohoyandou",
      "Tzaneen",
      "Giyani",
      "Musina"
    ],
    "MP": [
      "Nelspruit",
      "Witbank",
      "Middelburg",
      "Secunda",
      "Standerton"
    ],
    "NW": [
      "Mahikeng",
      "Rustenburg",
      "Klerksdorp",
      "Potchefstroom",
      "Brits"
    ],
    "NC": [
      "Kimberley",
      "Upington",
      "Springbok",
      "De Aar",
      "Kuruman"
    ]
  },
  
  townships: {
    // Gauteng - Johannesburg
    "Johannesburg": [
      "Soweto",
      "Alexandra",
      "Diepsloot",
      "Orange Farm",
      "Ivory Park",
      "Tembisa",
      "Katlehong",
      "Thokoza",
      "Vosloorus",
      "Daveyton"
    ],
    
    // Gauteng - Pretoria
    "Pretoria": [
      "Mamelodi",
      "Atteridgeville",
      "Soshanguve",
      "Hammanskraal",
      "Ga-Rankuwa",
      "Mabopane"
    ],
    
    // Western Cape - Cape Town
    "Cape Town": [
      "Khayelitsha",
      "Mitchells Plain",
      "Gugulethu",
      "Langa",
      "Nyanga",
      "Philippi",
      "Delft",
      "Manenberg",
      "Hanover Park",
      "Elsies River"
    ],
    
    // KwaZulu-Natal - Durban
    "Durban": [
      "Umlazi",
      "KwaMashu",
      "Chatsworth",
      "Phoenix",
      "Inanda",
      "Ntuzuma",
      "Lamontville",
      "Chesterville"
    ],
    
    // Eastern Cape - Port Elizabeth
    "Port Elizabeth": [
      "New Brighton",
      "Kwazakhele",
      "Zwide",
      "Motherwell",
      "Uitenhage"
    ],
    
    // Free State - Bloemfontein
    "Bloemfontein": [
      "Mangaung",
      "Botshabelo",
      "Thaba Nchu",
      "Heidedal"
    ]
  },
  
  locations: {
    // Soweto locations
    "Soweto": [
      "Orlando East",
      "Orlando West", 
      "Diepkloof",
      "Meadowlands",
      "Dobsonville",
      "Protea Glen",
      "Lenasia",
      "Pimville",
      "Kliptown",
      "Moroka"
    ],
    
    // Alexandra locations
    "Alexandra": [
      "East Bank",
      "West Bank",
      "Far East Bank",
      "Old Alexandra",
      "Stjwetla"
    ],
    
    // Khayelitsha locations
    "Khayelitsha": [
      "Site B",
      "Site C",
      "Harare",
      "Ilitha Park",
      "Makaza",
      "Makhaza",
      "Greenpoint",
      "Town Two"
    ],
    
    // Mitchells Plain locations
    "Mitchells Plain": [
      "Rocklands",
      "Eastridge",
      "Westridge",
      "Tafelsig",
      "Portlands",
      "Lentegeur"
    ],
    
    // Umlazi locations
    "Umlazi": [
      "Section A",
      "Section B",
      "Section C",
      "Section D",
      "Section E",
      "Section F",
      "Section G",
      "Section H"
    ],
    
    // Mamelodi locations
    "Mamelodi": [
      "Mamelodi East",
      "Mamelodi West",
      "Eerste Fabrieke",
      "Nellmapius",
      "Mahube Valley"
    ],
    
    // Diepsloot locations
    "Diepsloot": [
      "Extension 1",
      "Extension 2",
      "Extension 3",
      "Extension 4",
      "Extension 5",
      "Extension 6",
      "Extension 7",
      "West Village"
    ],
    
    // Orange Farm locations
    "Orange Farm": [
      "Extension 1",
      "Extension 2",
      "Extension 3",
      "Extension 4",
      "Extension 5",
      "Extension 6",
      "Extension 7",
      "Extension 8"
    ]
  }
};

/**
 * Helper function to get all locations for a specific province
 */
export function getLocationsByProvince(provinceCode: string) {
  const cities = SOUTH_AFRICAN_LOCATIONS.cities[provinceCode as keyof typeof SOUTH_AFRICAN_LOCATIONS.cities] || [];
  const result: any[] = [];
  
  cities.forEach(city => {
    const townships = SOUTH_AFRICAN_LOCATIONS.townships[city as keyof typeof SOUTH_AFRICAN_LOCATIONS.townships] || [];
    townships.forEach(township => {
      const locations = SOUTH_AFRICAN_LOCATIONS.locations[township as keyof typeof SOUTH_AFRICAN_LOCATIONS.locations] || [];
      locations.forEach(location => {
        result.push({
          province: SOUTH_AFRICAN_LOCATIONS.provinces.find(p => p.code === provinceCode)?.name,
          city,
          township,
          location
        });
      });
    });
  });
  
  return result;
}

/**
 * Helper function to get total counts
 */
export function getLocationCounts() {
  const provinces = SOUTH_AFRICAN_LOCATIONS.provinces.length;
  const cities = Object.values(SOUTH_AFRICAN_LOCATIONS.cities).flat().length;
  const townships = Object.values(SOUTH_AFRICAN_LOCATIONS.townships).flat().length;
  const locations = Object.values(SOUTH_AFRICAN_LOCATIONS.locations).flat().length;
  
  return { provinces, cities, townships, locations };
}
