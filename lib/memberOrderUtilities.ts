// lib/memberOrderUtilities.ts

import mongoose from 'mongoose';
import { MemberOrder, IMemberOrder, MemberOrderStatus } from '@/models/MemberOrder';
import { GroupOrder, IGroupOrder } from '@/models/GroupOrder';
import { ShoppingCart, IShoppingCart } from '@/models/ShoppingCart';
import { User } from '@/models/User';
import { Product } from '@/models/Product';

export interface CreateMemberOrderInput {
  userId: string;
  groupId: string;
  cart: IShoppingCart;
  customerInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    country: string;
    postalCode: string;
    phone?: string;
  };
  paymentMethod: string;
}

export interface MemberOrderSummary {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  ordersByStatus: Record<string, number>;
  recentOrders: IMemberOrder[];
}

/**
 * Create a member order from cart items
 */
export async function createMemberOrder(input: CreateMemberOrderInput): Promise<IMemberOrder | null> {
  try {
    const { userId, groupId, cart, customerInfo, paymentMethod } = input;

    // Validate inputs
    if (!mongoose.Types.ObjectId.isValid(userId) || 
        !mongoose.Types.ObjectId.isValid(groupId) || 
        !cart || !cart.items || cart.items.length === 0) {
      throw new Error('Invalid input parameters');
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Find or create group order
    let groupOrder = await GroupOrder.findOne({ groupId: new mongoose.Types.ObjectId(groupId) });
    if (!groupOrder) {
      throw new Error('Group order not found');
    }

    // Calculate total amount for member order
    let totalAmount = 0;
    const memberOrderItems = await Promise.all(
      cart.items.map(async (item) => {
        const product = await Product.findById(item.product);
        if (!product) {
          throw new Error(`Product not found: ${item.product}`);
        }
        
        const unitPrice = product.price;
        const subtotal = unitPrice * item.quantity;
        totalAmount += subtotal;

        return {
          product: item.product,
          quantity: item.quantity,
          unitPrice,
          subtotal
        };
      })
    );

    // Create member order
    const memberOrder = new MemberOrder({
      userId: new mongoose.Types.ObjectId(userId),
      groupId: new mongoose.Types.ObjectId(groupId),
      groupOrderId: groupOrder._id,
      items: memberOrderItems,
      totalAmount,
      status: MemberOrderStatus.PENDING,
      statusHistory: [{
        status: MemberOrderStatus.PENDING,
        timestamp: new Date(),
        notes: 'Order created'
      }],
      customerInfo,
      paymentInfo: {
        method: paymentMethod,
        status: 'pending'
      }
    });

    await memberOrder.save();

    // Update group order with member contribution
    await updateGroupOrderWithMemberOrder(groupOrder, memberOrder);

    return memberOrder;
  } catch (error) {
    console.error('Error creating member order:', error);
    return null;
  }
}

/**
 * Update group order when a member order is created
 */
export async function updateGroupOrderWithMemberOrder(
  groupOrder: IGroupOrder, 
  memberOrder: IMemberOrder
): Promise<void> {
  try {
    // Add member order items to group order
    memberOrder.items.forEach(memberItem => {
      const existingItem = groupOrder.orderItems.find(
        item => item.product.toString() === memberItem.product.toString() && 
                item.userId.toString() === memberOrder.userId.toString()
      );

      if (existingItem) {
        existingItem.quantity += memberItem.quantity;
        existingItem.subtotal += memberItem.subtotal;
      } else {
        groupOrder.orderItems.push({
          product: memberItem.product,
          quantity: memberItem.quantity,
          userId: memberOrder.userId,
          unitPrice: memberItem.unitPrice,
          subtotal: memberItem.subtotal
        });
      }
    });

    // Update user contribution
    const userContribIndex = groupOrder.userContributions.findIndex(
      contrib => contrib.userId.toString() === memberOrder.userId.toString()
    );

    if (userContribIndex !== -1) {
      groupOrder.userContributions[userContribIndex].totalSpent += memberOrder.totalAmount;
      groupOrder.userContributions[userContribIndex].items = memberOrder.items.map(item => ({
        product: item.product,
        quantity: item.quantity,
        subtotal: item.subtotal
      }));
    } else {
      const user = await User.findById(memberOrder.userId);
      groupOrder.userContributions.push({
        userId: memberOrder.userId,
        userName: user?.name || 'Unknown User',
        totalSpent: memberOrder.totalAmount,
        items: memberOrder.items.map(item => ({
          product: item.product,
          quantity: item.quantity,
          subtotal: item.subtotal
        })),
        contributionPercentage: 0 // Will be calculated below
      });
    }

    // Recalculate total order value
    groupOrder.totalOrderValue = groupOrder.userContributions.reduce(
      (total, contrib) => total + contrib.totalSpent, 0
    );

    // Update contribution percentages
    groupOrder.userContributions.forEach(contrib => {
      contrib.contributionPercentage = groupOrder.totalOrderValue > 0 
        ? (contrib.totalSpent / groupOrder.totalOrderValue) * 100 
        : 0;
    });

    // Update milestones
    groupOrder.milestones.forEach(milestone => {
      if (groupOrder.totalOrderValue >= milestone.targetAmount && !milestone.isReached) {
        milestone.isReached = true;
        milestone.reachedAt = new Date();
        milestone.currentAmount = groupOrder.totalOrderValue;
      }
    });

    groupOrder.lastUpdatedAt = new Date();
    await groupOrder.save();
  } catch (error) {
    console.error('Error updating group order with member order:', error);
    throw error;
  }
}

/**
 * Get member orders by user ID
 */
export async function getMemberOrdersByUser(userId: string): Promise<IMemberOrder[]> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return [];
    }

    const orders = await MemberOrder.find({ userId })
      .populate('items.product', 'name price image')
      .populate('groupId', 'name')
      .sort({ createdAt: -1 });

    return orders;
  } catch (error) {
    console.error('Error fetching member orders by user:', error);
    return [];
  }
}

/**
 * Get member orders by group ID
 */
export async function getMemberOrdersByGroup(groupId: string): Promise<IMemberOrder[]> {
  try {
    if (!mongoose.Types.ObjectId.isValid(groupId)) {
      return [];
    }

    const orders = await MemberOrder.find({ groupId })
      .populate('items.product', 'name price image')
      .populate('userId', 'name email')
      .sort({ createdAt: -1 });

    return orders;
  } catch (error) {
    console.error('Error fetching member orders by group:', error);
    return [];
  }
}

/**
 * Update member order status
 */
export async function updateMemberOrderStatus(
  orderId: string, 
  status: MemberOrderStatus, 
  notes?: string
): Promise<IMemberOrder | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(orderId)) {
      return null;
    }

    const memberOrder = await MemberOrder.findById(orderId);
    if (!memberOrder) {
      return null;
    }

    await memberOrder.updateStatus(status, notes);
    return memberOrder;
  } catch (error) {
    console.error('Error updating member order status:', error);
    return null;
  }
}

/**
 * Get member order summary for a user
 */
export async function getMemberOrderSummary(userId: string): Promise<MemberOrderSummary | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return null;
    }

    const orders = await MemberOrder.find({ userId });
    
    if (orders.length === 0) {
      return {
        totalOrders: 0,
        totalSpent: 0,
        averageOrderValue: 0,
        ordersByStatus: {},
        recentOrders: []
      };
    }

    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const averageOrderValue = totalSpent / totalOrders;

    const ordersByStatus = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const recentOrders = await MemberOrder.find({ userId })
      .populate('items.product', 'name price image')
      .sort({ createdAt: -1 })
      .limit(5);

    return {
      totalOrders,
      totalSpent,
      averageOrderValue,
      ordersByStatus,
      recentOrders
    };
  } catch (error) {
    console.error('Error generating member order summary:', error);
    return null;
  }
}

/**
 * Cancel member order
 */
export async function cancelMemberOrder(orderId: string, reason?: string): Promise<IMemberOrder | null> {
  try {
    const memberOrder = await MemberOrder.findById(orderId);
    if (!memberOrder) {
      return null;
    }

    // Only allow cancellation for certain statuses
    const cancellableStatuses = [MemberOrderStatus.PENDING, MemberOrderStatus.CONFIRMED];
    if (!cancellableStatuses.includes(memberOrder.status)) {
      throw new Error('Order cannot be cancelled in current status');
    }

    await memberOrder.updateStatus(MemberOrderStatus.CANCELLED, reason || 'Order cancelled by user');
    
    // Update group order by removing this member's contribution
    await removeFromGroupOrder(memberOrder);

    return memberOrder;
  } catch (error) {
    console.error('Error cancelling member order:', error);
    return null;
  }
}

/**
 * Remove member order from group order
 */
async function removeFromGroupOrder(memberOrder: IMemberOrder): Promise<void> {
  try {
    const groupOrder = await GroupOrder.findById(memberOrder.groupOrderId);
    if (!groupOrder) return;

    // Remove member's items from group order
    groupOrder.orderItems = groupOrder.orderItems.filter(
      item => !(item.userId.toString() === memberOrder.userId.toString())
    );

    // Remove or update user contribution
    const contribIndex = groupOrder.userContributions.findIndex(
      contrib => contrib.userId.toString() === memberOrder.userId.toString()
    );

    if (contribIndex !== -1) {
      groupOrder.userContributions[contribIndex].totalSpent -= memberOrder.totalAmount;
      if (groupOrder.userContributions[contribIndex].totalSpent <= 0) {
        groupOrder.userContributions.splice(contribIndex, 1);
      }
    }

    // Recalculate totals
    groupOrder.totalOrderValue = groupOrder.userContributions.reduce(
      (total, contrib) => total + contrib.totalSpent, 0
    );

    groupOrder.lastUpdatedAt = new Date();
    await groupOrder.save();
  } catch (error) {
    console.error('Error removing from group order:', error);
    throw error;
  }
}
