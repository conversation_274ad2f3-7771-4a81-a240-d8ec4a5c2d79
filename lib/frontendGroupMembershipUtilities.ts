// lib/frontendGroupMembershipUtilities.ts

import type { StokvelGroup } from "@/types/stokvelgroup"

export interface MembershipCheckResponse {
  isMember: boolean
  groupDetails?: StokvelGroup
}

export async function checkGroupMembership(groupId: string): Promise<MembershipCheckResponse> {
  try {
    const response = await fetch(`/api/groups/${groupId}/check-membership`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    })

    if (!response.ok) {
      let errorMessage = `Failed to check group membership. Status: ${response.status}`
      try {
        const errorData = await response.json()
        errorMessage = errorData.message || errorMessage
      } catch (jsonError) {
        console.error("Error parsing JSON from error response:", jsonError)
      }
      throw new Error(errorMessage)
    }

    const responseText = await response.text()
    if (!responseText) {
      throw new Error("Empty response from server")
    }

    try {
      const data = JSON.parse(responseText) as MembershipCheckResponse
      return data
    } catch (jsonError) {
      console.error("Error parsing JSON from successful response:", jsonError)
      throw new Error("Invalid JSON in server response")
    }
  } catch (error) {
    console.error("Error checking group membership:", error)
    throw error
  }
}

export async function checkAnyGroupMembership(userId: string): Promise<boolean> {
  const response = await fetch(`/api/users/check-any-group-membership?userId=${userId}`, {
    method: "GET",
  })
  if (!response.ok) {
    throw new Error("Failed to check any group membership")
  }
  const data = await response.json()
  return data.isMemberOfAnyGroup
}

export async function listUserGroups(userId: string): Promise<StokvelGroup[]> {
  if (!userId) {
    console.error('listUserGroups called with empty userId')
    throw new Error('User ID is required')
  }

  console.log(`Making API request to fetch groups for user: ${userId}`)
  const response = await fetch(`/api/users/groups?userId=${userId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error(`API error (${response.status}): ${errorText}`)
    throw new Error(`Failed to list user groups: ${response.status} ${errorText}`)
  }

  const data = await response.json()
  console.log(`Successfully fetched ${data?.length || 0} groups for user ${userId}`)
  return data
}

export async function getAllGroupMembers(groupId: string) {
    const response = await fetch(`/api/groups/${groupId}/all-members`);
    if (!response.ok) {
        throw new Error('Failed to fetch group members');
    }
    return await response.json();
}

/**
 * Get the first group for a user and return its ID for redirection
 * @param userId The ID of the user
 * @returns The ID of the first group or null if the user has no groups
 */
export async function getFirstUserGroupForRedirect(userId: string): Promise<string | null> {
  if (!userId) {
    console.error('getFirstUserGroupForRedirect called with empty userId')
    throw new Error('User ID is required')
  }

  try {
    console.log(`Fetching groups for user: ${userId}`)
    // Fetch the user's groups
    const groups = await listUserGroups(userId)
    console.log(`Found ${groups?.length || 0} groups for user ${userId}`)

    // If the user has at least one group, return the ID of the first group
    if (groups && groups.length > 0) {
      console.log(`First group ID: ${groups[0]._id}`)
      return groups[0]._id
    }

    // If the user has no groups, return null
    console.log('User has no groups')
    return null
  } catch (error) {
    console.error('Error getting user group for redirect:', error)
    throw error // Propagate the error to handle it in the calling function
  }
}

// Interface for group with undelivered items information
export interface GroupWithUndeliveredItems extends StokvelGroup {
  hasUndeliveredItems: boolean;
  undeliveredItemsCount: number;
  isCurrentGroup: boolean;
}

/**
 * Get all groups for a user, including previous groups, with information about undelivered items
 * @param userId The ID of the user
 * @returns Array of groups with undelivered items information
 */
export async function getUserGroupsWithUndeliveredItems(userId: string): Promise<GroupWithUndeliveredItems[]> {
  if (!userId) {
    console.error('getUserGroupsWithUndeliveredItems called with empty userId')
    throw new Error('User ID is required')
  }

  try {
    // Fetch all groups the user has been a member of
    const response = await fetch(`/api/users/${userId}/all-groups-history`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch user groups history: ${response.status}`)
    }

    const data = await response.json()
    return data.groups
  } catch (error) {
    console.error('Error getting user groups with undelivered items:', error)
    throw error
  }
}

/**
 * Check if a group has undelivered items for a user
 * @param userId The ID of the user
 * @param groupId The ID of the group
 * @returns Information about undelivered items
 */
export async function checkGroupUndeliveredItems(userId: string, groupId: string): Promise<{
  hasUndeliveredItems: boolean;
  undeliveredItemsCount: number;
}> {
  if (!userId || !groupId) {
    throw new Error('User ID and Group ID are required')
  }

  try {
    const response = await fetch(`/api/shopping-cart/check-undelivered?userId=${userId}&groupId=${groupId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to check undelivered items: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Error checking undelivered items:', error)
    throw error
  }
}
