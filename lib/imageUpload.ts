
// lib/imageUpload.ts

import { getGridFSBucket } from './dbconnect';
import { Readable } from 'stream';

interface FileBuffer {
  buffer: Buffer;
  filename: string;
  mimeType: string;
}

export const uploadImageToGridFSFromBuffer = async (file: FileBuffer): Promise<string> => {
  const bucket = await getGridFSBucket();

  if (!bucket) {
    throw new Error('GridFSBucket is not initialized. Please check the database connection.');
  }

  const readableStream = Readable.from(file.buffer);

  return new Promise<string>((resolve, reject) => {
    const uploadStream = bucket.openUploadStream(file.filename, {
      contentType: file.mimeType,
    });

    readableStream
      .pipe(uploadStream)
      .on('error', (error) => {
        console.error('Error uploading to GridFS:', error);
        reject(error);
      })
      .on('finish', () => {
        console.log('File uploaded to GridFS with ID:', uploadStream.id);
        resolve(uploadStream.id.toString());
      });
  });
};
