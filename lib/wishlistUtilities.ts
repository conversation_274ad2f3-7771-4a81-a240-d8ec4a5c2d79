// lib/wishlistUtilities.ts

import mongoose from 'mongoose';
import { Wishlist, IWishlist, IWishlistItem } from '@/models/Wishlist';
import { Product } from '@/models/Product';
import { User } from '@/models/User';

export interface CreateWishlistInput {
  userId: string;
  name?: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface AddToWishlistInput {
  userId: string;
  productId: string;
  wishlistId?: string;
  notes?: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface UpdateWishlistInput {
  wishlistId: string;
  name?: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface WishlistSummary {
  totalWishlists: number;
  totalItems: number;
  totalValue: number;
  recentlyAdded: IWishlistItem[];
  topPriority: IWishlistItem[];
}

/**
 * Get or create default wishlist for user
 */
export async function getOrCreateDefaultWishlist(userId: string): Promise<IWishlist | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID');
    }

    // Try to find existing default wishlist
    let wishlist = await Wishlist.findOne({ userId, name: 'My Wishlist' });

    if (!wishlist) {
      // Create default wishlist
      wishlist = new Wishlist({
        userId: new mongoose.Types.ObjectId(userId),
        name: 'My Wishlist',
        description: 'My default wishlist',
        isPublic: false,
        items: [],
        tags: []
      });
      await wishlist.save();
    }

    return wishlist;
  } catch (error) {
    console.error('Error getting or creating default wishlist:', error);
    return null;
  }
}

/**
 * Add product to wishlist
 */
export async function addToWishlist(input: AddToWishlistInput): Promise<IWishlist | null> {
  try {
    const { userId, productId, wishlistId, notes, priority = 'medium' } = input;

    // Validate inputs
    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error('Invalid user ID or product ID');
    }

    // Verify product exists
    const product = await Product.findById(productId);
    if (!product) {
      throw new Error('Product not found');
    }

    let wishlist: IWishlist | null;

    if (wishlistId) {
      // Add to specific wishlist
      if (!mongoose.Types.ObjectId.isValid(wishlistId)) {
        throw new Error('Invalid wishlist ID');
      }
      wishlist = await Wishlist.findOne({ _id: wishlistId, userId });
      if (!wishlist) {
        throw new Error('Wishlist not found or access denied');
      }
    } else {
      // Add to default wishlist
      wishlist = await getOrCreateDefaultWishlist(userId);
      if (!wishlist) {
        throw new Error('Failed to create default wishlist');
      }
    }

    // Add item to wishlist
    await wishlist.addItem(productId, notes, priority);
    return wishlist;
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    return null;
  }
}

/**
 * Remove product from wishlist
 */
export async function removeFromWishlist(userId: string, productId: string, wishlistId?: string): Promise<IWishlist | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error('Invalid user ID or product ID');
    }

    let wishlist: IWishlist | null;

    if (wishlistId && wishlistId !== 'default') {
      if (!mongoose.Types.ObjectId.isValid(wishlistId)) {
        throw new Error('Invalid wishlist ID');
      }
      wishlist = await Wishlist.findOne({ _id: wishlistId, userId });
    } else {
      // Find any wishlist that contains this product for this user
      wishlist = await Wishlist.findOne({
        userId,
        'items.product': new mongoose.Types.ObjectId(productId)
      });
    }

    if (!wishlist) {
      throw new Error('Wishlist not found');
    }

    await wishlist.removeItem(productId);
    return wishlist;
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    return null;
  }
}

/**
 * Get user's wishlists
 */
export async function getUserWishlists(userId: string): Promise<IWishlist[]> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return [];
    }

    const wishlists = await Wishlist.findByUser(userId);
    return wishlists;
  } catch (error) {
    console.error('Error fetching user wishlists:', error);
    return [];
  }
}

/**
 * Get wishlist by ID
 */
export async function getWishlistById(wishlistId: string, userId?: string): Promise<IWishlist | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(wishlistId)) {
      return null;
    }

    const query: any = { _id: wishlistId };
    
    // If userId provided, ensure user owns the wishlist or it's public
    if (userId) {
      query.$or = [
        { userId: new mongoose.Types.ObjectId(userId) },
        { isPublic: true }
      ];
    } else {
      query.isPublic = true;
    }

    const wishlist = await Wishlist.findOne(query);
    return wishlist;
  } catch (error) {
    console.error('Error fetching wishlist by ID:', error);
    return null;
  }
}

/**
 * Create new wishlist
 */
export async function createWishlist(input: CreateWishlistInput): Promise<IWishlist | null> {
  try {
    const { userId, name = 'New Wishlist', description, isPublic = false, tags = [] } = input;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID');
    }

    const wishlist = new Wishlist({
      userId: new mongoose.Types.ObjectId(userId),
      name,
      description,
      isPublic,
      tags,
      items: []
    });

    await wishlist.save();
    return wishlist;
  } catch (error) {
    console.error('Error creating wishlist:', error);
    return null;
  }
}

/**
 * Update wishlist
 */
export async function updateWishlist(input: UpdateWishlistInput, userId: string): Promise<IWishlist | null> {
  try {
    const { wishlistId, ...updates } = input;

    if (!mongoose.Types.ObjectId.isValid(wishlistId) || !mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid wishlist ID or user ID');
    }

    const wishlist = await Wishlist.findOne({ _id: wishlistId, userId });
    if (!wishlist) {
      throw new Error('Wishlist not found or access denied');
    }

    Object.assign(wishlist, updates);
    await wishlist.save();
    return wishlist;
  } catch (error) {
    console.error('Error updating wishlist:', error);
    return null;
  }
}

/**
 * Delete wishlist
 */
export async function deleteWishlist(wishlistId: string, userId: string): Promise<boolean> {
  try {
    if (!mongoose.Types.ObjectId.isValid(wishlistId) || !mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid wishlist ID or user ID');
    }

    const result = await Wishlist.deleteOne({ _id: wishlistId, userId });
    return result.deletedCount > 0;
  } catch (error) {
    console.error('Error deleting wishlist:', error);
    return false;
  }
}

/**
 * Check if product is in user's wishlist
 */
export async function isProductInWishlist(userId: string, productId: string): Promise<boolean> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(productId)) {
      return false;
    }

    const wishlist = await Wishlist.findOne({
      userId,
      'items.product': new mongoose.Types.ObjectId(productId)
    });

    return !!wishlist;
  } catch (error) {
    console.error('Error checking if product is in wishlist:', error);
    return false;
  }
}

/**
 * Get wishlist summary for user
 */
export async function getWishlistSummary(userId: string): Promise<WishlistSummary | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return null;
    }

    const wishlists = await Wishlist.find({ userId });
    
    if (wishlists.length === 0) {
      return {
        totalWishlists: 0,
        totalItems: 0,
        totalValue: 0,
        recentlyAdded: [],
        topPriority: []
      };
    }

    const allItems = wishlists.flatMap(w => w.items);
    const totalItems = allItems.length;
    const totalValue = allItems.reduce((sum, item) => {
      const product = item.product as any;
      return sum + (product?.price || 0);
    }, 0);

    // Get recently added items (last 5)
    const recentlyAdded = allItems
      .sort((a, b) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime())
      .slice(0, 5);

    // Get high priority items
    const topPriority = allItems
      .filter(item => item.priority === 'high')
      .slice(0, 5);

    return {
      totalWishlists: wishlists.length,
      totalItems,
      totalValue,
      recentlyAdded,
      topPriority
    };
  } catch (error) {
    console.error('Error generating wishlist summary:', error);
    return null;
  }
}

/**
 * Get public wishlists
 */
export async function getPublicWishlists(limit: number = 10): Promise<IWishlist[]> {
  try {
    const wishlists = await Wishlist.findPublicWishlists(limit);
    return wishlists;
  } catch (error) {
    console.error('Error fetching public wishlists:', error);
    return [];
  }
}

/**
 * Get wishlist by share code
 */
export async function getWishlistByShareCode(shareCode: string): Promise<IWishlist | null> {
  try {
    const wishlist = await Wishlist.findByShareCode(shareCode);
    return wishlist;
  } catch (error) {
    console.error('Error fetching wishlist by share code:', error);
    return null;
  }
}
