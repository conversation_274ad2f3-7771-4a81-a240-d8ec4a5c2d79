"use client"

import { useQ<PERSON>y, useMutation, useQueryClient, type UseMutationResult } from "@tanstack/react-query"
import { Rating, CreateRatingInput, UpdateRatingInput } from "../types/rating"

// API Functions
export async function getProductRatings(productId: string): Promise<Rating[]> {
  const response = await fetch(`/api/ratings/get?productId=${productId}`, { method: "GET" })
  if (!response.ok) {
    throw new Error("Failed to fetch Ratings")
  }
  return response.json()
}

export async function createRating(data: CreateRatingInput): Promise<Rating> {
  const response = await fetch("/api/ratings/create", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error("Failed to create Rating")
  }
  return response.json()
}

export async function updateRating(input: UpdateRatingInput): Promise<Rating> {
  const response = await fetch("/api/ratings/update", {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(input),
  })
  if (!response.ok) {
    throw new Error("Failed to update Rating")
  }
  return response.json()
}

export async function deleteRating(id: string): Promise<void> {
  const response = await fetch("/api/ratings/delete", {
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ id }),
  })
  if (!response.ok) {
    throw new Error("Failed to delete Rating")
  }
}

export const getUserProductRating = async (productId: string, userId: string): Promise<Rating | null> => {
  const response = await fetch(`/api/ratings/${productId}/user/${userId}`);
  if (!response.ok) {
    throw new Error('Failed to fetch user rating');
  }
  return response.json();
};

// React Query Hooks
export function useGetProductRatings(productId: string) {
  return useQuery<Rating[], Error>({
    queryKey: ["ratings", productId],
    queryFn: () => getProductRatings(productId),
  })
}

export function useGetUserProductRating(productId: string, userId: string) {
  return useQuery<Rating | null, Error>({
    queryKey: ["userRating", productId, userId],
    queryFn: () => getUserProductRating(productId, userId),
  })
}

export function useCreateRating(): UseMutationResult<Rating, Error, CreateRatingInput, unknown> {
  const queryClient = useQueryClient()

  return useMutation<Rating, Error, CreateRatingInput, unknown>({
    mutationFn: createRating,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["ratings", data.productId] })
      queryClient.invalidateQueries({ queryKey: ["userRating", data.productId] })
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}

export function useUpdateRating(): UseMutationResult<Rating, Error, UpdateRatingInput, unknown> {
  const queryClient = useQueryClient()

  return useMutation<Rating, Error, UpdateRatingInput, unknown>({
    mutationFn: updateRating,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["ratings", data.productId] })
      queryClient.invalidateQueries({ queryKey: ["userRating", data.productId] })
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}

export function useDeleteRating(): UseMutationResult<void, Error, string, unknown> {
  const queryClient = useQueryClient()

  return useMutation<void, Error, string, unknown>({
    mutationFn: deleteRating,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["ratings"] })
      queryClient.invalidateQueries({ queryKey: ["userRating"] })
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}