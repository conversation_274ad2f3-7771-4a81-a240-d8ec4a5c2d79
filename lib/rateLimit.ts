

// lib/rateLimit.ts

class RateLimiter {
    private rateLimitWindowMs: number;
    private maxRequests: number;
    private ipRequestsMap: Map<string, { count: number; lastRequest: number }>;
  
    constructor(rateLimitWindowMs: number, maxRequests: number) {
      this.rateLimitWindowMs = rateLimitWindowMs;
      this.maxRequests = maxRequests;
      this.ipRequestsMap = new Map();
  
      // Setup cleanup interval
      setInterval(() => {
        const now = Date.now();
        this.ipRequestsMap.forEach((data, ip) => {
          if (now - data.lastRequest > this.rateLimitWindowMs) {
            this.ipRequestsMap.delete(ip);
          }
        });
      }, this.rateLimitWindowMs);
    }
  
    public allowRequest(ip: string): boolean {
      const now = Date.now();
  
      if (!this.ipRequestsMap.has(ip)) {
        this.ipRequestsMap.set(ip, { count: 1, lastRequest: now });
        return true; // Allow request
      }
  
      const ipData = this.ipRequestsMap.get(ip)!;
      const timeSinceLastRequest = now - ipData.lastRequest;
  
      if (timeSinceLastRequest > this.rateLimitWindowMs) {
        this.ipRequestsMap.set(ip, { count: 1, lastRequest: now });
        return true; // Reset the count and allow request
      }
  
      if (ipData.count >= this.maxRequests) {
        return false; // Block request, too many attempts
      }
  
      this.ipRequestsMap.set(ip, { count: ipData.count + 1, lastRequest: now });
      return true; // Allow request
    }
  }
  
  // Implementing Singleton Pattern
  const rateLimiter = new RateLimiter(60 * 1000, 5); // 1 minute window, 5 requests
  export default rateLimiter;
  