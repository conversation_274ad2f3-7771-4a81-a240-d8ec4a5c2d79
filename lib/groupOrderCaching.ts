import { IGroupOrder } from '@/models/GroupOrder'
import { IShoppingCart } from '@/models/ShoppingCart'
import { GroupOrderAnalytics } from './groupOrderAnalytics'
import inMemoryCache from './inMemoryCache'

// Cache key generators
const generateAnalyticsCacheKey = (
  groupId?: string, 
  startDate?: Date, 
  endDate?: Date
): string => {
  const baseKey = 'group_order_analytics'
  const parts = [baseKey]
  
  if (groupId) parts.push(`group:${groupId}`)
  if (startDate) parts.push(`start:${startDate.toISOString()}`)
  if (endDate) parts.push(`end:${endDate.toISOString()}`)
  
  return parts.join(':')
}

const generateOrderCacheKey = (orderId: string): string => {
  return `group_order:${orderId}`
}

// Centralized Group Order Caching Service
export class GroupOrderCacheService {
  // Group Order Caching Methods
  static cacheGroupOrder(
    groupOrder: IGroupOrder, 
    ttlSeconds: number = 1800 // 30 minutes default
  ): void {
    const cacheKey = generateOrderCacheKey(groupOrder._id?.toString() || groupOrder.id?.toString())
    inMemoryCache.setGroupOrder(cacheKey, groupOrder, ttlSeconds * 1000)
  }

  static getCachedGroupOrder(
    orderId: string
  ): IGroupOrder | null {
    const cacheKey = generateOrderCacheKey(orderId)
    const order = inMemoryCache.getGroupOrder(cacheKey)
    return order || null
  }

  static invalidateGroupOrderCache(
    orderId: string,
    groupId?: string
  ): void {
    const orderCacheKey = generateOrderCacheKey(orderId)
    
    // Remove specific order cache
    inMemoryCache.deleteGroupOrder(orderCacheKey)
    
    // Invalidate related analytics caches
    if (groupId) {
      const analyticsCacheKey = generateAnalyticsCacheKey(groupId)
      inMemoryCache.deleteAnalytics(analyticsCacheKey)
    }
  }

  // Shopping Cart Caching Methods
  static cacheShoppingCart(
    cartId: string, 
    cart: IShoppingCart, 
    ttl: number = 1800000 // 30 minutes default
  ): void {
    inMemoryCache.setShoppingCart(cartId, cart, ttl)
  }

  static getCachedShoppingCart(
    cartId: string
  ): IShoppingCart | undefined {
    return inMemoryCache.getShoppingCart(cartId)
  }

  static invalidateShoppingCartCache(
    cartId: string
  ): void {
    inMemoryCache.deleteShoppingCart(cartId)
  }

  // Analytics Caching Methods
  static cacheGroupOrderAnalytics(
    analytics: GroupOrderAnalytics,
    groupId?: string,
    startDate?: Date,
    endDate?: Date,
    ttlSeconds: number = 3600 // 1 hour default
  ): void {
    const cacheKey = generateAnalyticsCacheKey(
      groupId?.toString(), 
      startDate, 
      endDate
    )
    inMemoryCache.setAnalytics(cacheKey, analytics, ttlSeconds * 1000)
  }

  static getCachedGroupOrderAnalytics(
    groupId?: string,
    startDate?: Date,
    endDate?: Date
  ): GroupOrderAnalytics | null {
    const cacheKey = generateAnalyticsCacheKey(
      groupId?.toString(), 
      startDate, 
      endDate
    )
    return inMemoryCache.getAnalytics(cacheKey) || null
  }

  // Cache Management Methods
  static clearAllCaches(): void {
    inMemoryCache.clearAll()
  }

  static getCacheStats(): {
    cacheSize: number,
    hitRate: number
  } {
    return inMemoryCache.getCacheStats()
  }
}
