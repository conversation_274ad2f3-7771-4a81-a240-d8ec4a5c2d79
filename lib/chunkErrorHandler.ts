// lib/chunkErrorHandler.ts

/**
 * Global chunk loading error handler
 * Automatically retries failed chunk loads and provides fallback mechanisms
 */

let retryCount = 0;
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

export function initChunkErrorHandler() {
  if (typeof window === 'undefined') return;

  // Handle chunk loading errors
  window.addEventListener('error', (event) => {
    const error = event.error;
    const message = event.message || '';
    
    // Check if it's a chunk loading error
    if (
      message.includes('ChunkLoadError') ||
      message.includes('Loading chunk') ||
      message.includes('Failed to import') ||
      (error && error.name === 'ChunkLoadError')
    ) {
      console.warn('Chunk loading error detected:', message);
      handleChunkError(event);
    }
  });

  // Handle unhandled promise rejections (often chunk loading issues)
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason;
    
    if (
      reason &&
      (reason.message?.includes('ChunkLoadError') ||
       reason.message?.includes('Loading chunk') ||
       reason.message?.includes('Failed to import'))
    ) {
      console.warn('Chunk loading promise rejection:', reason.message);
      handleChunkError(event);
    }
  });
}

function handleChunkError(event: ErrorEvent | PromiseRejectionEvent) {
  if (retryCount >= MAX_RETRIES) {
    console.error('Max chunk loading retries exceeded, showing fallback');
    showChunkErrorFallback();
    return;
  }

  retryCount++;
  console.log(`Attempting chunk loading retry ${retryCount}/${MAX_RETRIES}`);

  // Prevent the error from propagating
  event.preventDefault();

  // Retry after a delay
  setTimeout(() => {
    // Try to reload the current page
    if (retryCount === MAX_RETRIES) {
      // Last attempt - force reload
      window.location.reload();
    } else {
      // Soft retry - just refresh the current route
      if (window.history && window.history.go) {
        window.history.go(0);
      } else {
        window.location.reload();
      }
    }
  }, RETRY_DELAY * retryCount);
}

function showChunkErrorFallback() {
  // Create a simple fallback UI
  const fallbackHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      font-family: system-ui, -apple-system, sans-serif;
    ">
      <div style="
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 400px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      ">
        <h2 style="margin: 0 0 1rem 0; color: #dc2626;">Loading Issue</h2>
        <p style="margin: 0 0 1.5rem 0; color: #6b7280;">
          There was an issue loading some components. Please refresh the page to continue.
        </p>
        <button onclick="window.location.reload()" style="
          background: #3b82f6;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 6px;
          cursor: pointer;
          font-size: 1rem;
        ">
          Refresh Page
        </button>
      </div>
    </div>
  `;

  // Only show if not already shown
  if (!document.getElementById('chunk-error-fallback')) {
    const fallbackElement = document.createElement('div');
    fallbackElement.id = 'chunk-error-fallback';
    fallbackElement.innerHTML = fallbackHTML;
    document.body.appendChild(fallbackElement);
  }
}

// Reset retry count on successful navigation
export function resetChunkErrorHandler() {
  retryCount = 0;
  
  // Remove any existing fallback UI
  const fallback = document.getElementById('chunk-error-fallback');
  if (fallback) {
    fallback.remove();
  }
}

// Utility to preload critical chunks
export function preloadCriticalChunks() {
  if (typeof window === 'undefined') return;

  // Preload critical chunks that might fail
  const criticalChunks = [
    '/_next/static/chunks/pages/_app.js',
    '/_next/static/chunks/main.js',
    '/_next/static/chunks/webpack.js',
  ];

  criticalChunks.forEach(chunk => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'script';
    link.href = chunk;
    document.head.appendChild(link);
  });
}

// Export for use in _app.tsx
export default {
  initChunkErrorHandler,
  resetChunkErrorHandler,
  preloadCriticalChunks,
};
