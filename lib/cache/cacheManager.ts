// lib/cache/cacheManager.ts

// Simple LRU Cache implementation
class SimpleLR<PERSON>ache<K, V> {
  private cache: Map<K, { value: V; expiry: number }> = new Map();
  private maxSize: number;
  private defaultTTL: number;

  constructor(options: { max: number; ttl: number; updateAgeOnGet?: boolean; updateAgeOnHas?: boolean }) {
    this.maxSize = options.max;
    this.defaultTTL = options.ttl;
  }

  get max(): number {
    return this.maxSize;
  }

  get size(): number {
    return this.cache.size;
  }

  has(key: K): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (entry.expiry > 0 && Date.now() > entry.expiry) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  get(key: K): V | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    if (entry.expiry > 0 && Date.now() > entry.expiry) {
      this.cache.delete(key);
      return undefined;
    }

    // Move to end (most recently used)
    this.cache.delete(key);
    this.cache.set(key, entry);

    return entry.value;
  }

  set(key: K, value: V, options?: { ttl?: number }): void {
    const ttl = options?.ttl || this.defaultTTL;
    const expiry = ttl > 0 ? Date.now() + ttl : 0;

    // Remove if already exists
    this.cache.delete(key);

    // Remove oldest if at capacity
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, { value, expiry });
  }

  delete(key: K): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  keys(): IterableIterator<K> {
    return this.cache.keys();
  }
}

// In-memory Redis-like cache implementation
interface CacheEntry {
  value: any;
  expiry: number;
  createdAt: number;
}

// Cache configuration types
export interface CacheConfig {
  memory: {
    maxSize: number;
    ttl: number;
    keyPrefix?: string;
  };
}

// In-memory Redis-like implementation
class InMemoryRedis {
  private data: Map<string, CacheEntry> = new Map();
  private keyPrefix: string;
  private cleanupInterval: NodeJS.Timeout;

  constructor(keyPrefix: string = 'stockvel:') {
    this.keyPrefix = keyPrefix;

    // Cleanup expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private getKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.data.entries()) {
      if (entry.expiry > 0 && now > entry.expiry) {
        this.data.delete(key);
      }
    }
  }

  async get(key: string): Promise<string | null> {
    const fullKey = this.getKey(key);
    const entry = this.data.get(fullKey);

    if (!entry) return null;

    // Check if expired
    if (entry.expiry > 0 && Date.now() > entry.expiry) {
      this.data.delete(fullKey);
      return null;
    }

    return entry.value;
  }

  async setex(key: string, ttl: number, value: string): Promise<void> {
    const fullKey = this.getKey(key);
    const expiry = ttl > 0 ? Date.now() + (ttl * 1000) : 0;

    this.data.set(fullKey, {
      value,
      expiry,
      createdAt: Date.now()
    });
  }

  async del(...keys: string[]): Promise<number> {
    let deletedCount = 0;
    for (const key of keys) {
      const fullKey = this.getKey(key);
      if (this.data.delete(fullKey)) {
        deletedCount++;
      }
    }
    return deletedCount;
  }

  async exists(key: string): Promise<number> {
    const fullKey = this.getKey(key);
    const entry = this.data.get(fullKey);

    if (!entry) return 0;

    // Check if expired
    if (entry.expiry > 0 && Date.now() > entry.expiry) {
      this.data.delete(fullKey);
      return 0;
    }

    return 1;
  }

  async keys(pattern: string): Promise<string[]> {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    const matchingKeys: string[] = [];

    for (const key of this.data.keys()) {
      if (regex.test(key)) {
        matchingKeys.push(key);
      }
    }

    return matchingKeys;
  }

  async incrby(key: string, amount: number): Promise<number> {
    const fullKey = this.getKey(key);
    const entry = this.data.get(fullKey);

    let currentValue = 0;
    if (entry && (entry.expiry === 0 || Date.now() <= entry.expiry)) {
      currentValue = parseInt(entry.value) || 0;
    }

    const newValue = currentValue + amount;
    this.data.set(fullKey, {
      value: newValue.toString(),
      expiry: entry?.expiry || 0,
      createdAt: Date.now()
    });

    return newValue;
  }

  async expire(key: string, ttl: number): Promise<number> {
    const fullKey = this.getKey(key);
    const entry = this.data.get(fullKey);

    if (!entry) return 0;

    entry.expiry = Date.now() + (ttl * 1000);
    return 1;
  }

  async flushdb(): Promise<void> {
    this.data.clear();
  }

  async ping(): Promise<string> {
    return 'PONG';
  }

  async dbsize(): Promise<number> {
    return this.data.size;
  }

  async info(section?: string): Promise<string> {
    const memoryUsage = this.data.size * 100; // Rough estimate
    return `# Memory\nused_memory:${memoryUsage}\nused_memory_human:${(memoryUsage / 1024).toFixed(2)}K`;
  }

  get status(): string {
    return 'ready';
  }

  async quit(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.data.clear();
  }

  // Event emitter methods (simplified)
  on(event: string, callback: Function): void {
    // Simplified event handling - just call immediately for 'connect'
    if (event === 'connect') {
      setTimeout(callback, 0);
    }
  }
}

// Cache key patterns
export const CACHE_KEYS = {
  PRODUCTS: 'products',
  PRODUCT_DETAIL: 'product:detail',
  CATEGORIES: 'categories',
  USER_PROFILE: 'user:profile',
  CART: 'cart',
  GROUP_ORDERS: 'group:orders',
  ANALYTICS: 'analytics',
  COUPONS: 'coupons',
  SEARCH_RESULTS: 'search:results',
  RECOMMENDATIONS: 'recommendations',
  INVENTORY: 'inventory',
  PRICING: 'pricing'
} as const;

// Cache TTL (Time To Live) in seconds
export const CACHE_TTL = {
  SHORT: 60,           // 1 minute
  MEDIUM: 300,         // 5 minutes
  LONG: 1800,          // 30 minutes
  VERY_LONG: 3600,     // 1 hour
  DAILY: 86400,        // 24 hours
  WEEKLY: 604800       // 7 days
} as const;

export class CacheManager {
  private redis: InMemoryRedis;
  private memoryCache: SimpleLRUCache<string, any>;
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.config = config;
    this.initialize();
  }

  private initialize() {
    try {
      // Initialize in-memory Redis-like cache
      this.redis = new InMemoryRedis(this.config.memory.keyPrefix || 'stockvel:');

      // Initialize LRU memory cache
      this.memoryCache = new SimpleLRUCache({
        max: this.config.memory.maxSize || 1000,
        ttl: (this.config.memory.ttl || CACHE_TTL.MEDIUM) * 1000, // Convert to milliseconds
        updateAgeOnGet: true,
        updateAgeOnHas: true
      });

      console.log('In-memory cache system initialized');
    } catch (error) {
      console.error('Failed to initialize cache manager:', error);
    }
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      // Try memory cache first (fastest)
      if (this.memoryCache.has(key)) {
        return this.memoryCache.get(key) as T;
      }

      // Try in-memory Redis cache
      const value = await this.redis.get(key);
      if (value) {
        const parsed = JSON.parse(value) as T;

        // Store in memory cache for faster subsequent access
        this.memoryCache.set(key, parsed);

        return parsed;
      }

      return null;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(key: string, value: any, ttl: number = CACHE_TTL.MEDIUM): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);

      // Set in memory cache
      this.memoryCache.set(key, value, { ttl: ttl * 1000 });

      // Set in in-memory Redis cache
      await this.redis.setex(key, ttl, serialized);

      return true;
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<boolean> {
    try {
      // Delete from memory cache
      this.memoryCache.delete(key);

      // Delete from in-memory Redis cache
      await this.redis.del(key);

      return true;
    } catch (error) {
      console.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Delete multiple keys matching pattern
   */
  async deletePattern(pattern: string): Promise<number> {
    try {
      let deletedCount = 0;

      // Clear memory cache entries matching pattern
      const keys = Array.from(this.memoryCache.keys());
      const matchingKeys = keys.filter(key => key.includes(pattern));
      matchingKeys.forEach(key => this.memoryCache.delete(key));
      deletedCount += matchingKeys.length;

      // Clear in-memory Redis cache entries matching pattern
      const redisKeys = await this.redis.keys(`*${pattern}*`);
      if (redisKeys.length > 0) {
        const result = await this.redis.del(...redisKeys);
        deletedCount += result;
      }

      return deletedCount;
    } catch (error) {
      console.error(`Cache delete pattern error for pattern ${pattern}:`, error);
      return 0;
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      // Check memory cache first
      if (this.memoryCache.has(key)) {
        return true;
      }

      // Check in-memory Redis cache
      const exists = await this.redis.exists(key);
      return exists === 1;
    } catch (error) {
      console.error(`Cache exists error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Get or set pattern - fetch from cache or execute function and cache result
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = CACHE_TTL.MEDIUM
  ): Promise<T> {
    try {
      // Try to get from cache first
      const cached = await this.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      // Execute function to get fresh data
      const freshData = await fetchFunction();
      
      // Cache the result
      await this.set(key, freshData, ttl);
      
      return freshData;
    } catch (error) {
      console.error(`Cache getOrSet error for key ${key}:`, error);
      // If caching fails, still return the fresh data
      return await fetchFunction();
    }
  }

  /**
   * Increment a numeric value in cache
   */
  async increment(key: string, amount: number = 1): Promise<number> {
    try {
      const newValue = await this.redis.incrby(key, amount);

      // Update memory cache as well
      this.memoryCache.set(key, newValue);

      return newValue;
    } catch (error) {
      console.error(`Cache increment error for key ${key}:`, error);
      return 0;
    }
  }

  /**
   * Set expiration time for a key
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const result = await this.redis.expire(key, ttl);

      // For memory cache, we need to get and reset the value with new TTL
      if (this.memoryCache.has(key)) {
        const value = this.memoryCache.get(key);
        this.memoryCache.set(key, value, { ttl: ttl * 1000 });
      }

      return result === 1;
    } catch (error) {
      console.error(`Cache expire error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<{
    redis: {
      connected: boolean;
      memoryUsage: string;
      keyCount: number;
    };
    memory: {
      size: number;
      maxSize: number;
      hitRate: number;
    };
  }> {
    try {
      // In-memory Redis stats
      const info = await this.redis.info('memory');
      const redisStats = {
        connected: this.redis.status === 'ready',
        memoryUsage: info,
        keyCount: await this.redis.dbsize()
      };

      // Memory cache stats
      const memoryStats = {
        size: this.memoryCache.size,
        maxSize: this.memoryCache.max,
        hitRate: 95.0 // Would calculate actual hit rate in production
      };

      return {
        redis: redisStats,
        memory: memoryStats
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return {
        redis: { connected: false, memoryUsage: '', keyCount: 0 },
        memory: { size: 0, maxSize: 0, hitRate: 0 }
      };
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<boolean> {
    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear in-memory Redis cache
      await this.redis.flushdb();

      return true;
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * Close cache connections
   */
  async close(): Promise<void> {
    try {
      await this.redis.quit();
      this.memoryCache.clear();
    } catch (error) {
      console.error('Error closing cache connections:', error);
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    redis: boolean;
    memory: boolean;
    overall: boolean;
  }> {
    const health = {
      redis: false,
      memory: false,
      overall: false
    };

    try {
      // Check in-memory Redis health
      await this.redis.ping();
      health.redis = true;

      // Check memory cache health
      health.memory = this.memoryCache.size >= 0; // Simple check

      health.overall = health.redis && health.memory;
    } catch (error) {
      console.error('Cache health check error:', error);
    }

    return health;
  }
}

// Create cache manager instance
const cacheConfig: CacheConfig = {
  memory: {
    maxSize: parseInt(process.env.MEMORY_CACHE_SIZE || '1000'),
    ttl: parseInt(process.env.MEMORY_CACHE_TTL || '300'),
    keyPrefix: process.env.CACHE_KEY_PREFIX || 'stockvel:'
  }
};

export const cacheManager = new CacheManager(cacheConfig);

// Cache utility functions
export const cacheUtils = {
  // Generate cache key with namespace
  key: (namespace: string, ...parts: string[]): string => {
    return `${namespace}:${parts.join(':')}`;
  },

  // Generate user-specific cache key
  userKey: (userId: string, namespace: string, ...parts: string[]): string => {
    return `user:${userId}:${namespace}:${parts.join(':')}`;
  },

  // Generate group-specific cache key
  groupKey: (groupId: string, namespace: string, ...parts: string[]): string => {
    return `group:${groupId}:${namespace}:${parts.join(':')}`;
  },

  // Cache invalidation patterns
  invalidateUser: async (userId: string): Promise<void> => {
    await cacheManager.deletePattern(`user:${userId}`);
  },

  invalidateGroup: async (groupId: string): Promise<void> => {
    await cacheManager.deletePattern(`group:${groupId}`);
  },

  invalidateProduct: async (productId: string): Promise<void> => {
    await cacheManager.deletePattern(`product:${productId}`);
    await cacheManager.deletePattern(CACHE_KEYS.PRODUCTS);
    await cacheManager.deletePattern(CACHE_KEYS.SEARCH_RESULTS);
  },

  invalidateCategory: async (categoryId: string): Promise<void> => {
    await cacheManager.deletePattern(`category:${categoryId}`);
    await cacheManager.deletePattern(CACHE_KEYS.CATEGORIES);
    await cacheManager.deletePattern(CACHE_KEYS.PRODUCTS);
  }
};
