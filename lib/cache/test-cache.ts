// lib/cache/test-cache.ts
// Simple test file to verify cache functionality

import { cacheManager, CACHE_KEYS, CACHE_TTL } from './cacheManager';

export async function testCacheSystem() {
  console.log('🧪 Testing Cache System...');

  try {
    // Test basic set/get operations
    console.log('📝 Testing basic set/get operations...');
    await cacheManager.set('test:key1', { message: 'Hello Cache!' }, CACHE_TTL.SHORT);
    const result1 = await cacheManager.get('test:key1');
    console.log('✅ Basic set/get:', result1);

    // Test cache existence
    console.log('🔍 Testing cache existence...');
    const exists = await cacheManager.exists('test:key1');
    console.log('✅ Key exists:', exists);

    // Test increment operation
    console.log('📈 Testing increment operation...');
    await cacheManager.set('test:counter', 0);
    const count1 = await cacheManager.increment('test:counter', 5);
    const count2 = await cacheManager.increment('test:counter', 3);
    console.log('✅ Increment results:', count1, count2);

    // Test getOrSet pattern
    console.log('🔄 Testing getOrSet pattern...');
    const expensiveData = await cacheManager.getOrSet(
      'test:expensive',
      async () => {
        console.log('💰 Executing expensive operation...');
        return { computed: Date.now(), data: 'expensive result' };
      },
      CACHE_TTL.MEDIUM
    );
    console.log('✅ GetOrSet result:', expensiveData);

    // Test second call (should be cached)
    const cachedData = await cacheManager.getOrSet(
      'test:expensive',
      async () => {
        console.log('💰 This should not execute (cached)');
        return { computed: Date.now(), data: 'new expensive result' };
      },
      CACHE_TTL.MEDIUM
    );
    console.log('✅ Cached result:', cachedData);

    // Test pattern deletion
    console.log('🗑️ Testing pattern deletion...');
    await cacheManager.set('test:pattern1', 'data1');
    await cacheManager.set('test:pattern2', 'data2');
    await cacheManager.set('other:key', 'other data');
    
    const deletedCount = await cacheManager.deletePattern('test:pattern');
    console.log('✅ Deleted count:', deletedCount);

    // Test cache statistics
    console.log('📊 Testing cache statistics...');
    const stats = await cacheManager.getStats();
    console.log('✅ Cache stats:', stats);

    // Test health check
    console.log('🏥 Testing health check...');
    const health = await cacheManager.healthCheck();
    console.log('✅ Health check:', health);

    console.log('🎉 All cache tests passed!');
    return true;
  } catch (error) {
    console.error('❌ Cache test failed:', error);
    return false;
  }
}

// Test cache performance
export async function testCachePerformance() {
  console.log('⚡ Testing Cache Performance...');

  const iterations = 1000;
  const testData = { 
    id: 'test-product-123',
    name: 'Test Product',
    price: 99.99,
    description: 'A test product for performance testing',
    metadata: {
      category: 'electronics',
      tags: ['test', 'performance', 'cache'],
      created: new Date().toISOString()
    }
  };

  try {
    // Test write performance
    console.log(`📝 Testing write performance (${iterations} operations)...`);
    const writeStart = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await cacheManager.set(`perf:test:${i}`, { ...testData, id: i });
    }
    
    const writeTime = Date.now() - writeStart;
    console.log(`✅ Write performance: ${writeTime}ms (${(writeTime / iterations).toFixed(2)}ms per operation)`);

    // Test read performance
    console.log(`📖 Testing read performance (${iterations} operations)...`);
    const readStart = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await cacheManager.get(`perf:test:${i}`);
    }
    
    const readTime = Date.now() - readStart;
    console.log(`✅ Read performance: ${readTime}ms (${(readTime / iterations).toFixed(2)}ms per operation)`);

    // Test mixed operations
    console.log(`🔄 Testing mixed operations (${iterations} operations)...`);
    const mixedStart = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      if (i % 3 === 0) {
        await cacheManager.set(`mixed:${i}`, testData);
      } else if (i % 3 === 1) {
        await cacheManager.get(`mixed:${i - 1}`);
      } else {
        await cacheManager.exists(`mixed:${i - 2}`);
      }
    }
    
    const mixedTime = Date.now() - mixedStart;
    console.log(`✅ Mixed performance: ${mixedTime}ms (${(mixedTime / iterations).toFixed(2)}ms per operation)`);

    // Cleanup performance test data
    await cacheManager.deletePattern('perf:test');
    await cacheManager.deletePattern('mixed:');

    console.log('🎉 Performance tests completed!');
    return {
      writeTime,
      readTime,
      mixedTime,
      operationsPerSecond: {
        write: Math.round(iterations / (writeTime / 1000)),
        read: Math.round(iterations / (readTime / 1000)),
        mixed: Math.round(iterations / (mixedTime / 1000))
      }
    };
  } catch (error) {
    console.error('❌ Performance test failed:', error);
    return null;
  }
}

// Test cache with realistic e-commerce data
export async function testEcommerceCache() {
  console.log('🛒 Testing E-commerce Cache Patterns...');

  try {
    // Test product caching
    const product = {
      id: 'prod-123',
      name: 'Wireless Headphones',
      price: 199.99,
      category: 'electronics',
      inStock: true,
      description: 'High-quality wireless headphones with noise cancellation',
      images: ['image1.jpg', 'image2.jpg'],
      reviews: { average: 4.5, count: 128 }
    };

    await cacheManager.set(
      `${CACHE_KEYS.PRODUCT_DETAIL}:${product.id}`,
      product,
      CACHE_TTL.LONG
    );

    // Test user cart caching
    const userCart = {
      userId: 'user-456',
      items: [
        { productId: 'prod-123', quantity: 2, price: 199.99 },
        { productId: 'prod-124', quantity: 1, price: 99.99 }
      ],
      total: 499.97,
      updatedAt: new Date().toISOString()
    };

    await cacheManager.set(
      `${CACHE_KEYS.CART}:${userCart.userId}`,
      userCart,
      CACHE_TTL.MEDIUM
    );

    // Test search results caching
    const searchResults = {
      query: 'wireless headphones',
      results: [product],
      totalCount: 1,
      page: 1,
      cachedAt: new Date().toISOString()
    };

    await cacheManager.set(
      `${CACHE_KEYS.SEARCH_RESULTS}:${Buffer.from(searchResults.query).toString('base64')}`,
      searchResults,
      CACHE_TTL.SHORT
    );

    // Test analytics caching
    const analyticsData = {
      date: new Date().toISOString().split('T')[0],
      metrics: {
        totalOrders: 150,
        totalRevenue: 15000,
        averageOrderValue: 100,
        topProducts: [product.id]
      }
    };

    await cacheManager.set(
      `${CACHE_KEYS.ANALYTICS}:daily:${analyticsData.date}`,
      analyticsData,
      CACHE_TTL.DAILY
    );

    // Verify all cached data
    const cachedProduct = await cacheManager.get(`${CACHE_KEYS.PRODUCT_DETAIL}:${product.id}`);
    const cachedCart = await cacheManager.get(`${CACHE_KEYS.CART}:${userCart.userId}`);
    const cachedSearch = await cacheManager.get(`${CACHE_KEYS.SEARCH_RESULTS}:${Buffer.from(searchResults.query).toString('base64')}`);
    const cachedAnalytics = await cacheManager.get(`${CACHE_KEYS.ANALYTICS}:daily:${analyticsData.date}`);

    console.log('✅ Product cached:', !!cachedProduct);
    console.log('✅ Cart cached:', !!cachedCart);
    console.log('✅ Search cached:', !!cachedSearch);
    console.log('✅ Analytics cached:', !!cachedAnalytics);

    // Test cache invalidation patterns
    console.log('🗑️ Testing cache invalidation...');
    
    // Invalidate product cache (simulating product update)
    await cacheManager.deletePattern(`${CACHE_KEYS.PRODUCT_DETAIL}:${product.id}`);
    await cacheManager.deletePattern(CACHE_KEYS.SEARCH_RESULTS); // Invalidate search results
    
    const productAfterInvalidation = await cacheManager.get(`${CACHE_KEYS.PRODUCT_DETAIL}:${product.id}`);
    console.log('✅ Product invalidated:', !productAfterInvalidation);

    console.log('🎉 E-commerce cache tests passed!');
    return true;
  } catch (error) {
    console.error('❌ E-commerce cache test failed:', error);
    return false;
  }
}

// Run all tests
export async function runAllCacheTests() {
  console.log('🚀 Running All Cache Tests...\n');

  const results = {
    basic: await testCacheSystem(),
    performance: await testCachePerformance(),
    ecommerce: await testEcommerceCache()
  };

  console.log('\n📋 Test Results Summary:');
  console.log('Basic functionality:', results.basic ? '✅ PASSED' : '❌ FAILED');
  console.log('Performance tests:', results.performance ? '✅ PASSED' : '❌ FAILED');
  console.log('E-commerce patterns:', results.ecommerce ? '✅ PASSED' : '❌ FAILED');

  const allPassed = results.basic && !!results.performance && results.ecommerce;
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');

  return results;
}
