import { Rating, IRating } from '@/models/Rating';
import mongoose from 'mongoose';

/**
 * CREATE a new Rating
 */
export async function createRating(
  userId: string,
  productId: string,
  rating: number,
  comment?: string
): Promise<IRating> {
  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    // Create the rating
    const newRating = new Rating({
      userId: new mongoose.Types.ObjectId(userId),
      productId: new mongoose.Types.ObjectId(productId),
      rating,
      comment
    });

    // Save the rating - this will trigger the post-save middleware
    // that updates the product's average rating
    const savedRating = await newRating.save({ session });

    await session.commitTransaction();
    return savedRating;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
}

/**
 * READ: Get all ratings for a product
 */
export async function getProductRatings(productId: string): Promise<IRating[]> {
  if (!mongoose.Types.ObjectId.isValid(productId)) return [];
  return Rating.find({ productId: new mongoose.Types.ObjectId(productId) })
    .populate('userId', 'username') // Assuming you want the username of the rater
    .exec();
}

/**
 * READ: Get a single rating by ID
 */
export async function getRatingById(id: string): Promise<IRating | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Rating.findById(id)
    .populate('userId', 'username')
    .exec();
}

/**
 * READ: Get user's rating for a product
 */
export async function getUserProductRating(
  userId: string,
  productId: string
): Promise<IRating | null> {
  if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(productId)) {
    return null;
  }
  return Rating.findOne({
    userId: new mongoose.Types.ObjectId(userId),
    productId: new mongoose.Types.ObjectId(productId)
  }).exec();
}

/**
 * UPDATE a rating
 */
export async function updateRating(
  id: string,
  userId: string,
  updateData: { rating?: number; comment?: string }
): Promise<IRating | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;

  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    // Find and update the rating, ensuring the user owns it
    const updatedRating = await Rating.findOneAndUpdate(
      { _id: id, userId: new mongoose.Types.ObjectId(userId) },
      updateData,
      { new: true, session }
    ).exec();

    if (!updatedRating) {
      throw new Error('Rating not found or user not authorized');
    }

    await session.commitTransaction();
    return updatedRating;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
}

/**
 * DELETE a rating
 */
export async function deleteRating(
  id: string,
  userId: string
): Promise<IRating | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;

  const session = await mongoose.startSession();
  try {
    session.startTransaction();

    // Find and delete the rating, ensuring the user owns it
    const deletedRating = await Rating.findOneAndDelete(
      { _id: id, userId: new mongoose.Types.ObjectId(userId) },
      { session }
    ).exec();

    if (!deletedRating) {
      throw new Error('Rating not found or user not authorized');
    }

    await session.commitTransaction();
    return deletedRating;
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
}