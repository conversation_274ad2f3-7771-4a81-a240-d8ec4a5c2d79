// lib/retrievers.ts
import { Product } from '@/models/Product';
// import { GroupOrder } from '@/models/GroupOrder';
// Import other models as needed...

// Example: Retrieve top N products (or based on some criteria)
export async function getProductsContext(query: string): Promise<string> {
  const products = await Product.find({ name: { $regex: query, $options: 'i' } }).limit(3).lean();
  if (!products || products.length === 0) {
    return 'No products found.';
  }
  return products
    .map((p) => `Product: ${p.name}. Description: ${p.description}. Price: ${p.price}`)
    .join('\n');
}

// Similarly, you can create functions for other models if needed.
