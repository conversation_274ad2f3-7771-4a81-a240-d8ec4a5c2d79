// lib/services/orderFulfillmentService.ts
import { OrderFulfillment } from '@/models/OrderFulfillment';
import { GroupOrder } from '@/models/GroupOrder';
import { Product } from '@/models/Product';
import { 
  FulfillmentStatus, 
  OrderPriority,
  ShippingProvider,
  IOrderFulfillment,
  FulfillmentStep,
  ShippingInfo,
  UpdateOrderStatusRequest,
  UpdateOrderStatusResponse,
  CreateFulfillmentRequest,
  CreateFulfillmentResponse,
  ShipOrderRequest,
  ShipOrderResponse,
  FulfillmentAnalytics
} from '@/types/orderFulfillment';
import { connectToDatabase } from '@/lib/dbconnect';
import mongoose from 'mongoose';

// Default fulfillment workflow steps
const DEFAULT_FULFILLMENT_STEPS: Omit<FulfillmentStep, 'completedAt'>[] = [
  {
    id: 'pending',
    name: 'Order Received',
    status: 'pending',
    description: 'Order has been received and is awaiting confirmation',
    estimatedCompletion: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours
    duration: 120
  },
  {
    id: 'confirmed',
    name: 'Order Confirmed',
    status: 'confirmed',
    description: 'Order has been confirmed and payment verified',
    estimatedCompletion: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours
    duration: 60
  },
  {
    id: 'processing',
    name: 'Processing',
    status: 'processing',
    description: 'Order is being processed and items are being prepared',
    estimatedCompletion: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    duration: 480
  },
  {
    id: 'packed',
    name: 'Packed',
    status: 'packed',
    description: 'Items have been packed and are ready for shipping',
    estimatedCompletion: new Date(Date.now() + 26 * 60 * 60 * 1000), // 26 hours
    duration: 120
  },
  {
    id: 'shipped',
    name: 'Shipped',
    status: 'shipped',
    description: 'Order has been shipped and is in transit',
    estimatedCompletion: new Date(Date.now() + 72 * 60 * 60 * 1000), // 72 hours
    duration: 60
  },
  {
    id: 'out_for_delivery',
    name: 'Out for Delivery',
    status: 'out_for_delivery',
    description: 'Order is out for delivery and will arrive soon',
    estimatedCompletion: new Date(Date.now() + 96 * 60 * 60 * 1000), // 96 hours
    duration: 240
  },
  {
    id: 'delivered',
    name: 'Delivered',
    status: 'delivered',
    description: 'Order has been successfully delivered',
    estimatedCompletion: new Date(Date.now() + 120 * 60 * 60 * 1000), // 120 hours
    duration: 0
  }
];

export class OrderFulfillmentService {
  
  /**
   * Create a new order fulfillment record
   */
  async createFulfillment(request: CreateFulfillmentRequest): Promise<CreateFulfillmentResponse> {
    await connectToDatabase();

    try {
      // Validate order exists
      const order = await GroupOrder.findById(request.orderId);
      if (!order) {
        return {
          success: false,
          error: 'Order not found'
        };
      }

      // Check if fulfillment already exists
      const existingFulfillment = await OrderFulfillment.findByOrderId(request.orderId);
      if (existingFulfillment) {
        return {
          success: false,
          error: 'Fulfillment record already exists for this order'
        };
      }

      // Create fulfillment record
      const fulfillment = new OrderFulfillment({
        orderId: new mongoose.Types.ObjectId(request.orderId),
        groupOrderId: order.groupId,
        status: 'pending',
        priority: request.priority || 'normal',
        steps: DEFAULT_FULFILLMENT_STEPS,
        inventoryAllocations: [],
        fulfillmentNotes: request.notes ? [request.notes] : [],
        assignedWarehouse: request.assignedWarehouse,
        estimatedFulfillmentDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 5 days
      });

      await fulfillment.save();

      return {
        success: true,
        fulfillment: this.transformFulfillmentForResponse(fulfillment)
      };
    } catch (error) {
      console.error('Error creating fulfillment:', error);
      return {
        success: false,
        error: 'Failed to create fulfillment record'
      };
    }
  }

  /**
   * Update order fulfillment status
   */
  async updateOrderStatus(request: UpdateOrderStatusRequest): Promise<UpdateOrderStatusResponse> {
    await connectToDatabase();

    try {
      const fulfillment = await OrderFulfillment.findByOrderId(request.orderId);
      if (!fulfillment) {
        return {
          success: false,
          error: 'Fulfillment record not found'
        };
      }

      // Update status
      fulfillment.status = request.status;
      
      // Add notes if provided
      if (request.notes) {
        fulfillment.fulfillmentNotes.push(request.notes);
      }

      // Update assigned staff if provided
      if (request.assignedTo) {
        if (!fulfillment.assignedStaff.includes(request.assignedTo)) {
          fulfillment.assignedStaff.push(request.assignedTo);
        }
      }

      // Update step completion
      const currentStep = fulfillment.steps.find(step => step.status === request.status);
      if (currentStep && !currentStep.completedAt) {
        currentStep.completedAt = new Date();
        if (request.assignedTo) {
          currentStep.assignedTo = request.assignedTo;
        }
        if (request.notes) {
          currentStep.notes = request.notes;
        }
      }

      // Update estimated completion for next step
      if (request.estimatedCompletion) {
        const nextStepIndex = fulfillment.steps.findIndex(step => step.status === request.status) + 1;
        if (nextStepIndex < fulfillment.steps.length) {
          fulfillment.steps[nextStepIndex].estimatedCompletion = request.estimatedCompletion;
        }
      }

      await fulfillment.save();

      return {
        success: true,
        fulfillment: this.transformFulfillmentForResponse(fulfillment)
      };
    } catch (error) {
      console.error('Error updating order status:', error);
      return {
        success: false,
        error: 'Failed to update order status'
      };
    }
  }

  /**
   * Ship an order
   */
  async shipOrder(request: ShipOrderRequest): Promise<ShipOrderResponse> {
    await connectToDatabase();

    try {
      const fulfillment = await OrderFulfillment.findByOrderId(request.orderId);
      if (!fulfillment) {
        return {
          success: false,
          error: 'Fulfillment record not found'
        };
      }

      // Validate current status allows shipping
      if (!['packed', 'processing'].includes(fulfillment.status)) {
        return {
          success: false,
          error: `Cannot ship order with status: ${fulfillment.status}`
        };
      }

      // Get order details for shipping address
      const order = await GroupOrder.findById(fulfillment.orderId);
      if (!order) {
        return {
          success: false,
          error: 'Order not found'
        };
      }

      // Create shipping info
      const shippingInfo: ShippingInfo = {
        provider: request.shippingProvider,
        trackingNumber: request.trackingNumber || this.generateTrackingNumber(request.shippingProvider),
        estimatedDelivery: request.estimatedDelivery || new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        shippingCost: request.shippingCost || this.calculateShippingCost(request.shippingProvider, order),
        shippingAddress: this.extractShippingAddress(order)
      };

      // Generate tracking URL
      shippingInfo.trackingUrl = this.generateTrackingUrl(request.shippingProvider, shippingInfo.trackingNumber!);

      // Update fulfillment
      fulfillment.shippingInfo = shippingInfo;
      fulfillment.status = 'shipped';
      fulfillment.fulfillmentNotes.push(`Order shipped via ${request.shippingProvider}`);

      await fulfillment.save();

      return {
        success: true,
        trackingInfo: {
          trackingNumber: shippingInfo.trackingNumber!,
          trackingUrl: shippingInfo.trackingUrl!,
          estimatedDelivery: shippingInfo.estimatedDelivery!
        }
      };
    } catch (error) {
      console.error('Error shipping order:', error);
      return {
        success: false,
        error: 'Failed to ship order'
      };
    }
  }

  /**
   * Get fulfillment analytics
   */
  async getFulfillmentAnalytics(startDate?: Date, endDate?: Date): Promise<FulfillmentAnalytics> {
    await connectToDatabase();

    try {
      const analytics = await OrderFulfillment.getFulfillmentAnalytics(startDate, endDate);
      
      // Get additional analytics
      const warehousePerformance = await this.getWarehousePerformance(startDate, endDate);
      const shippingProviderPerformance = await this.getShippingProviderPerformance(startDate, endDate);
      const monthlyTrends = await this.getMonthlyTrends(startDate, endDate);

      return {
        totalOrders: analytics.totalOrders,
        ordersByStatus: analytics.ordersByStatus,
        averageFulfillmentTime: analytics.averageFulfillmentTime,
        onTimeDeliveryRate: analytics.onTimeDeliveryRate,
        topPerformingWarehouses: warehousePerformance,
        shippingProviderPerformance: shippingProviderPerformance,
        monthlyTrends: monthlyTrends
      };
    } catch (error) {
      console.error('Error getting fulfillment analytics:', error);
      throw new Error('Failed to get fulfillment analytics');
    }
  }

  /**
   * Get order fulfillment by order ID
   */
  async getFulfillmentByOrderId(orderId: string): Promise<IOrderFulfillment | null> {
    await connectToDatabase();
    return await OrderFulfillment.findByOrderId(orderId);
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(status: FulfillmentStatus): Promise<IOrderFulfillment[]> {
    await connectToDatabase();
    return await OrderFulfillment.findByStatus(status);
  }

  /**
   * Get orders by warehouse
   */
  async getOrdersByWarehouse(warehouse: string): Promise<IOrderFulfillment[]> {
    await connectToDatabase();
    return await OrderFulfillment.findByWarehouse(warehouse);
  }

  // Private helper methods
  private transformFulfillmentForResponse(fulfillment: IOrderFulfillment): any {
    return {
      _id: fulfillment._id.toString(),
      orderId: fulfillment.orderId.toString(),
      groupOrderId: fulfillment.groupOrderId?.toString(),
      status: fulfillment.status,
      priority: fulfillment.priority,
      steps: fulfillment.steps,
      shippingInfo: fulfillment.shippingInfo,
      inventoryAllocations: fulfillment.inventoryAllocations,
      fulfillmentNotes: fulfillment.fulfillmentNotes,
      estimatedFulfillmentDate: fulfillment.estimatedFulfillmentDate,
      actualFulfillmentDate: fulfillment.actualFulfillmentDate,
      assignedWarehouse: fulfillment.assignedWarehouse,
      assignedStaff: fulfillment.assignedStaff,
      createdAt: fulfillment.createdAt,
      updatedAt: fulfillment.updatedAt
    };
  }

  private generateTrackingNumber(provider: ShippingProvider): string {
    const prefix = provider.toUpperCase().substring(0, 3);
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  private generateTrackingUrl(provider: ShippingProvider, trackingNumber: string): string {
    const trackingUrls = {
      courier_guy: `https://www.thecourierguy.co.za/track/${trackingNumber}`,
      fastway: `https://www.fastway.co.za/track-your-parcel?l=${trackingNumber}`,
      dawn_wing: `https://www.dawnwing.co.za/track/${trackingNumber}`,
      aramex: `https://www.aramex.com/track/results?mode=0&ShipmentNumber=${trackingNumber}`,
      pudo: `https://www.pudo.co.za/track/${trackingNumber}`,
      self_collection: '#'
    };
    return trackingUrls[provider] || '#';
  }

  private calculateShippingCost(provider: ShippingProvider, order: any): number {
    // Basic shipping cost calculation - in production, integrate with actual shipping APIs
    const baseCosts = {
      courier_guy: 99,
      fastway: 89,
      dawn_wing: 95,
      aramex: 120,
      pudo: 69,
      self_collection: 0
    };
    
    const baseCost = baseCosts[provider] || 99;
    const orderValue = order.totalOrderValue || 0;
    
    // Free shipping for orders over R500
    if (orderValue > 500) {
      return 0;
    }
    
    return baseCost;
  }

  private extractShippingAddress(order: any): any {
    // Extract shipping address from order - adapt based on your order structure
    const customerInfo = order.groupOrderNotes ? JSON.parse(order.groupOrderNotes) : {};
    return {
      street: customerInfo.address || '',
      city: customerInfo.city || '',
      state: customerInfo.state || '',
      postalCode: customerInfo.postalCode || '',
      country: customerInfo.country || 'South Africa'
    };
  }

  private async getWarehousePerformance(startDate?: Date, endDate?: Date): Promise<any[]> {
    // Placeholder implementation - replace with actual warehouse performance logic
    return [
      { warehouse: 'Cape Town', ordersProcessed: 150, averageTime: 18.5 },
      { warehouse: 'Johannesburg', ordersProcessed: 200, averageTime: 16.2 },
      { warehouse: 'Durban', ordersProcessed: 120, averageTime: 20.1 }
    ];
  }

  private async getShippingProviderPerformance(startDate?: Date, endDate?: Date): Promise<any[]> {
    // Placeholder implementation - replace with actual shipping provider performance logic
    return [
      { provider: 'courier_guy', ordersShipped: 100, onTimeRate: 92.5, averageDeliveryTime: 2.1 },
      { provider: 'fastway', ordersShipped: 80, onTimeRate: 88.7, averageDeliveryTime: 2.3 },
      { provider: 'pudo', ordersShipped: 60, onTimeRate: 95.2, averageDeliveryTime: 1.8 }
    ];
  }

  private async getMonthlyTrends(startDate?: Date, endDate?: Date): Promise<any[]> {
    // Placeholder implementation - replace with actual monthly trends logic
    return [
      { month: '2024-01', ordersProcessed: 450, averageFulfillmentTime: 18.2, onTimeRate: 91.5 },
      { month: '2024-02', ordersProcessed: 520, averageFulfillmentTime: 17.8, onTimeRate: 93.2 },
      { month: '2024-03', ordersProcessed: 480, averageFulfillmentTime: 16.9, onTimeRate: 94.1 }
    ];
  }
}

// Export singleton instance
export const orderFulfillmentService = new OrderFulfillmentService();
