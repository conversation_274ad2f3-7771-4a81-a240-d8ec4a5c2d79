// Gamification Service for Enhanced Group Buying
// Handles challenges, badges, rewards, leaderboards, and social features

import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { StokvelGroup } from '@/models/StokvelGroup';
import { realTimeGroupService } from './realTimeGroupService';

// Types for gamification system
export interface GroupChallenge {
  id: string;
  groupId: string;
  title: string;
  description: string;
  type: 'savings_target' | 'bulk_purchase' | 'member_growth' | 'activity_streak' | 'product_discovery' | 'collaboration_score' | 'speed_challenge' | 'eco_friendly';
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary';
  targetValue: number;
  currentValue: number;
  unit: string;
  startDate: Date;
  endDate: Date;
  reward: ChallengeReward;
  participants: ChallengeParticipant[];
  status: 'upcoming' | 'active' | 'completed' | 'expired' | 'cancelled';
  rules: string[];
  milestones: ChallengeMilestone[];
  leaderboard: ChallengeLeaderboardEntry[];
  createdBy: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface ChallengeReward {
  type: 'discount' | 'points' | 'badge' | 'feature_unlock' | 'cash_reward' | 'product_voucher';
  value: number;
  description: string;
  imageUrl?: string;
  expiresAt?: Date;
  conditions?: string[];
}

export interface ChallengeParticipant {
  userId: string;
  userName: string;
  userAvatar?: string;
  joinedAt: Date;
  progress: number;
  rank: number;
  contributions: ChallengeContribution[];
  isActive: boolean;
}

export interface ChallengeContribution {
  id: string;
  type: 'purchase' | 'referral' | 'activity' | 'collaboration' | 'milestone';
  value: number;
  description: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export interface ChallengeMilestone {
  id: string;
  title: string;
  description: string;
  targetValue: number;
  reward: ChallengeReward;
  isCompleted: boolean;
  completedBy: string[];
  completedAt?: Date;
}

export interface ChallengeLeaderboardEntry {
  userId: string;
  userName: string;
  userAvatar?: string;
  score: number;
  rank: number;
  progress: number;
  badges: string[];
  achievements: string[];
  lastActivity: Date;
}

export interface UserBadge {
  id: string;
  name: string;
  description: string;
  category: 'achievement' | 'milestone' | 'social' | 'savings' | 'collaboration' | 'special';
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  iconUrl: string;
  earnedAt: Date;
  earnedFrom: string; // challenge ID, activity, etc.
  metadata?: Record<string, unknown>;
}

export interface UserAchievement {
  id: string;
  title: string;
  description: string;
  type: 'first_time' | 'milestone' | 'streak' | 'social' | 'savings' | 'collaboration';
  points: number;
  unlockedAt: Date;
  requirements: AchievementRequirement[];
  progress: number;
  isCompleted: boolean;
}

export interface AchievementRequirement {
  type: 'action_count' | 'value_threshold' | 'time_based' | 'social_interaction';
  target: number;
  current: number;
  description: string;
}

export interface UserReward {
  id: string;
  userId: string;
  type: 'discount' | 'points' | 'cash' | 'voucher' | 'feature_access';
  value: number;
  description: string;
  source: string; // challenge, achievement, etc.
  status: 'pending' | 'claimed' | 'expired' | 'used';
  expiresAt?: Date;
  claimedAt?: Date;
  usedAt?: Date;
  metadata?: Record<string, unknown>;
}

export interface SocialProofItem {
  id: string;
  type: 'group_success' | 'member_achievement' | 'savings_milestone' | 'product_review' | 'challenge_completion';
  title: string;
  description: string;
  imageUrl?: string;
  groupId?: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  metrics: SocialProofMetric[];
  tags: string[];
  isPublic: boolean;
  createdAt: Date;
  engagement: SocialProofEngagement;
}

export interface SocialProofMetric {
  label: string;
  value: number;
  unit: string;
  trend?: 'up' | 'down' | 'stable';
}

export interface SocialProofEngagement {
  views: number;
  likes: number;
  shares: number;
  comments: number;
}

export interface GroupLeaderboard {
  id: string;
  type: 'savings' | 'activity' | 'growth' | 'collaboration' | 'challenges';
  period: 'daily' | 'weekly' | 'monthly' | 'all_time';
  entries: GroupLeaderboardEntry[];
  lastUpdated: Date;
}

export interface GroupLeaderboardEntry {
  groupId: string;
  groupName: string;
  groupAvatar?: string;
  score: number;
  rank: number;
  change: number; // rank change from previous period
  metrics: LeaderboardMetric[];
  memberCount: number;
  isVerified: boolean;
}

export interface LeaderboardMetric {
  name: string;
  value: number;
  unit: string;
  displayValue: string;
}

class GamificationService {
  private static instance: GamificationService;
  private groupChallenges: Map<string, GroupChallenge[]> = new Map();
  private userBadges: Map<string, UserBadge[]> = new Map();
  private userAchievements: Map<string, UserAchievement[]> = new Map();
  private userRewards: Map<string, UserReward[]> = new Map();
  private socialProofItems: SocialProofItem[] = [];
  private leaderboards: Map<string, GroupLeaderboard> = new Map();

  public static getInstance(): GamificationService {
    if (!GamificationService.instance) {
      GamificationService.instance = new GamificationService();
    }
    return GamificationService.instance;
  }

  // Challenge Management
  public async createChallenge(
    groupId: string,
    createdBy: string,
    challengeData: Omit<GroupChallenge, 'id' | 'participants' | 'status' | 'leaderboard' | 'createdAt' | 'currentValue'>
  ): Promise<GroupChallenge> {
    await connectToDatabase();

    const challenge: GroupChallenge = {
      ...challengeData,
      id: this.generateId('challenge'),
      groupId,
      createdBy,
      participants: [],
      currentValue: 0,
      status: new Date() < challengeData.startDate ? 'upcoming' : 'active',
      leaderboard: [],
      createdAt: new Date()
    };

    // Store challenge
    if (!this.groupChallenges.has(groupId)) {
      this.groupChallenges.set(groupId, []);
    }
    this.groupChallenges.get(groupId)!.push(challenge);

    // Create activity
    await realTimeGroupService.createActivity(
      groupId,
      createdBy,
      'challenge_created',
      'New challenge created!',
      `Created "${challenge.title}" challenge`,
      { challengeId: challenge.id, challengeType: challenge.type }
    );

    return challenge;
  }

  public async joinChallenge(challengeId: string, userId: string): Promise<boolean> {
    await connectToDatabase();

    const user = await User.findById(userId);
    if (!user) return false;

    // Find challenge across all groups
    for (const [groupId, challenges] of this.groupChallenges.entries()) {
      const challenge = challenges.find(c => c.id === challengeId);
      if (challenge && challenge.status === 'active') {
        // Check if user is already participating
        if (challenge.participants.some(p => p.userId === userId)) {
          return false;
        }

        // Add participant
        const participant: ChallengeParticipant = {
          userId,
          userName: user.name,
          userAvatar: user.avatar,
          joinedAt: new Date(),
          progress: 0,
          rank: challenge.participants.length + 1,
          contributions: [],
          isActive: true
        };

        challenge.participants.push(participant);

        // Create activity
        await realTimeGroupService.createActivity(
          groupId,
          userId,
          'challenge_joined',
          'Joined challenge',
          `Joined "${challenge.title}" challenge`,
          { challengeId: challenge.id }
        );

        return true;
      }
    }

    return false;
  }

  public async updateChallengeProgress(
    challengeId: string,
    userId: string,
    contribution: Omit<ChallengeContribution, 'id' | 'timestamp'>
  ): Promise<boolean> {
    // Find and update challenge progress
    for (const [groupId, challenges] of this.groupChallenges.entries()) {
      const challenge = challenges.find(c => c.id === challengeId);
      if (challenge) {
        const participant = challenge.participants.find(p => p.userId === userId);
        if (participant) {
          // Add contribution
          const newContribution: ChallengeContribution = {
            ...contribution,
            id: this.generateId('contrib'),
            timestamp: new Date()
          };

          participant.contributions.push(newContribution);
          participant.progress += contribution.value;

          // Update challenge current value
          challenge.currentValue += contribution.value;

          // Update leaderboard
          this.updateChallengeLeaderboard(challenge);

          // Check for completion
          if (challenge.currentValue >= challenge.targetValue) {
            await this.completeChallenge(challenge, groupId);
          }

          // Check milestones
          await this.checkChallengeMilestones(challenge, groupId);

          return true;
        }
      }
    }

    return false;
  }

  private updateChallengeLeaderboard(challenge: GroupChallenge): void {
    // Sort participants by progress
    const sortedParticipants = [...challenge.participants]
      .sort((a, b) => b.progress - a.progress);

    // Update ranks
    sortedParticipants.forEach((participant, index) => {
      participant.rank = index + 1;
    });

    // Update leaderboard
    challenge.leaderboard = sortedParticipants.map(participant => ({
      userId: participant.userId,
      userName: participant.userName,
      userAvatar: participant.userAvatar,
      score: participant.progress,
      rank: participant.rank,
      progress: (participant.progress / challenge.targetValue) * 100,
      badges: [], // Would be populated from user badges
      achievements: [], // Would be populated from user achievements
      lastActivity: participant.contributions[participant.contributions.length - 1]?.timestamp || participant.joinedAt
    }));
  }

  private async completeChallenge(challenge: GroupChallenge, groupId: string): Promise<void> {
    challenge.status = 'completed';
    challenge.completedAt = new Date();

    // Award rewards to participants
    for (const participant of challenge.participants) {
      await this.awardReward(participant.userId, challenge.reward, `Challenge: ${challenge.title}`);
      
      // Award badges based on performance
      if (participant.rank === 1) {
        await this.awardBadge(participant.userId, 'challenge_winner', challenge.id);
      } else if (participant.rank <= 3) {
        await this.awardBadge(participant.userId, 'challenge_podium', challenge.id);
      } else {
        await this.awardBadge(participant.userId, 'challenge_participant', challenge.id);
      }
    }

    // Create activity
    await realTimeGroupService.createActivity(
      groupId,
      challenge.createdBy,
      'challenge_completed',
      'Challenge completed!',
      `"${challenge.title}" challenge has been completed`,
      { challengeId: challenge.id, participants: challenge.participants.length }
    );

    // Create social proof
    await this.createSocialProof({
      type: 'challenge_completion',
      title: `Challenge Completed: ${challenge.title}`,
      description: `Group successfully completed the ${challenge.title} challenge with ${challenge.participants.length} participants`,
      groupId,
      metrics: [
        { label: 'Participants', value: challenge.participants.length, unit: 'members' },
        { label: 'Target Achieved', value: challenge.currentValue, unit: challenge.unit },
        { label: 'Duration', value: Math.ceil((challenge.completedAt!.getTime() - challenge.startDate.getTime()) / (1000 * 60 * 60 * 24)), unit: 'days' }
      ],
      tags: [challenge.type, challenge.difficulty, 'completed'],
      isPublic: true
    });
  }

  private async checkChallengeMilestones(challenge: GroupChallenge, groupId: string): Promise<void> {
    for (const milestone of challenge.milestones) {
      if (!milestone.isCompleted && challenge.currentValue >= milestone.targetValue) {
        milestone.isCompleted = true;
        milestone.completedAt = new Date();

        // Award milestone rewards to all participants
        for (const participant of challenge.participants) {
          await this.awardReward(participant.userId, milestone.reward, `Milestone: ${milestone.title}`);
          milestone.completedBy.push(participant.userId);
        }

        // Create activity
        await realTimeGroupService.createActivity(
          groupId,
          challenge.createdBy,
          'milestone_reached',
          'Milestone achieved!',
          `Reached "${milestone.title}" milestone in ${challenge.title}`,
          { challengeId: challenge.id, milestoneId: milestone.id }
        );
      }
    }
  }

  // Badge and Achievement System
  public async awardBadge(userId: string, badgeType: string, source: string): Promise<UserBadge | null> {
    await connectToDatabase();

    const badgeDefinitions = this.getBadgeDefinitions();
    const badgeDefinition = badgeDefinitions[badgeType];
    
    if (!badgeDefinition) return null;

    const badge: UserBadge = {
      id: this.generateId('badge'),
      name: badgeDefinition.name,
      description: badgeDefinition.description,
      category: badgeDefinition.category,
      rarity: badgeDefinition.rarity,
      iconUrl: badgeDefinition.iconUrl,
      earnedAt: new Date(),
      earnedFrom: source
    };

    // Store badge
    if (!this.userBadges.has(userId)) {
      this.userBadges.set(userId, []);
    }
    this.userBadges.get(userId)!.push(badge);

    // Check for achievements
    await this.checkAchievements(userId);

    return badge;
  }

  public async awardReward(userId: string, reward: ChallengeReward, source: string): Promise<UserReward> {
    const userReward: UserReward = {
      id: this.generateId('reward'),
      userId,
      type: reward.type,
      value: reward.value,
      description: reward.description,
      source,
      status: 'pending',
      expiresAt: reward.expiresAt
    };

    // Store reward
    if (!this.userRewards.has(userId)) {
      this.userRewards.set(userId, []);
    }
    this.userRewards.get(userId)!.push(userReward);

    return userReward;
  }

  private async checkAchievements(userId: string): Promise<void> {
    const userBadges = this.userBadges.get(userId) || [];
    const userAchievements = this.userAchievements.get(userId) || [];

    // Check badge-based achievements
    const badgeCount = userBadges.length;
    const rareBadges = userBadges.filter(b => b.rarity === 'rare' || b.rarity === 'epic' || b.rarity === 'legendary').length;

    // Badge collector achievements
    if (badgeCount >= 5 && !userAchievements.some(a => a.id === 'badge_collector_5')) {
      await this.unlockAchievement(userId, 'badge_collector_5');
    }
    if (badgeCount >= 10 && !userAchievements.some(a => a.id === 'badge_collector_10')) {
      await this.unlockAchievement(userId, 'badge_collector_10');
    }
    if (rareBadges >= 3 && !userAchievements.some(a => a.id === 'rare_collector')) {
      await this.unlockAchievement(userId, 'rare_collector');
    }
  }

  private async unlockAchievement(userId: string, achievementId: string): Promise<void> {
    const achievementDefinitions = this.getAchievementDefinitions();
    const achievementDef = achievementDefinitions[achievementId];
    
    if (!achievementDef) return;

    const achievement: UserAchievement = {
      id: achievementId,
      title: achievementDef.title,
      description: achievementDef.description,
      type: achievementDef.type,
      points: achievementDef.points,
      unlockedAt: new Date(),
      requirements: achievementDef.requirements,
      progress: 100,
      isCompleted: true
    };

    if (!this.userAchievements.has(userId)) {
      this.userAchievements.set(userId, []);
    }
    this.userAchievements.get(userId)!.push(achievement);

    // Award points as reward
    await this.awardReward(userId, {
      type: 'points',
      value: achievement.points,
      description: `Achievement unlocked: ${achievement.title}`
    }, `Achievement: ${achievementId}`);
  }

  // Social Proof System
  public async createSocialProof(data: Omit<SocialProofItem, 'id' | 'createdAt' | 'engagement'>): Promise<SocialProofItem> {
    const socialProof: SocialProofItem = {
      ...data,
      id: this.generateId('social'),
      createdAt: new Date(),
      engagement: {
        views: 0,
        likes: 0,
        shares: 0,
        comments: 0
      }
    };

    this.socialProofItems.push(socialProof);
    return socialProof;
  }

  // Leaderboard Management
  public async updateGroupLeaderboards(): Promise<void> {
    // Update various leaderboard types
    await this.updateSavingsLeaderboard();
    await this.updateActivityLeaderboard();
    await this.updateGrowthLeaderboard();
    await this.updateCollaborationLeaderboard();
  }

  private async updateSavingsLeaderboard(): Promise<void> {
    // Mock implementation - would calculate actual savings from group data
    const entries: GroupLeaderboardEntry[] = [
      {
        groupId: 'group_1',
        groupName: 'Electronics Enthusiasts',
        score: 15000,
        rank: 1,
        change: 0,
        metrics: [
          { name: 'Total Savings', value: 15000, unit: 'ZAR', displayValue: 'R15,000' },
          { name: 'Avg per Member', value: 1250, unit: 'ZAR', displayValue: 'R1,250' }
        ],
        memberCount: 12,
        isVerified: true
      }
    ];

    this.leaderboards.set('savings_weekly', {
      id: 'savings_weekly',
      type: 'savings',
      period: 'weekly',
      entries,
      lastUpdated: new Date()
    });
  }

  private async updateActivityLeaderboard(): Promise<void> {
    // Similar implementation for activity leaderboard
  }

  private async updateGrowthLeaderboard(): Promise<void> {
    // Similar implementation for growth leaderboard
  }

  private async updateCollaborationLeaderboard(): Promise<void> {
    // Similar implementation for collaboration leaderboard
  }

  // Public API methods
  public async getGroupChallenges(groupId: string): Promise<GroupChallenge[]> {
    return this.groupChallenges.get(groupId) || [];
  }

  public async getUserBadges(userId: string): Promise<UserBadge[]> {
    return this.userBadges.get(userId) || [];
  }

  public async getUserAchievements(userId: string): Promise<UserAchievement[]> {
    return this.userAchievements.get(userId) || [];
  }

  public async getUserRewards(userId: string): Promise<UserReward[]> {
    return this.userRewards.get(userId) || [];
  }

  public async getSocialProofItems(limit: number = 10): Promise<SocialProofItem[]> {
    return this.socialProofItems
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }

  public async getLeaderboard(type: string, period: string): Promise<GroupLeaderboard | null> {
    return this.leaderboards.get(`${type}_${period}`) || null;
  }

  // Utility methods
  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getBadgeDefinitions(): Record<string, any> {
    return {
      challenge_winner: {
        name: 'Challenge Champion',
        description: 'Won a group challenge',
        category: 'achievement',
        rarity: 'rare',
        iconUrl: '/badges/challenge_winner.svg'
      },
      challenge_podium: {
        name: 'Top Performer',
        description: 'Finished in top 3 of a challenge',
        category: 'achievement',
        rarity: 'uncommon',
        iconUrl: '/badges/challenge_podium.svg'
      },
      challenge_participant: {
        name: 'Team Player',
        description: 'Participated in a group challenge',
        category: 'social',
        rarity: 'common',
        iconUrl: '/badges/challenge_participant.svg'
      }
    };
  }

  private getAchievementDefinitions(): Record<string, any> {
    return {
      badge_collector_5: {
        title: 'Badge Collector',
        description: 'Earned 5 badges',
        type: 'milestone',
        points: 100,
        requirements: [{ type: 'action_count', target: 5, current: 0, description: 'Earn 5 badges' }]
      },
      badge_collector_10: {
        title: 'Badge Master',
        description: 'Earned 10 badges',
        type: 'milestone',
        points: 250,
        requirements: [{ type: 'action_count', target: 10, current: 0, description: 'Earn 10 badges' }]
      },
      rare_collector: {
        title: 'Rare Collector',
        description: 'Earned 3 rare or higher badges',
        type: 'milestone',
        points: 500,
        requirements: [{ type: 'action_count', target: 3, current: 0, description: 'Earn 3 rare+ badges' }]
      }
    };
  }
}

export const gamificationService = GamificationService.getInstance();
