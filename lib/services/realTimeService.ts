// lib/services/realTimeService.ts
"use client";

import React from 'react';
import {
  RealTimeEvent,
  RealTimeEventType,
  ConnectionStatus,
  EventHandlers,
  RealTimeConfig,
  WebSocketMessage,
  Subscription,
  GroupOrderUpdate,
  CartUpdateEvent,
  PaymentUpdateEvent,
  OrderStatusUpdateEvent,
  DiscountMilestoneEvent,
  UserActivityEvent,
  NotificationEvent
} from '@/types/realTimeUpdates';

export class RealTimeService {
  private ws: WebSocket | null = null;
  private config: RealTimeConfig;
  private handlers: EventHandlers = {};
  private subscriptions: Map<string, Subscription> = new Map();
  private connectionStatus: ConnectionStatus = 'disconnected';
  private reconnectAttempts = 0;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private userId: string | null = null;

  constructor(config: RealTimeConfig) {
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 3000,
      heartbeatInterval: 30000,
      subscriptionTimeout: 60000,
      enableLogging: true,
      enableAnalytics: false,
      ...config
    };
  }

  /**
   * Connect to the WebSocket server
   */
  connect(userId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.userId = userId;
      this.setConnectionStatus('connecting');

      try {
        const wsUrl = `${this.config.serverUrl}?userId=${userId}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          this.log('WebSocket connected');
          this.setConnectionStatus('connected');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          this.log('WebSocket closed', event.code, event.reason);
          this.setConnectionStatus('disconnected');
          this.stopHeartbeat();
          
          if (!event.wasClean && this.reconnectAttempts < this.config.reconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          this.log('WebSocket error', error);
          this.setConnectionStatus('error');
          this.handlers.onError?.(new Error('WebSocket connection error'));
          reject(error);
        };

      } catch (error) {
        this.setConnectionStatus('error');
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.setConnectionStatus('disconnected');
    this.subscriptions.clear();
  }

  /**
   * Subscribe to events
   */
  subscribe(type: 'group' | 'user' | 'order' | 'global', targetId: string, events?: RealTimeEventType[]): string {
    const subscriptionId = `${type}_${targetId}_${Date.now()}`;
    
    const subscription: Subscription = {
      id: subscriptionId,
      type,
      targetId,
      userId: this.userId!,
      events: events || [],
      createdAt: new Date(),
      lastActivity: new Date()
    };

    this.subscriptions.set(subscriptionId, subscription);

    // Send subscription message to server
    this.sendMessage({
      type: 'subscription',
      subscription: {
        type,
        id: targetId
      },
      timestamp: new Date()
    });

    this.log(`Subscribed to ${type}:${targetId}`);
    return subscriptionId;
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      this.sendMessage({
        type: 'unsubscription',
        subscription: {
          type: subscription.type,
          id: subscription.targetId
        },
        timestamp: new Date()
      });

      this.subscriptions.delete(subscriptionId);
      this.log(`Unsubscribed from ${subscription.type}:${subscription.targetId}`);
    }
  }

  /**
   * Set event handlers
   */
  setHandlers(handlers: EventHandlers): void {
    this.handlers = { ...this.handlers, ...handlers };
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * Get active subscriptions
   */
  getSubscriptions(): Subscription[] {
    return Array.from(this.subscriptions.values());
  }

  // Private methods
  private handleMessage(data: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(data);

      switch (message.type) {
        case 'event':
          if (message.event) {
            this.handleEvent(message.event);
          }
          break;

        case 'heartbeat':
          // Respond to heartbeat
          this.sendMessage({
            type: 'heartbeat',
            timestamp: new Date()
          });
          break;

        case 'error':
          if (message.error) {
            this.log('Server error:', message.error);
            this.handlers.onError?.(new Error(message.error.message));
          }
          break;

        default:
          this.log('Unknown message type:', message.type);
      }
    } catch (error) {
      this.log('Error parsing message:', error);
    }
  }

  private handleEvent(event: RealTimeEvent): void {
    this.log('Received event:', event.type, event.data);

    // Update subscription activity
    this.subscriptions.forEach(subscription => {
      subscription.lastActivity = new Date();
    });

    // Route event to appropriate handler
    switch (event.type) {
      case 'group_order_updated':
        this.handlers.onGroupOrderUpdate?.(event.data as GroupOrderUpdate);
        break;

      case 'cart_updated':
        this.handlers.onCartUpdate?.(event.data as CartUpdateEvent);
        break;

      case 'payment_processed':
        this.handlers.onPaymentUpdate?.(event.data as PaymentUpdateEvent);
        break;

      case 'order_status_changed':
        this.handlers.onOrderStatusUpdate?.(event.data as OrderStatusUpdateEvent);
        break;

      case 'discount_milestone_reached':
        this.handlers.onDiscountMilestone?.(event.data as DiscountMilestoneEvent);
        break;

      case 'user_activity':
        this.handlers.onUserActivity?.(event.data as UserActivityEvent);
        break;

      case 'notification_sent':
        this.handlers.onNotification?.(event.data as NotificationEvent);
        break;

      default:
        this.log('Unhandled event type:', event.type);
    }
  }

  private sendMessage(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      this.log('Cannot send message: WebSocket not connected');
    }
  }

  private setConnectionStatus(status: ConnectionStatus): void {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status;
      this.handlers.onConnectionStatusChange?.(status);
      this.log('Connection status changed:', status);
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      this.sendMessage({
        type: 'heartbeat',
        timestamp: new Date()
      });
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private scheduleReconnect(): void {
    this.setConnectionStatus('reconnecting');
    this.reconnectAttempts++;
    
    const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    this.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

    this.reconnectTimeout = setTimeout(() => {
      if (this.userId) {
        this.connect(this.userId).catch(() => {
          // Reconnect failed, will try again if attempts remaining
        });
      }
    }, delay);
  }

  private log(...args: any[]): void {
    if (this.config.enableLogging) {
      console.log('[RealTimeService]', ...args);
    }
  }
}

// Singleton instance
let realTimeServiceInstance: RealTimeService | null = null;

export function getRealTimeService(config?: Partial<RealTimeConfig>): RealTimeService {
  if (!realTimeServiceInstance) {
    const defaultConfig: RealTimeConfig = {
      serverUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001/ws',
      reconnectAttempts: 5,
      reconnectDelay: 3000,
      heartbeatInterval: 30000,
      subscriptionTimeout: 60000,
      enableLogging: process.env.NODE_ENV === 'development',
      enableAnalytics: false
    };

    realTimeServiceInstance = new RealTimeService({ ...defaultConfig, ...config });
  }

  return realTimeServiceInstance;
}

// React hook for real-time updates
export function useRealTimeUpdates(userId: string | null, handlers: EventHandlers) {
  const service = getRealTimeService();

  React.useEffect(() => {
    if (!userId) return;

    service.setHandlers(handlers);
    service.connect(userId);

    return () => {
      service.disconnect();
    };
  }, [userId]);

  return {
    service,
    connectionStatus: service.getConnectionStatus(),
    subscribe: service.subscribe.bind(service),
    unsubscribe: service.unsubscribe.bind(service)
  };
}
