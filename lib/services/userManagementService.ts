// Enhanced User Management Service for Admin System
// Comprehensive user analytics, segmentation, and activity tracking

import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { GroupOrder } from '@/models/GroupOrder';

// Enhanced User Management Types
export interface UserAnalytics {
  userId: string;
  email: string;
  name: string;
  role: string;
  registrationDate: Date;
  lastLoginDate?: Date;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lifetimeValue: number;
  groupsJoined: number;
  groupsCreated: number;
  activityScore: number;
  engagementLevel: 'high' | 'medium' | 'low' | 'inactive';
  riskScore: number;
  churnProbability: number;
  preferredCategories: string[];
  behaviorSegment: string;
  acquisitionChannel: string;
  lastActivity: Date;
  deviceInfo: DeviceInfo;
  locationInfo: LocationInfo;
}

export interface UserSegment {
  id: string;
  name: string;
  description: string;
  criteria: SegmentCriteria;
  userCount: number;
  averageValue: number;
  growthRate: number;
  characteristics: SegmentCharacteristics;
  recommendations: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SegmentCriteria {
  demographics?: {
    ageRange?: { min: number; max: number };
    location?: string[];
    registrationPeriod?: { from: Date; to: Date };
  };
  behavioral?: {
    totalSpent?: { min: number; max: number };
    orderCount?: { min: number; max: number };
    lastActivity?: { days: number };
    engagementLevel?: string[];
  };
  transactional?: {
    averageOrderValue?: { min: number; max: number };
    preferredCategories?: string[];
    paymentMethods?: string[];
  };
  custom?: Record<string, unknown>;
}

export interface SegmentCharacteristics {
  averageAge: number;
  averageLifetimeValue: number;
  averageOrderValue: number;
  mostPopularCategories: string[];
  commonBehaviors: string[];
  retentionRate: number;
  conversionRate: number;
}

export interface UserActivity {
  userId: string;
  sessionId: string;
  timestamp: Date;
  action: string;
  page: string;
  duration: number;
  deviceInfo: DeviceInfo;
  locationInfo: LocationInfo;
  metadata: Record<string, unknown>;
}

export interface UserJourney {
  userId: string;
  stages: JourneyStage[];
  currentStage: string;
  totalDuration: number;
  conversionEvents: ConversionEvent[];
  dropoffPoints: string[];
  touchpoints: Touchpoint[];
}

export interface JourneyStage {
  stage: string;
  entryDate: Date;
  exitDate?: Date;
  duration: number;
  actions: string[];
  conversionRate: number;
}

export interface ConversionEvent {
  event: string;
  timestamp: Date;
  value: number;
  metadata: Record<string, unknown>;
}

export interface Touchpoint {
  channel: string;
  timestamp: Date;
  interaction: string;
  outcome: string;
}

export interface DeviceInfo {
  type: 'desktop' | 'mobile' | 'tablet';
  os: string;
  browser: string;
  screenResolution: string;
  userAgent: string;
}

export interface LocationInfo {
  country: string;
  region: string;
  city: string;
  timezone: string;
  ipAddress: string;
}

export interface CustomerLifetimeValue {
  userId: string;
  currentValue: number;
  predictedValue: number;
  valueSegment: 'high' | 'medium' | 'low';
  contributingFactors: string[];
  recommendations: string[];
  calculatedAt: Date;
}

export interface ChurnPrediction {
  userId: string;
  churnProbability: number;
  riskLevel: 'high' | 'medium' | 'low';
  contributingFactors: ChurnFactor[];
  recommendations: string[];
  nextBestAction: string;
  calculatedAt: Date;
}

export interface ChurnFactor {
  factor: string;
  impact: number;
  description: string;
}

export interface UserEngagementMetrics {
  userId: string;
  period: string;
  sessionCount: number;
  totalSessionDuration: number;
  averageSessionDuration: number;
  pageViews: number;
  uniquePages: number;
  bounceRate: number;
  conversionRate: number;
  engagementScore: number;
  trendDirection: 'increasing' | 'decreasing' | 'stable';
}

class UserManagementService {
  private static instance: UserManagementService;
  private cache: Map<string, { data: unknown; timestamp: number; ttl: number }> = new Map();

  public static getInstance(): UserManagementService {
    if (!UserManagementService.instance) {
      UserManagementService.instance = new UserManagementService();
    }
    return UserManagementService.instance;
  }

  // Cache management
  private getCachedData<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data as T;
    }
    this.cache.delete(key);
    return null;
  }

  private setCachedData<T>(key: string, data: T, ttl: number = 300000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  // User Analytics
  public async getUserAnalytics(userId?: string): Promise<UserAnalytics[]> {
    const cacheKey = `user_analytics_${userId || 'all'}`;
    const cached = this.getCachedData<UserAnalytics[]>(cacheKey);
    if (cached) return cached;

    await connectToDatabase();

    const users = userId 
      ? await User.findById(userId)
      : await User.find({ role: 'customer' }).limit(1000);

    const userList = Array.isArray(users) ? users : [users].filter(Boolean);
    
    const analytics = await Promise.all(
      userList.map(user => this.calculateUserAnalytics(user))
    );

    this.setCachedData(cacheKey, analytics);
    return analytics;
  }

  private async calculateUserAnalytics(user: any): Promise<UserAnalytics> {
    // Calculate user orders and spending
    const orders = await GroupOrder.find({ 
      'participants.userId': user._id 
    });

    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => {
      const userParticipation = order.participants.find(
        (p: any) => p.userId.toString() === user._id.toString()
      );
      return sum + (userParticipation?.amount || 0);
    }, 0);

    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    
    // Calculate engagement metrics
    const daysSinceRegistration = Math.floor(
      (Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    const activityScore = this.calculateActivityScore(user, orders);
    const engagementLevel = this.determineEngagementLevel(activityScore, user.lastLoginDate);
    const lifetimeValue = this.calculateLifetimeValue(totalSpent, daysSinceRegistration);
    const churnProbability = this.calculateChurnProbability(user, orders);

    return {
      userId: user._id.toString(),
      email: user.email,
      name: user.name || 'Unknown',
      role: user.role,
      registrationDate: new Date(user.createdAt),
      lastLoginDate: user.lastLoginDate ? new Date(user.lastLoginDate) : undefined,
      totalOrders,
      totalSpent,
      averageOrderValue,
      lifetimeValue,
      groupsJoined: orders.length, // Simplified - would need proper group tracking
      groupsCreated: 0, // Would need to track group creation
      activityScore,
      engagementLevel,
      riskScore: churnProbability * 100,
      churnProbability,
      preferredCategories: this.extractPreferredCategories(orders),
      behaviorSegment: this.determineBehaviorSegment(activityScore, totalSpent),
      acquisitionChannel: user.acquisitionChannel || 'organic',
      lastActivity: user.lastLoginDate || user.createdAt,
      deviceInfo: {
        type: 'desktop', // Would be tracked from user sessions
        os: 'unknown',
        browser: 'unknown',
        screenResolution: 'unknown',
        userAgent: 'unknown'
      },
      locationInfo: {
        country: user.country || 'South Africa',
        region: user.region || 'Unknown',
        city: user.city || 'Unknown',
        timezone: 'Africa/Johannesburg',
        ipAddress: 'hidden'
      }
    };
  }

  // User Segmentation
  public async createUserSegment(
    name: string,
    description: string,
    criteria: SegmentCriteria
  ): Promise<UserSegment> {
    const users = await this.getUsersMatchingCriteria(criteria);
    const characteristics = await this.calculateSegmentCharacteristics(users);

    const segment: UserSegment = {
      id: this.generateSegmentId(),
      name,
      description,
      criteria,
      userCount: users.length,
      averageValue: characteristics.averageLifetimeValue,
      growthRate: 0, // Would be calculated from historical data
      characteristics,
      recommendations: this.generateSegmentRecommendations(characteristics),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return segment;
  }

  public async getUserSegments(): Promise<UserSegment[]> {
    // Return predefined segments - in production, these would be stored in database
    return [
      {
        id: 'high_value',
        name: 'High Value Customers',
        description: 'Customers with high lifetime value and frequent purchases',
        criteria: {
          behavioral: {
            totalSpent: { min: 10000, max: Infinity },
            orderCount: { min: 5, max: Infinity }
          }
        },
        userCount: 150,
        averageValue: 15000,
        growthRate: 12,
        characteristics: {
          averageAge: 35,
          averageLifetimeValue: 15000,
          averageOrderValue: 2500,
          mostPopularCategories: ['Electronics', 'Home'],
          commonBehaviors: ['Frequent purchases', 'High engagement'],
          retentionRate: 85,
          conversionRate: 25
        },
        recommendations: [
          'Offer exclusive products and early access',
          'Implement VIP customer service',
          'Create loyalty rewards program'
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'at_risk',
        name: 'At-Risk Customers',
        description: 'Customers showing signs of potential churn',
        criteria: {
          behavioral: {
            lastActivity: { days: 60 },
            engagementLevel: ['low']
          }
        },
        userCount: 89,
        averageValue: 3500,
        growthRate: -8,
        characteristics: {
          averageAge: 42,
          averageLifetimeValue: 3500,
          averageOrderValue: 800,
          mostPopularCategories: ['Clothing', 'Books'],
          commonBehaviors: ['Infrequent purchases', 'Low engagement'],
          retentionRate: 45,
          conversionRate: 8
        },
        recommendations: [
          'Send re-engagement campaigns',
          'Offer personalized discounts',
          'Improve customer support touchpoints'
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  // Customer Lifetime Value
  public async calculateCustomerLifetimeValue(userId: string): Promise<CustomerLifetimeValue> {
    const userAnalytics = await this.getUserAnalytics(userId);
    const user = userAnalytics[0];

    if (!user) {
      throw new Error('User not found');
    }

    const currentValue = user.totalSpent;
    const predictedValue = this.predictFutureValue(user);
    const valueSegment = this.determineValueSegment(currentValue);

    return {
      userId,
      currentValue,
      predictedValue,
      valueSegment,
      contributingFactors: [
        'Purchase frequency',
        'Average order value',
        'Engagement level',
        'Retention probability'
      ],
      recommendations: this.generateCLVRecommendations(valueSegment, user),
      calculatedAt: new Date()
    };
  }

  // Churn Prediction
  public async predictCustomerChurn(userId: string): Promise<ChurnPrediction> {
    const userAnalytics = await this.getUserAnalytics(userId);
    const user = userAnalytics[0];

    if (!user) {
      throw new Error('User not found');
    }

    const churnFactors = this.analyzeChurnFactors(user);
    const churnProbability = user.churnProbability;
    const riskLevel = churnProbability > 0.7 ? 'high' : churnProbability > 0.4 ? 'medium' : 'low';

    return {
      userId,
      churnProbability,
      riskLevel,
      contributingFactors: churnFactors,
      recommendations: this.generateChurnPreventionRecommendations(riskLevel, churnFactors),
      nextBestAction: this.determineNextBestAction(riskLevel, user),
      calculatedAt: new Date()
    };
  }

  // Helper Methods
  private calculateActivityScore(user: any, orders: any[]): number {
    const daysSinceRegistration = Math.floor(
      (Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    const orderFrequency = orders.length / Math.max(daysSinceRegistration, 1);
    const recentActivity = user.lastLoginDate ? 
      Math.max(0, 30 - Math.floor((Date.now() - new Date(user.lastLoginDate).getTime()) / (1000 * 60 * 60 * 24))) : 0;
    
    return Math.min(100, (orderFrequency * 50) + recentActivity);
  }

  private determineEngagementLevel(activityScore: number, lastLogin?: Date): 'high' | 'medium' | 'low' | 'inactive' {
    if (!lastLogin) return 'inactive';
    
    const daysSinceLogin = Math.floor((Date.now() - new Date(lastLogin).getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysSinceLogin > 90) return 'inactive';
    if (activityScore > 70) return 'high';
    if (activityScore > 40) return 'medium';
    return 'low';
  }

  private calculateLifetimeValue(totalSpent: number, daysSinceRegistration: number): number {
    // Simple CLV calculation - in production, this would be more sophisticated
    const monthlyValue = totalSpent / Math.max(daysSinceRegistration / 30, 1);
    const predictedLifespan = 24; // months
    return monthlyValue * predictedLifespan;
  }

  private calculateChurnProbability(user: any, orders: any[]): number {
    const daysSinceLastLogin = user.lastLoginDate ? 
      Math.floor((Date.now() - new Date(user.lastLoginDate).getTime()) / (1000 * 60 * 60 * 24)) : 365;
    
    const daysSinceLastOrder = orders.length > 0 ?
      Math.floor((Date.now() - new Date(orders[orders.length - 1].createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 365;
    
    // Simple churn probability calculation
    const loginFactor = Math.min(daysSinceLastLogin / 90, 1);
    const orderFactor = Math.min(daysSinceLastOrder / 60, 1);
    
    return (loginFactor + orderFactor) / 2;
  }

  private extractPreferredCategories(orders: any[]): string[] {
    // Extract categories from orders - simplified implementation
    return ['Electronics', 'Home', 'Clothing']; // Mock data
  }

  private determineBehaviorSegment(activityScore: number, totalSpent: number): string {
    if (activityScore > 70 && totalSpent > 5000) return 'Champion';
    if (activityScore > 50 && totalSpent > 2000) return 'Loyal Customer';
    if (activityScore > 30) return 'Potential Loyalist';
    return 'New Customer';
  }

  private async getUsersMatchingCriteria(_criteria: SegmentCriteria): Promise<any[]> {
    // Implementation would filter users based on criteria
    return [];
  }

  private async calculateSegmentCharacteristics(_users: any[]): Promise<SegmentCharacteristics> {
    // Implementation would calculate characteristics from user data
    return {
      averageAge: 35,
      averageLifetimeValue: 5000,
      averageOrderValue: 1000,
      mostPopularCategories: ['Electronics'],
      commonBehaviors: ['Regular purchases'],
      retentionRate: 75,
      conversionRate: 15
    };
  }

  private generateSegmentRecommendations(_characteristics: SegmentCharacteristics): string[] {
    return [
      'Implement targeted marketing campaigns',
      'Optimize product recommendations',
      'Enhance customer experience'
    ];
  }

  private generateSegmentId(): string {
    return `segment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private predictFutureValue(user: UserAnalytics): number {
    // Simple prediction based on current trends
    return user.lifetimeValue * 1.2; // 20% growth assumption
  }

  private determineValueSegment(value: number): 'high' | 'medium' | 'low' {
    if (value > 10000) return 'high';
    if (value > 3000) return 'medium';
    return 'low';
  }

  private generateCLVRecommendations(segment: string, _user: UserAnalytics): string[] {
    switch (segment) {
      case 'high':
        return ['Offer premium services', 'Provide dedicated support', 'Create exclusive experiences'];
      case 'medium':
        return ['Increase engagement', 'Upsell complementary products', 'Improve retention'];
      default:
        return ['Focus on activation', 'Provide onboarding support', 'Encourage first purchase'];
    }
  }

  private analyzeChurnFactors(user: UserAnalytics): ChurnFactor[] {
    const factors: ChurnFactor[] = [];

    if (user.engagementLevel === 'low' || user.engagementLevel === 'inactive') {
      factors.push({
        factor: 'Low Engagement',
        impact: 0.4,
        description: 'User shows minimal interaction with the platform'
      });
    }

    if (user.totalOrders === 0) {
      factors.push({
        factor: 'No Purchases',
        impact: 0.6,
        description: 'User has not made any purchases'
      });
    }

    const daysSinceLastActivity = Math.floor(
      (Date.now() - new Date(user.lastActivity).getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceLastActivity > 30) {
      factors.push({
        factor: 'Inactivity',
        impact: 0.3,
        description: `No activity for ${daysSinceLastActivity} days`
      });
    }

    return factors;
  }

  private generateChurnPreventionRecommendations(riskLevel: string, _factors: ChurnFactor[]): string[] {
    switch (riskLevel) {
      case 'high':
        return [
          'Send immediate re-engagement campaign',
          'Offer significant discount or incentive',
          'Provide personal customer service outreach'
        ];
      case 'medium':
        return [
          'Send targeted email campaign',
          'Offer personalized product recommendations',
          'Provide helpful content or tutorials'
        ];
      default:
        return [
          'Monitor engagement levels',
          'Send regular newsletters',
          'Provide excellent customer experience'
        ];
    }
  }

  private determineNextBestAction(riskLevel: string, user: UserAnalytics): string {
    if (riskLevel === 'high') {
      return 'Immediate personal outreach with special offer';
    }
    if (riskLevel === 'medium') {
      return 'Send personalized email with product recommendations';
    }
    if (user.totalOrders === 0) {
      return 'Send welcome series with first-purchase incentive';
    }
    return 'Continue regular engagement activities';
  }
}

export const userManagementService = UserManagementService.getInstance();
