// lib/services/analyticsService.ts
import { connectToDatabase } from '@/lib/dbconnect';
import { GroupOrder } from '@/models/GroupOrder';
import { User } from '@/models/User';
import { OrderFulfillment } from '@/models/OrderFulfillment';
import {
  DateRange,
  TimeSeriesDataPoint,
  RevenueAnalytics,
  UserAnalytics,
  OrderAnalytics
} from '@/types/analytics';

export class AnalyticsService {
  
  /**
   * Get comprehensive revenue analytics
   */
  async getRevenueAnalytics(dateRange: DateRange): Promise<RevenueAnalytics> {
    await connectToDatabase();

    try {
      // Total revenue and growth
      const revenueData = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end },
            status: { $in: ['completed', 'delivered'] }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$totalOrderValue' },
            orderCount: { $sum: 1 },
            averageOrderValue: { $avg: '$totalOrderValue' }
          }
        }
      ]);

      // Previous period for comparison
      const previousPeriod = this.getPreviousPeriod(dateRange);
      const previousRevenueData = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: previousPeriod.start, $lte: previousPeriod.end },
            status: { $in: ['completed', 'delivered'] }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$totalOrderValue' }
          }
        }
      ]);

      const currentRevenue = revenueData[0]?.totalRevenue || 0;
      const previousRevenue = previousRevenueData[0]?.totalRevenue || 0;
      const revenueGrowth = previousRevenue > 0 ? 
        ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;

      // Revenue by period (daily breakdown)
      const revenueByPeriod = await this.getRevenueTimeSeries(dateRange, 'day');

      // Revenue by category
      const revenueByCategory = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end },
            status: { $in: ['completed', 'delivered'] }
          }
        },
        { $unwind: '$items' },
        {
          $lookup: {
            from: 'products',
            localField: 'items.product',
            foreignField: '_id',
            as: 'productInfo'
          }
        },
        { $unwind: '$productInfo' },
        {
          $group: {
            _id: '$productInfo.category',
            revenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } }
          }
        },
        { $sort: { revenue: -1 } }
      ]);

      const totalCategoryRevenue = revenueByCategory.reduce((sum, cat) => sum + cat.revenue, 0);
      const categoryData = revenueByCategory.map(cat => ({
        category: cat._id || 'Uncategorized',
        revenue: cat.revenue,
        percentage: totalCategoryRevenue > 0 ? (cat.revenue / totalCategoryRevenue) * 100 : 0
      }));

      // Revenue by group
      const revenueByGroup = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end },
            status: { $in: ['completed', 'delivered'] }
          }
        },
        {
          $lookup: {
            from: 'groups',
            localField: 'groupId',
            foreignField: '_id',
            as: 'groupInfo'
          }
        },
        { $unwind: { path: '$groupInfo', preserveNullAndEmptyArrays: true } },
        {
          $group: {
            _id: '$groupId',
            groupName: { $first: '$groupInfo.name' },
            revenue: { $sum: '$totalOrderValue' },
            orderCount: { $sum: 1 },
            averageOrderValue: { $avg: '$totalOrderValue' }
          }
        },
        { $sort: { revenue: -1 } },
        { $limit: 10 }
      ]);

      // Top products
      const topProducts = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end },
            status: { $in: ['completed', 'delivered'] }
          }
        },
        { $unwind: '$items' },
        {
          $lookup: {
            from: 'products',
            localField: 'items.product',
            foreignField: '_id',
            as: 'productInfo'
          }
        },
        { $unwind: '$productInfo' },
        {
          $group: {
            _id: '$items.product',
            productName: { $first: '$productInfo.name' },
            revenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
            quantity: { $sum: '$items.quantity' },
            margin: { $avg: { $subtract: ['$items.price', '$productInfo.cost'] } }
          }
        },
        { $sort: { revenue: -1 } },
        { $limit: 10 }
      ]);

      // Monthly recurring revenue (simplified)
      const monthlyRecurring = currentRevenue / this.getDaysInPeriod(dateRange) * 30;

      // Seasonal trends (last 12 months)
      const seasonalTrends = await this.getSeasonalTrends();

      return {
        totalRevenue: currentRevenue,
        revenueGrowth,
        averageOrderValue: revenueData[0]?.averageOrderValue || 0,
        revenueByPeriod,
        revenueByCategory: categoryData,
        revenueByGroup: revenueByGroup.map(group => ({
          groupId: group._id?.toString() || '',
          groupName: group.groupName || 'Individual Orders',
          revenue: group.revenue,
          orderCount: group.orderCount,
          averageOrderValue: group.averageOrderValue
        })),
        topProducts: topProducts.map(product => ({
          productId: product._id.toString(),
          productName: product.productName,
          revenue: product.revenue,
          quantity: product.quantity,
          margin: product.margin || 0
        })),
        monthlyRecurring,
        seasonalTrends
      };
    } catch (error) {
      console.error('Error getting revenue analytics:', error);
      throw new Error('Failed to get revenue analytics');
    }
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(dateRange: DateRange): Promise<UserAnalytics> {
    await connectToDatabase();

    try {
      // Total and active users
      const userStats = await User.aggregate([
        {
          $facet: {
            total: [{ $count: "count" }],
            new: [
              { $match: { createdAt: { $gte: dateRange.start, $lte: dateRange.end } } },
              { $count: "count" }
            ],
            active: [
              { $match: { lastLoginAt: { $gte: dateRange.start, $lte: dateRange.end } } },
              { $count: "count" }
            ]
          }
        }
      ]);

      const totalUsers = userStats[0]?.total[0]?.count || 0;
      const newUsers = userStats[0]?.new[0]?.count || 0;
      const activeUsers = userStats[0]?.active[0]?.count || 0;

      // User growth rate
      const previousPeriod = this.getPreviousPeriod(dateRange);
      const previousNewUsers = await User.countDocuments({
        createdAt: { $gte: previousPeriod.start, $lte: previousPeriod.end }
      });
      const userGrowthRate = previousNewUsers > 0 ? 
        ((newUsers - previousNewUsers) / previousNewUsers) * 100 : 0;

      // User retention rate (simplified)
      const userRetentionRate = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;

      // Users by period
      const usersByPeriod = await this.getUserTimeSeries(dateRange, 'day');

      // Demographics (simplified)
      const userDemographics = {
        ageGroups: [
          { range: '18-25', count: Math.floor(totalUsers * 0.25), percentage: 25 },
          { range: '26-35', count: Math.floor(totalUsers * 0.35), percentage: 35 },
          { range: '36-45', count: Math.floor(totalUsers * 0.25), percentage: 25 },
          { range: '46+', count: Math.floor(totalUsers * 0.15), percentage: 15 }
        ],
        locations: [
          { city: 'Cape Town', count: Math.floor(totalUsers * 0.3), percentage: 30 },
          { city: 'Johannesburg', count: Math.floor(totalUsers * 0.4), percentage: 40 },
          { city: 'Durban', count: Math.floor(totalUsers * 0.2), percentage: 20 },
          { city: 'Other', count: Math.floor(totalUsers * 0.1), percentage: 10 }
        ],
        deviceTypes: [
          { type: 'Mobile', count: Math.floor(totalUsers * 0.7), percentage: 70 },
          { type: 'Desktop', count: Math.floor(totalUsers * 0.25), percentage: 25 },
          { type: 'Tablet', count: Math.floor(totalUsers * 0.05), percentage: 5 }
        ]
      };

      // User engagement (simplified)
      const userEngagement = {
        averageSessionDuration: 15.5, // minutes
        averagePageViews: 8.2,
        bounceRate: 35.5, // percentage
        returnVisitorRate: 65.0 // percentage
      };

      // Cohort analysis (simplified)
      const cohortAnalysis = await this.getCohortAnalysis();

      return {
        totalUsers,
        activeUsers,
        newUsers,
        userGrowthRate,
        userRetentionRate,
        usersByPeriod,
        userDemographics,
        userEngagement,
        cohortAnalysis
      };
    } catch (error) {
      console.error('Error getting user analytics:', error);
      throw new Error('Failed to get user analytics');
    }
  }

  /**
   * Get order analytics
   */
  async getOrderAnalytics(dateRange: DateRange): Promise<OrderAnalytics> {
    await connectToDatabase();

    try {
      // Order statistics
      const orderStats = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end }
          }
        },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            completedOrders: {
              $sum: { $cond: [{ $in: ['$status', ['completed', 'delivered']] }, 1, 0] }
            },
            cancelledOrders: {
              $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
            },
            averageOrderValue: { $avg: '$totalOrderValue' }
          }
        }
      ]);

      const stats = orderStats[0] || {
        totalOrders: 0,
        completedOrders: 0,
        cancelledOrders: 0,
        averageOrderValue: 0
      };

      const orderCompletionRate = stats.totalOrders > 0 ? 
        (stats.completedOrders / stats.totalOrders) * 100 : 0;

      // Orders by period
      const ordersByPeriod = await this.getOrderTimeSeries(dateRange, 'day');

      // Orders by status
      const ordersByStatus = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end }
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const statusData = ordersByStatus.map(status => ({
        status: status._id,
        count: status.count,
        percentage: stats.totalOrders > 0 ? (status.count / stats.totalOrders) * 100 : 0
      }));

      // Orders by payment method
      const ordersByPaymentMethod = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end }
          }
        },
        {
          $group: {
            _id: '$paymentMethod',
            count: { $sum: 1 },
            revenue: { $sum: '$totalOrderValue' }
          }
        }
      ]);

      const paymentMethodData = ordersByPaymentMethod.map(method => ({
        method: method._id || 'Unknown',
        count: method.count,
        revenue: method.revenue,
        percentage: stats.totalOrders > 0 ? (method.count / stats.totalOrders) * 100 : 0
      }));

      // Group order metrics
      const groupOrderStats = await GroupOrder.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end }
          }
        },
        {
          $group: {
            _id: null,
            totalGroupOrders: {
              $sum: { $cond: [{ $ne: ['$groupId', null] }, 1, 0] }
            },
            totalIndividualOrders: {
              $sum: { $cond: [{ $eq: ['$groupId', null] }, 1, 0] }
            },
            groupOrderValue: {
              $sum: { $cond: [{ $ne: ['$groupId', null] }, '$totalOrderValue', 0] }
            },
            individualOrderValue: {
              $sum: { $cond: [{ $eq: ['$groupId', null] }, '$totalOrderValue', 0] }
            },
            groupDiscountSavings: { $sum: '$discountAmount' }
          }
        }
      ]);

      const groupStats = groupOrderStats[0] || {};
      const averageGroupSize = groupStats.totalGroupOrders > 0 ? 
        await this.getAverageGroupSize(dateRange) : 0;

      // Fulfillment metrics
      const fulfillmentStats = await OrderFulfillment.aggregate([
        {
          $match: {
            createdAt: { $gte: dateRange.start, $lte: dateRange.end }
          }
        },
        {
          $group: {
            _id: null,
            averageFulfillmentTime: {
              $avg: {
                $cond: [
                  { $and: [{ $ne: ['$actualFulfillmentDate', null] }, { $ne: ['$createdAt', null] }] },
                  { $divide: [{ $subtract: ['$actualFulfillmentDate', '$createdAt'] }, 1000 * 60 * 60] },
                  null
                ]
              }
            },
            onTimeDeliveries: {
              $sum: {
                $cond: [
                  { 
                    $and: [
                      { $ne: ['$actualFulfillmentDate', null] },
                      { $ne: ['$estimatedFulfillmentDate', null] },
                      { $lte: ['$actualFulfillmentDate', '$estimatedFulfillmentDate'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            },
            totalDelivered: {
              $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
            },
            totalReturned: {
              $sum: { $cond: [{ $eq: ['$status', 'returned'] }, 1, 0] }
            }
          }
        }
      ]);

      const fulfillmentData = fulfillmentStats[0] || {};
      const onTimeDeliveryRate = fulfillmentData.totalDelivered > 0 ? 
        (fulfillmentData.onTimeDeliveries / fulfillmentData.totalDelivered) * 100 : 0;
      const returnRate = stats.completedOrders > 0 ? 
        (fulfillmentData.totalReturned / stats.completedOrders) * 100 : 0;

      return {
        totalOrders: stats.totalOrders,
        completedOrders: stats.completedOrders,
        cancelledOrders: stats.cancelledOrders,
        averageOrderValue: stats.averageOrderValue,
        orderCompletionRate,
        ordersByPeriod,
        ordersByStatus: statusData,
        ordersByPaymentMethod: paymentMethodData,
        groupOrderMetrics: {
          totalGroupOrders: groupStats.totalGroupOrders || 0,
          averageGroupSize,
          groupOrderValue: groupStats.groupOrderValue || 0,
          individualOrderValue: groupStats.individualOrderValue || 0,
          groupDiscountSavings: groupStats.groupDiscountSavings || 0
        },
        fulfillmentMetrics: {
          averageFulfillmentTime: fulfillmentData.averageFulfillmentTime || 0,
          onTimeDeliveryRate,
          shippingCosts: 0, // Would need shipping cost data
          returnRate
        }
      };
    } catch (error) {
      console.error('Error getting order analytics:', error);
      throw new Error('Failed to get order analytics');
    }
  }

  // Helper methods
  private getPreviousPeriod(dateRange: DateRange): DateRange {
    const duration = dateRange.end.getTime() - dateRange.start.getTime();
    return {
      start: new Date(dateRange.start.getTime() - duration),
      end: new Date(dateRange.start.getTime())
    };
  }

  private getDaysInPeriod(dateRange: DateRange): number {
    return Math.ceil((dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24));
  }

  private async getRevenueTimeSeries(_dateRange: DateRange, _period: string): Promise<TimeSeriesDataPoint[]> {
    // Implementation for revenue time series
    return [];
  }

  private async getUserTimeSeries(_dateRange: DateRange, _period: string): Promise<TimeSeriesDataPoint[]> {
    // Implementation for user time series
    return [];
  }

  private async getOrderTimeSeries(_dateRange: DateRange, _period: string): Promise<TimeSeriesDataPoint[]> {
    // Implementation for order time series
    return [];
  }

  private async getSeasonalTrends(): Promise<unknown[]> {
    // Implementation for seasonal trends
    return [];
  }

  private async getCohortAnalysis(): Promise<unknown[]> {
    // Implementation for cohort analysis
    return [];
  }

  private async getAverageGroupSize(_dateRange: DateRange): Promise<number> {
    // Implementation for average group size
    return 0;
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();
