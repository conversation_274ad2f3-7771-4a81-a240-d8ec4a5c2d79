// Real-time Group Communication Service
// Handles real-time messaging, activity feeds, and group collaboration

import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { StokvelGroup } from '@/models/StokvelGroup';

// Types for real-time group features
export interface GroupMessage {
  id: string;
  groupId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'product' | 'system';
  metadata?: {
    productId?: string;
    fileName?: string;
    fileUrl?: string;
    fileSize?: number;
    imageUrl?: string;
    productName?: string;
    productPrice?: number;
    productImage?: string;
  };
  reactions: MessageReaction[];
  threadId?: string;
  replyTo?: string;
  isEdited: boolean;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface MessageReaction {
  userId: string;
  userName: string;
  emoji: string;
  createdAt: Date;
}

export interface GroupActivity {
  id: string;
  groupId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  type: 'member_joined' | 'member_left' | 'product_added' | 'product_removed' | 'order_placed' | 'discount_achieved' | 'milestone_reached' | 'chat_message' | 'product_voted' | 'list_created' | 'challenge_completed';
  title: string;
  description: string;
  metadata?: {
    productId?: string;
    productName?: string;
    productImage?: string;
    quantity?: number;
    price?: number;
    discountTier?: string;
    milestone?: string;
    challengeId?: string;
    listId?: string;
    orderId?: string;
  };
  isPublic: boolean;
  createdAt: Date;
}

export interface GroupNotification {
  id: string;
  groupId: string;
  userId: string;
  type: 'mention' | 'reply' | 'product_suggestion' | 'discount_alert' | 'order_update' | 'member_activity' | 'challenge_update' | 'milestone_achieved';
  title: string;
  message: string;
  actionUrl?: string;
  metadata?: Record<string, unknown>;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: Date;
  expiresAt?: Date;
}

export interface CollaborativeList {
  id: string;
  groupId: string;
  name: string;
  description: string;
  createdBy: string;
  createdByName: string;
  items: CollaborativeListItem[];
  collaborators: string[];
  isPublic: boolean;
  tags: string[];
  status: 'active' | 'completed' | 'archived';
  targetDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CollaborativeListItem {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  productPrice: number;
  quantity: number;
  priority: 'low' | 'medium' | 'high';
  addedBy: string;
  addedByName: string;
  votes: ProductVote[];
  notes: string;
  status: 'pending' | 'approved' | 'rejected' | 'purchased';
  addedAt: Date;
}

export interface ProductVote {
  userId: string;
  userName: string;
  vote: 'up' | 'down';
  reason?: string;
  createdAt: Date;
}

export interface GroupChallenge {
  id: string;
  groupId: string;
  title: string;
  description: string;
  type: 'savings_target' | 'bulk_purchase' | 'member_growth' | 'activity_streak' | 'product_discovery';
  targetValue: number;
  currentValue: number;
  unit: string;
  startDate: Date;
  endDate: Date;
  reward: {
    type: 'discount' | 'points' | 'badge' | 'feature_unlock';
    value: number;
    description: string;
  };
  participants: string[];
  status: 'active' | 'completed' | 'expired' | 'cancelled';
  createdBy: string;
  createdAt: Date;
}

export interface GroupMember {
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: Date;
  lastActive: Date;
  contributionScore: number;
  totalSavings: number;
  totalOrders: number;
  badges: string[];
  permissions: string[];
  isOnline: boolean;
}

class RealTimeGroupService {
  private static instance: RealTimeGroupService;
  private groupMessages: Map<string, GroupMessage[]> = new Map();
  private groupActivities: Map<string, GroupActivity[]> = new Map();
  private groupNotifications: Map<string, GroupNotification[]> = new Map();
  private collaborativeLists: Map<string, CollaborativeList[]> = new Map();
  private groupChallenges: Map<string, GroupChallenge[]> = new Map();
  private onlineMembers: Map<string, Set<string>> = new Map();

  public static getInstance(): RealTimeGroupService {
    if (!RealTimeGroupService.instance) {
      RealTimeGroupService.instance = new RealTimeGroupService();
    }
    return RealTimeGroupService.instance;
  }

  // Group Chat Management
  public async sendMessage(groupId: string, senderId: string, content: string, type: GroupMessage['type'] = 'text', metadata?: GroupMessage['metadata']): Promise<GroupMessage> {
    await connectToDatabase();

    const sender = await User.findById(senderId);
    if (!sender) {
      throw new Error('Sender not found');
    }

    const message: GroupMessage = {
      id: this.generateId('msg'),
      groupId,
      senderId,
      senderName: sender.name,
      senderAvatar: sender.avatar,
      content,
      type,
      metadata,
      reactions: [],
      isEdited: false,
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Store message
    if (!this.groupMessages.has(groupId)) {
      this.groupMessages.set(groupId, []);
    }
    this.groupMessages.get(groupId)!.push(message);

    // Create activity for chat message
    await this.createActivity(groupId, senderId, 'chat_message', 'Sent a message', content.substring(0, 100), {}, false);

    // Notify group members
    await this.notifyGroupMembers(groupId, senderId, 'member_activity', 'New message', `${sender.name} sent a message`, `/groups/${groupId}/chat`);

    return message;
  }

  public async getGroupMessages(groupId: string, limit: number = 50, offset: number = 0): Promise<GroupMessage[]> {
    const messages = this.groupMessages.get(groupId) || [];
    return messages
      .filter(msg => !msg.isDeleted)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(offset, offset + limit);
  }

  public async addMessageReaction(messageId: string, userId: string, emoji: string): Promise<boolean> {
    await connectToDatabase();

    const user = await User.findById(userId);
    if (!user) return false;

    // Find message across all groups
    for (const [groupId, messages] of this.groupMessages.entries()) {
      const message = messages.find(m => m.id === messageId);
      if (message) {
        // Remove existing reaction from this user
        message.reactions = message.reactions.filter(r => r.userId !== userId);
        
        // Add new reaction
        message.reactions.push({
          userId,
          userName: user.name,
          emoji,
          createdAt: new Date()
        });

        message.updatedAt = new Date();
        return true;
      }
    }

    return false;
  }

  // Activity Feed Management
  public async createActivity(
    groupId: string,
    userId: string,
    type: GroupActivity['type'],
    title: string,
    description: string,
    metadata: GroupActivity['metadata'] = {},
    isPublic: boolean = true
  ): Promise<GroupActivity> {
    await connectToDatabase();

    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const activity: GroupActivity = {
      id: this.generateId('act'),
      groupId,
      userId,
      userName: user.name,
      userAvatar: user.avatar,
      type,
      title,
      description,
      metadata,
      isPublic,
      createdAt: new Date()
    };

    // Store activity
    if (!this.groupActivities.has(groupId)) {
      this.groupActivities.set(groupId, []);
    }
    this.groupActivities.get(groupId)!.push(activity);

    return activity;
  }

  public async getGroupActivities(groupId: string, limit: number = 20, offset: number = 0): Promise<GroupActivity[]> {
    const activities = this.groupActivities.get(groupId) || [];
    return activities
      .filter(activity => activity.isPublic)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(offset, offset + limit);
  }

  // Notification Management
  public async notifyGroupMembers(
    groupId: string,
    excludeUserId: string,
    type: GroupNotification['type'],
    title: string,
    message: string,
    actionUrl?: string,
    metadata?: Record<string, unknown>,
    priority: GroupNotification['priority'] = 'medium'
  ): Promise<void> {
    await connectToDatabase();

    const group = await StokvelGroup.findById(groupId).populate('members');
    if (!group) return;

    const notifications: GroupNotification[] = group.members
      .filter((member: any) => member._id.toString() !== excludeUserId)
      .map((member: any) => ({
        id: this.generateId('notif'),
        groupId,
        userId: member._id.toString(),
        type,
        title,
        message,
        actionUrl,
        metadata,
        isRead: false,
        priority,
        createdAt: new Date()
      }));

    // Store notifications
    notifications.forEach(notification => {
      if (!this.groupNotifications.has(notification.userId)) {
        this.groupNotifications.set(notification.userId, []);
      }
      this.groupNotifications.get(notification.userId)!.push(notification);
    });
  }

  public async getUserNotifications(userId: string, limit: number = 20): Promise<GroupNotification[]> {
    const notifications = this.groupNotifications.get(userId) || [];
    return notifications
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }

  public async markNotificationAsRead(notificationId: string, userId: string): Promise<boolean> {
    const notifications = this.groupNotifications.get(userId) || [];
    const notification = notifications.find(n => n.id === notificationId);
    
    if (notification) {
      notification.isRead = true;
      return true;
    }
    
    return false;
  }

  // Collaborative Lists Management
  public async createCollaborativeList(
    groupId: string,
    createdBy: string,
    name: string,
    description: string,
    isPublic: boolean = true
  ): Promise<CollaborativeList> {
    await connectToDatabase();

    const user = await User.findById(createdBy);
    if (!user) {
      throw new Error('User not found');
    }

    const list: CollaborativeList = {
      id: this.generateId('list'),
      groupId,
      name,
      description,
      createdBy,
      createdByName: user.name,
      items: [],
      collaborators: [createdBy],
      isPublic,
      tags: [],
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Store list
    if (!this.collaborativeLists.has(groupId)) {
      this.collaborativeLists.set(groupId, []);
    }
    this.collaborativeLists.get(groupId)!.push(list);

    // Create activity
    await this.createActivity(groupId, createdBy, 'list_created', 'Created a shopping list', `Created "${name}" list`, { listId: list.id });

    return list;
  }

  public async addItemToList(
    listId: string,
    productId: string,
    productName: string,
    productImage: string,
    productPrice: number,
    quantity: number,
    addedBy: string,
    notes: string = '',
    priority: CollaborativeListItem['priority'] = 'medium'
  ): Promise<boolean> {
    await connectToDatabase();

    const user = await User.findById(addedBy);
    if (!user) return false;

    // Find list across all groups
    for (const [groupId, lists] of this.collaborativeLists.entries()) {
      const list = lists.find(l => l.id === listId);
      if (list) {
        const item: CollaborativeListItem = {
          id: this.generateId('item'),
          productId,
          productName,
          productImage,
          productPrice,
          quantity,
          priority,
          addedBy,
          addedByName: user.name,
          votes: [],
          notes,
          status: 'pending',
          addedAt: new Date()
        };

        list.items.push(item);
        list.updatedAt = new Date();

        // Create activity
        await this.createActivity(groupId, addedBy, 'product_added', 'Added item to list', `Added ${productName} to ${list.name}`, { 
          productId, 
          productName, 
          listId 
        });

        return true;
      }
    }

    return false;
  }

  public async voteOnListItem(listId: string, itemId: string, userId: string, vote: 'up' | 'down', reason?: string): Promise<boolean> {
    await connectToDatabase();

    const user = await User.findById(userId);
    if (!user) return false;

    // Find list and item
    for (const lists of this.collaborativeLists.values()) {
      const list = lists.find(l => l.id === listId);
      if (list) {
        const item = list.items.find(i => i.id === itemId);
        if (item) {
          // Remove existing vote from this user
          item.votes = item.votes.filter(v => v.userId !== userId);
          
          // Add new vote
          item.votes.push({
            userId,
            userName: user.name,
            vote,
            reason,
            createdAt: new Date()
          });

          list.updatedAt = new Date();
          return true;
        }
      }
    }

    return false;
  }

  public async getGroupLists(groupId: string): Promise<CollaborativeList[]> {
    return this.collaborativeLists.get(groupId) || [];
  }

  // Member Management
  public async setMemberOnline(groupId: string, userId: string): Promise<void> {
    if (!this.onlineMembers.has(groupId)) {
      this.onlineMembers.set(groupId, new Set());
    }
    this.onlineMembers.get(groupId)!.add(userId);
  }

  public async setMemberOffline(groupId: string, userId: string): Promise<void> {
    if (this.onlineMembers.has(groupId)) {
      this.onlineMembers.get(groupId)!.delete(userId);
    }
  }

  public async getOnlineMembers(groupId: string): Promise<string[]> {
    return Array.from(this.onlineMembers.get(groupId) || []);
  }

  public async getGroupMembers(groupId: string): Promise<GroupMember[]> {
    await connectToDatabase();

    const group = await StokvelGroup.findById(groupId).populate('members');
    if (!group) return [];

    const onlineUserIds = await this.getOnlineMembers(groupId);

    return group.members.map((member: any) => ({
      userId: member._id.toString(),
      userName: member.name,
      userEmail: member.email,
      userAvatar: member.avatar,
      role: member._id.toString() === group.admin.toString() ? 'admin' : 'member',
      joinedAt: member.createdAt || new Date(),
      lastActive: new Date(),
      contributionScore: 0, // Calculate based on activities
      totalSavings: 0, // Calculate from orders
      totalOrders: 0, // Calculate from orders
      badges: [],
      permissions: [],
      isOnline: onlineUserIds.includes(member._id.toString())
    }));
  }

  // Utility methods
  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API methods for external use
  public async getGroupChatSummary(groupId: string): Promise<{
    totalMessages: number;
    activeMembers: number;
    lastActivity: Date | null;
    onlineMembers: number;
  }> {
    const messages = this.groupMessages.get(groupId) || [];
    const activities = this.groupActivities.get(groupId) || [];
    const onlineMembers = await this.getOnlineMembers(groupId);

    const lastActivity = [...messages, ...activities]
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0]?.createdAt || null;

    const activeMembers = new Set([
      ...messages.map(m => m.senderId),
      ...activities.map(a => a.userId)
    ]).size;

    return {
      totalMessages: messages.length,
      activeMembers,
      lastActivity,
      onlineMembers: onlineMembers.length
    };
  }
}

export const realTimeGroupService = RealTimeGroupService.getInstance();
