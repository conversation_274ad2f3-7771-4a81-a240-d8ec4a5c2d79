// lib/services/paymentProcessor.ts
import { Payment } from '@/models/Payment';
import { GroupOrder } from '@/models/GroupOrder';
import { 
  PaymentData, 
  PaymentResult, 
  PaymentStatus, 
  PaymentValidationResult,
  PaymentMethodType,
  PaymentProvider,
  IPayment
} from '@/types/payment';
import { connectToDatabase } from '@/lib/dbconnect';
import mongoose from 'mongoose';

// Payment processor configuration
interface PaymentConfig {
  stripe?: {
    publicKey: string;
    secretKey: string;
    webhookSecret: string;
  };
  payfast?: {
    merchantId: string;
    merchantKey: string;
    passphrase: string;
    sandbox: boolean;
  };
  yoco?: {
    secretKey: string;
    publicKey: string;
  };
}

export class PaymentProcessor {
  private config: PaymentConfig;

  constructor(config: PaymentConfig) {
    this.config = config;
  }

  /**
   * Process a payment
   */
  async processPayment(paymentData: PaymentData): Promise<PaymentResult> {
    await connectToDatabase();

    try {
      // Validate payment data
      const validation = await this.validatePaymentData(paymentData);
      if (!validation.isValid) {
        return {
          success: false,
          paymentId: '',
          status: 'failed',
          amount: paymentData.amount,
          currency: paymentData.currency,
          error: validation.errors.join(', ')
        };
      }

      // Create payment record
      const payment = await this.createPaymentRecord(paymentData);

      // Process payment based on method
      let result: PaymentResult;
      switch (paymentData.paymentMethod) {
        case 'credit_card':
          result = await this.processCreditCardPayment(paymentData, payment);
          break;
        case 'bank_transfer':
          result = await this.processBankTransferPayment(paymentData, payment);
          break;
        case 'eft':
          result = await this.processEFTPayment(paymentData, payment);
          break;
        default:
          result = {
            success: false,
            paymentId: payment._id.toString(),
            status: 'failed',
            amount: paymentData.amount,
            currency: paymentData.currency,
            error: 'Unsupported payment method'
          };
      }

      // Update payment record with result
      await this.updatePaymentRecord(payment._id.toString(), result);

      return result;
    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        paymentId: '',
        status: 'failed',
        amount: paymentData.amount,
        currency: paymentData.currency,
        error: 'Payment processing failed'
      };
    }
  }

  /**
   * Validate payment data
   */
  async validatePaymentData(paymentData: PaymentData): Promise<PaymentValidationResult> {
    const errors: string[] = [];

    // Basic validation
    if (!paymentData.orderId) errors.push('Order ID is required');
    if (!paymentData.userId) errors.push('User ID is required');
    if (!paymentData.amount || paymentData.amount <= 0) errors.push('Valid amount is required');
    if (!paymentData.currency) errors.push('Currency is required');
    if (!paymentData.paymentMethod) errors.push('Payment method is required');

    // Validate order exists
    if (paymentData.orderId) {
      const order = await GroupOrder.findById(paymentData.orderId);
      if (!order) {
        errors.push('Order not found');
      }
    }

    // Payment method specific validation
    switch (paymentData.paymentMethod) {
      case 'credit_card':
        if (!paymentData.creditCard) {
          errors.push('Credit card information is required');
        } else {
          if (!paymentData.creditCard.cardNumber) errors.push('Card number is required');
          if (!paymentData.creditCard.expiryMonth) errors.push('Expiry month is required');
          if (!paymentData.creditCard.expiryYear) errors.push('Expiry year is required');
          if (!paymentData.creditCard.cvv) errors.push('CVV is required');
          if (!paymentData.creditCard.cardholderName) errors.push('Cardholder name is required');
        }
        break;
      case 'bank_transfer':
        if (!paymentData.bankTransfer) {
          errors.push('Bank transfer information is required');
        } else {
          if (!paymentData.bankTransfer.bankName) errors.push('Bank name is required');
          if (!paymentData.bankTransfer.accountNumber) errors.push('Account number is required');
          if (!paymentData.bankTransfer.accountHolderName) errors.push('Account holder name is required');
        }
        break;
      case 'eft':
        if (!paymentData.eft) {
          errors.push('EFT information is required');
        } else {
          if (!paymentData.eft.bankName) errors.push('Bank name is required');
          if (!paymentData.eft.accountNumber) errors.push('Account number is required');
          if (!paymentData.eft.branchCode) errors.push('Branch code is required');
          if (!paymentData.eft.accountHolderName) errors.push('Account holder name is required');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Create payment record in database
   */
  private async createPaymentRecord(paymentData: PaymentData): Promise<IPayment> {
    const payment = new Payment({
      orderId: new mongoose.Types.ObjectId(paymentData.orderId),
      userId: new mongoose.Types.ObjectId(paymentData.userId),
      amount: paymentData.amount,
      currency: paymentData.currency,
      status: 'pending',
      paymentMethod: paymentData.paymentMethod,
      provider: paymentData.provider,
      description: paymentData.description,
      metadata: paymentData.metadata
    });

    return await payment.save();
  }

  /**
   * Update payment record with processing result
   */
  private async updatePaymentRecord(paymentId: string, result: PaymentResult): Promise<void> {
    const updateData: any = {
      status: result.status,
      transactionId: result.transactionId,
      processingFee: result.processingFee,
      netAmount: result.amount - (result.processingFee || 0)
    };

    if (result.error) {
      updateData.failureReason = result.error;
    }

    if (result.status === 'completed') {
      updateData.processedAt = new Date();
    }

    await Payment.findByIdAndUpdate(paymentId, updateData);
  }

  /**
   * Process credit card payment
   */
  private async processCreditCardPayment(paymentData: PaymentData, payment: IPayment): Promise<PaymentResult> {
    // For now, simulate payment processing
    // In production, integrate with actual payment provider (Stripe, PayFast, etc.)
    
    try {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate success/failure (90% success rate for demo)
      const isSuccess = Math.random() > 0.1;

      if (isSuccess) {
        return {
          success: true,
          paymentId: payment._id.toString(),
          transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          status: 'completed',
          amount: paymentData.amount,
          currency: paymentData.currency,
          processingFee: paymentData.amount * 0.029, // 2.9% processing fee
          message: 'Payment processed successfully'
        };
      } else {
        return {
          success: false,
          paymentId: payment._id.toString(),
          status: 'failed',
          amount: paymentData.amount,
          currency: paymentData.currency,
          error: 'Card declined'
        };
      }
    } catch (error) {
      return {
        success: false,
        paymentId: payment._id.toString(),
        status: 'failed',
        amount: paymentData.amount,
        currency: paymentData.currency,
        error: 'Payment processing failed'
      };
    }
  }

  /**
   * Process bank transfer payment
   */
  private async processBankTransferPayment(paymentData: PaymentData, payment: IPayment): Promise<PaymentResult> {
    // Bank transfers typically require manual verification
    return {
      success: true,
      paymentId: payment._id.toString(),
      transactionId: `bt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'pending',
      amount: paymentData.amount,
      currency: paymentData.currency,
      message: 'Bank transfer initiated. Please allow 1-3 business days for processing.',
      requiresAction: true,
      actionType: 'redirect',
      actionData: {
        instructions: 'Please transfer the amount to the provided bank details and use the reference number.',
        bankDetails: {
          bankName: 'StockvelMarket Bank',
          accountNumber: '**********',
          branchCode: '123456',
          reference: `REF_${payment._id.toString().slice(-8)}`
        }
      }
    };
  }

  /**
   * Process EFT payment
   */
  private async processEFTPayment(paymentData: PaymentData, payment: IPayment): Promise<PaymentResult> {
    // EFT payments are similar to bank transfers but typically faster
    return {
      success: true,
      paymentId: payment._id.toString(),
      transactionId: `eft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'processing',
      amount: paymentData.amount,
      currency: paymentData.currency,
      message: 'EFT payment is being processed. You will receive confirmation shortly.',
      estimatedSettlement: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    };
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(paymentId: string): Promise<IPayment | null> {
    await connectToDatabase();
    return await Payment.findById(paymentId)
      .populate('orderId')
      .populate('userId', 'name email');
  }

  /**
   * Verify payment with provider
   */
  async verifyPayment(paymentId: string): Promise<boolean> {
    const payment = await this.getPaymentStatus(paymentId);
    if (!payment) return false;

    // In production, verify with actual payment provider
    // For now, return true if payment is completed
    return payment.status === 'completed';
  }

  /**
   * Process refund
   */
  async processRefund(paymentId: string, amount?: number, reason?: string): Promise<PaymentResult> {
    await connectToDatabase();

    const payment = await Payment.findById(paymentId);
    if (!payment) {
      return {
        success: false,
        paymentId,
        status: 'failed',
        amount: 0,
        currency: 'ZAR',
        error: 'Payment not found'
      };
    }

    if (payment.status !== 'completed') {
      return {
        success: false,
        paymentId,
        status: 'failed',
        amount: payment.amount,
        currency: payment.currency,
        error: 'Cannot refund incomplete payment'
      };
    }

    const refundAmount = amount || payment.amount;
    
    // Update payment record
    await Payment.findByIdAndUpdate(paymentId, {
      status: refundAmount >= payment.amount ? 'refunded' : 'partially_refunded',
      refundAmount: (payment.refundAmount || 0) + refundAmount,
      refundReason: reason
    });

    return {
      success: true,
      paymentId,
      transactionId: `refund_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: refundAmount >= payment.amount ? 'refunded' : 'partially_refunded',
      amount: refundAmount,
      currency: payment.currency,
      message: 'Refund processed successfully'
    };
  }
}
