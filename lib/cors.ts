

// lib/cors.ts

// Define Allowed Origins (Replace with your actual frontend domain)
const allowedOrigins = ['https://stockvel-market.vercel.app'];

// Utility function to get CORS headers based on the request origin
export const getCorsHeaders = (origin: string | null): Record<string, string> => {
  if (origin && allowedOrigins.includes(origin)) {
    return {
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Client-Type',
      'Access-Control-Allow-Credentials': 'true', // Enable credentials
    };
  }
  // Default CORS headers if origin is not allowed
  return {
    'Access-Control-Allow-Origin': allowedOrigins[0],
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Client-Type',
    'Access-Control-Allow-Credentials': 'true', // Enable credentials
  };
};
