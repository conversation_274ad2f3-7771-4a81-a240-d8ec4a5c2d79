"use client"

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

export interface ProductCategory {
  _id: string;
  name: string;
  description: string;
  product_count: number;
  is_active: boolean;
  parent_category?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UpdateProductCategoryInput {
  id: string;
  updateData: Partial<Omit<ProductCategory, "_id" | "createdAt" | "updatedAt">>
}

export async function getAllProductCategories(): Promise<ProductCategory[]> {
  const response = await fetch("/api/product-categories/get-all", { method: "GET" })
  if (!response.ok) {
    throw new Error("Failed to fetch Product Categories")
  }
  return response.json()
}

export async function updateProductCategory(input: UpdateProductCategoryInput): Promise<ProductCategory> {
  const response = await fetch("/api/product-categories/update", {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(input),
  })
  if (!response.ok) {
    throw new Error("Failed to update Product Category")
  }
  return response.json()
}

export async function deleteProductCategory(id: string): Promise<void> {
  const response = await fetch("/api/product-categories/delete", {
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ id }),
  })
  if (!response.ok) {
    throw new Error("Failed to delete Product Category")
  }
}

export function useGetAllProductCategories() {
  return useQuery({
    queryKey: ['productCategories'],
    queryFn: getAllProductCategories,
  })
}

export function useUpdateProductCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateProductCategory,
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['productCategories'] });
    },
  })
}

export function useDeleteProductCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteProductCategory,
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['productCategories'] });
    },
  })
}
