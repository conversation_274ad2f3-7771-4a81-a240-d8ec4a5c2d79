// // lib/productBackendUtilities.ts
// import { Product, IProduct } from '@/models/Product';
// import mongoose from 'mongoose';



// interface PaginatedProducts {
//   products: IProduct[];
//   totalPages: number;
//   currentPage: number;
//   totalProducts: number;
// }



// /**
//  * CREATE a new Product
//  */
// export async function createProduct(
//   name: string,
//   description: string,
//   price: number,
//   category: mongoose.Types.ObjectId,
//   stock: number,
//   image: string
// ): Promise<IProduct> {
//   console.log('[DEBUG] In createProduct, about to save with data:', {
//     name,
//     description,
//     price,
//     category,
//     stock,
//     image,
//   });
//   const newProduct = new Product({
//     name,
//     description,
//     price,
//     category,
//     stock,
//     image,
//   });

//   // Save and debug what we actually saved
//   const savedProduct = await newProduct.save();
//   console.log('[DEBUG] createProduct - saved document:', savedProduct);
//   return savedProduct;
// }

// /**
//  * READ: Get all Products
//  */
// // export async function getAllProducts(): Promise<IProduct[]> {
// //   return Product.find().populate('category').exec();
// // }
// // In lib/productBackendUtilities.ts
// // export async function getAllProducts(): Promise<IProduct[]> {
// //   // Return just the plain product documents with category as an ID string
// //   return Product.find().exec();
// // }

// export async function getAllProducts(): Promise<IProduct[]> {
//   return Product.find()
//     .populate({
//       path: 'category',       // Populate the category field
//       select: 'name _id',      // Only include the category name and _id
//     })
//     .exec();
// }


// /**
//  * READ: Get a single Product by its ID
//  */
// export async function getProductById(id: string): Promise<IProduct | null> {
//   if (!mongoose.Types.ObjectId.isValid(id)) return null;
//   return Product.findById(id).populate('category').exec();
// }

// /**
//  * UPDATE a Product by ID
//  */
// export async function updateProduct(
//   id: string,
//   updateData: Partial<IProduct>
// ): Promise<IProduct | null> {
//   if (!mongoose.Types.ObjectId.isValid(id)) return null;
//   return Product.findByIdAndUpdate(id, updateData, { new: true }).populate('category').exec();
// }

// /**
//  * DELETE a Product by ID
//  */
// export async function deleteProduct(id: string): Promise<IProduct | null> {
//   if (!mongoose.Types.ObjectId.isValid(id)) return null;
//   return Product.findByIdAndDelete(id).exec();
// }



// /**
//  * Get all products with pagination and optional category filtering
//  */
// export async function getAllProductsPaginated(
//   page: number = 1,
//   limit: number = 10,
//   category?: string
// ): Promise<PaginatedProducts> {
//   // Calculate skip for pagination
//   const skip = (page - 1) * limit;

//   // Build the query object
//   const query: any = {};
//   if (category) {
//     query.category = category;
//   }

//   // Get total product count (used for pagination metadata)
//   const totalProducts = await Product.countDocuments(query);

//   // Fetch products with pagination and optional category filter
//   const products = await Product.find(query)
//     .populate({
//       path: 'category',
//       select: 'name _id',
//     })
//     .skip(skip)
//     .limit(limit)
//     .exec();

//   // Calculate total pages
//   const totalPages = Math.ceil(totalProducts / limit);

//   return {
//     products,
//     totalPages,
//     currentPage: page,
//     totalProducts,
//   };
// }



import { Product, IProduct } from "@/models/Product";
import mongoose, { FilterQuery } from "mongoose";

interface PaginatedProducts {
  products: IProduct[];
  totalPages: number;
  currentPage: number;
  totalProducts: number;
}

/**
 * CREATE a new Product
 */
export async function createProduct(
  name: string,
  description: string,
  price: number,
  category: mongoose.Types.ObjectId,
  stock: number,
  image: string
): Promise<IProduct> {
  const newProduct = new Product({
    name,
    description,
    price,
    category,
    stock,
    image,
  });
  return await newProduct.save();
}

/**
 * READ: Get all Products
 */
export async function getAllProducts(): Promise<IProduct[]> {
  return Product.find()
    .populate({
      path: "category", // Populate the category field
      select: "name _id", // Only include the category name and _id
    })
    .exec();
}

/**
 * READ: Get a single Product by its ID
 */
export async function getProductById(id: string): Promise<IProduct | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Product.findById(id).populate("category").exec();
}

/**
 * UPDATE a Product by ID
 */
export async function updateProduct(
  id: string,
  updateData: Partial<IProduct>
): Promise<IProduct | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Product.findByIdAndUpdate(id, updateData, { new: true })
    .populate("category")
    .exec();
}

/**
 * DELETE a Product by ID
 */
export async function deleteProduct(id: string): Promise<IProduct | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return Product.findByIdAndDelete(id).exec();
}

/**
 * Get all products with pagination and optional category filtering
 */
export async function getAllProductsPaginated(
  page: number = 1,
  limit: number = 10,
  category?: string
): Promise<PaginatedProducts> {
  // Calculate skip for pagination
  const skip = (page - 1) * limit;

  // Build the query object with precise type
  const query: FilterQuery<IProduct> = category
    ? { category: new mongoose.Types.ObjectId(category) }
    : {};

  // Get total product count (used for pagination metadata)
  const totalProducts = await Product.countDocuments(query);

  // Fetch products with pagination and optional category filter
  const products = await Product.find(query)
    .populate({
      path: "category",
      select: "name _id",
    })
    .skip(skip)
    .limit(limit)
    .exec();

  // Calculate total pages
  const totalPages = Math.ceil(totalProducts / limit);

  return {
    products,
    totalPages,
    currentPage: page,
    totalProducts,
  };
}
