// lib/backendSubscriptionUtilities.ts
import mongoose from "mongoose"
import { YearEndBundle, type IYearEndBundle } from "@/models/YearEndBundle"
import { GrocerySchoolBundle, type IGrocerySchoolBundle } from "@/models/GrocerySchoolBundle"
import { MonthlyGroceries, type IMonthlyGroceries } from "@/models/MonthlyGroceries"
import { FuneralBenefits, type IFuneralBenefits } from "@/models/FuneralBenefits"

// YearEndBundle Utilities

export async function createYearEndBundle(
  userId: mongoose.Types.ObjectId,
  startDate: Date,
  endDate: Date,
  monthlyContribution: number,
  totalSavingsGoal: number,
  name: string,
  description: string,
  price: number,
  bundleSize: "basic" | "standard" | "premium",
  itemList: string[],
  deliveryMonth: number,
): Promise<IYearEndBundle> {
  const newBundle = new YearEndBundle({
    userId,
    name,
    description,
    price,
    monthlyContribution,
    status: "pending",
    startDate,
    endDate,
    bundleSize,
    itemList,
    deliveryMonth,
    totalSavingsGoal,
  })
  return await newBundle.save()
}

export async function getAllYearEndBundles(): Promise<IYearEndBundle[]> {
  return YearEndBundle.find().exec()
}

export async function getYearEndBundleById(id: string): Promise<IYearEndBundle | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return YearEndBundle.findById(id).exec()
}

export async function getYearEndBundlesByUserId(userId: string): Promise<IYearEndBundle[]> {
  if (!mongoose.Types.ObjectId.isValid(userId)) return []
  return YearEndBundle.find({ userId: new mongoose.Types.ObjectId(userId) }).exec()
}

export async function updateYearEndBundle(
  id: string,
  updateData: Partial<IYearEndBundle>,
): Promise<IYearEndBundle | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return YearEndBundle.findByIdAndUpdate(id, updateData, { new: true }).exec()
}

export async function deleteYearEndBundle(id: string): Promise<IYearEndBundle | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return YearEndBundle.findByIdAndDelete(id).exec()
}

// GrocerySchoolBundle Utilities

export async function createGrocerySchoolBundle(
  userId: mongoose.Types.ObjectId,
  startDate: Date,
  endDate: Date,
  monthlyContribution: number,
  groceryAllocation: number,
  schoolSuppliesAllocation: number,
): Promise<IGrocerySchoolBundle> {
  const newBundle = new GrocerySchoolBundle({
    userId,
    startDate,
    endDate,
    monthlyContribution,
    groceryAllocation,
    schoolSuppliesAllocation,
  })
  return await newBundle.save()
}

export async function getAllGrocerySchoolBundles(): Promise<IGrocerySchoolBundle[]> {
  return GrocerySchoolBundle.find().exec()
}

export async function getGrocerySchoolBundleById(id: string): Promise<IGrocerySchoolBundle | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return GrocerySchoolBundle.findById(id).exec()
}

export async function getGrocerySchoolBundlesByUserId(userId: string): Promise<IGrocerySchoolBundle[]> {
  if (!mongoose.Types.ObjectId.isValid(userId)) return []
  return GrocerySchoolBundle.find({ userId: new mongoose.Types.ObjectId(userId) }).exec()
}

export async function updateGrocerySchoolBundle(
  id: string,
  updateData: Partial<IGrocerySchoolBundle>,
): Promise<IGrocerySchoolBundle | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return GrocerySchoolBundle.findByIdAndUpdate(id, updateData, { new: true }).exec()
}

export async function deleteGrocerySchoolBundle(id: string): Promise<IGrocerySchoolBundle | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return GrocerySchoolBundle.findByIdAndDelete(id).exec()
}

// MonthlyGroceries Utilities

export async function createMonthlyGroceries(
  userId: mongoose.Types.ObjectId,
  startDate: Date,
  endDate: Date,
  monthlyContribution: number,
  groceryList: string[],
): Promise<IMonthlyGroceries> {
  const newSubscription = new MonthlyGroceries({
    userId,
    startDate,
    endDate,
    monthlyContribution,
    groceryList,
  })
  return await newSubscription.save()
}

export async function getAllMonthlyGroceries(): Promise<IMonthlyGroceries[]> {
  return MonthlyGroceries.find().exec()
}

export async function getMonthlyGroceriesById(id: string): Promise<IMonthlyGroceries | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return MonthlyGroceries.findById(id).exec()
}

export async function getMonthlyGroceriesByUserId(userId: string): Promise<IMonthlyGroceries[]> {
  if (!mongoose.Types.ObjectId.isValid(userId)) return []
  return MonthlyGroceries.find({ userId: new mongoose.Types.ObjectId(userId) }).exec()
}

export async function updateMonthlyGroceries(
  id: string,
  updateData: Partial<IMonthlyGroceries>,
): Promise<IMonthlyGroceries | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return MonthlyGroceries.findByIdAndUpdate(id, updateData, { new: true }).exec()
}

export async function deleteMonthlyGroceries(id: string): Promise<IMonthlyGroceries | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return MonthlyGroceries.findByIdAndDelete(id).exec()
}

// FuneralBenefits Utilities

export async function createFuneralBenefits(
  userId: mongoose.Types.ObjectId,
  startDate: Date,
  endDate: Date,
  monthlyContribution: number,
  coverageAmount: number,
  beneficiaries: string[],
): Promise<IFuneralBenefits> {
  const newSubscription = new FuneralBenefits({
    userId,
    startDate,
    endDate,
    monthlyContribution,
    coverageAmount,
    beneficiaries,
  })
  return await newSubscription.save()
}

export async function getAllFuneralBenefits(): Promise<IFuneralBenefits[]> {
  return FuneralBenefits.find().exec()
}

export async function getFuneralBenefitsById(id: string): Promise<IFuneralBenefits | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return FuneralBenefits.findById(id).exec()
}

export async function getFuneralBenefitsByUserId(userId: string): Promise<IFuneralBenefits[]> {
  if (!mongoose.Types.ObjectId.isValid(userId)) return []
  return FuneralBenefits.find({ userId: new mongoose.Types.ObjectId(userId) }).exec()
}

export async function updateFuneralBenefits(
  id: string,
  updateData: Partial<IFuneralBenefits>,
): Promise<IFuneralBenefits | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return FuneralBenefits.findByIdAndUpdate(id, updateData, { new: true }).exec()
}

export async function deleteFuneralBenefits(id: string): Promise<IFuneralBenefits | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null
  return FuneralBenefits.findByIdAndDelete(id).exec()
}
