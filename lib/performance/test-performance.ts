// lib/performance/test-performance.ts
// Test suite for performance monitoring system

import { cacheManager } from '@/lib/cache/cacheManager';
import { databaseOptimizer } from '@/lib/performance/databaseOptimizer';
import { apiMonitor } from '@/lib/performance/apiMonitor';
import { loadTester } from '@/lib/performance/loadTester';

export async function testPerformanceSystem() {
  console.log('🚀 Testing Performance Monitoring System...\n');

  const results = {
    cache: false,
    database: false,
    apiMonitor: false,
    loadTester: false
  };

  // Test Cache System
  try {
    console.log('📦 Testing Cache System...');
    
    // Test basic cache operations
    await cacheManager.set('test:performance', { message: 'Performance test data' }, 60);
    const cachedData = await cacheManager.get('test:performance');
    
    if (cachedData && cachedData.message === 'Performance test data') {
      console.log('✅ Cache system working correctly');
      results.cache = true;
    } else {
      console.log('❌ Cache system test failed');
    }

    // Test cache statistics
    const stats = await cacheManager.getStats();
    console.log('📊 Cache stats:', {
      redisConnected: stats.redis.connected,
      memorySize: stats.memory.size,
      hitRate: stats.memory.hitRate
    });

    // Cleanup
    await cacheManager.delete('test:performance');
  } catch (error) {
    console.error('❌ Cache system error:', error);
  }

  // Test Database Optimizer
  try {
    console.log('\n🗄️ Testing Database Optimizer...');
    
    // Test health metrics
    const health = await databaseOptimizer.getHealthMetrics();
    console.log('📊 Database health:', {
      status: health.connectionStatus,
      responseTime: health.responseTime,
      slowQueries: health.slowQueryCount
    });

    if (health.connectionStatus === 'connected' || health.responseTime >= 0) {
      console.log('✅ Database optimizer working correctly');
      results.database = true;
    } else {
      console.log('❌ Database optimizer test failed');
    }
  } catch (error) {
    console.error('❌ Database optimizer error:', error);
  }

  // Test API Monitor
  try {
    console.log('\n📡 Testing API Monitor...');
    
    // Test recording metrics
    apiMonitor.recordMetrics({
      endpoint: '/api/test',
      method: 'GET',
      responseTime: 150,
      statusCode: 200,
      requestSize: 1024,
      responseSize: 2048,
      timestamp: new Date()
    });

    // Test real-time metrics
    const realTimeMetrics = apiMonitor.getRealTimeMetrics();
    console.log('📊 API metrics:', {
      currentRPS: realTimeMetrics.currentRPS,
      averageResponseTime: realTimeMetrics.averageResponseTime,
      errorRate: realTimeMetrics.errorRate
    });

    console.log('✅ API monitor working correctly');
    results.apiMonitor = true;
  } catch (error) {
    console.error('❌ API monitor error:', error);
  }

  // Test Load Tester
  try {
    console.log('\n⚡ Testing Load Tester...');
    
    // Test scenario generation
    const scenarios = loadTester.generateCommonScenarios();
    console.log('📊 Generated scenarios:', scenarios.length);

    // Test active tests tracking
    const activeTests = loadTester.getActiveTests();
    console.log('📊 Active tests:', activeTests.length);

    console.log('✅ Load tester working correctly');
    results.loadTester = true;
  } catch (error) {
    console.error('❌ Load tester error:', error);
  }

  // Summary
  console.log('\n📋 Performance System Test Results:');
  console.log('Cache System:', results.cache ? '✅ PASSED' : '❌ FAILED');
  console.log('Database Optimizer:', results.database ? '✅ PASSED' : '❌ FAILED');
  console.log('API Monitor:', results.apiMonitor ? '✅ PASSED' : '❌ FAILED');
  console.log('Load Tester:', results.loadTester ? '✅ PASSED' : '❌ FAILED');

  const allPassed = Object.values(results).every(result => result);
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL SYSTEMS OPERATIONAL' : '❌ SOME SYSTEMS NEED ATTENTION');

  return results;
}

// Test performance optimization features
export async function testPerformanceOptimizations() {
  console.log('⚡ Testing Performance Optimizations...\n');

  try {
    // Test cache performance
    console.log('📦 Testing cache performance...');
    const cacheStart = Date.now();
    
    // Perform multiple cache operations
    for (let i = 0; i < 100; i++) {
      await cacheManager.set(`perf:test:${i}`, { id: i, data: `test data ${i}` });
    }
    
    for (let i = 0; i < 100; i++) {
      await cacheManager.get(`perf:test:${i}`);
    }
    
    const cacheTime = Date.now() - cacheStart;
    console.log(`✅ Cache operations (200 ops): ${cacheTime}ms (${(cacheTime / 200).toFixed(2)}ms per op)`);

    // Cleanup
    for (let i = 0; i < 100; i++) {
      await cacheManager.delete(`perf:test:${i}`);
    }

    // Test API monitoring overhead
    console.log('\n📡 Testing API monitoring overhead...');
    const monitorStart = Date.now();
    
    for (let i = 0; i < 100; i++) {
      apiMonitor.recordMetrics({
        endpoint: `/api/test/${i}`,
        method: 'GET',
        responseTime: Math.random() * 200 + 50,
        statusCode: 200,
        requestSize: 1024,
        responseSize: 2048,
        timestamp: new Date()
      });
    }
    
    const monitorTime = Date.now() - monitorStart;
    console.log(`✅ API monitoring (100 records): ${monitorTime}ms (${(monitorTime / 100).toFixed(2)}ms per record)`);

    // Test performance stats generation
    console.log('\n📊 Testing performance analytics...');
    const statsStart = Date.now();
    
    const performanceStats = apiMonitor.getPerformanceStats();
    const realTimeMetrics = apiMonitor.getRealTimeMetrics();
    const cacheStats = await cacheManager.getStats();
    
    const statsTime = Date.now() - statsStart;
    console.log(`✅ Analytics generation: ${statsTime}ms`);
    console.log('📊 Performance summary:', {
      totalRequests: performanceStats.totalRequests,
      averageResponseTime: performanceStats.averageResponseTime.toFixed(2),
      currentRPS: realTimeMetrics.currentRPS.toFixed(2),
      cacheHitRate: cacheStats.memory.hitRate
    });

    console.log('\n🎉 Performance optimization tests completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Performance optimization test failed:', error);
    return false;
  }
}

// Test system under simulated load
export async function testSystemUnderLoad() {
  console.log('🔥 Testing System Under Simulated Load...\n');

  try {
    const startTime = Date.now();
    const operations = [];

    // Simulate concurrent operations
    console.log('🚀 Starting concurrent operations...');
    
    // Cache operations
    for (let i = 0; i < 50; i++) {
      operations.push(
        cacheManager.set(`load:test:${i}`, { 
          id: i, 
          timestamp: Date.now(),
          data: `Load test data ${i}`.repeat(10) // Larger data
        })
      );
    }

    // API monitoring
    for (let i = 0; i < 50; i++) {
      operations.push(
        Promise.resolve().then(() => {
          apiMonitor.recordMetrics({
            endpoint: `/api/load/test/${i}`,
            method: 'POST',
            responseTime: Math.random() * 500 + 100,
            statusCode: Math.random() > 0.1 ? 200 : 500,
            requestSize: 2048,
            responseSize: 4096,
            timestamp: new Date()
          });
        })
      );
    }

    // Wait for all operations to complete
    await Promise.all(operations);
    
    const operationTime = Date.now() - startTime;
    console.log(`✅ Completed 100 concurrent operations in ${operationTime}ms`);

    // Test system responsiveness after load
    console.log('📊 Testing system responsiveness after load...');
    
    const responseStart = Date.now();
    const testData = await cacheManager.get('load:test:25');
    const stats = await cacheManager.getStats();
    const metrics = apiMonitor.getRealTimeMetrics();
    const responseTime = Date.now() - responseStart;
    
    console.log(`✅ System still responsive: ${responseTime}ms`);
    console.log('📊 Post-load metrics:', {
      cacheSize: stats.memory.size,
      apiRequests: metrics.currentRPS.toFixed(2),
      errorRate: metrics.errorRate.toFixed(2)
    });

    // Cleanup
    for (let i = 0; i < 50; i++) {
      await cacheManager.delete(`load:test:${i}`);
    }

    console.log('\n🎉 Load test completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Load test failed:', error);
    return false;
  }
}

// Run all performance tests
export async function runAllPerformanceTests() {
  console.log('🧪 Running Complete Performance Test Suite...\n');

  const results = {
    systemTest: await testPerformanceSystem(),
    optimizationTest: await testPerformanceOptimizations(),
    loadTest: await testSystemUnderLoad()
  };

  console.log('\n📋 Complete Test Results:');
  console.log('System Tests:', Object.values(results.systemTest).every(r => r) ? '✅ PASSED' : '❌ FAILED');
  console.log('Optimization Tests:', results.optimizationTest ? '✅ PASSED' : '❌ FAILED');
  console.log('Load Tests:', results.loadTest ? '✅ PASSED' : '❌ FAILED');

  const allPassed = Object.values(results.systemTest).every(r => r) && 
                   results.optimizationTest && 
                   results.loadTest;

  console.log('\n🏆 Final Result:', allPassed ? '✅ ALL PERFORMANCE TESTS PASSED' : '❌ SOME TESTS FAILED');
  console.log('🚀 Performance monitoring system is ready for production!');

  return results;
}
