// lib/performance/loadTester.ts
// Load testing and stress testing utilities
export interface LoadTestConfig {
  targetUrl: string;
  concurrentUsers: number;
  duration: number; // seconds
  rampUpTime: number; // seconds
  requestsPerSecond?: number;
  scenarios: LoadTestScenario[];
}

export interface LoadTestScenario {
  name: string;
  weight: number; // percentage of traffic
  requests: Array<{
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    path: string;
    headers?: Record<string, string>;
    body?: any;
    expectedStatus?: number;
    timeout?: number;
  }>;
}

export interface LoadTestResult {
  summary: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    minResponseTime: number;
    maxResponseTime: number;
    p50ResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    requestsPerSecond: number;
    errorRate: number;
    duration: number;
  };
  timeline: Array<{
    timestamp: number;
    activeUsers: number;
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
  }>;
  errors: Array<{
    error: string;
    count: number;
    percentage: number;
  }>;
  statusCodes: Record<number, number>;
  scenarios: Array<{
    name: string;
    requests: number;
    averageTime: number;
    errorRate: number;
  }>;
}

export interface StressTestConfig extends LoadTestConfig {
  maxUsers: number;
  userIncrement: number;
  incrementInterval: number; // seconds
  breakingPoint?: {
    errorRateThreshold: number;
    responseTimeThreshold: number;
  };
}

export class LoadTester {
  private activeTests: Map<string, boolean> = new Map();

  /**
   * Run a load test
   */
  async runLoadTest(config: LoadTestConfig): Promise<LoadTestResult> {
    const testId = this.generateTestId();
    this.activeTests.set(testId, true);

    try {
      console.log(`Starting load test ${testId} with ${config.concurrentUsers} users for ${config.duration}s`);

      const results: Array<{
        timestamp: number;
        responseTime: number;
        statusCode: number;
        error?: string;
        scenario: string;
      }> = [];

      const startTime = Date.now();
      const endTime = startTime + (config.duration * 1000);
      
      // Simulate load test execution
      // In a real implementation, this would spawn actual HTTP requests
      await this.simulateLoadTest(config, results, startTime, endTime, testId);

      // Analyze results
      return this.analyzeResults(results, config);
    } finally {
      this.activeTests.delete(testId);
    }
  }

  /**
   * Run a stress test to find breaking point
   */
  async runStressTest(config: StressTestConfig): Promise<{
    loadTestResults: LoadTestResult[];
    breakingPoint?: {
      userCount: number;
      errorRate: number;
      averageResponseTime: number;
    };
    recommendations: string[];
  }> {
    const testId = this.generateTestId();
    this.activeTests.set(testId, true);

    try {
      console.log(`Starting stress test ${testId} from ${config.concurrentUsers} to ${config.maxUsers} users`);

      const results: LoadTestResult[] = [];
      let currentUsers = config.concurrentUsers;
      let breakingPoint: any = null;

      while (currentUsers <= config.maxUsers && this.activeTests.get(testId)) {
        console.log(`Testing with ${currentUsers} concurrent users`);

        const testConfig: LoadTestConfig = {
          ...config,
          concurrentUsers: currentUsers,
          duration: Math.min(config.duration, 60) // Shorter duration for stress test
        };

        const result = await this.runLoadTest(testConfig);
        results.push(result);

        // Check for breaking point
        if (config.breakingPoint) {
          const { errorRateThreshold, responseTimeThreshold } = config.breakingPoint;
          
          if (result.summary.errorRate > errorRateThreshold || 
              result.summary.averageResponseTime > responseTimeThreshold) {
            breakingPoint = {
              userCount: currentUsers,
              errorRate: result.summary.errorRate,
              averageResponseTime: result.summary.averageResponseTime
            };
            console.log(`Breaking point found at ${currentUsers} users`);
            break;
          }
        }

        currentUsers += config.userIncrement;
        
        // Wait before next increment
        if (currentUsers <= config.maxUsers) {
          await new Promise(resolve => setTimeout(resolve, config.incrementInterval * 1000));
        }
      }

      const recommendations = this.generateRecommendations(results, breakingPoint);

      return {
        loadTestResults: results,
        breakingPoint,
        recommendations
      };
    } finally {
      this.activeTests.delete(testId);
    }
  }

  /**
   * Run endurance test
   */
  async runEnduranceTest(config: LoadTestConfig & { extendedDuration: number }): Promise<{
    result: LoadTestResult;
    memoryLeaks: boolean;
    performanceDegradation: boolean;
    recommendations: string[];
  }> {
    const testId = this.generateTestId();
    this.activeTests.set(testId, true);

    try {
      console.log(`Starting endurance test ${testId} for ${config.extendedDuration}s`);

      const extendedConfig = {
        ...config,
        duration: config.extendedDuration
      };

      const result = await this.runLoadTest(extendedConfig);

      // Analyze for memory leaks and performance degradation
      const memoryLeaks = this.detectMemoryLeaks(result);
      const performanceDegradation = this.detectPerformanceDegradation(result);

      const recommendations = [
        ...(memoryLeaks ? ['Investigate potential memory leaks'] : []),
        ...(performanceDegradation ? ['Optimize for sustained load'] : []),
        'Monitor resource usage during extended periods',
        'Implement proper connection pooling',
        'Consider implementing circuit breakers'
      ];

      return {
        result,
        memoryLeaks,
        performanceDegradation,
        recommendations
      };
    } finally {
      this.activeTests.delete(testId);
    }
  }

  /**
   * Generate load test scenarios for common patterns
   */
  generateCommonScenarios(): LoadTestScenario[] {
    return [
      {
        name: 'Browse Products',
        weight: 40,
        requests: [
          { method: 'GET', path: '/api/products', expectedStatus: 200 },
          { method: 'GET', path: '/api/categories', expectedStatus: 200 },
          { method: 'GET', path: '/api/products/search?q=laptop', expectedStatus: 200 }
        ]
      },
      {
        name: 'User Authentication',
        weight: 20,
        requests: [
          { 
            method: 'POST', 
            path: '/api/auth/login',
            headers: { 'Content-Type': 'application/json' },
            body: { email: '<EMAIL>', password: 'password' },
            expectedStatus: 200
          },
          { method: 'GET', path: '/api/user/profile', expectedStatus: 200 }
        ]
      },
      {
        name: 'Shopping Cart',
        weight: 25,
        requests: [
          { method: 'GET', path: '/api/cart', expectedStatus: 200 },
          {
            method: 'POST',
            path: '/api/cart/add',
            headers: { 'Content-Type': 'application/json' },
            body: { productId: 'test-product', quantity: 1 },
            expectedStatus: 200
          },
          { method: 'GET', path: '/api/cart', expectedStatus: 200 }
        ]
      },
      {
        name: 'Group Orders',
        weight: 15,
        requests: [
          { method: 'GET', path: '/api/groups', expectedStatus: 200 },
          { method: 'GET', path: '/api/groups/orders', expectedStatus: 200 },
          {
            method: 'POST',
            path: '/api/groups/join',
            headers: { 'Content-Type': 'application/json' },
            body: { groupId: 'test-group' },
            expectedStatus: 200
          }
        ]
      }
    ];
  }

  /**
   * Stop all active tests
   */
  stopAllTests(): void {
    this.activeTests.clear();
    console.log('All load tests stopped');
  }

  /**
   * Get active test status
   */
  getActiveTests(): string[] {
    return Array.from(this.activeTests.keys());
  }

  // Private helper methods
  private generateTestId(): string {
    return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async simulateLoadTest(
    config: LoadTestConfig,
    results: any[],
    startTime: number,
    endTime: number,
    testId: string
  ): Promise<void> {
    const rampUpEnd = startTime + (config.rampUpTime * 1000);
    let currentUsers = 0;

    // Simulate the test execution
    const interval = setInterval(() => {
      if (!this.activeTests.get(testId) || Date.now() > endTime) {
        clearInterval(interval);
        return;
      }

      const now = Date.now();
      
      // Ramp up users
      if (now < rampUpEnd) {
        const progress = (now - startTime) / (rampUpEnd - startTime);
        currentUsers = Math.floor(config.concurrentUsers * progress);
      } else {
        currentUsers = config.concurrentUsers;
      }

      // Simulate requests
      for (let i = 0; i < currentUsers; i++) {
        const scenario = this.selectScenario(config.scenarios);
        const request = scenario.requests[Math.floor(Math.random() * scenario.requests.length)];
        
        // Simulate response time (with some randomness)
        const baseTime = 100 + Math.random() * 200;
        const responseTime = Math.max(50, baseTime + (Math.random() - 0.5) * 100);
        
        // Simulate occasional errors
        const errorRate = 0.02; // 2% error rate
        const isError = Math.random() < errorRate;
        
        results.push({
          timestamp: now,
          responseTime,
          statusCode: isError ? 500 : (request.expectedStatus || 200),
          error: isError ? 'Simulated error' : undefined,
          scenario: scenario.name
        });
      }
    }, 1000); // Check every second

    // Wait for test duration
    await new Promise(resolve => setTimeout(resolve, config.duration * 1000));
    clearInterval(interval);
  }

  private selectScenario(scenarios: LoadTestScenario[]): LoadTestScenario {
    const random = Math.random() * 100;
    let cumulative = 0;
    
    for (const scenario of scenarios) {
      cumulative += scenario.weight;
      if (random <= cumulative) {
        return scenario;
      }
    }
    
    return scenarios[0]; // Fallback
  }

  private analyzeResults(results: any[], config: LoadTestConfig): LoadTestResult {
    if (results.length === 0) {
      throw new Error('No results to analyze');
    }

    const responseTimes = results.map(r => r.responseTime).sort((a, b) => a - b);
    const errors = results.filter(r => r.error);
    const statusCodes: Record<number, number> = {};
    const scenarioStats = new Map<string, { count: number; totalTime: number; errors: number }>();

    // Analyze results
    results.forEach(result => {
      // Status codes
      statusCodes[result.statusCode] = (statusCodes[result.statusCode] || 0) + 1;
      
      // Scenario stats
      const scenarioStat = scenarioStats.get(result.scenario) || { count: 0, totalTime: 0, errors: 0 };
      scenarioStat.count++;
      scenarioStat.totalTime += result.responseTime;
      if (result.error) scenarioStat.errors++;
      scenarioStats.set(result.scenario, scenarioStat);
    });

    // Calculate percentiles
    const p50 = responseTimes[Math.floor(responseTimes.length * 0.5)];
    const p95 = responseTimes[Math.floor(responseTimes.length * 0.95)];
    const p99 = responseTimes[Math.floor(responseTimes.length * 0.99)];

    // Error analysis
    const errorCounts = new Map<string, number>();
    errors.forEach(error => {
      const key = error.error || 'Unknown error';
      errorCounts.set(key, (errorCounts.get(key) || 0) + 1);
    });

    return {
      summary: {
        totalRequests: results.length,
        successfulRequests: results.length - errors.length,
        failedRequests: errors.length,
        averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
        minResponseTime: responseTimes[0],
        maxResponseTime: responseTimes[responseTimes.length - 1],
        p50ResponseTime: p50,
        p95ResponseTime: p95,
        p99ResponseTime: p99,
        requestsPerSecond: results.length / config.duration,
        errorRate: (errors.length / results.length) * 100,
        duration: config.duration
      },
      timeline: this.generateTimeline(results),
      errors: Array.from(errorCounts.entries()).map(([error, count]) => ({
        error,
        count,
        percentage: (count / errors.length) * 100
      })),
      statusCodes,
      scenarios: Array.from(scenarioStats.entries()).map(([name, stats]) => ({
        name,
        requests: stats.count,
        averageTime: stats.totalTime / stats.count,
        errorRate: (stats.errors / stats.count) * 100
      }))
    };
  }

  private generateTimeline(results: any[]): any[] {
    const timeline: any[] = [];
    const buckets = new Map<number, any[]>();

    // Group results by time buckets (10-second intervals)
    results.forEach(result => {
      const bucket = Math.floor(result.timestamp / 10000) * 10000;
      if (!buckets.has(bucket)) {
        buckets.set(bucket, []);
      }
      buckets.get(bucket)!.push(result);
    });

    // Calculate metrics for each bucket
    Array.from(buckets.entries()).sort(([a], [b]) => a - b).forEach(([timestamp, bucketResults]) => {
      const responseTimes = bucketResults.map(r => r.responseTime);
      const errors = bucketResults.filter(r => r.error);
      
      timeline.push({
        timestamp,
        activeUsers: bucketResults.length / 10, // Approximate
        requestsPerSecond: bucketResults.length / 10,
        averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
        errorRate: (errors.length / bucketResults.length) * 100
      });
    });

    return timeline;
  }

  private detectMemoryLeaks(result: LoadTestResult): boolean {
    // Simplified memory leak detection based on response time trends
    const timeline = result.timeline;
    if (timeline.length < 10) return false;

    const firstHalf = timeline.slice(0, Math.floor(timeline.length / 2));
    const secondHalf = timeline.slice(Math.floor(timeline.length / 2));

    const firstAvg = firstHalf.reduce((sum, t) => sum + t.averageResponseTime, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, t) => sum + t.averageResponseTime, 0) / secondHalf.length;

    // If response time increased by more than 50% in second half, possible memory leak
    return (secondAvg - firstAvg) / firstAvg > 0.5;
  }

  private detectPerformanceDegradation(result: LoadTestResult): boolean {
    // Check if performance degraded over time
    const timeline = result.timeline;
    if (timeline.length < 5) return false;

    const trend = this.calculateTrend(timeline.map(t => t.averageResponseTime));
    return trend > 0.2; // 20% increase trend indicates degradation
  }

  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + (i * val), 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope / (sumY / n); // Normalized slope
  }

  private generateRecommendations(results: LoadTestResult[], breakingPoint?: any): string[] {
    const recommendations: string[] = [];
    
    if (breakingPoint) {
      recommendations.push(`System breaking point: ${breakingPoint.userCount} concurrent users`);
      recommendations.push('Consider horizontal scaling before reaching breaking point');
    }

    const lastResult = results[results.length - 1];
    if (lastResult) {
      if (lastResult.summary.averageResponseTime > 1000) {
        recommendations.push('Optimize slow endpoints and database queries');
      }
      
      if (lastResult.summary.errorRate > 5) {
        recommendations.push('Investigate and fix high error rate issues');
      }
      
      if (lastResult.summary.requestsPerSecond < 10) {
        recommendations.push('Improve system throughput with caching and optimization');
      }
    }

    recommendations.push('Implement proper monitoring and alerting');
    recommendations.push('Consider implementing auto-scaling based on load');
    
    return recommendations;
  }
}

// Export singleton instance
export const loadTester = new LoadTester();
