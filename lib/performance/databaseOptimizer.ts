// lib/performance/databaseOptimizer.ts
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/dbconnect';

// Database performance monitoring types
export interface QueryPerformance {
  query: string;
  collection: string;
  executionTime: number;
  documentsExamined: number;
  documentsReturned: number;
  indexUsed: boolean;
  timestamp: Date;
}

export interface IndexRecommendation {
  collection: string;
  fields: string[];
  type: 'single' | 'compound' | 'text' | 'geospatial';
  reason: string;
  estimatedImprovement: number;
  priority: 'high' | 'medium' | 'low';
}

export interface DatabaseStats {
  collections: Array<{
    name: string;
    documentCount: number;
    avgDocumentSize: number;
    totalSize: number;
    indexCount: number;
    indexes: Array<{
      name: string;
      keys: any;
      size: number;
      usage: number;
    }>;
  }>;
  slowQueries: QueryPerformance[];
  recommendations: IndexRecommendation[];
  connectionPool: {
    active: number;
    available: number;
    total: number;
  };
}

export class DatabaseOptimizer {
  private slowQueryThreshold: number = 100; // milliseconds
  private slowQueries: QueryPerformance[] = [];

  constructor() {
    this.setupMonitoring();
  }

  /**
   * Setup database monitoring
   */
  private setupMonitoring() {
    // Monitor slow queries
    mongoose.set('debug', (collectionName: string, method: string, query: any, doc?: any) => {
      const startTime = Date.now();
      
      // This is a simplified monitoring setup
      // In production, you'd use MongoDB's built-in profiler
      setTimeout(() => {
        const executionTime = Date.now() - startTime;
        if (executionTime > this.slowQueryThreshold) {
          this.recordSlowQuery({
            query: JSON.stringify(query),
            collection: collectionName,
            executionTime,
            documentsExamined: 0, // Would need actual profiling data
            documentsReturned: 0, // Would need actual profiling data
            indexUsed: false, // Would need actual profiling data
            timestamp: new Date()
          });
        }
      }, 0);
    });
  }

  /**
   * Record slow query for analysis
   */
  private recordSlowQuery(queryPerf: QueryPerformance) {
    this.slowQueries.push(queryPerf);
    
    // Keep only last 1000 slow queries
    if (this.slowQueries.length > 1000) {
      this.slowQueries = this.slowQueries.slice(-1000);
    }

    console.warn(`Slow query detected: ${queryPerf.collection}.${queryPerf.query} (${queryPerf.executionTime}ms)`);
  }

  /**
   * Analyze database performance and generate recommendations
   */
  async analyzePerformance(): Promise<DatabaseStats> {
    await connectToDatabase();

    try {
      const db = mongoose.connection.db;
      const admin = db.admin();
      
      // Get database statistics
      const dbStats = await db.stats();
      const collections = await db.listCollections().toArray();
      
      const collectionStats = [];
      const recommendations: IndexRecommendation[] = [];

      // Analyze each collection
      for (const collection of collections) {
        const collName = collection.name;
        const coll = db.collection(collName);
        
        // Get collection stats
        const stats = await coll.stats();
        const indexes = await coll.indexes();
        
        // Analyze indexes
        const indexAnalysis = await this.analyzeIndexes(collName, indexes);
        recommendations.push(...indexAnalysis);

        collectionStats.push({
          name: collName,
          documentCount: stats.count || 0,
          avgDocumentSize: stats.avgObjSize || 0,
          totalSize: stats.size || 0,
          indexCount: indexes.length,
          indexes: indexes.map((index: any) => ({
            name: index.name,
            keys: index.key,
            size: index.totalIndexSize || 0,
            usage: 0 // Would need to track index usage
          }))
        });
      }

      // Get connection pool stats
      const connectionPool = {
        active: mongoose.connection.readyState === 1 ? 1 : 0,
        available: 10, // Default pool size
        total: 10
      };

      return {
        collections: collectionStats,
        slowQueries: this.getRecentSlowQueries(),
        recommendations,
        connectionPool
      };
    } catch (error) {
      console.error('Error analyzing database performance:', error);
      throw new Error('Failed to analyze database performance');
    }
  }

  /**
   * Analyze indexes and generate recommendations
   */
  private async analyzeIndexes(collectionName: string, indexes: any[]): Promise<IndexRecommendation[]> {
    const recommendations: IndexRecommendation[] = [];

    // Common patterns that need indexes
    const commonQueries = this.getCommonQueryPatterns(collectionName);
    
    for (const pattern of commonQueries) {
      const hasIndex = indexes.some(index => 
        this.indexCoversQuery(index.key, pattern.fields)
      );

      if (!hasIndex) {
        recommendations.push({
          collection: collectionName,
          fields: pattern.fields,
          type: pattern.type,
          reason: pattern.reason,
          estimatedImprovement: pattern.estimatedImprovement,
          priority: pattern.priority
        });
      }
    }

    return recommendations;
  }

  /**
   * Get common query patterns for collections
   */
  private getCommonQueryPatterns(collectionName: string): Array<{
    fields: string[];
    type: 'single' | 'compound' | 'text' | 'geospatial';
    reason: string;
    estimatedImprovement: number;
    priority: 'high' | 'medium' | 'low';
  }> {
    const patterns: any = {
      products: [
        {
          fields: ['category'],
          type: 'single',
          reason: 'Frequently filtered by category',
          estimatedImprovement: 80,
          priority: 'high'
        },
        {
          fields: ['price'],
          type: 'single',
          reason: 'Price range queries',
          estimatedImprovement: 60,
          priority: 'medium'
        },
        {
          fields: ['name', 'description'],
          type: 'text',
          reason: 'Text search functionality',
          estimatedImprovement: 90,
          priority: 'high'
        },
        {
          fields: ['category', 'price'],
          type: 'compound',
          reason: 'Category + price filtering',
          estimatedImprovement: 85,
          priority: 'high'
        },
        {
          fields: ['inStock', 'category'],
          type: 'compound',
          reason: 'Stock status with category',
          estimatedImprovement: 75,
          priority: 'medium'
        }
      ],
      grouporders: [
        {
          fields: ['userId'],
          type: 'single',
          reason: 'User order history',
          estimatedImprovement: 90,
          priority: 'high'
        },
        {
          fields: ['groupId'],
          type: 'single',
          reason: 'Group order queries',
          estimatedImprovement: 85,
          priority: 'high'
        },
        {
          fields: ['status'],
          type: 'single',
          reason: 'Order status filtering',
          estimatedImprovement: 70,
          priority: 'medium'
        },
        {
          fields: ['createdAt'],
          type: 'single',
          reason: 'Date range queries',
          estimatedImprovement: 80,
          priority: 'medium'
        },
        {
          fields: ['userId', 'status'],
          type: 'compound',
          reason: 'User orders by status',
          estimatedImprovement: 85,
          priority: 'high'
        }
      ],
      users: [
        {
          fields: ['email'],
          type: 'single',
          reason: 'Login and user lookup',
          estimatedImprovement: 95,
          priority: 'high'
        },
        {
          fields: ['createdAt'],
          type: 'single',
          reason: 'User registration analytics',
          estimatedImprovement: 60,
          priority: 'low'
        }
      ],
      groups: [
        {
          fields: ['createdBy'],
          type: 'single',
          reason: 'Groups created by user',
          estimatedImprovement: 80,
          priority: 'medium'
        },
        {
          fields: ['isActive'],
          type: 'single',
          reason: 'Active group filtering',
          estimatedImprovement: 70,
          priority: 'medium'
        },
        {
          fields: ['members.userId'],
          type: 'single',
          reason: 'User group membership',
          estimatedImprovement: 85,
          priority: 'high'
        }
      ],
      coupons: [
        {
          fields: ['code'],
          type: 'single',
          reason: 'Coupon code lookup',
          estimatedImprovement: 95,
          priority: 'high'
        },
        {
          fields: ['status'],
          type: 'single',
          reason: 'Active coupon filtering',
          estimatedImprovement: 80,
          priority: 'high'
        },
        {
          fields: ['validFrom', 'validUntil'],
          type: 'compound',
          reason: 'Date range validation',
          estimatedImprovement: 85,
          priority: 'high'
        }
      ]
    };

    return patterns[collectionName] || [];
  }

  /**
   * Check if an index covers a query pattern
   */
  private indexCoversQuery(indexKeys: any, queryFields: string[]): boolean {
    const indexFields = Object.keys(indexKeys);
    
    // For single field queries
    if (queryFields.length === 1) {
      return indexFields.includes(queryFields[0]);
    }

    // For compound queries, check if index covers all fields
    return queryFields.every(field => indexFields.includes(field));
  }

  /**
   * Create recommended indexes
   */
  async createRecommendedIndexes(recommendations: IndexRecommendation[]): Promise<{
    created: number;
    failed: number;
    errors: string[];
  }> {
    await connectToDatabase();
    
    let created = 0;
    let failed = 0;
    const errors: string[] = [];

    try {
      const db = mongoose.connection.db;

      for (const rec of recommendations) {
        try {
          const collection = db.collection(rec.collection);
          
          // Build index specification
          const indexSpec: any = {};
          
          if (rec.type === 'text') {
            // Text index
            rec.fields.forEach(field => {
              indexSpec[field] = 'text';
            });
          } else {
            // Regular index
            rec.fields.forEach(field => {
              indexSpec[field] = 1; // Ascending
            });
          }

          // Create index
          await collection.createIndex(indexSpec, {
            background: true, // Create in background
            name: `${rec.fields.join('_')}_${rec.type}`
          });

          created++;
          console.log(`Created index on ${rec.collection}: ${JSON.stringify(indexSpec)}`);
        } catch (error) {
          failed++;
          const errorMsg = `Failed to create index on ${rec.collection}: ${error}`;
          errors.push(errorMsg);
          console.error(errorMsg);
        }
      }
    } catch (error) {
      console.error('Error creating indexes:', error);
      errors.push(`General error: ${error}`);
    }

    return { created, failed, errors };
  }

  /**
   * Optimize database connections
   */
  async optimizeConnections(): Promise<void> {
    try {
      // Configure connection pool settings
      const optimizedOptions = {
        maxPoolSize: 10, // Maximum number of connections
        minPoolSize: 2,  // Minimum number of connections
        maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
        serverSelectionTimeoutMS: 5000, // How long to try selecting a server
        socketTimeoutMS: 45000, // How long a send or receive on a socket can take
        bufferMaxEntries: 0, // Disable mongoose buffering
        bufferCommands: false, // Disable mongoose buffering
      };

      // Apply optimizations (would need to reconnect with new options)
      console.log('Database connection optimization settings:', optimizedOptions);
    } catch (error) {
      console.error('Error optimizing database connections:', error);
    }
  }

  /**
   * Clean up old data
   */
  async cleanupOldData(): Promise<{
    collectionsProcessed: number;
    documentsRemoved: number;
  }> {
    await connectToDatabase();
    
    let collectionsProcessed = 0;
    let documentsRemoved = 0;

    try {
      const db = mongoose.connection.db;
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const sixMonthsAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);

      // Cleanup strategies for different collections
      const cleanupTasks = [
        {
          collection: 'notifications',
          condition: { createdAt: { $lt: thirtyDaysAgo }, read: true },
          reason: 'Remove old read notifications'
        },
        {
          collection: 'couponusages',
          condition: { usedAt: { $lt: sixMonthsAgo } },
          reason: 'Remove old coupon usage records'
        },
        {
          collection: 'analytics',
          condition: { createdAt: { $lt: sixMonthsAgo } },
          reason: 'Remove old analytics data'
        }
      ];

      for (const task of cleanupTasks) {
        try {
          const collection = db.collection(task.collection);
          const result = await collection.deleteMany(task.condition);
          
          documentsRemoved += result.deletedCount || 0;
          collectionsProcessed++;
          
          console.log(`Cleaned up ${result.deletedCount} documents from ${task.collection}: ${task.reason}`);
        } catch (error) {
          console.error(`Error cleaning up ${task.collection}:`, error);
        }
      }
    } catch (error) {
      console.error('Error during data cleanup:', error);
    }

    return { collectionsProcessed, documentsRemoved };
  }

  /**
   * Get recent slow queries
   */
  getRecentSlowQueries(limit: number = 50): QueryPerformance[] {
    return this.slowQueries
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Get database health metrics
   */
  async getHealthMetrics(): Promise<{
    connectionStatus: string;
    responseTime: number;
    activeConnections: number;
    slowQueryCount: number;
    indexEfficiency: number;
  }> {
    const startTime = Date.now();
    
    try {
      await connectToDatabase();
      
      // Test query to measure response time
      await mongoose.connection.db.admin().ping();
      
      const responseTime = Date.now() - startTime;
      const connectionStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
      
      return {
        connectionStatus,
        responseTime,
        activeConnections: 1, // Simplified
        slowQueryCount: this.slowQueries.length,
        indexEfficiency: 85 // Would calculate based on actual metrics
      };
    } catch (error) {
      console.error('Error getting database health metrics:', error);
      return {
        connectionStatus: 'error',
        responseTime: -1,
        activeConnections: 0,
        slowQueryCount: this.slowQueries.length,
        indexEfficiency: 0
      };
    }
  }
}

// Export singleton instance
export const databaseOptimizer = new DatabaseOptimizer();
