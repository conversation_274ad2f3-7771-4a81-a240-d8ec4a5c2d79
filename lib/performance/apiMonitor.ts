// lib/performance/apiMonitor.ts
import { NextRequest, NextResponse } from 'next/server';

// Performance monitoring types
export interface APIMetrics {
  endpoint: string;
  method: string;
  responseTime: number;
  statusCode: number;
  requestSize: number;
  responseSize: number;
  userAgent?: string;
  ip?: string;
  timestamp: Date;
  error?: string;
}

export interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  errorRate: number;
  requestsPerSecond: number;
  slowestEndpoints: Array<{
    endpoint: string;
    averageTime: number;
    requestCount: number;
  }>;
  statusCodeDistribution: Record<number, number>;
  hourlyStats: Array<{
    hour: string;
    requests: number;
    averageTime: number;
    errors: number;
  }>;
}

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: NextRequest) => string;
}

export class APIPerformanceMonitor {
  private metrics: APIMetrics[] = [];
  private rateLimitStore: Map<string, { count: number; resetTime: number }> = new Map();
  private maxMetricsHistory = 10000;

  /**
   * Record API request metrics
   */
  recordMetrics(metrics: APIMetrics): void {
    this.metrics.push(metrics);

    // Keep only recent metrics to prevent memory issues
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // Log slow requests
    if (metrics.responseTime > 1000) {
      console.warn(`Slow API request: ${metrics.method} ${metrics.endpoint} - ${metrics.responseTime}ms`);
    }

    // Log errors
    if (metrics.statusCode >= 400) {
      console.error(`API error: ${metrics.method} ${metrics.endpoint} - ${metrics.statusCode} ${metrics.error || ''}`);
    }
  }

  /**
   * Create middleware for automatic performance monitoring
   */
  createMiddleware() {
    return async (req: NextRequest, handler: (req: NextRequest) => Promise<NextResponse>) => {
      const startTime = Date.now();
      const requestSize = this.getRequestSize(req);
      
      let response: NextResponse;
      let error: string | undefined;

      try {
        response = await handler(req);
      } catch (err) {
        error = err instanceof Error ? err.message : 'Unknown error';
        response = NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }

      const endTime = Date.now();
      const responseTime = endTime - startTime;
      const responseSize = this.getResponseSize(response);

      // Record metrics
      this.recordMetrics({
        endpoint: req.nextUrl.pathname,
        method: req.method,
        responseTime,
        statusCode: response.status,
        requestSize,
        responseSize,
        userAgent: req.headers.get('user-agent') || undefined,
        ip: this.getClientIP(req),
        timestamp: new Date(),
        error
      });

      // Add performance headers
      response.headers.set('X-Response-Time', `${responseTime}ms`);
      response.headers.set('X-Request-ID', this.generateRequestId());

      return response;
    };
  }

  /**
   * Rate limiting middleware
   */
  createRateLimiter(config: RateLimitConfig) {
    return (req: NextRequest): { allowed: boolean; resetTime?: number; remaining?: number } => {
      const key = config.keyGenerator ? config.keyGenerator(req) : this.getClientIP(req) || 'anonymous';
      const now = Date.now();
      
      // Clean up expired entries
      this.cleanupRateLimitStore(now);

      const entry = this.rateLimitStore.get(key);
      
      if (!entry || now > entry.resetTime) {
        // New window or expired entry
        this.rateLimitStore.set(key, {
          count: 1,
          resetTime: now + config.windowMs
        });
        
        return {
          allowed: true,
          resetTime: now + config.windowMs,
          remaining: config.maxRequests - 1
        };
      }

      if (entry.count >= config.maxRequests) {
        return {
          allowed: false,
          resetTime: entry.resetTime,
          remaining: 0
        };
      }

      // Increment counter
      entry.count++;
      
      return {
        allowed: true,
        resetTime: entry.resetTime,
        remaining: config.maxRequests - entry.count
      };
    };
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(timeRange?: { start: Date; end: Date }): PerformanceStats {
    let filteredMetrics = this.metrics;

    if (timeRange) {
      filteredMetrics = this.metrics.filter(
        m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end
      );
    }

    if (filteredMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        errorRate: 0,
        requestsPerSecond: 0,
        slowestEndpoints: [],
        statusCodeDistribution: {},
        hourlyStats: []
      };
    }

    // Calculate basic stats
    const totalRequests = filteredMetrics.length;
    const averageResponseTime = filteredMetrics.reduce((sum, m) => sum + m.responseTime, 0) / totalRequests;
    const errorCount = filteredMetrics.filter(m => m.statusCode >= 400).length;
    const errorRate = (errorCount / totalRequests) * 100;

    // Calculate requests per second
    const timeSpan = timeRange 
      ? (timeRange.end.getTime() - timeRange.start.getTime()) / 1000
      : (Date.now() - filteredMetrics[0].timestamp.getTime()) / 1000;
    const requestsPerSecond = totalRequests / Math.max(timeSpan, 1);

    // Group by endpoint for slowest endpoints
    const endpointStats = new Map<string, { totalTime: number; count: number }>();
    filteredMetrics.forEach(m => {
      const key = `${m.method} ${m.endpoint}`;
      const existing = endpointStats.get(key) || { totalTime: 0, count: 0 };
      existing.totalTime += m.responseTime;
      existing.count += 1;
      endpointStats.set(key, existing);
    });

    const slowestEndpoints = Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({
        endpoint,
        averageTime: stats.totalTime / stats.count,
        requestCount: stats.count
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 10);

    // Status code distribution
    const statusCodeDistribution: Record<number, number> = {};
    filteredMetrics.forEach(m => {
      statusCodeDistribution[m.statusCode] = (statusCodeDistribution[m.statusCode] || 0) + 1;
    });

    // Hourly stats
    const hourlyStats = this.calculateHourlyStats(filteredMetrics);

    return {
      totalRequests,
      averageResponseTime,
      errorRate,
      requestsPerSecond,
      slowestEndpoints,
      statusCodeDistribution,
      hourlyStats
    };
  }

  /**
   * Get real-time metrics
   */
  getRealTimeMetrics(): {
    currentRPS: number;
    averageResponseTime: number;
    errorRate: number;
    activeConnections: number;
  } {
    const now = Date.now();
    const lastMinute = now - 60000; // Last minute
    
    const recentMetrics = this.metrics.filter(m => m.timestamp.getTime() > lastMinute);
    
    if (recentMetrics.length === 0) {
      return {
        currentRPS: 0,
        averageResponseTime: 0,
        errorRate: 0,
        activeConnections: 0
      };
    }

    const currentRPS = recentMetrics.length / 60; // Requests per second
    const averageResponseTime = recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / recentMetrics.length;
    const errorCount = recentMetrics.filter(m => m.statusCode >= 400).length;
    const errorRate = (errorCount / recentMetrics.length) * 100;

    return {
      currentRPS,
      averageResponseTime,
      errorRate,
      activeConnections: this.rateLimitStore.size // Approximate
    };
  }

  /**
   * Get endpoint performance analysis
   */
  getEndpointAnalysis(endpoint: string): {
    totalRequests: number;
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    errorRate: number;
    throughput: number;
    recentTrend: 'improving' | 'degrading' | 'stable';
  } {
    const endpointMetrics = this.metrics.filter(m => m.endpoint === endpoint);
    
    if (endpointMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        errorRate: 0,
        throughput: 0,
        recentTrend: 'stable'
      };
    }

    // Sort by response time for percentile calculations
    const sortedTimes = endpointMetrics.map(m => m.responseTime).sort((a, b) => a - b);
    
    const totalRequests = endpointMetrics.length;
    const averageResponseTime = sortedTimes.reduce((sum, time) => sum + time, 0) / totalRequests;
    const p95ResponseTime = sortedTimes[Math.floor(totalRequests * 0.95)] || 0;
    const p99ResponseTime = sortedTimes[Math.floor(totalRequests * 0.99)] || 0;
    
    const errorCount = endpointMetrics.filter(m => m.statusCode >= 400).length;
    const errorRate = (errorCount / totalRequests) * 100;
    
    // Calculate throughput (requests per minute)
    const timeSpan = (Date.now() - endpointMetrics[0].timestamp.getTime()) / 60000; // minutes
    const throughput = totalRequests / Math.max(timeSpan, 1);
    
    // Determine trend (simplified)
    const recentTrend = this.calculateTrend(endpointMetrics);

    return {
      totalRequests,
      averageResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      errorRate,
      throughput,
      recentTrend
    };
  }

  /**
   * Generate performance report
   */
  generateReport(): {
    summary: PerformanceStats;
    topEndpoints: Array<{ endpoint: string; analysis: any }>;
    recommendations: string[];
    alerts: Array<{ type: 'warning' | 'error'; message: string }>;
  } {
    const summary = this.getPerformanceStats();
    const realTime = this.getRealTimeMetrics();
    
    // Get top endpoints by request count
    const endpointCounts = new Map<string, number>();
    this.metrics.forEach(m => {
      const key = m.endpoint;
      endpointCounts.set(key, (endpointCounts.get(key) || 0) + 1);
    });

    const topEndpoints = Array.from(endpointCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([endpoint]) => ({
        endpoint,
        analysis: this.getEndpointAnalysis(endpoint)
      }));

    // Generate recommendations
    const recommendations: string[] = [];
    const alerts: Array<{ type: 'warning' | 'error'; message: string }> = [];

    if (summary.averageResponseTime > 500) {
      recommendations.push('Consider implementing caching for frequently accessed endpoints');
      alerts.push({
        type: 'warning',
        message: `Average response time is ${summary.averageResponseTime.toFixed(0)}ms (target: <500ms)`
      });
    }

    if (summary.errorRate > 5) {
      recommendations.push('Investigate and fix high error rate endpoints');
      alerts.push({
        type: 'error',
        message: `Error rate is ${summary.errorRate.toFixed(1)}% (target: <5%)`
      });
    }

    if (realTime.currentRPS > 100) {
      recommendations.push('Consider implementing rate limiting and load balancing');
      alerts.push({
        type: 'warning',
        message: `High request rate: ${realTime.currentRPS.toFixed(1)} RPS`
      });
    }

    // Check for slow endpoints
    summary.slowestEndpoints.forEach(endpoint => {
      if (endpoint.averageTime > 1000) {
        recommendations.push(`Optimize ${endpoint.endpoint} - average response time: ${endpoint.averageTime.toFixed(0)}ms`);
      }
    });

    return {
      summary,
      topEndpoints,
      recommendations,
      alerts
    };
  }

  // Helper methods
  private getRequestSize(req: NextRequest): number {
    const contentLength = req.headers.get('content-length');
    return contentLength ? parseInt(contentLength, 10) : 0;
  }

  private getResponseSize(response: NextResponse): number {
    const contentLength = response.headers.get('content-length');
    return contentLength ? parseInt(contentLength, 10) : 0;
  }

  private getClientIP(req: NextRequest): string | undefined {
    return req.headers.get('x-forwarded-for')?.split(',')[0] ||
           req.headers.get('x-real-ip') ||
           req.ip ||
           undefined;
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  private cleanupRateLimitStore(now: number): void {
    for (const [key, entry] of this.rateLimitStore.entries()) {
      if (now > entry.resetTime) {
        this.rateLimitStore.delete(key);
      }
    }
  }

  private calculateHourlyStats(metrics: APIMetrics[]): Array<{
    hour: string;
    requests: number;
    averageTime: number;
    errors: number;
  }> {
    const hourlyData = new Map<string, { requests: number; totalTime: number; errors: number }>();

    metrics.forEach(m => {
      const hour = new Date(m.timestamp).toISOString().substring(0, 13) + ':00:00';
      const existing = hourlyData.get(hour) || { requests: 0, totalTime: 0, errors: 0 };
      
      existing.requests += 1;
      existing.totalTime += m.responseTime;
      if (m.statusCode >= 400) existing.errors += 1;
      
      hourlyData.set(hour, existing);
    });

    return Array.from(hourlyData.entries())
      .map(([hour, data]) => ({
        hour,
        requests: data.requests,
        averageTime: data.totalTime / data.requests,
        errors: data.errors
      }))
      .sort((a, b) => a.hour.localeCompare(b.hour));
  }

  private calculateTrend(metrics: APIMetrics[]): 'improving' | 'degrading' | 'stable' {
    if (metrics.length < 10) return 'stable';

    const recent = metrics.slice(-5);
    const previous = metrics.slice(-10, -5);

    const recentAvg = recent.reduce((sum, m) => sum + m.responseTime, 0) / recent.length;
    const previousAvg = previous.reduce((sum, m) => sum + m.responseTime, 0) / previous.length;

    const change = (recentAvg - previousAvg) / previousAvg;

    if (change > 0.1) return 'degrading';
    if (change < -0.1) return 'improving';
    return 'stable';
  }
}

// Export singleton instance
export const apiMonitor = new APIPerformanceMonitor();
