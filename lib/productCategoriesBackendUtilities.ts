import { ProductCategory, IProductCategory } from '@/models/ProductCategory';
import { ProductCategoryArchive, IProductCategoryArchive } from '@/models/ProductCategoryArchive';
import mongoose from 'mongoose';

/**
 * CREATE a new ProductCategory
 */
export async function createProductCategory(
  name: string,
  description: string
): Promise<IProductCategory> {
  const newCategory = new ProductCategory({
    name,
    description,
  });
  return await newCategory.save();
}

/**
 * READ: Get all ProductCategories
 */
export async function getAllProductCategories(): Promise<IProductCategory[]> {
  return ProductCategory.find().exec();
}

/**
 * READ: Get a single ProductCategory by its ID
 */
export async function getProductCategoryById(id: string): Promise<IProductCategory | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return ProductCategory.findById(id).exec();
}

/**
 * UPDATE a ProductCategory by ID
 */
export async function updateProductCategory(
  id: string,
  updateData: Partial<IProductCategory>
): Promise<IProductCategory | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return ProductCategory.findByIdAndUpdate(id, updateData, { new: true }).exec();
}

/**
 * DELETE a ProductCategory by ID
 */
export async function deleteProductCategory(id: string): Promise<IProductCategory | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;
  return ProductCategory.findByIdAndDelete(id).exec();
}

/**
 * ARCHIVE a ProductCategory by ID with full archival process
 */
export async function archiveProductCategory(
  id: string, 
  userId?: string
): Promise<IProductCategory | null> {
  if (!mongoose.Types.ObjectId.isValid(id)) return null;

  const session = await mongoose.startSession();
  
  try {
    session.startTransaction();

    // Find the category to archive
    const categoryToArchive = await ProductCategory.findById(id).session(session);
    
    if (!categoryToArchive) {
      throw new Error('Category not found');
    }

    // Create archive record
    const archiveRecord = new ProductCategoryArchive({
      originalCategoryId: categoryToArchive._id,
      name: categoryToArchive.name,
      description: categoryToArchive.description,
      product_count: categoryToArchive.product_count,
      parent_category: categoryToArchive.parent_category,
      originalCreatedAt: categoryToArchive.createdAt,
      originalUpdatedAt: categoryToArchive.updatedAt,
      deletedBy: userId ? new mongoose.Types.ObjectId(userId) : undefined
    });

    // Save archive record
    await archiveRecord.save({ session });

    // Mark original category as inactive and archived
    const updatedCategory = await ProductCategory.findByIdAndUpdate(
      id, 
      { 
        is_active: false,
        archivedAt: new Date() 
      }, 
      { new: true, session }
    );

    // Commit transaction
    await session.commitTransaction();

    return updatedCategory;
  } catch (error) {
    // Rollback transaction if anything fails
    await session.abortTransaction();
    console.error('Failed to archive product category:', error);
    throw error;
  } finally {
    session.endSession();
  }
}

/**
 * RESTORE a ProductCategory from archive
 */
export async function restoreProductCategory(
  archivedCategoryId: string
): Promise<IProductCategory | null> {
  const session = await mongoose.startSession();

  try {
    session.startTransaction();

    // Find the archived category
    const archivedCategory = await ProductCategoryArchive.findById(archivedCategoryId).session(session);
    
    if (!archivedCategory) {
      throw new Error('Archived category not found');
    }

    // Restore the category
    const restoredCategory = await ProductCategory.findByIdAndUpdate(
      archivedCategory.originalCategoryId,
      {
        is_active: true,
        archivedAt: null
      },
      { new: true, session }
    );

    // Remove from archive
    await ProductCategoryArchive.findByIdAndDelete(archivedCategoryId).session(session);

    // Commit transaction
    await session.commitTransaction();

    return restoredCategory;
  } catch (error) {
    // Rollback transaction if anything fails
    await session.abortTransaction();
    console.error('Failed to restore product category:', error);
    throw error;
  } finally {
    session.endSession();
  }
}

/**
 * GET all archived ProductCategories
 */
export async function getArchivedProductCategories(): Promise<IProductCategoryArchive[]> {
  return ProductCategoryArchive.find()
    .sort({ archivedAt: -1 })
    .populate('originalCategoryId')
    .populate('deletedBy')
    .exec();
}
