// lib/ratingUtilities.ts

import mongoose from 'mongoose';
import { ProductRating, ProductRatingStats, IProductRating } from '@/models/ProductRating';
import { Product } from '@/models/Product';
import { User } from '@/models/User';
import { MemberOrder } from '@/models/MemberOrder';

export interface CreateRatingInput {
  userId: string;
  productId: string;
  rating: number;
  review?: string;
  title?: string;
}

export interface UpdateRatingInput {
  ratingId: string;
  rating?: number;
  review?: string;
  title?: string;
}

export interface RatingSummary {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  recentRatings: IProductRating[];
}

/**
 * Create a new product rating
 */
export async function createProductRating(input: CreateRatingInput): Promise<IProductRating | null> {
  try {
    const { userId, productId, rating, review, title } = input;

    // Validate inputs
    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error('Invalid user ID or product ID');
    }

    if (rating < 1 || rating > 5) {
      throw new Error('Rating must be between 1 and 5');
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      throw new Error('Product not found');
    }

    // Check if user has already rated this product
    const existingRating = await ProductRating.findOne({ userId, productId });
    if (existingRating) {
      throw new Error('You have already rated this product');
    }

    // Check if user has purchased this product (for verified purchase)
    const hasPurchased = await MemberOrder.findOne({
      userId,
      'items.product': productId,
      status: { $in: ['delivered', 'confirmed'] }
    });

    // Create the rating
    const productRating = new ProductRating({
      userId: new mongoose.Types.ObjectId(userId),
      productId: new mongoose.Types.ObjectId(productId),
      rating,
      review: review?.trim(),
      title: title?.trim(),
      isVerifiedPurchase: !!hasPurchased,
      status: 'active'
    });

    await productRating.save();
    
    // Populate user data for return
    await productRating.populate('userId', 'name');
    
    return productRating;
  } catch (error) {
    console.error('Error creating product rating:', error);
    return null;
  }
}

/**
 * Update an existing product rating
 */
export async function updateProductRating(input: UpdateRatingInput, userId: string): Promise<IProductRating | null> {
  try {
    const { ratingId, rating, review, title } = input;

    if (!mongoose.Types.ObjectId.isValid(ratingId)) {
      throw new Error('Invalid rating ID');
    }

    // Find the rating and ensure it belongs to the user
    const productRating = await ProductRating.findOne({ _id: ratingId, userId });
    if (!productRating) {
      throw new Error('Rating not found or access denied');
    }

    // Update fields if provided
    if (rating !== undefined) {
      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }
      productRating.rating = rating;
    }

    if (review !== undefined) {
      productRating.review = review.trim();
    }

    if (title !== undefined) {
      productRating.title = title.trim();
    }

    await productRating.save();
    await productRating.populate('userId', 'name');
    
    return productRating;
  } catch (error) {
    console.error('Error updating product rating:', error);
    return null;
  }
}

/**
 * Delete a product rating
 */
export async function deleteProductRating(ratingId: string, userId: string): Promise<boolean> {
  try {
    if (!mongoose.Types.ObjectId.isValid(ratingId)) {
      throw new Error('Invalid rating ID');
    }

    // Find and delete the rating, ensuring it belongs to the user
    const result = await ProductRating.deleteOne({ _id: ratingId, userId });
    return result.deletedCount > 0;
  } catch (error) {
    console.error('Error deleting product rating:', error);
    return false;
  }
}

/**
 * Get product ratings with pagination
 */
export async function getProductRatings(
  productId: string, 
  page: number = 1, 
  limit: number = 10,
  sortBy: 'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful' = 'newest'
): Promise<{ ratings: IProductRating[]; total: number; hasMore: boolean } | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error('Invalid product ID');
    }

    const skip = (page - 1) * limit;
    
    // Determine sort criteria
    let sortCriteria: any = { createdAt: -1 }; // Default: newest first
    switch (sortBy) {
      case 'oldest':
        sortCriteria = { createdAt: 1 };
        break;
      case 'highest':
        sortCriteria = { rating: -1, createdAt: -1 };
        break;
      case 'lowest':
        sortCriteria = { rating: 1, createdAt: -1 };
        break;
      case 'helpful':
        sortCriteria = { helpfulVotes: -1, createdAt: -1 };
        break;
    }

    const [ratings, total] = await Promise.all([
      ProductRating.find({ productId, status: 'active' })
        .populate('userId', 'name')
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit),
      ProductRating.countDocuments({ productId, status: 'active' })
    ]);

    return {
      ratings,
      total,
      hasMore: skip + ratings.length < total
    };
  } catch (error) {
    console.error('Error fetching product ratings:', error);
    return null;
  }
}

/**
 * Get rating summary for a product
 */
export async function getProductRatingSummary(productId: string): Promise<RatingSummary | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error('Invalid product ID');
    }

    // Get rating stats
    const stats = await ProductRating.getAverageRating(productId);
    
    // Get recent ratings
    const recentRatings = await ProductRating.find({ productId, status: 'active' })
      .populate('userId', 'name')
      .sort({ createdAt: -1 })
      .limit(5);

    return {
      averageRating: stats.averageRating,
      totalRatings: stats.totalRatings,
      ratingDistribution: stats.ratingDistribution,
      recentRatings
    };
  } catch (error) {
    console.error('Error getting product rating summary:', error);
    return null;
  }
}

/**
 * Get user's rating for a specific product
 */
export async function getUserProductRating(userId: string, productId: string): Promise<IProductRating | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(productId)) {
      return null;
    }

    const rating = await ProductRating.findOne({ userId, productId })
      .populate('userId', 'name');
    
    return rating;
  } catch (error) {
    console.error('Error getting user product rating:', error);
    return null;
  }
}

/**
 * Get user's all ratings
 */
export async function getUserRatings(userId: string): Promise<IProductRating[]> {
  try {
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return [];
    }

    const ratings = await ProductRating.find({ userId })
      .populate('productId', 'name image price')
      .sort({ createdAt: -1 });
    
    return ratings;
  } catch (error) {
    console.error('Error getting user ratings:', error);
    return [];
  }
}

/**
 * Mark rating as helpful
 */
export async function markRatingHelpful(ratingId: string): Promise<IProductRating | null> {
  try {
    if (!mongoose.Types.ObjectId.isValid(ratingId)) {
      throw new Error('Invalid rating ID');
    }

    const rating = await ProductRating.findById(ratingId);
    if (!rating) {
      throw new Error('Rating not found');
    }

    await rating.markHelpful();
    await rating.populate('userId', 'name');
    
    return rating;
  } catch (error) {
    console.error('Error marking rating as helpful:', error);
    return null;
  }
}

/**
 * Report a rating
 */
export async function reportRating(ratingId: string): Promise<boolean> {
  try {
    if (!mongoose.Types.ObjectId.isValid(ratingId)) {
      throw new Error('Invalid rating ID');
    }

    const rating = await ProductRating.findById(ratingId);
    if (!rating) {
      throw new Error('Rating not found');
    }

    await rating.report();
    return true;
  } catch (error) {
    console.error('Error reporting rating:', error);
    return false;
  }
}

/**
 * Get related products based on category and ratings
 */
export async function getRelatedProducts(productId: string, limit: number = 6): Promise<any[]> {
  try {
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      return [];
    }

    // Get the current product to find its category
    const currentProduct = await Product.findById(productId);
    if (!currentProduct) {
      return [];
    }

    // Find products in the same category, excluding the current product
    const relatedProducts = await Product.find({
      _id: { $ne: productId },
      category: currentProduct.category,
      stock: { $gt: 0 } // Only in-stock products
    })
    .populate('category', 'name')
    .sort({ averageRating: -1, createdAt: -1 }) // Sort by rating and recency
    .limit(limit);

    return relatedProducts;
  } catch (error) {
    console.error('Error getting related products:', error);
    return [];
  }
}
