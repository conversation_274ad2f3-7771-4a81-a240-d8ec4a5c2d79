"use client";

import { useQuery, useMutation, useQueryClient, UseMutationResult } from "@tanstack/react-query";
import axios from 'axios';

export interface ProductCategory {
  product_count: number;
  _id: string;
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateProductCategoryInput {
  name: string;
  description: string;
}

export interface UpdateProductCategoryInput {
  id: string;
  updateData: Partial<Omit<ProductCategory, '_id' | 'createdAt' | 'updatedAt'>>;
}

export interface ArchivedProductCategory extends ProductCategory {
  originalCategoryId: string;
  originalCreatedAt: Date;
  originalUpdatedAt: Date;
  archivedAt: Date;
  deletedBy?: string;
}

export async function getAllProductCategories(): Promise<ProductCategory[]> {
  const response = await fetch("/api/product-categories/get-all", { method: "GET" });
  if (!response.ok) {
    throw new Error("Failed to fetch ProductCategories");
  }
  return response.json();
}

export async function createProductCategory(data: CreateProductCategoryInput): Promise<ProductCategory> {
  const response = await fetch("/api/product-categories/create", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error("Failed to create ProductCategory");
  }
  return response.json();
}

export async function updateProductCategory(input: UpdateProductCategoryInput): Promise<ProductCategory> {
  const response = await fetch("/api/product-categories/update", {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(input),
  });
  if (!response.ok) {
    throw new Error("Failed to update ProductCategory");
  }
  return response.json();
}

export async function deleteProductCategory(id: string): Promise<void> {
  const response = await fetch("/api/product-categories/delete", {
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ id }),
  });
  if (!response.ok) {
    throw new Error("Failed to delete ProductCategory");
  }
}

export const archiveProductCategory = async (categoryId: string) => {
  try {
    const response = await axios.delete(`/api/product-categories/delete?id=${categoryId}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.status !== 200) {
      throw new Error('Failed to archive product category');
    }

    return response.data;
  } catch (error) {
    console.error('Error archiving product category:', error);
    throw error;
  }
};

export const restoreArchivedProductCategory = async (archivedCategoryId: string) => {
  try {
    const response = await axios.post(`/api/product-categories/restore`, 
      { archivedCategoryId },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.status !== 200) {
      throw new Error('Failed to restore product category');
    }

    return response.data;
  } catch (error) {
    console.error('Error restoring product category:', error);
    throw error;
  }
};

export const getArchivedProductCategories = async (): Promise<ArchivedProductCategory[]> => {
  try {
    const response = await axios.get('/api/product-categories/get-archived');
    return response.data;
  } catch (error) {
    console.error('Error fetching archived product categories:', error);
    throw error;
  }
};

export function useGetAllProductCategories() {
  return useQuery<ProductCategory[], Error>({
    queryKey: ["productCategories"],
    queryFn: getAllProductCategories,
  });
}

export function useCreateProductCategory(): UseMutationResult<ProductCategory, Error, CreateProductCategoryInput, unknown> {
  const queryClient = useQueryClient();

  return useMutation<ProductCategory, Error, CreateProductCategoryInput, unknown>({
    mutationFn: createProductCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
    },
  });
}

export function useUpdateProductCategory(): UseMutationResult<ProductCategory, Error, UpdateProductCategoryInput, unknown> {
  const queryClient = useQueryClient();

  return useMutation<ProductCategory, Error, UpdateProductCategoryInput, unknown>({
    mutationFn: updateProductCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
    },
  });
}

export function useDeleteProductCategory(): UseMutationResult<void, Error, string, unknown> {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string, unknown>({
    mutationFn: deleteProductCategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
    },
  });
}

export function useGetArchivedProductCategories() {
  return useQuery<ArchivedProductCategory[], Error>({
    queryKey: ["archivedProductCategories"],
    queryFn: getArchivedProductCategories,
  });
}

export function useRestoreArchivedProductCategory(): UseMutationResult<
  ProductCategory, 
  Error, 
  string, 
  unknown
> {
  const queryClient = useQueryClient();

  return useMutation<ProductCategory, Error, string, unknown>({
    mutationFn: restoreArchivedProductCategory,
    onSuccess: () => {
      // Invalidate queries to refetch latest data
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
      queryClient.invalidateQueries({ queryKey: ["archivedProductCategories"] });
    },
  });
}
