#!/bin/bash

echo "🧹 Cleaning build artifacts and caches..."

# Remove Next.js build cache
rm -rf .next

# Remove TypeScript build cache
rm -rf .tsbuildinfo

# Remove node_modules cache (if needed)
# rm -rf node_modules/.cache

# Remove ESLint cache
rm -rf .eslintcache

echo "✅ Cleaned all caches"

echo "🔧 Running type check..."
npx tsc --noEmit --skipLibCheck

echo "🔍 Running ESLint..."
npx eslint . --ext .ts,.tsx --max-warnings 0

echo "🏗️ Building project..."
npm run build

echo "✅ Build completed!"
