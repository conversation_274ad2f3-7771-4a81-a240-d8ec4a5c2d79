

export type ProductId = string;

export interface BaseProduct {
  _id: ProductId;
  name: string;
  price: number;
  imageUrl: string;
  backgroundColor: string;
}

export interface ShoeProduct extends BaseProduct {
  category: string;
  colors: string[];
  sizes: string[];
}

export interface GroceryProduct extends BaseProduct {
  brand: string;
  category: string;
}

export interface FoodProduct extends BaseProduct {
  cuisine: string;
  dietaryRestrictions: string[];
}

export interface WholesaleProduct extends BaseProduct {
  category: string;
  quantity: string;
}

export const shoeProducts: ShoeProduct[] = [
  {
    _id: "1",
    name: "Nike Air Max 270",
    category: "Men's Shoe",
    price: 2775, // 150 * 18.5
    colors: ["Black", "White"],
    sizes: ["9", "10", "11"],
    imageUrl: "/products/bq9647-002-phsrh000-2000_1.jpg",
    backgroundColor: "#E8F5E9"
  },
  {
    _id: "2",
    name: "Nike Air VaporMax Utility",
    category: "Men's Shoe",
    price: 3515, // 190 * 18.5
    colors: ["Blue", "Black"],
    sizes: ["8", "9", "10", "11"],
    imageUrl: "/products/Nike-Air-VaporMax-Utility-4.jpeg",
    backgroundColor: "#E3F2FD"
  },
  {
    _id: "3",
    name: "Nike Free RN Flyknit 3.0",
    category: "Men's Shoe",
    price: 2405, // 130 * 18.5
    colors: ["Red", "White"],
    sizes: ["8", "9", "10"],
    imageUrl: "/products/NIKE+FREE+METCON+5.png",
    backgroundColor: "#FBE9E7"
  },
  {
    _id: "4",
    name: "Nike React Infinity Run",
    category: "Women's Shoe",
    price: 2960, // 160 * 18.5
    colors: ["Pink", "White"],
    sizes: ["6", "7", "8", "9"],
    imageUrl: "/products/react-infinity-run-2-hd-1600-1578610530.jpg",
    backgroundColor: "#FCE4EC"
  },
  {
    _id: "5",
    name: "Nike Air Zoom Pegasus 37",
    category: "Women's Shoe",
    price: 2220, // 120 * 18.5
    colors: ["Purple", "Black"],
    sizes: ["7", "8", "9"],
    imageUrl: "/products/jd_019354_a.webp",
    backgroundColor: "#EDE7F6"
  },
  {
    _id: "6",
    name: "Nike Metcon 5",
    category: "Training Shoe",
    price: 2405, // 130 * 18.5
    colors: ["Green", "Black"],
    sizes: ["8", "9", "10", "11"],
    imageUrl: "/products/61BzOAVGpTL._AC_UY1000_.jpg",
    backgroundColor: "#E8F5E9"
  }
];


export const foodProducts: FoodProduct[] = [
  {
    _id: "f1",
    name: "Margherita Pizza",
    cuisine: "Italian",
    price: 240.32, // 12.99 * 18.5
    dietaryRestrictions: ["Vegetarian"],
    imageUrl: "/products/istockphoto-686957124-612x612.jpg",
    backgroundColor: "#FFEBEE"
  },
  {
    _id: "f2",
    name: "Chicken Tikka Masala",
    cuisine: "Indian",
    price: 277.32, // 14.99 * 18.5
    dietaryRestrictions: ["Gluten-Free"],
    imageUrl: "/products/chicken-8df7443b3619054bfff7d3f4cb8d30b7.jpeg",
    backgroundColor: "#FFF3E0"
  },
  {
    _id: "f3",
    name: "Vegan Burrito Bowl",
    cuisine: "Mexican",
    price: 203.32, // 10.99 * 18.5
    dietaryRestrictions: ["Vegan", "Gluten-Free"],
    imageUrl: "/products/220223.webp",
    backgroundColor: "#E8F5E9"
  },
  {
    _id: "f4",
    name: "Sushi Platter",
    cuisine: "Japanese",
    price: 351.32, // 18.99 * 18.5
    dietaryRestrictions: ["Pescatarian"],
    imageUrl: "/products/istockphoto-177096343-612x612.jpg",
    backgroundColor: "#E0F7FA"
  }
];

// export const shoeProducts: ShoeProduct[] = [
//   {
//     _id: "000000000000000000000001",
//     name: "Nike Air Max 270",
//     category: "Men's Shoe",
//     price: 2775,
//     colors: ["Black", "White"],
//     sizes: ["9", "10", "11"],
//     imageUrl: "/products/bq9647-002-phsrh000-2000_1.jpg",
//     backgroundColor: "#E8F5E9"
//   },
//   // ... other shoe products (keep _id as string)
// ];

export const groceryProducts: GroceryProduct[] = [
  {
    _id: "g1",
    name: "Fresh Apples",
    brand: "Organic Farms",
    category: "Fruits & Vegetables",
    price: 55.32, // 2.99 * 18.5
    imageUrl: "/products/depositphotos_122644810-stock-photo-vintage-box-with-freshly-harvested.jpg",
    backgroundColor: "#FFEBEE"
  },
  {
    _id: "g2",
    name: "Whole Milk",
    brand: "Dairy Delight",
    category: "Dairy",
    price: 64.57, // 3.49 * 18.5
    imageUrl: "/products/d75b3d0152c5c23165ce523054d68f24.jpg",
    backgroundColor: "#E8EAF6"
  },
  {
    _id: "g3",
    name: "Whole Wheat Bread",
    brand: "Bakery Bliss",
    category: "Bakery",
    price: 51.62, // 2.79 * 18.5
    imageUrl: "/products/wholebread.png",
    backgroundColor: "#FFF3E0"
  },
  {
    _id: "g4",
    name: "Canned Tomatoes",
    brand: "Pantry Essentials",
    category: "Canned Goods",
    price: 36.82, // 1.99 * 18.5
    imageUrl: "/products/High-Quality-Canned-Tomato-Paste-for-Exporting.avif",
    backgroundColor: "#FFCDD2"
  }
];

// export const foodProducts: FoodProduct[] = [
//   {
//     _id: "00000000000000000000000b",
//     name: "Margherita Pizza",
//     cuisine: "Italian",
//     price: 240.32,
//     dietaryRestrictions: ["Vegetarian"],
//     imageUrl: "/products/istockphoto-686957124-612x612.jpg",
//     backgroundColor: "#FFEBEE"
//   },
//   // ... other food products (keep _id as string)
// ];


export const wholesaleProducts: WholesaleProduct[] = [
  {
    _id: "w1",
    name: "Bulk Rice",
    category: "Bulk Foods",
    price: 850.82, // 45.99 * 18.5
    quantity: "25 kg",
    imageUrl: "/products/bulk-buying-items-such-as-rice-lentils_419341-146115.jpg",
    backgroundColor: "#FFF3E0"
  },
  {
    _id: "w2",
    name: "Industrial Cleaning Solution",
    category: "Cleaning Supplies",
    price: 554.82, // 29.99 * 18.5
    quantity: "5 L",
    imageUrl: "/products/21675-zep-products.jpg",
    backgroundColor: "#E0F7FA"
  },
  {
    _id: "w3",
    name: "Office Paper",
    category: "Office Supplies",
    price: 647.32, // 34.99 * 18.5
    quantity: "5000 sheets",
    imageUrl: "/products/Hd51a2dfa186a4d608c37195bfd2515da6.jpg_720x720q50.avif",
    backgroundColor: "#F3E5F5"
  },
  {
    _id: "w4",
    name: "Cardboard Boxes",
    category: "Packaging",
    price: 369.82, // 19.99 * 18.5
    quantity: "50 units",
    imageUrl: "/products/istockphoto-183027527-612x612.jpg",
    backgroundColor: "#FFFDE7"
  }
];


