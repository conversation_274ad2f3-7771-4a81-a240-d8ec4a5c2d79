# Cart Migration Plan

## Files to Remove After Testing

Once we've confirmed that all components are working correctly with Redux, we can remove these files:

1. `context/CombinedCartContext.tsx` - Replaced by Redux cart state
2. `context/ShoppingCartProvider.tsx` - Replaced by CartProvider component
3. `context/CartContext.tsx` - Replaced by Redux cart state
4. `components/cart/CartPayment.tsx` - Replaced by ReduxCartPayment
5. `components/cart/Checkout.tsx` - Replaced by ReduxCheckout
6. `components/group-orders/GroupOrderCheckout.tsx` - Replaced by GroupOrderReduxCheckout
7. `components/group-orders/GroupOrderCart.tsx` - Replaced by GroupOrderReduxCart
8. `components/group-orders/GroupOrderOverlay.tsx` - Replaced by GroupOrderReduxOverlay

## Components to Update

These components have been updated to use Redux:

1. `components/cart/CartList.tsx`
2. `components/search/FilterProductCard.tsx`
3. `components/home/<USER>
4. `app/(group)/groups/[groupId]/page.tsx`
5. `app/(group)/groups/[groupId]/new-order/page.tsx`
6. `components/AuthWrapper.tsx`
7. `app/(profile)/profile/layout.tsx`

## New Components Created

These new components have been created to use Redux:

1. `components/cart/ReduxCartPayment.tsx`
2. `components/checkout/ReduxCheckout.tsx`
3. `components/group-orders/GroupOrderReduxCheckout.tsx`
4. `components/group-orders/GroupOrderReduxCart.tsx`
5. `components/group-orders/GroupOrderReduxOverlay.tsx`
6. `lib/redux/hooks/useCheckout.ts`

## Testing Plan

1. Test adding products to cart from different pages
2. Test updating cart quantities
3. Test removing items from cart
4. Test checkout flow
5. Test group order creation
6. Test cart persistence between page refreshes
7. Test cart synchronization between tabs

## Final Steps

After testing is complete:

1. Rename the new Redux components to replace the old ones
2. Remove the old context files
3. Update any remaining imports
